"""
Web爬取模块
用于从steamdt.com网站爬取排行榜数据和K线数据
"""

import asyncio
import aiohttp
import logging
import json
import re
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

from core.exceptions import APIResponseError, ErrorContext


@dataclass
class RankingItem:
    """排行榜项目数据"""
    rank: int
    item_name: str
    market_hash_name: str
    current_price: float
    price_change: float
    price_change_percent: float
    volume_24h: int
    icon_url: str
    item_url: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'rank': self.rank,
            'item_name': self.item_name,
            'market_hash_name': self.market_hash_name,
            'current_price': self.current_price,
            'price_change': self.price_change,
            'price_change_percent': self.price_change_percent,
            'volume_24h': self.volume_24h,
            'icon_url': self.icon_url,
            'item_url': self.item_url
        }


@dataclass
class KLineData:
    """K线数据"""
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'open': self.open_price,
            'high': self.high_price,
            'low': self.low_price,
            'close': self.close_price,
            'volume': self.volume
        }


class SteamdtWebScraper:
    """SteamDT网站爬取器"""
    
    def __init__(self):
        """初始化爬取器"""
        self.base_url = "https://steamdt.com"
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger(__name__)
        
        # 请求头，模拟浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # 排行榜URL映射
        self.ranking_urls = {
            'hot': '/ranking/hot',
            'trending': '/ranking/trending', 
            'rising': '/ranking/rising',
            'falling': '/ranking/falling'
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
    
    async def start_session(self):
        """启动HTTP会话"""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self.session = aiohttp.ClientSession(
                headers=self.headers,
                timeout=timeout,
                connector=aiohttp.TCPConnector(limit=10, limit_per_host=5)
            )
            self.logger.info("Web scraper session started")
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None
            self.logger.info("Web scraper session closed")
    
    async def _fetch_page(self, url: str, params: Dict[str, Any] = None) -> str:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            params: 查询参数
            
        Returns:
            str: 网页HTML内容
        """
        if not self.session:
            await self.start_session()
        
        try:
            full_url = urljoin(self.base_url, url)
            self.logger.debug(f"Fetching page: {full_url}")
            
            async with self.session.get(full_url, params=params) as response:
                if response.status == 200:
                    content = await response.text()
                    self.logger.debug(f"Successfully fetched page: {full_url}")
                    return content
                else:
                    self.logger.error(f"HTTP {response.status} for {full_url}")
                    raise APIResponseError(f"HTTP {response.status} for {full_url}")
                    
        except aiohttp.ClientError as e:
            self.logger.error(f"Network error fetching {url}: {e}")
            raise APIResponseError(f"Network error: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error fetching {url}: {e}")
            raise APIResponseError(f"Unexpected error: {e}")
    
    async def get_ranking_data(self, ranking_type: str, limit: int = 50) -> List[RankingItem]:
        """
        获取排行榜数据
        
        Args:
            ranking_type: 排行榜类型 ('hot', 'trending', 'rising', 'falling')
            limit: 获取数量限制
            
        Returns:
            List[RankingItem]: 排行榜项目列表
        """
        if ranking_type not in self.ranking_urls:
            raise ValueError(f"Invalid ranking type: {ranking_type}")
        
        try:
            url = self.ranking_urls[ranking_type]
            html_content = await self._fetch_page(url, {'limit': limit})
            
            # 解析HTML内容
            soup = BeautifulSoup(html_content, 'html.parser')
            ranking_items = self._parse_ranking_page(soup, ranking_type)
            
            self.logger.info(f"Successfully scraped {len(ranking_items)} items from {ranking_type} ranking")
            return ranking_items
            
        except Exception as e:
            self.logger.error(f"Error scraping {ranking_type} ranking: {e}")
            return []
    
    def _parse_ranking_page(self, soup: BeautifulSoup, ranking_type: str) -> List[RankingItem]:
        """
        解析排行榜页面
        
        Args:
            soup: BeautifulSoup对象
            ranking_type: 排行榜类型
            
        Returns:
            List[RankingItem]: 解析后的排行榜项目
        """
        items = []
        
        try:
            # 查找排行榜表格或列表容器
            # 这里需要根据steamdt.com的实际HTML结构来调整选择器
            ranking_container = soup.find('div', class_='ranking-list') or soup.find('table', class_='ranking-table')
            
            if not ranking_container:
                self.logger.warning(f"No ranking container found for {ranking_type}")
                return items
            
            # 查找排行榜项目
            item_elements = ranking_container.find_all('tr', class_='ranking-item') or \
                           ranking_container.find_all('div', class_='item-row')
            
            for idx, element in enumerate(item_elements, 1):
                try:
                    item = self._parse_ranking_item(element, idx)
                    if item:
                        items.append(item)
                except Exception as e:
                    self.logger.warning(f"Error parsing ranking item {idx}: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Error parsing ranking page: {e}")
        
        return items
    
    def _parse_ranking_item(self, element, rank: int) -> Optional[RankingItem]:
        """
        解析单个排行榜项目
        
        Args:
            element: HTML元素
            rank: 排名
            
        Returns:
            Optional[RankingItem]: 解析后的项目，失败时返回None
        """
        try:
            # 这里需要根据steamdt.com的实际HTML结构来调整
            # 以下是示例解析逻辑，需要根据实际页面结构调整
            
            # 饰品名称
            name_element = element.find('a', class_='item-name') or element.find('td', class_='name')
            item_name = name_element.get_text(strip=True) if name_element else ""
            
            # 市场哈希名称（通常与饰品名称相同或从链接中提取）
            market_hash_name = item_name
            
            # 当前价格
            price_element = element.find('span', class_='price') or element.find('td', class_='price')
            price_text = price_element.get_text(strip=True) if price_element else "0"
            current_price = self._parse_price(price_text)
            
            # 价格变化
            change_element = element.find('span', class_='change') or element.find('td', class_='change')
            change_text = change_element.get_text(strip=True) if change_element else "0"
            price_change, price_change_percent = self._parse_price_change(change_text)
            
            # 24小时交易量
            volume_element = element.find('span', class_='volume') or element.find('td', class_='volume')
            volume_text = volume_element.get_text(strip=True) if volume_element else "0"
            volume_24h = self._parse_volume(volume_text)
            
            # 图标URL
            icon_element = element.find('img', class_='item-icon')
            icon_url = icon_element.get('src', '') if icon_element else ""
            
            # 项目URL
            link_element = element.find('a')
            item_url = link_element.get('href', '') if link_element else ""
            if item_url and not item_url.startswith('http'):
                item_url = urljoin(self.base_url, item_url)
            
            return RankingItem(
                rank=rank,
                item_name=item_name,
                market_hash_name=market_hash_name,
                current_price=current_price,
                price_change=price_change,
                price_change_percent=price_change_percent,
                volume_24h=volume_24h,
                icon_url=icon_url,
                item_url=item_url
            )
            
        except Exception as e:
            self.logger.error(f"Error parsing ranking item: {e}")
            return None
    
    def _parse_price(self, price_text: str) -> float:
        """解析价格文本"""
        try:
            # 移除货币符号和其他非数字字符
            price_clean = re.sub(r'[^\d.,]', '', price_text)
            price_clean = price_clean.replace(',', '')
            return float(price_clean) if price_clean else 0.0
        except (ValueError, TypeError):
            return 0.0
    
    def _parse_price_change(self, change_text: str) -> tuple[float, float]:
        """解析价格变化文本"""
        try:
            # 提取数字和百分比
            numbers = re.findall(r'[-+]?\d*\.?\d+', change_text)
            if len(numbers) >= 2:
                return float(numbers[0]), float(numbers[1])
            elif len(numbers) == 1:
                if '%' in change_text:
                    return 0.0, float(numbers[0])
                else:
                    return float(numbers[0]), 0.0
            return 0.0, 0.0
        except (ValueError, TypeError):
            return 0.0, 0.0
    
    def _parse_volume(self, volume_text: str) -> int:
        """解析交易量文本"""
        try:
            volume_clean = re.sub(r'[^\d]', '', volume_text)
            return int(volume_clean) if volume_clean else 0
        except (ValueError, TypeError):
            return 0


# 全局爬取器实例
_web_scraper: Optional[SteamdtWebScraper] = None


async def get_web_scraper() -> SteamdtWebScraper:
    """获取全局web爬取器实例"""
    global _web_scraper
    
    if _web_scraper is None:
        _web_scraper = SteamdtWebScraper()
        await _web_scraper.start_session()
    
    return _web_scraper


async def close_web_scraper():
    """关闭全局web爬取器"""
    global _web_scraper
    
    if _web_scraper:
        await _web_scraper.close_session()
        _web_scraper = None
