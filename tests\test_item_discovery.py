"""
饰品发现算法测试
验证饰品发现和评估逻辑的正确性
"""

import pytest
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.item_discovery import (
    ItemDiscoveryService, ItemEvaluation, ItemValueTier
)
from services.web_scraper import RankingItem


class TestItemValueTier:
    """饰品价值等级测试"""
    
    def test_value_tier_values(self):
        """测试价值等级枚举值"""
        assert ItemValueTier.PREMIUM.value == "premium"
        assert ItemValueTier.HIGH.value == "high"
        assert ItemValueTier.MEDIUM.value == "medium"
        assert ItemValueTier.LOW.value == "low"
        assert ItemValueTier.MICRO.value == "micro"


class TestItemEvaluation:
    """饰品评估结果测试"""
    
    def test_item_evaluation_creation(self):
        """测试饰品评估结果创建"""
        now = datetime.now()
        evaluation = ItemEvaluation(
            item_name="AK-47 | Redline",
            market_hash_name="AK-47 | Redline (Field-Tested)",
            current_price=50.0,
            value_tier=ItemValueTier.MEDIUM,
            investment_score=75.0,
            liquidity_score=80.0,
            volatility_score=70.0,
            trend_score=65.0,
            final_score=72.5,
            reasons=["高流动性", "适度波动"],
            discovered_from="ranking_analysis",
            discovered_at=now
        )
        
        assert evaluation.item_name == "AK-47 | Redline"
        assert evaluation.value_tier == ItemValueTier.MEDIUM
        assert evaluation.final_score == 72.5
        assert len(evaluation.reasons) == 2
    
    def test_item_evaluation_to_dict(self):
        """测试饰品评估结果字典转换"""
        now = datetime.now()
        evaluation = ItemEvaluation(
            item_name="Test Item",
            market_hash_name="Test Item",
            current_price=100.0,
            value_tier=ItemValueTier.HIGH,
            investment_score=80.0,
            liquidity_score=75.0,
            volatility_score=70.0,
            trend_score=65.0,
            final_score=75.0,
            reasons=["测试原因"],
            discovered_from="test",
            discovered_at=now
        )
        
        eval_dict = evaluation.to_dict()
        
        assert eval_dict['item_name'] == "Test Item"
        assert eval_dict['value_tier'] == "high"
        assert eval_dict['final_score'] == 75.0
        assert eval_dict['discovered_at'] == now.isoformat()


class TestItemDiscoveryService:
    """饰品发现服务测试"""
    
    @pytest.fixture
    def discovery_service(self):
        """创建饰品发现服务实例"""
        return ItemDiscoveryService()
    
    @pytest.fixture
    def mock_ranking_items(self):
        """创建模拟排行榜数据"""
        items = []
        
        # 高价值饰品
        items.append(RankingItem(
            rank=1,
            item_name="AK-47 | Redline (Field-Tested)",
            market_hash_name="AK-47 | Redline (Field-Tested)",
            current_price=50.0,
            price_change=2.5,
            price_change_percent=5.0,
            volume_24h=150,
            icon_url="https://example.com/icon1.png",
            item_url="https://example.com/item1"
        ))
        
        # 中等价值饰品
        items.append(RankingItem(
            rank=10,
            item_name="Glock-18 | Water Elemental (Minimal Wear)",
            market_hash_name="Glock-18 | Water Elemental (Minimal Wear)",
            current_price=25.0,
            price_change=-1.0,
            price_change_percent=-3.8,
            volume_24h=80,
            icon_url="https://example.com/icon2.png",
            item_url="https://example.com/item2"
        ))
        
        # 低价值饰品
        items.append(RankingItem(
            rank=50,
            item_name="P250 | Sand Dune (Battle-Scarred)",
            market_hash_name="P250 | Sand Dune (Battle-Scarred)",
            current_price=0.5,
            price_change=0.0,
            price_change_percent=0.0,
            volume_24h=5,
            icon_url="https://example.com/icon3.png",
            item_url="https://example.com/item3"
        ))
        
        # 高价饰品
        items.append(RankingItem(
            rank=5,
            item_name="Karambit | Fade (Factory New)",
            market_hash_name="Karambit | Fade (Factory New)",
            current_price=1500.0,
            price_change=50.0,
            price_change_percent=3.4,
            volume_24h=20,
            icon_url="https://example.com/icon4.png",
            item_url="https://example.com/item4"
        ))
        
        return items
    
    def test_determine_value_tier(self, discovery_service):
        """测试价值等级确定"""
        assert discovery_service._determine_value_tier(0.5) == ItemValueTier.MICRO
        assert discovery_service._determine_value_tier(10.0) == ItemValueTier.LOW
        assert discovery_service._determine_value_tier(50.0) == ItemValueTier.MEDIUM
        assert discovery_service._determine_value_tier(200.0) == ItemValueTier.HIGH
        assert discovery_service._determine_value_tier(800.0) == ItemValueTier.PREMIUM
    
    def test_passes_basic_filter(self, discovery_service, mock_ranking_items):
        """测试基础筛选"""
        ak47 = mock_ranking_items[0]  # AK-47 Redline
        p250 = mock_ranking_items[2]  # P250 Sand Dune
        
        # AK-47应该通过筛选
        assert discovery_service._passes_basic_filter(ak47) == True
        
        # P250价格太低，不应该通过筛选
        assert discovery_service._passes_basic_filter(p250) == False
    
    def test_calculate_investment_score(self, discovery_service, mock_ranking_items):
        """测试投资评分计算"""
        ak47 = mock_ranking_items[0]  # 排名1，价格50，变化5%
        score = discovery_service._calculate_investment_score(ak47)
        
        # 验证评分在合理范围内
        assert 0 <= score <= 100
        assert isinstance(score, float)
        
        # 高排名、合理价格、有变化的饰品应该有较高评分
        assert score >= 60
    
    def test_calculate_liquidity_score(self, discovery_service, mock_ranking_items):
        """测试流动性评分计算"""
        ak47 = mock_ranking_items[0]  # 交易量150
        glock = mock_ranking_items[1]  # 交易量80
        p250 = mock_ranking_items[2]  # 交易量5
        
        ak47_score = discovery_service._calculate_liquidity_score(ak47)
        glock_score = discovery_service._calculate_liquidity_score(glock)
        p250_score = discovery_service._calculate_liquidity_score(p250)
        
        # 交易量越高，流动性评分应该越高
        assert ak47_score > glock_score > p250_score
        assert ak47_score >= 80  # 高交易量
        assert p250_score <= 50  # 低交易量
    
    def test_calculate_volatility_score(self, discovery_service, mock_ranking_items):
        """测试波动性评分计算"""
        ak47 = mock_ranking_items[0]  # 变化5%
        glock = mock_ranking_items[1]  # 变化-3.8%
        p250 = mock_ranking_items[2]  # 变化0%
        
        ak47_score = discovery_service._calculate_volatility_score(ak47)
        glock_score = discovery_service._calculate_volatility_score(glock)
        p250_score = discovery_service._calculate_volatility_score(p250)
        
        # 适度波动应该有较高评分
        assert ak47_score >= 70  # 5%变化是适度的
        assert glock_score >= 60  # 3.8%变化也是合理的
        assert p250_score <= 50   # 无变化评分较低
    
    def test_calculate_trend_score(self, discovery_service, mock_ranking_items):
        """测试趋势评分计算"""
        ak47 = mock_ranking_items[0]  # 上涨5%
        glock = mock_ranking_items[1]  # 下跌3.8%
        p250 = mock_ranking_items[2]  # 无变化
        
        ak47_score = discovery_service._calculate_trend_score(ak47)
        glock_score = discovery_service._calculate_trend_score(glock)
        p250_score = discovery_service._calculate_trend_score(p250)
        
        # 上涨趋势应该有较高评分
        assert ak47_score >= 70
        # 适度下跌也可能是机会
        assert glock_score >= 55
        # 无变化评分中等
        assert p250_score == 50
    
    def test_calculate_final_score(self, discovery_service):
        """测试最终评分计算"""
        final_score = discovery_service._calculate_final_score(80, 75, 70, 65)
        
        # 验证加权平均计算
        expected = 80 * 0.4 + 75 * 0.3 + 70 * 0.2 + 65 * 0.1
        assert final_score == round(expected, 2)
    
    def test_generate_evaluation_reasons(self, discovery_service, mock_ranking_items):
        """测试评估原因生成"""
        ak47 = mock_ranking_items[0]
        reasons = discovery_service._generate_evaluation_reasons(ak47, 80, 85, 75, 70)
        
        assert isinstance(reasons, list)
        assert len(reasons) > 0
        
        # 应该包含高投资价值和高流动性的原因
        reason_text = ' '.join(reasons)
        assert '高投资价值' in reason_text or '高流动性' in reason_text
    
    def test_deduplicate_candidates(self, discovery_service, mock_ranking_items):
        """测试候选饰品去重"""
        # 添加重复项目
        duplicate_item = RankingItem(
            rank=20,
            item_name="AK-47 | Redline (Field-Tested)",  # 与第一个相同
            market_hash_name="AK-47 | Redline (Field-Tested)",
            current_price=48.0,
            price_change=1.0,
            price_change_percent=2.0,
            volume_24h=120,
            icon_url="https://example.com/icon1_dup.png",
            item_url="https://example.com/item1_dup"
        )
        
        candidates_with_dup = mock_ranking_items + [duplicate_item]
        unique_candidates = discovery_service._deduplicate_candidates(candidates_with_dup)
        
        # 验证去重效果
        market_hash_names = [item.market_hash_name for item in unique_candidates]
        assert len(set(market_hash_names)) == len(market_hash_names)
        assert len(unique_candidates) == len(mock_ranking_items)  # 应该去掉重复项
    
    def test_ensure_diversity(self, discovery_service):
        """测试饰品多样性确保"""
        # 创建不同价值等级的评估结果
        evaluations = []
        
        # 高价值饰品
        for i in range(5):
            eval_item = ItemEvaluation(
                item_name=f"Premium Item {i}",
                market_hash_name=f"Premium Item {i}",
                current_price=800.0,
                value_tier=ItemValueTier.PREMIUM,
                investment_score=90.0,
                liquidity_score=80.0,
                volatility_score=70.0,
                trend_score=75.0,
                final_score=85.0,
                reasons=["高价值"],
                discovered_from="test",
                discovered_at=datetime.now()
            )
            evaluations.append(eval_item)
        
        # 中等价值饰品
        for i in range(10):
            eval_item = ItemEvaluation(
                item_name=f"Medium Item {i}",
                market_hash_name=f"Medium Item {i}",
                current_price=50.0,
                value_tier=ItemValueTier.MEDIUM,
                investment_score=70.0,
                liquidity_score=75.0,
                volatility_score=65.0,
                trend_score=60.0,
                final_score=70.0,
                reasons=["中等价值"],
                discovered_from="test",
                discovered_at=datetime.now()
            )
            evaluations.append(eval_item)
        
        balanced_items = discovery_service._ensure_diversity(evaluations, 10)
        
        # 验证多样性
        assert len(balanced_items) <= 10
        
        # 应该包含不同价值等级的饰品
        tiers = [item.value_tier for item in balanced_items]
        assert ItemValueTier.PREMIUM in tiers
        assert ItemValueTier.MEDIUM in tiers
    
    def test_get_discovery_summary(self, discovery_service):
        """测试发现摘要获取"""
        # 添加一些测试数据
        test_evaluation = ItemEvaluation(
            item_name="Test Item",
            market_hash_name="Test Item",
            current_price=50.0,
            value_tier=ItemValueTier.MEDIUM,
            investment_score=75.0,
            liquidity_score=80.0,
            volatility_score=70.0,
            trend_score=65.0,
            final_score=72.5,
            reasons=["测试"],
            discovered_from="test",
            discovered_at=datetime.now()
        )
        
        discovery_service.discovered_items["test_item"] = test_evaluation
        
        summary = discovery_service.get_discovery_summary()
        
        assert 'total_discovered' in summary
        assert 'tier_distribution' in summary
        assert 'average_score' in summary
        assert summary['total_discovered'] >= 1


if __name__ == "__main__":
    # 运行基本测试
    print("运行饰品发现基本测试...")
    
    # 测试价值等级
    print("测试价值等级...")
    assert ItemValueTier.PREMIUM.value == "premium"
    assert ItemValueTier.MEDIUM.value == "medium"
    print("✓ 价值等级测试通过")
    
    # 测试发现服务
    print("测试发现服务...")
    discovery_service = ItemDiscoveryService()
    assert discovery_service._determine_value_tier(50.0) == ItemValueTier.MEDIUM
    assert discovery_service._determine_value_tier(800.0) == ItemValueTier.PREMIUM
    print("✓ 发现服务测试通过")
    
    # 测试评分计算
    print("测试评分计算...")
    final_score = discovery_service._calculate_final_score(80, 75, 70, 65)
    expected = 80 * 0.4 + 75 * 0.3 + 70 * 0.2 + 65 * 0.1
    assert final_score == round(expected, 2)
    print("✓ 评分计算测试通过")
    
    print("所有饰品发现基本测试通过！")
    
    # 运行pytest
    pytest.main(["-xvs", __file__])
