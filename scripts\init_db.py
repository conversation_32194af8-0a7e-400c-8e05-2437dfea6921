#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表、索引和初始数据
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import DatabaseManager, Item, Price, WatchlistItem, Holding, AlertRule, MacroStat
from core.exceptions import DatabaseConnectionError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_database_directory(db_path: str):
    """创建数据库目录"""
    db_dir = os.path.dirname(db_path)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        logger.info(f"创建数据库目录: {db_dir}")


def initialize_database(database_url: str = "sqlite:///data/ares.db"):
    """初始化数据库"""
    try:
        # 创建数据库目录
        if database_url.startswith('sqlite'):
            db_path = database_url.replace('sqlite:///', '')
            create_database_directory(db_path)
        
        # 创建数据库管理器
        db_manager = DatabaseManager(database_url)
        
        # 创建所有表
        logger.info("开始创建数据库表...")
        db_manager.create_tables()
        logger.info("数据库表创建完成")
        
        # 验证表创建
        health_status = db_manager.health_check()
        if health_status['status'] == 'healthy':
            logger.info("数据库健康检查通过")
            logger.info(f"表统计: {health_status['table_stats']}")
        else:
            logger.error(f"数据库健康检查失败: {health_status}")
            return False
        
        # 插入初始数据
        insert_initial_data(db_manager)
        
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        return False


def insert_initial_data(db_manager: DatabaseManager):
    """插入初始数据"""
    logger.info("开始插入初始数据...")
    
    try:
        with db_manager.get_session() as session:
            # 检查是否已有数据
            existing_items = session.query(Item).count()
            if existing_items > 0:
                logger.info(f"数据库中已有 {existing_items} 个饰品，跳过初始数据插入")
                return
            
            # 插入示例饰品数据
            sample_items = [
                {
                    'item_id': 'ak47_redline_ft',
                    'name': 'AK-47 | Redline (Field-Tested)',
                    'category': 'rifle',
                    'rarity': 'classified',
                    'market_hash_name': 'AK-47 | Redline (Field-Tested)',
                    'float_value': 0.25
                },
                {
                    'item_id': 'awp_dragon_lore_fn',
                    'name': 'AWP | Dragon Lore (Factory New)',
                    'category': 'sniper_rifle',
                    'rarity': 'covert',
                    'market_hash_name': 'AWP | Dragon Lore (Factory New)',
                    'float_value': 0.03
                },
                {
                    'item_id': 'm4a4_howl_mw',
                    'name': 'M4A4 | Howl (Minimal Wear)',
                    'category': 'rifle',
                    'rarity': 'contraband',
                    'market_hash_name': 'M4A4 | Howl (Minimal Wear)',
                    'float_value': 0.12
                },
                {
                    'item_id': 'knife_karambit_fade_fn',
                    'name': 'Karambit | Fade (Factory New)',
                    'category': 'knife',
                    'rarity': 'covert',
                    'market_hash_name': 'Karambit | Fade (Factory New)',
                    'float_value': 0.01
                },
                {
                    'item_id': 'glock_fade_fn',
                    'name': 'Glock-18 | Fade (Factory New)',
                    'category': 'pistol',
                    'rarity': 'restricted',
                    'market_hash_name': 'Glock-18 | Fade (Factory New)',
                    'float_value': 0.02
                }
            ]
            
            # 插入饰品
            for item_data in sample_items:
                item = Item(**item_data)
                session.add(item)
            
            session.flush()  # 确保饰品已插入，获得ID
            
            # 插入示例价格数据
            sample_prices = []
            platforms = ['steam', 'buff163', 'c5game']
            
            for item_data in sample_items:
                item_id = item_data['item_id']
                base_price = {
                    'ak47_redline_ft': 50.0,
                    'awp_dragon_lore_fn': 8000.0,
                    'm4a4_howl_mw': 3500.0,
                    'knife_karambit_fade_fn': 2200.0,
                    'glock_fade_fn': 180.0
                }.get(item_id, 100.0)
                
                for platform in platforms:
                    # 为每个平台生成不同的价格
                    price_multiplier = {
                        'steam': 1.0,
                        'buff163': 0.85,
                        'c5game': 0.90
                    }.get(platform, 1.0)
                    
                    ask_price = base_price * price_multiplier * 1.05  # 卖方价格稍高
                    bid_price = base_price * price_multiplier * 0.95  # 买方价格稍低
                    
                    price = Price(
                        item_id=item_id,
                        platform=platform,
                        ask_price=ask_price,
                        bid_price=bid_price,
                        last_price=base_price * price_multiplier,
                        volume_24h=50 + hash(item_id + platform) % 200,  # 随机交易量
                        timestamp=datetime.utcnow() - timedelta(minutes=hash(item_id + platform) % 60)
                    )
                    sample_prices.append(price)
            
            session.add_all(sample_prices)
            
            # 插入示例监控列表
            sample_watchlist = [
                WatchlistItem(
                    item_id='ak47_redline_ft',
                    pool_type='core',
                    priority_score=0.8,
                    update_frequency=30,
                    user_added=True,
                    last_updated=datetime.utcnow() - timedelta(minutes=15)
                ),
                WatchlistItem(
                    item_id='awp_dragon_lore_fn',
                    pool_type='core',
                    priority_score=0.9,
                    update_frequency=30,
                    user_added=True,
                    last_updated=datetime.utcnow() - timedelta(minutes=20)
                ),
                WatchlistItem(
                    item_id='m4a4_howl_mw',
                    pool_type='main',
                    priority_score=0.7,
                    update_frequency=240,
                    user_added=False,
                    last_updated=datetime.utcnow() - timedelta(hours=2)
                )
            ]
            
            session.add_all(sample_watchlist)
            
            # 插入示例持仓
            sample_holdings = [
                Holding(
                    item_id='ak47_redline_ft',
                    quantity=2,
                    cost_basis=45.0,
                    purchase_date=datetime.utcnow() - timedelta(days=30),
                    purchase_platform='buff163',
                    target_price=60.0,
                    notes='长期持有，等待价格上涨'
                ),
                Holding(
                    item_id='glock_fade_fn',
                    quantity=1,
                    cost_basis=170.0,
                    purchase_date=datetime.utcnow() - timedelta(days=15),
                    purchase_platform='steam',
                    target_price=200.0,
                    stop_loss_price=150.0,
                    notes='短期交易机会'
                )
            ]
            
            session.add_all(sample_holdings)
            
            # 插入示例预警规则
            sample_alerts = [
                AlertRule(
                    rule_name='价格上涨10%预警',
                    rule_type='price_change',
                    conditions='{"price_change_percent": 10, "timeframe": "24h"}',
                    actions='{"notify_discord": true, "notify_telegram": false}',
                    priority=1
                ),
                AlertRule(
                    rule_name='交易量异常预警',
                    rule_type='volume_spike',
                    conditions='{"volume_multiplier": 3, "baseline_days": 7}',
                    actions='{"notify_discord": true, "log_event": true}',
                    priority=2
                )
            ]
            
            session.add_all(sample_alerts)
            
            # 插入示例宏观数据
            sample_macro = [
                MacroStat(
                    metric_name='cs2_online_players',
                    metric_value=850000,
                    metric_unit='players',
                    data_source='steamcharts',
                    timestamp=datetime.utcnow() - timedelta(hours=1)
                ),
                MacroStat(
                    metric_name='market_total_volume',
                    metric_value=12500000,
                    metric_unit='usd',
                    data_source='steam_market',
                    timestamp=datetime.utcnow() - timedelta(hours=1)
                )
            ]
            
            session.add_all(sample_macro)
            
            logger.info("初始数据插入完成")
            
    except Exception as e:
        logger.error(f"插入初始数据失败: {str(e)}")
        raise


def verify_database_setup(database_url: str):
    """验证数据库设置"""
    logger.info("开始验证数据库设置...")
    
    try:
        db_manager = DatabaseManager(database_url)
        
        # 健康检查
        health_status = db_manager.health_check()
        logger.info(f"健康检查结果: {health_status}")
        
        # 测试查询性能
        from core.database import OptimizedQueries
        queries = OptimizedQueries(db_manager)
        
        # 测试获取监控列表
        start_time = datetime.now()
        watchlist = queries.get_watchlist_with_prices(limit=100)
        query_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"监控列表查询耗时: {query_time:.3f}秒, 返回 {len(watchlist)} 条记录")
        
        # 测试获取最新价格
        if watchlist:
            item_ids = [item['item_id'] for item in watchlist[:5]]
            start_time = datetime.now()
            prices = queries.get_latest_prices(item_ids)
            query_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"价格查询耗时: {query_time:.3f}秒, 返回 {len(prices)} 条记录")
        
        # 测试投资组合摘要
        start_time = datetime.now()
        portfolio = queries.get_portfolio_summary()
        query_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"投资组合查询耗时: {query_time:.3f}秒")
        logger.info(f"投资组合摘要: {portfolio}")
        
        logger.info("数据库验证完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库验证失败: {str(e)}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='初始化Ares系统数据库')
    parser.add_argument('--database-url', default='sqlite:///data/ares.db',
                       help='数据库连接URL (默认: sqlite:///data/ares.db)')
    parser.add_argument('--skip-initial-data', action='store_true',
                       help='跳过初始数据插入')
    parser.add_argument('--verify-only', action='store_true',
                       help='仅验证数据库，不进行初始化')
    
    args = parser.parse_args()
    
    if args.verify_only:
        success = verify_database_setup(args.database_url)
    else:
        success = initialize_database(args.database_url)
        if success and not args.skip_initial_data:
            success = verify_database_setup(args.database_url)
    
    if success:
        logger.info("数据库初始化成功！")
        sys.exit(0)
    else:
        logger.error("数据库初始化失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()
