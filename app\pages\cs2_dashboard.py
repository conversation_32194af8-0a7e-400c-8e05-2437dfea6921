"""
CS2饰品专用仪表盘页面
专门展示CS2饰品投资信息，包括战略储备、核心关注池、发现监控的CS2特定展示
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any

from app.components.cs2_components import (
    CS2MetricsOverview, CS2FilterPanel, CS2ItemCard, CS2PriceChart,
    CS2ArbitragePanel, create_cs2_portfolio_table, create_cs2_watchlist_table
)
from app.utils.state_manager import StateManager
from services.cs2_macro_collector import get_cs2_macro_collector
from services.cs2_market_analyzer import get_cs2_market_analyzer


def show_cs2_dashboard_page():
    """显示CS2饰品专用仪表盘页面"""
    # 页面标题和样式
    st.markdown("""
    <style>
    .cs2-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        color: white;
        text-align: center;
    }
    .cs2-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
        border-left: 4px solid #667eea;
    }
    .cs2-tab {
        background: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    </style>
    """, unsafe_allow_html=True)
    
    st.markdown("""
    <div class="cs2-header">
        <h1>🎮 CS2饰品投资仪表盘</h1>
        <p>专业的CS2饰品投资分析与监控平台</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 获取状态管理器
    state_manager = StateManager()
    
    # 渲染CS2投资指标概览
    render_cs2_metrics_overview(state_manager)
    
    # 创建标签页布局
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["🏆 战略储备", "⭐ 核心关注池", "🔍 发现监控", "💰 套利机会", "📊 宏观数据"])

    with tab1:
        render_strategic_holdings_tab(state_manager)

    with tab2:
        render_core_watchlist_tab(state_manager)

    with tab3:
        render_discovery_monitoring_tab(state_manager)

    with tab4:
        render_arbitrage_opportunities_tab(state_manager)

    with tab5:
        render_macro_data_tab(state_manager)


def render_cs2_metrics_overview(state_manager: StateManager):
    """渲染CS2投资指标概览"""
    # 获取CS2特定的指标数据
    metrics_data = get_cs2_metrics_data(state_manager)
    
    # 渲染指标概览
    metrics_overview = CS2MetricsOverview(metrics_data, "cs2_metrics")
    metrics_overview.render()


def render_strategic_holdings_tab(state_manager: StateManager):
    """渲染战略储备标签页"""
    st.markdown('<div class="cs2-section">', unsafe_allow_html=True)
    st.markdown("### 🏆 战略储备 - 高价值CS2饰品投资组合")
    
    # 获取战略储备数据
    strategic_data = get_strategic_holdings_data(state_manager)
    
    if strategic_data.empty:
        st.info("暂无战略储备项目")
        st.markdown('</div>', unsafe_allow_html=True)
        return
    
    # 创建筛选面板
    filter_panel = CS2FilterPanel("strategic_filter", "战略储备筛选")
    active_filters = filter_panel.render()
    
    # 应用筛选
    filtered_data = filter_panel.apply_filters_to_dataframe(strategic_data)
    
    # 显示统计信息
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        total_value = filtered_data['total_value'].sum()
        st.metric("总价值", f"¥{total_value:,.2f}")
    with col2:
        total_profit = filtered_data['profit_loss'].sum()
        st.metric("总盈亏", f"¥{total_profit:+,.2f}")
    with col3:
        avg_score = filtered_data['investment_score'].mean()
        st.metric("平均投资评分", f"{avg_score:.1f}")
    with col4:
        item_count = len(filtered_data)
        st.metric("项目数量", item_count)
    
    # 创建战略储备表格
    if len(filtered_data) > 0:
        portfolio_table = create_cs2_portfolio_table(filtered_data, "strategic_table")
        portfolio_table.render()
        
        # 显示价格趋势图表
        if st.checkbox("显示价格趋势", key="strategic_price_trend"):
            render_portfolio_price_trends(filtered_data)
    
    st.markdown('</div>', unsafe_allow_html=True)


def render_core_watchlist_tab(state_manager: StateManager):
    """渲染核心关注池标签页"""
    st.markdown('<div class="cs2-section">', unsafe_allow_html=True)
    st.markdown("### ⭐ 核心关注池 - 30个重点监控饰品")
    
    # 获取核心关注池数据
    core_data = get_core_watchlist_data(state_manager)
    
    if core_data.empty:
        st.info("暂无核心关注池数据")
        st.markdown('</div>', unsafe_allow_html=True)
        return
    
    # 创建筛选面板
    filter_panel = CS2FilterPanel("core_filter", "核心关注池筛选")
    active_filters = filter_panel.render()
    
    # 应用筛选
    filtered_data = filter_panel.apply_filters_to_dataframe(core_data)
    
    # 显示核心关注池统计
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        avg_price = filtered_data['current_price'].mean()
        st.metric("平均价格", f"¥{avg_price:.2f}")
    with col2:
        rising_count = len(filtered_data[filtered_data['change_24h'] > 0])
        st.metric("上涨项目", rising_count)
    with col3:
        high_volume = len(filtered_data[filtered_data['volume_24h'] > 100])
        st.metric("高交易量", high_volume)
    with col4:
        high_score = len(filtered_data[filtered_data['investment_score'] >= 80])
        st.metric("高评分项目", high_score)
    
    # 显示方式选择
    view_mode = st.radio("显示方式", ["表格视图", "卡片视图"], horizontal=True, key="core_view_mode")
    
    if view_mode == "表格视图":
        # 创建关注列表表格
        if len(filtered_data) > 0:
            watchlist_table = create_cs2_watchlist_table(filtered_data, "core_watchlist_table")
            watchlist_table.render()
    else:
        # 卡片视图
        if len(filtered_data) > 0:
            render_item_cards(filtered_data, "core_cards")
    
    st.markdown('</div>', unsafe_allow_html=True)


def render_discovery_monitoring_tab(state_manager: StateManager):
    """渲染发现监控标签页"""
    st.markdown('<div class="cs2-section">', unsafe_allow_html=True)
    st.markdown("### 🔍 发现监控 - 新兴投资机会识别")
    
    # 获取发现监控数据
    discovery_data = get_discovery_monitoring_data(state_manager)
    
    if discovery_data.empty:
        st.info("暂无发现监控数据")
        st.markdown('</div>', unsafe_allow_html=True)
        return
    
    # 发现来源统计
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        hot_count = len(discovery_data[discovery_data['ranking_type'] == 'hot'])
        st.metric("🔥 热门榜", hot_count)
    with col2:
        rising_count = len(discovery_data[discovery_data['ranking_type'] == 'rising'])
        st.metric("📈 上升榜", rising_count)
    with col3:
        new_count = len(discovery_data[discovery_data['ranking_type'] == 'new'])
        st.metric("🆕 新品榜", new_count)
    with col4:
        today_count = len(discovery_data[discovery_data['discovery_date'] == datetime.now().date()])
        st.metric("📅 今日发现", today_count)
    
    # 按排行榜类型分组显示
    ranking_types = discovery_data['ranking_type'].unique()
    
    for ranking_type in ranking_types:
        ranking_data = discovery_data[discovery_data['ranking_type'] == ranking_type]
        
        with st.expander(f"📊 {ranking_type.upper()} 排行榜 ({len(ranking_data)} 项)", expanded=True):
            if len(ranking_data) > 0:
                # 显示前5个项目的卡片
                top_items = ranking_data.nlargest(5, 'opportunity_score')
                render_item_cards(top_items, f"discovery_{ranking_type}")
    
    st.markdown('</div>', unsafe_allow_html=True)


def render_arbitrage_opportunities_tab(state_manager: StateManager):
    """渲染套利机会标签页"""
    st.markdown('<div class="cs2-section">', unsafe_allow_html=True)
    st.markdown("### 💰 套利机会 - 跨平台价差分析")
    
    # 获取套利机会数据
    arbitrage_data = get_arbitrage_opportunities_data(state_manager)
    
    if not arbitrage_data:
        st.info("暂无套利机会")
        st.markdown('</div>', unsafe_allow_html=True)
        return
    
    # 套利机会统计
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        total_opportunities = len(arbitrage_data)
        st.metric("总机会数", total_opportunities)
    with col2:
        high_profit = len([x for x in arbitrage_data if x.get('profit_margin', 0) >= 10])
        st.metric("高利润机会", high_profit)
    with col3:
        low_risk = len([x for x in arbitrage_data if x.get('risk_level') == 'low'])
        st.metric("低风险机会", low_risk)
    with col4:
        avg_profit = np.mean([x.get('profit_margin', 0) for x in arbitrage_data])
        st.metric("平均利润率", f"{avg_profit:.1f}%")
    
    # 渲染套利面板
    arbitrage_panel = CS2ArbitragePanel(arbitrage_data, "arbitrage_panel")
    arbitrage_panel.render()
    
    st.markdown('</div>', unsafe_allow_html=True)


def render_item_cards(data: pd.DataFrame, key_prefix: str):
    """渲染饰品卡片"""
    # 每行显示3个卡片
    items_per_row = 3
    rows = len(data) // items_per_row + (1 if len(data) % items_per_row > 0 else 0)
    
    for row in range(rows):
        cols = st.columns(items_per_row)
        for col_idx in range(items_per_row):
            item_idx = row * items_per_row + col_idx
            if item_idx < len(data):
                item_data = data.iloc[item_idx].to_dict()
                with cols[col_idx]:
                    card = CS2ItemCard(item_data, f"{key_prefix}_{item_idx}")
                    card.render()


def render_portfolio_price_trends(data: pd.DataFrame):
    """渲染投资组合价格趋势"""
    st.markdown("#### 📈 价格趋势分析")
    
    # 选择要显示趋势的饰品
    selected_items = st.multiselect(
        "选择饰品",
        options=data['item_name'].tolist(),
        default=data['item_name'].tolist()[:3],
        key="trend_items"
    )
    
    if selected_items:
        for item_name in selected_items:
            # 生成模拟价格历史数据
            price_data = generate_mock_price_history(item_name)
            
            # 渲染价格图表
            price_chart = CS2PriceChart(item_name, price_data, f"chart_{item_name}")
            price_chart.render()


def get_cs2_metrics_data(state_manager: StateManager) -> Dict[str, Any]:
    """获取CS2指标数据"""
    # 这里应该从实际的数据源获取数据
    # 目前使用模拟数据
    return {
        'total_value': 125000.50,
        'total_change_percent': 8.5,
        'total_profit': 15000.25,
        'daily_profit': 850.75,
        'core_pool_count': 30,
        'core_pool_new': 2,
        'arbitrage_count': 12,
        'arbitrage_new': 3,
        'main_pool_count': 970,
        'main_pool_new': 15,
        'rising_count': 18,
        'avg_rise_percent': 5.2,
        'falling_count': 8,
        'avg_fall_percent': -2.8,
        'hot_count': 25,
        'hot_new': 5
    }


def get_strategic_holdings_data(state_manager: StateManager) -> pd.DataFrame:
    """获取战略储备数据"""
    # 生成模拟的战略储备数据
    np.random.seed(42)
    
    strategic_items = [
        "AK-47 | Fire Serpent (Field-Tested)",
        "AWP | Dragon Lore (Factory New)", 
        "Karambit | Fade (Factory New)",
        "M4A4 | Howl (Field-Tested)",
        "Butterfly Knife | Doppler (Factory New)",
        "StatTrak™ AK-47 | Redline (Field-Tested)",
        "AWP | Medusa (Well-Worn)",
        "Karambit | Crimson Web (Minimal Wear)",
        "M4A1-S | Hot Rod (Factory New)",
        "Desert Eagle | Blaze (Factory New)"
    ]
    
    data = []
    for i, item in enumerate(strategic_items):
        quantity = np.random.randint(1, 5)
        avg_cost = np.random.uniform(500, 3000)
        current_price = avg_cost * np.random.uniform(0.8, 1.4)
        total_value = current_price * quantity
        profit_loss = (current_price - avg_cost) * quantity
        
        data.append({
            'item_name': item,
            'weapon_type': np.random.choice(['步枪', '狙击枪', '刀具', '手枪']),
            'rarity': np.random.choice(['隐秘', '保密', '违禁品']),
            'quality': np.random.choice(['普通', 'StatTrak™']),
            'quantity': quantity,
            'avg_cost': round(avg_cost, 2),
            'current_price': round(current_price, 2),
            'total_value': round(total_value, 2),
            'profit_loss': round(profit_loss, 2),
            'profit_percentage': round((profit_loss / (avg_cost * quantity)) * 100, 2),
            'investment_score': round(np.random.uniform(70, 95), 1)
        })
    
    return pd.DataFrame(data)


def get_core_watchlist_data(state_manager: StateManager) -> pd.DataFrame:
    """获取核心关注池数据"""
    # 生成模拟的核心关注池数据
    np.random.seed(123)
    
    core_items = [
        "AK-47 | Redline (Field-Tested)",
        "AWP | Asiimov (Field-Tested)",
        "M4A4 | Asiimov (Field-Tested)",
        "Glock-18 | Fade (Factory New)",
        "USP-S | Kill Confirmed (Minimal Wear)",
        "AK-47 | Case Hardened (Well-Worn)",
        "AWP | Lightning Strike (Factory New)",
        "M4A1-S | Cyrex (Factory New)",
        "Desert Eagle | Hypnotic (Factory New)",
        "P90 | Asiimov (Field-Tested)",
        "StatTrak™ AK-47 | Vulcan (Minimal Wear)",
        "AWP | Hyper Beast (Field-Tested)",
        "M4A4 | Royal Paladin (Field-Tested)",
        "Glock-18 | Water Elemental (Minimal Wear)",
        "USP-S | Orion (Factory New)",
        "AK-47 | Fuel Injector (Field-Tested)",
        "AWP | Fever Dream (Minimal Wear)",
        "M4A1-S | Golden Coil (Minimal Wear)",
        "Desert Eagle | Code Red (Minimal Wear)",
        "P250 | Asiimov (Field-Tested)",
        "StatTrak™ M4A4 | Buzz Kill (Field-Tested)",
        "AWP | Wildfire (Field-Tested)",
        "AK-47 | Bloodsport (Minimal Wear)",
        "M4A1-S | Decimator (Field-Tested)",
        "Glock-18 | Twilight Galaxy (Minimal Wear)",
        "USP-S | Cortex (Factory New)",
        "AK-47 | Neon Revolution (Field-Tested)",
        "AWP | Oni Taiji (Minimal Wear)",
        "M4A4 | Neo-Noir (Field-Tested)",
        "Desert Eagle | Sunset Storm 壱 (Minimal Wear)"
    ]
    
    data = []
    for i, item in enumerate(core_items):
        data.append({
            'item_name': item,
            'weapon_type': np.random.choice(['步枪', '狙击枪', '手枪', '冲锋枪']),
            'rarity': np.random.choice(['军规级', '受限', '保密', '隐秘']),
            'quality': np.random.choice(['普通', 'StatTrak™', '纪念品']),
            'current_price': round(np.random.uniform(20, 500), 2),
            'change_24h': round(np.random.uniform(-10, 15), 2),
            'volume_24h': np.random.randint(10, 300),
            'investment_score': round(np.random.uniform(60, 90), 1),
            'priority_score': round(np.random.uniform(7.0, 9.5), 1),
            'last_updated': datetime.now() - timedelta(minutes=np.random.randint(1, 30))
        })
    
    return pd.DataFrame(data)


def get_discovery_monitoring_data(state_manager: StateManager) -> pd.DataFrame:
    """获取发现监控数据"""
    # 生成模拟的发现监控数据
    np.random.seed(456)
    
    discovery_items = [
        "StatTrak™ M4A1-S | Hyper Beast (Field-Tested)",
        "AK-47 | Neon Rider (Minimal Wear)",
        "AWP | Neo-Noir (Field-Tested)",
        "Karambit | Autotronic (Field-Tested)",
        "M4A4 | The Emperor (Field-Tested)",
        "Glock-18 | Moonrise (Factory New)",
        "USP-S | Neo-Noir (Factory New)",
        "AK-47 | Phantom Disruptor (Field-Tested)",
        "AWP | Containment Breach (Field-Tested)",
        "Butterfly Knife | Lore (Field-Tested)",
        "M4A1-S | Printstream (Field-Tested)",
        "Desert Eagle | Printstream (Factory New)"
    ]
    
    data = []
    for i, item in enumerate(discovery_items):
        discovery_date = datetime.now().date() - timedelta(days=np.random.randint(0, 7))
        data.append({
            'item_name': item,
            'weapon_type': np.random.choice(['步枪', '狙击枪', '手枪', '刀具']),
            'rarity': np.random.choice(['军规级', '受限', '保密', '隐秘']),
            'quality': np.random.choice(['普通', 'StatTrak™']),
            'ranking_type': np.random.choice(['hot', 'rising', 'new', 'falling']),
            'ranking_position': np.random.randint(1, 50),
            'opportunity_score': round(np.random.uniform(5.0, 9.0), 1),
            'current_price': round(np.random.uniform(30, 800), 2),
            'change_24h': round(np.random.uniform(-5, 20), 2),
            'volume_24h': np.random.randint(5, 150),
            'discovery_date': discovery_date,
            'source': 'steamdt_rankings',
            'status': np.random.choice(['new', 'monitoring', 'added_to_watchlist'])
        })
    
    return pd.DataFrame(data)


def get_arbitrage_opportunities_data(state_manager: StateManager) -> List[Dict[str, Any]]:
    """获取套利机会数据"""
    # 生成模拟的套利机会数据
    opportunities = [
        {
            'item_name': 'AK-47 | Redline (Field-Tested)',
            'buy_platform': 'BUFF',
            'sell_platform': 'Steam',
            'buy_price': 95.0,
            'sell_price': 120.0,
            'gross_profit': 25.0,
            'net_profit': 12.5,
            'profit_margin': 13.2,
            'risk_level': 'medium',
            'confidence_score': 78.5
        },
        {
            'item_name': 'AWP | Asiimov (Field-Tested)',
            'buy_platform': 'C5Game',
            'sell_platform': 'Steam',
            'buy_price': 78.0,
            'sell_price': 85.0,
            'gross_profit': 7.0,
            'net_profit': 3.2,
            'profit_margin': 4.1,
            'risk_level': 'low',
            'confidence_score': 65.2
        },
        {
            'item_name': 'M4A4 | Asiimov (Field-Tested)',
            'buy_platform': 'BUFF',
            'sell_platform': 'IGXE',
            'buy_price': 45.0,
            'sell_price': 52.0,
            'gross_profit': 7.0,
            'net_profit': 4.8,
            'profit_margin': 10.7,
            'risk_level': 'medium',
            'confidence_score': 72.1
        }
    ]
    
    return opportunities


def generate_mock_price_history(item_name: str) -> List[Dict]:
    """生成模拟价格历史数据"""
    np.random.seed(hash(item_name) % 1000)
    
    # 生成30天的价格数据
    dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
    base_price = np.random.uniform(50, 500)
    
    price_data = []
    current_price = base_price
    
    for date in dates:
        # 模拟价格波动
        change = np.random.normal(0, 0.02)  # 2%的标准差
        current_price *= (1 + change)
        current_price = max(current_price, base_price * 0.5)  # 最低不低于基础价格的50%
        
        volume = np.random.randint(10, 200)
        
        price_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'price': round(current_price, 2),
            'volume': volume
        })
    
    return price_data


def render_macro_data_tab(state_manager: StateManager):
    """渲染宏观数据标签页"""
    st.markdown('<div class="cs2-section">', unsafe_allow_html=True)
    st.markdown("### 📊 宏观数据 - CS2市场整体趋势分析")

    # 获取CS2宏观数据
    macro_data = get_cs2_macro_data(state_manager)

    if not macro_data:
        st.info("正在收集CS2宏观数据，请稍候...")
        if st.button("🔄 立即收集数据"):
            with st.spinner("正在收集CS2宏观数据..."):
                macro_data = collect_cs2_macro_data_sync()
                if macro_data:
                    st.success("数据收集成功！")
                    st.experimental_rerun()
                else:
                    st.error("数据收集失败，请检查网络连接")
        st.markdown('</div>', unsafe_allow_html=True)
        return

    # 显示宏观指标概览
    render_macro_indicators_overview(macro_data)

    # 显示市场分析
    render_market_analysis(macro_data)

    # 显示玩家数据趋势
    render_player_trends(macro_data)

    # 显示市场信号
    render_market_signals(macro_data)

    st.markdown('</div>', unsafe_allow_html=True)


def render_macro_indicators_overview(macro_data):
    """渲染宏观指标概览"""
    st.markdown("#### 📈 CS2宏观指标概览")

    indicators = macro_data.get('indicators')
    if not indicators:
        st.warning("暂无宏观指标数据")
        return

    # 第一行指标
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="🎮 当前在线玩家",
            value=f"{indicators.get('current_players', 0):,}",
            delta=f"{indicators.get('player_trend_24h', 0):+.1f}%"
        )

    with col2:
        heat_index = indicators.get('market_heat_index', 50)
        heat_delta = "🔥" if heat_index > 70 else "❄️" if heat_index < 30 else "🌡️"
        st.metric(
            label="🌡️ 市场热度指数",
            value=f"{heat_index:.1f}",
            delta=heat_delta
        )

    with col3:
        trend_7d = indicators.get('player_trend_7d', 0)
        st.metric(
            label="📊 7天趋势",
            value=f"{trend_7d:+.1f}%",
            delta="上升" if trend_7d > 0 else "下降" if trend_7d < 0 else "平稳"
        )

    with col4:
        confidence = indicators.get('confidence_score', 50)
        st.metric(
            label="🎯 数据置信度",
            value=f"{confidence:.1f}%",
            delta="高" if confidence > 80 else "中" if confidence > 60 else "低"
        )


def get_cs2_macro_data(state_manager: StateManager) -> Dict[str, Any]:
    """获取CS2宏观数据"""
    try:
        # 这里应该从实际的数据源获取数据
        # 目前使用模拟数据进行演示
        return generate_mock_macro_data()
    except Exception as e:
        st.error(f"获取宏观数据时出错: {e}")
        return {}


def collect_cs2_macro_data_sync() -> Dict[str, Any]:
    """同步收集CS2宏观数据"""
    try:
        import asyncio

        async def collect_data():
            collector = get_cs2_macro_collector()
            analyzer = get_cs2_market_analyzer()

            # 收集玩家数据
            player_data = await collector.collect_current_player_data()

            # 获取宏观指标
            indicators = await collector.get_macro_indicators()

            # 进行市场分析
            analysis = await analyzer.analyze_market()

            # 检测市场信号
            signals = await analyzer.detect_market_signals()

            # 获取历史数据
            historical_data = collector.get_historical_data(days=7)

            return {
                'player_data': player_data,
                'indicators': indicators,
                'analysis': analysis,
                'signals': signals,
                'historical_data': historical_data
            }

        # 在新的事件循环中运行
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(collect_data())

    except Exception as e:
        st.error(f"收集数据时出错: {e}")
        return {}


def generate_mock_macro_data() -> Dict[str, Any]:
    """生成模拟宏观数据"""
    import random

    # 模拟当前指标
    current_players = random.randint(800000, 1200000)

    indicators = {
        'current_players': current_players,
        'player_trend_24h': random.uniform(-5, 8),
        'player_trend_7d': random.uniform(-10, 15),
        'market_heat_index': random.uniform(30, 85),
        'peak_hours_ratio': random.uniform(0.4, 0.7),
        'weekend_effect': random.uniform(-5, 12),
        'confidence_score': random.uniform(70, 95),
        'update_time': datetime.now()
    }

    # 模拟市场分析
    trends = ['bullish', 'bearish', 'sideways', 'volatile']
    timings = ['buy', 'sell', 'hold', 'wait']

    analysis = {
        'trend': random.choice(trends),
        'timing': random.choice(timings),
        'confidence': random.uniform(60, 90),
        'heat_level': random.choice(['极冷', '冷', '温', '热', '极热']),
        'key_insights': [
            f"当前CS2在线玩家数: {current_players:,}",
            f"24小时玩家数变化 {indicators['player_trend_24h']:+.1f}%",
            f"市场热度指数为 {indicators['market_heat_index']:.1f}"
        ],
        'recommendations': [
            "基于当前市场趋势，建议关注高流动性饰品",
            "市场波动性适中，可考虑中长期投资策略",
            "注意监控玩家数据变化，及时调整投资组合"
        ]
    }

    return {
        'indicators': indicators,
        'analysis': analysis,
        'historical_data': [],
        'signals': []
    }


if __name__ == "__main__":
    show_cs2_dashboard_page()
