# Redis配置文件 - Ares投资系统
# 版本: 7.0+
# 优化用于缓存和会话存储

# ================================ 网络配置 ================================
# 绑定地址
bind 127.0.0.1

# 端口
port 6379

# TCP监听队列长度
tcp-backlog 511

# 客户端超时时间（秒）
timeout 300

# TCP keepalive
tcp-keepalive 300

# ================================ 通用配置 ================================
# 是否以守护进程运行
daemonize no

# 进程文件
pidfile /var/run/redis_6379.pid

# 日志级别: debug, verbose, notice, warning
loglevel notice

# 日志文件
logfile ""

# 数据库数量
databases 16

# ================================ 内存管理 ================================
# 最大内存限制（建议设置为系统内存的70%）
maxmemory 2gb

# 内存淘汰策略
# allkeys-lru: 在所有键中使用LRU算法删除
# volatile-lru: 在设置了过期时间的键中使用LRU算法删除
# allkeys-random: 在所有键中随机删除
# volatile-random: 在设置了过期时间的键中随机删除
# volatile-ttl: 删除即将过期的键
# noeviction: 不删除，返回错误
maxmemory-policy allkeys-lru

# 内存采样大小
maxmemory-samples 5

# ================================ 持久化配置 ================================
# RDB持久化
# 900秒内至少1个键发生变化时保存
# 300秒内至少10个键发生变化时保存
# 60秒内至少10000个键发生变化时保存
save 900 1
save 300 10
save 60 10000

# RDB文件压缩
rdbcompression yes

# RDB文件校验
rdbchecksum yes

# RDB文件名
dbfilename dump.rdb

# 工作目录
dir ./

# AOF持久化（推荐关闭以提高性能）
appendonly no

# AOF文件名
appendfilename "appendonly.aof"

# AOF同步策略
# always: 每次写操作都同步
# everysec: 每秒同步一次
# no: 由操作系统决定
appendfsync everysec

# ================================ 性能优化 ================================
# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# ================================ 客户端配置 ================================
# 最大客户端连接数
maxclients 10000

# 客户端输出缓冲区限制
# 普通客户端
client-output-buffer-limit normal 0 0 0

# 副本客户端
client-output-buffer-limit replica 256mb 64mb 60

# 发布订阅客户端
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# ================================ 安全配置 ================================
# 密码认证（生产环境建议启用）
# requirepass your_redis_password

# 重命名危险命令
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""

# ================================ 慢查询日志 ================================
# 慢查询阈值（微秒）
slowlog-log-slower-than 10000

# 慢查询日志长度
slowlog-max-len 128

# ================================ 延迟监控 ================================
# 延迟监控阈值（毫秒）
latency-monitor-threshold 100

# ================================ 事件通知 ================================
# 键空间通知
# K: 键空间事件
# E: 键事件
# g: 通用命令（DEL, EXPIRE等）
# $: 字符串命令
# l: 列表命令
# s: 集合命令
# h: 哈希命令
# z: 有序集合命令
# x: 过期事件
# e: 驱逐事件
notify-keyspace-events "Ex"

# ================================ 高级配置 ================================
# 哈希种子
hash-seed 0

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区硬限制
stop-writes-on-bgsave-error yes

# 副本只读
replica-read-only yes

# 副本优先级
replica-priority 100

# 最小副本数量
# min-replicas-to-write 3
# min-replicas-max-lag 10

# ================================ 模块配置 ================================
# 加载模块
# loadmodule /path/to/module.so

# ================================ 集群配置 ================================
# 启用集群模式
# cluster-enabled yes

# 集群配置文件
# cluster-config-file nodes-6379.conf

# 集群节点超时
# cluster-node-timeout 15000

# ================================ 监控配置 ================================
# 信息刷新间隔
# info-refresh-interval 1

# ================================ Ares系统特定配置 ================================
# 针对Ares系统的优化配置

# 1. 缓存优化
# 适合缓存场景的内存策略
maxmemory-policy allkeys-lru

# 2. 会话存储优化
# 适合会话数据的过期策略
# 大部分数据都有TTL，使用volatile-lru更合适
# maxmemory-policy volatile-lru

# 3. 性能监控
# 启用慢查询日志
slowlog-log-slower-than 5000
slowlog-max-len 256

# 4. 键空间通知
# 监控过期事件，用于缓存失效
notify-keyspace-events "Ex"

# 5. 内存优化
# 针对小对象优化
hash-max-ziplist-entries 1024
hash-max-ziplist-value 128
list-max-ziplist-size -1
set-max-intset-entries 1024
zset-max-ziplist-entries 256
zset-max-ziplist-value 128

# 6. 连接优化
# 适合Web应用的连接配置
timeout 300
tcp-keepalive 60
maxclients 1000

# 7. 持久化策略
# 缓存数据可以接受一定程度的数据丢失
# 使用较宽松的RDB策略
save 3600 1
save 1800 10
save 900 100

# 关闭AOF以提高性能
appendonly no

# ================================ 开发环境配置 ================================
# 开发环境特定配置

# 降低内存限制
# maxmemory 512mb

# 更频繁的持久化
# save 300 1
# save 60 10
# save 30 100

# 启用详细日志
# loglevel verbose

# ================================ 生产环境配置 ================================
# 生产环境特定配置（注释掉，需要时启用）

# 增加内存限制
# maxmemory 4gb

# 启用密码认证
# requirepass your_strong_password_here

# 绑定到内网地址
# bind ********

# 启用TLS（如果需要）
# port 0
# tls-port 6380
# tls-cert-file redis.crt
# tls-key-file redis.key
# tls-ca-cert-file ca.crt

# 更严格的客户端限制
# maxclients 500

# 启用保护模式
# protected-mode yes
