{"tests/test_scheduler.py::TestIntelligentScheduler::test_scheduler_initialization": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_add_remove_item_from_schedule": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_api_limit_check": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_state_persistence": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_get_schedule_status": true}