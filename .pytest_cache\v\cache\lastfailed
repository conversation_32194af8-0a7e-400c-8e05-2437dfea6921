{"tests/test_scheduler.py::TestIntelligentScheduler::test_scheduler_initialization": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_add_remove_item_from_schedule": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_api_limit_check": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_state_persistence": true, "tests/test_scheduler.py::TestIntelligentScheduler::test_get_schedule_status": true, "tests/test_integration.py::TestSystemIntegration::test_api_integration_flow": true, "tests/test_simple_integration.py::TestCoreIntegration::test_arbitrage_detection_integration": true}