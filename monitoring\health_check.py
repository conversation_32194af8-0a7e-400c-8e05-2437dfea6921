"""
Ares系统健康检查模块
实现全面的系统健康检查和状态监控
"""

import asyncio
import logging
import time
import psutil
import aiohttp
import json
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    name: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    response_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'status': self.status.value,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'response_time': self.response_time
        }


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        """初始化健康检查器"""
        self.checks: Dict[str, Callable] = {}
        self.check_intervals: Dict[str, int] = {}
        self.last_check_times: Dict[str, datetime] = {}
        self.check_results: Dict[str, HealthCheckResult] = {}
        
        # 默认检查间隔（秒）
        self.default_interval = 60
        
        # 超时设置
        self.default_timeout = 30
        
        # 注册默认检查
        self._register_default_checks()
    
    def _register_default_checks(self):
        """注册默认健康检查"""
        self.register_check("database", self._check_database, interval=30)
        self.register_check("redis", self._check_redis, interval=30)
        self.register_check("api_endpoints", self._check_api_endpoints, interval=60)
        self.register_check("disk_space", self._check_disk_space, interval=300)
        self.register_check("memory_usage", self._check_memory_usage, interval=60)
        self.register_check("cpu_usage", self._check_cpu_usage, interval=60)
        self.register_check("external_apis", self._check_external_apis, interval=120)
    
    def register_check(self, name: str, check_func: Callable, interval: int = None):
        """注册健康检查"""
        self.checks[name] = check_func
        self.check_intervals[name] = interval or self.default_interval
        logger.info(f"Registered health check: {name} (interval: {self.check_intervals[name]}s)")
    
    async def run_health_checks(self, force: bool = False) -> Dict[str, HealthCheckResult]:
        """运行所有健康检查"""
        results = {}
        
        for check_name, check_func in self.checks.items():
            # 检查是否需要运行
            if not force and not self._should_run_check(check_name):
                # 使用缓存结果
                if check_name in self.check_results:
                    results[check_name] = self.check_results[check_name]
                continue
            
            try:
                start_time = time.time()
                result = await check_func()
                response_time = time.time() - start_time
                
                if isinstance(result, HealthCheckResult):
                    result.response_time = response_time
                    results[check_name] = result
                else:
                    # 兼容简单返回格式
                    results[check_name] = HealthCheckResult(
                        name=check_name,
                        status=HealthStatus.HEALTHY if result else HealthStatus.CRITICAL,
                        message="Check completed",
                        response_time=response_time
                    )
                
                # 更新缓存
                self.check_results[check_name] = results[check_name]
                self.last_check_times[check_name] = datetime.utcnow()
                
            except Exception as e:
                logger.error(f"Health check failed for {check_name}: {str(e)}")
                results[check_name] = HealthCheckResult(
                    name=check_name,
                    status=HealthStatus.CRITICAL,
                    message=f"Check failed: {str(e)}",
                    response_time=time.time() - start_time if 'start_time' in locals() else 0
                )
        
        return results
    
    def _should_run_check(self, check_name: str) -> bool:
        """检查是否应该运行检查"""
        if check_name not in self.last_check_times:
            return True
        
        last_check = self.last_check_times[check_name]
        interval = self.check_intervals[check_name]
        
        return (datetime.utcnow() - last_check).total_seconds() >= interval
    
    async def _check_database(self) -> HealthCheckResult:
        """检查数据库连接"""
        try:
            # 这里应该实际连接数据库进行检查
            # 暂时模拟检查
            await asyncio.sleep(0.1)  # 模拟数据库查询时间
            
            # 模拟数据库状态
            import random
            is_healthy = random.random() > 0.05  # 95%概率健康
            
            if is_healthy:
                return HealthCheckResult(
                    name="database",
                    status=HealthStatus.HEALTHY,
                    message="Database connection successful",
                    details={
                        "connection_pool_size": 10,
                        "active_connections": 3,
                        "query_time_avg": 0.05
                    }
                )
            else:
                return HealthCheckResult(
                    name="database",
                    status=HealthStatus.CRITICAL,
                    message="Database connection failed",
                    details={"error": "Connection timeout"}
                )
        
        except Exception as e:
            return HealthCheckResult(
                name="database",
                status=HealthStatus.CRITICAL,
                message=f"Database check failed: {str(e)}"
            )
    
    async def _check_redis(self) -> HealthCheckResult:
        """检查Redis连接"""
        try:
            # 模拟Redis检查
            await asyncio.sleep(0.05)
            
            import random
            is_healthy = random.random() > 0.02  # 98%概率健康
            
            if is_healthy:
                return HealthCheckResult(
                    name="redis",
                    status=HealthStatus.HEALTHY,
                    message="Redis connection successful",
                    details={
                        "memory_usage": "45.2MB",
                        "connected_clients": 5,
                        "keyspace_hits": 1234,
                        "keyspace_misses": 56
                    }
                )
            else:
                return HealthCheckResult(
                    name="redis",
                    status=HealthStatus.CRITICAL,
                    message="Redis connection failed"
                )
        
        except Exception as e:
            return HealthCheckResult(
                name="redis",
                status=HealthStatus.CRITICAL,
                message=f"Redis check failed: {str(e)}"
            )
    
    async def _check_api_endpoints(self) -> HealthCheckResult:
        """检查API端点"""
        try:
            # 检查内部API端点
            endpoints = [
                "http://localhost:8000/health",
                "http://localhost:8000/api/status"
            ]
            
            healthy_count = 0
            total_count = len(endpoints)
            details = {}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                for endpoint in endpoints:
                    try:
                        async with session.get(endpoint) as response:
                            if response.status == 200:
                                healthy_count += 1
                                details[endpoint] = "healthy"
                            else:
                                details[endpoint] = f"status_{response.status}"
                    except Exception as e:
                        details[endpoint] = f"error: {str(e)}"
            
            if healthy_count == total_count:
                status = HealthStatus.HEALTHY
                message = "All API endpoints are healthy"
            elif healthy_count > 0:
                status = HealthStatus.WARNING
                message = f"{healthy_count}/{total_count} API endpoints are healthy"
            else:
                status = HealthStatus.CRITICAL
                message = "All API endpoints are down"
            
            return HealthCheckResult(
                name="api_endpoints",
                status=status,
                message=message,
                details=details
            )
        
        except Exception as e:
            return HealthCheckResult(
                name="api_endpoints",
                status=HealthStatus.CRITICAL,
                message=f"API endpoint check failed: {str(e)}"
            )
    
    async def _check_disk_space(self) -> HealthCheckResult:
        """检查磁盘空间"""
        try:
            disk_usage = psutil.disk_usage('/')
            
            # 计算使用百分比
            used_percent = (disk_usage.used / disk_usage.total) * 100
            
            # 确定状态
            if used_percent < 80:
                status = HealthStatus.HEALTHY
                message = f"Disk usage is normal ({used_percent:.1f}%)"
            elif used_percent < 90:
                status = HealthStatus.WARNING
                message = f"Disk usage is high ({used_percent:.1f}%)"
            else:
                status = HealthStatus.CRITICAL
                message = f"Disk usage is critical ({used_percent:.1f}%)"
            
            return HealthCheckResult(
                name="disk_space",
                status=status,
                message=message,
                details={
                    "total_gb": round(disk_usage.total / (1024**3), 2),
                    "used_gb": round(disk_usage.used / (1024**3), 2),
                    "free_gb": round(disk_usage.free / (1024**3), 2),
                    "used_percent": round(used_percent, 1)
                }
            )
        
        except Exception as e:
            return HealthCheckResult(
                name="disk_space",
                status=HealthStatus.CRITICAL,
                message=f"Disk space check failed: {str(e)}"
            )
    
    async def _check_memory_usage(self) -> HealthCheckResult:
        """检查内存使用"""
        try:
            memory = psutil.virtual_memory()
            
            # 确定状态
            if memory.percent < 80:
                status = HealthStatus.HEALTHY
                message = f"Memory usage is normal ({memory.percent:.1f}%)"
            elif memory.percent < 90:
                status = HealthStatus.WARNING
                message = f"Memory usage is high ({memory.percent:.1f}%)"
            else:
                status = HealthStatus.CRITICAL
                message = f"Memory usage is critical ({memory.percent:.1f}%)"
            
            return HealthCheckResult(
                name="memory_usage",
                status=status,
                message=message,
                details={
                    "total_gb": round(memory.total / (1024**3), 2),
                    "used_gb": round(memory.used / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_percent": round(memory.percent, 1)
                }
            )
        
        except Exception as e:
            return HealthCheckResult(
                name="memory_usage",
                status=HealthStatus.CRITICAL,
                message=f"Memory usage check failed: {str(e)}"
            )
    
    async def _check_cpu_usage(self) -> HealthCheckResult:
        """检查CPU使用率"""
        try:
            # 获取CPU使用率（1秒采样）
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 确定状态
            if cpu_percent < 70:
                status = HealthStatus.HEALTHY
                message = f"CPU usage is normal ({cpu_percent:.1f}%)"
            elif cpu_percent < 85:
                status = HealthStatus.WARNING
                message = f"CPU usage is high ({cpu_percent:.1f}%)"
            else:
                status = HealthStatus.CRITICAL
                message = f"CPU usage is critical ({cpu_percent:.1f}%)"
            
            return HealthCheckResult(
                name="cpu_usage",
                status=status,
                message=message,
                details={
                    "cpu_percent": round(cpu_percent, 1),
                    "cpu_count": psutil.cpu_count(),
                    "load_avg": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                }
            )
        
        except Exception as e:
            return HealthCheckResult(
                name="cpu_usage",
                status=HealthStatus.CRITICAL,
                message=f"CPU usage check failed: {str(e)}"
            )
    
    async def _check_external_apis(self) -> HealthCheckResult:
        """检查外部API"""
        try:
            # 检查外部API（如steamdt.com）
            external_apis = [
                "https://steamdt.com/api/health",  # 假设的健康检查端点
                "https://httpbin.org/status/200"   # 测试端点
            ]
            
            healthy_count = 0
            total_count = len(external_apis)
            details = {}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                for api in external_apis:
                    try:
                        async with session.get(api) as response:
                            if response.status == 200:
                                healthy_count += 1
                                details[api] = "healthy"
                            else:
                                details[api] = f"status_{response.status}"
                    except Exception as e:
                        details[api] = f"error: {str(e)}"
            
            if healthy_count == total_count:
                status = HealthStatus.HEALTHY
                message = "All external APIs are accessible"
            elif healthy_count > 0:
                status = HealthStatus.WARNING
                message = f"{healthy_count}/{total_count} external APIs are accessible"
            else:
                status = HealthStatus.CRITICAL
                message = "All external APIs are inaccessible"
            
            return HealthCheckResult(
                name="external_apis",
                status=status,
                message=message,
                details=details
            )
        
        except Exception as e:
            return HealthCheckResult(
                name="external_apis",
                status=HealthStatus.CRITICAL,
                message=f"External API check failed: {str(e)}"
            )
    
    def get_overall_status(self, results: Dict[str, HealthCheckResult]) -> HealthStatus:
        """获取整体健康状态"""
        if not results:
            return HealthStatus.UNKNOWN
        
        statuses = [result.status for result in results.values()]
        
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def get_health_summary(self, results: Dict[str, HealthCheckResult]) -> Dict[str, Any]:
        """获取健康检查摘要"""
        overall_status = self.get_overall_status(results)
        
        status_counts = {
            HealthStatus.HEALTHY.value: 0,
            HealthStatus.WARNING.value: 0,
            HealthStatus.CRITICAL.value: 0,
            HealthStatus.UNKNOWN.value: 0
        }
        
        for result in results.values():
            status_counts[result.status.value] += 1
        
        return {
            "overall_status": overall_status.value,
            "total_checks": len(results),
            "status_counts": status_counts,
            "timestamp": datetime.utcnow().isoformat(),
            "checks": {name: result.to_dict() for name, result in results.items()}
        }


# 全局健康检查器实例
_health_checker: Optional[HealthChecker] = None


def get_health_checker() -> HealthChecker:
    """获取全局健康检查器实例"""
    global _health_checker
    if _health_checker is None:
        _health_checker = HealthChecker()
    return _health_checker
