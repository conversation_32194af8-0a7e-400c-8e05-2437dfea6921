version: '3.8'

# 生产环境Docker Compose配置
# 包含负载均衡、SSL、监控等生产级特性

services:
  # PostgreSQL数据库 (生产环境推荐)
  postgres:
    image: postgres:15-alpine
    container_name: ares-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ares
      POSTGRES_USER: ares_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ares_user -d ares"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Redis集群 (主节点)
  redis-master:
    image: redis:7-alpine
    container_name: ares-redis-master
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_master_data:/data
      - ./config/redis/master.conf:/usr/local/etc/redis/redis.conf:ro
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Redis从节点 (只读副本)
  redis-slave:
    image: redis:7-alpine
    container_name: ares-redis-slave
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf --slaveof redis-master 6379
    volumes:
      - redis_slave_data:/data
      - ./config/redis/slave.conf:/usr/local/etc/redis/redis.conf:ro
    depends_on:
      redis-master:
        condition: service_healthy
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # 主应用服务 (多实例负载均衡)
  app1:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-app1
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=postgresql://ares_user:${POSTGRES_PASSWORD}@postgres:5432/ares
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - INSTANCE_ID=app1
    volumes:
      - app_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  app2:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-app2
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=postgresql://ares_user:${POSTGRES_PASSWORD}@postgres:5432/ares
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - INSTANCE_ID=app2
    volumes:
      - app_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # 调度器服务 (单实例)
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-scheduler
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=postgresql://ares_user:${POSTGRES_PASSWORD}@postgres:5432/ares
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - SERVICE_MODE=scheduler
    volumes:
      - app_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    command: ["scheduler"]
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # 追踪器服务 (可扩展)
  tracker:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-tracker
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=postgresql://ares_user:${POSTGRES_PASSWORD}@postgres:5432/ares
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - SERVICE_MODE=tracker
    volumes:
      - app_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    command: ["tracker"]
    networks:
      - ares-network
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # 发现器服务
  discoverer:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-discoverer
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis-master:6379/0
      - DATABASE_URL=postgresql://ares_user:${POSTGRES_PASSWORD}@postgres:5432/ares
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - SERVICE_MODE=discoverer
    volumes:
      - app_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    command: ["discoverer"]
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: ares-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/ssl:/etc/nginx/ssl:ro
      - logs_data:/var/log/nginx
    depends_on:
      - app1
      - app2
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.1'

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: ares-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: ares-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # 日志聚合 (ELK Stack - Elasticsearch)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: ares-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: ares-logstash
    restart: unless-stopped
    volumes:
      - ./config/logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - logs_data:/logs:ro
    depends_on:
      - elasticsearch
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: ares-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - ares-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

# 网络配置
networks:
  ares-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_master_data:
    driver: local
  redis_slave_data:
    driver: local
  app_data:
    driver: local
  logs_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
