"""
Ares系统配置管理模块
实现分层配置管理、环境变量覆盖、敏感信息加密和配置热更新
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
# 暂时不使用Pydantic BaseSettings，使用基础配置验证
# from pydantic import BaseSettings, Field, validator
from cryptography.fernet import Fernet

from .exceptions import ConfigurationError, ErrorContext

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    key: str
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=datetime.utcnow)
    source: str = "unknown"


class AresSettings:
    """Ares系统配置模型 - 简化版配置验证"""

    def __init__(self):
        """初始化配置设置"""
        # 应用基础配置
        self.app_name = "Ares Investment System"
        self.app_version = "1.0.0"
        self.app_environment = os.getenv("APP_ENV", "development")
        self.app_debug = os.getenv("APP_DEBUG", "false").lower() == "true"

        # API配置
        self.steamdt_api_key = os.getenv("STEAMDT_API_KEY")
        self.steamdt_base_url = "https://api.steamdt.com"

        # 数据库配置
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///data/ares.db")
        self.database_pool_size = int(os.getenv("DATABASE_POOL_SIZE", "20"))
        self.database_max_overflow = int(os.getenv("DATABASE_MAX_OVERFLOW", "30"))

        # Redis配置
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_db = int(os.getenv("REDIS_DB", "0"))
        self.redis_password = os.getenv("REDIS_PASSWORD")

        # 加密配置
        self.encryption_key = os.getenv("ENCRYPTION_KEY")
        self.secret_key = os.getenv("SECRET_KEY")

        # 通知服务配置
        self.discord_webhook_url = os.getenv("DISCORD_WEBHOOK_URL")
        self.telegram_bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.telegram_chat_id = os.getenv("TELEGRAM_CHAT_ID")

        # 日志配置
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.log_file_path = "logs/ares.log"
        self.log_max_size = "100MB"
        self.log_backup_count = 5

        # 监控配置
        self.core_pool_size = int(os.getenv("CORE_POOL_SIZE", "30"))
        self.main_pool_size = int(os.getenv("MAIN_POOL_SIZE", "970"))
        self.core_update_interval = int(os.getenv("CORE_UPDATE_INTERVAL", "30"))
        self.main_update_interval = int(os.getenv("MAIN_UPDATE_INTERVAL", "240"))

        # API限制配置
        self.api_calls_per_minute = int(os.getenv("API_CALLS_PER_MINUTE", "10"))
        self.api_retry_attempts = int(os.getenv("API_RETRY_ATTEMPTS", "3"))
        self.api_retry_base_delay = float(os.getenv("API_RETRY_BASE_DELAY", "1.0"))
        self.api_retry_max_delay = float(os.getenv("API_RETRY_MAX_DELAY", "60.0"))

        # 缓存配置
        self.cache_default_ttl = int(os.getenv("CACHE_DEFAULT_TTL", "1800"))
        self.cache_hot_data_ttl = int(os.getenv("CACHE_HOT_DATA_TTL", "600"))
        self.cache_query_result_ttl = int(os.getenv("CACHE_QUERY_RESULT_TTL", "300"))

        # UI配置
        self.ui_port = int(os.getenv("UI_PORT", "8501"))
        self.ui_host = os.getenv("UI_HOST", "0.0.0.0")
        self.ui_theme = os.getenv("UI_THEME", "dark")

        # 验证必需配置
        self._validate()

    def _validate(self):
        """验证配置"""
        errors = []

        # 验证必需配置
        if not self.steamdt_api_key:
            errors.append("STEAMDT_API_KEY 环境变量未设置")

        if not self.encryption_key:
            errors.append("ENCRYPTION_KEY 环境变量未设置")
        else:
            try:
                Fernet(self.encryption_key.encode())
            except Exception:
                errors.append("ENCRYPTION_KEY 格式无效，必须是有效的Fernet密钥")

        if not self.secret_key:
            errors.append("SECRET_KEY 环境变量未设置")

        # 验证环境
        valid_envs = ['development', 'testing', 'staging', 'production']
        if self.app_environment not in valid_envs:
            errors.append(f"APP_ENV 必须是以下之一: {valid_envs}")

        # 验证日志级别
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.log_level not in valid_levels:
            errors.append(f"LOG_LEVEL 必须是以下之一: {valid_levels}")

        # 验证UI主题
        valid_themes = ['light', 'dark']
        if self.ui_theme not in valid_themes:
            errors.append(f"UI_THEME 必须是以下之一: {valid_themes}")

        if errors:
            raise ConfigurationError(
                f"配置验证失败: {'; '.join(errors)}",
                context=ErrorContext(operation="settings_validation")
            )

    def dict(self):
        """转换为字典格式"""
        return {
            'app_name': self.app_name,
            'app_version': self.app_version,
            'app_environment': self.app_environment,
            'app_debug': self.app_debug,
            'steamdt_api_key': self.steamdt_api_key,
            'steamdt_base_url': self.steamdt_base_url,
            'database_url': self.database_url,
            'database_pool_size': self.database_pool_size,
            'database_max_overflow': self.database_max_overflow,
            'redis_url': self.redis_url,
            'redis_db': self.redis_db,
            'redis_password': self.redis_password,
            'encryption_key': self.encryption_key,
            'secret_key': self.secret_key,
            'discord_webhook_url': self.discord_webhook_url,
            'telegram_bot_token': self.telegram_bot_token,
            'telegram_chat_id': self.telegram_chat_id,
            'log_level': self.log_level,
            'log_file_path': self.log_file_path,
            'log_max_size': self.log_max_size,
            'log_backup_count': self.log_backup_count,
            'core_pool_size': self.core_pool_size,
            'main_pool_size': self.main_pool_size,
            'core_update_interval': self.core_update_interval,
            'main_update_interval': self.main_update_interval,
            'api_calls_per_minute': self.api_calls_per_minute,
            'api_retry_attempts': self.api_retry_attempts,
            'api_retry_base_delay': self.api_retry_base_delay,
            'api_retry_max_delay': self.api_retry_max_delay,
            'cache_default_ttl': self.cache_default_ttl,
            'cache_hot_data_ttl': self.cache_hot_data_ttl,
            'cache_query_result_ttl': self.cache_query_result_ttl,
            'ui_port': self.ui_port,
            'ui_host': self.ui_host,
            'ui_theme': self.ui_theme
        }


class ConfigManager:
    """配置管理器 - 统一管理分层配置"""
    
    def __init__(self, config_file: str = "config/settings.yaml", env_file: str = ".env"):
        """
        初始化配置管理器
        
        Args:
            config_file: 基础配置文件路径
            env_file: 环境变量文件路径
        """
        self.config_file = Path(config_file)
        self.env_file = Path(env_file)
        self.config_data: Dict[str, Any] = {}
        self.runtime_config: Dict[str, Any] = {}
        self.change_listeners: List[callable] = []
        self.change_history: List[ConfigChangeEvent] = []
        
        # 加载配置
        self._load_all_configs()
        
        # 初始化设置
        try:
            self.settings = AresSettings()
            logger.info("配置验证通过")
        except Exception as e:
            raise ConfigurationError(
                f"配置验证失败: {str(e)}",
                context=ErrorContext(operation="config_validation")
            )
        
        # 初始化加密工具
        try:
            self.cipher = Fernet(self.settings.encryption_key.encode())
        except Exception as e:
            raise ConfigurationError(
                f"加密工具初始化失败: {str(e)}",
                context=ErrorContext(operation="encryption_init")
            )
    
    def _load_all_configs(self):
        """加载所有配置源"""
        # 1. 加载基础配置文件
        self._load_yaml_config()
        
        # 2. 加载环境变量
        self._load_env_config()
        
        # 3. 初始化运行时配置
        self.runtime_config = {}
    
    def _load_yaml_config(self):
        """加载YAML配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = yaml.safe_load(f) or {}
                logger.info(f"已加载配置文件: {self.config_file}")
            else:
                logger.warning(f"配置文件不存在: {self.config_file}")
                self.config_data = {}
        except Exception as e:
            raise ConfigurationError(
                f"加载配置文件失败: {str(e)}",
                context=ErrorContext(operation="load_yaml_config", additional_data={'file': str(self.config_file)})
            )
    
    def _load_env_config(self):
        """加载环境变量配置"""
        try:
            if self.env_file.exists():
                from dotenv import load_dotenv
                load_dotenv(self.env_file)
                logger.info(f"已加载环境变量文件: {self.env_file}")
            else:
                logger.info("环境变量文件不存在，使用系统环境变量")
        except Exception as e:
            logger.warning(f"加载环境变量文件失败: {str(e)}")
    
    def get(self, key: str, default: Any = None, source: str = "auto") -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            source: 配置源 ("auto", "yaml", "env", "runtime", "pydantic")
            
        Returns:
            配置值
        """
        if source == "auto":
            # 按优先级顺序查找：运行时 > 环境变量 > YAML > Pydantic
            value = self._get_from_runtime(key)
            if value is not None:
                return value

            value = self._get_from_env(key)
            if value is not None:
                return value

            value = self._get_from_yaml(key)
            if value is not None:
                return value

            value = self._get_from_pydantic(key)
            if value is not None:
                return value

            return default
        
        elif source == "yaml":
            return self._get_from_yaml(key, default)
        elif source == "env":
            return self._get_from_env(key, default)
        elif source == "runtime":
            return self._get_from_runtime(key, default)
        elif source == "pydantic":
            return self._get_from_pydantic(key, default)
        else:
            raise ValueError(f"不支持的配置源: {source}")
    
    def _get_from_yaml(self, key: str, default: Any = None) -> Any:
        """从YAML配置获取值"""
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def _get_from_env(self, key: str, default: Any = None) -> Any:
        """从环境变量获取值"""
        # 将点号分隔的键转换为大写下划线格式
        env_key = key.upper().replace('.', '_')
        return os.getenv(env_key, default)
    
    def _get_from_runtime(self, key: str, default: Any = None) -> Any:
        """从运行时配置获取值"""
        return self.runtime_config.get(key, default)
    
    def _get_from_pydantic(self, key: str, default: Any = None) -> Any:
        """从Pydantic设置获取值"""
        try:
            # 将点号分隔的键转换为下划线格式
            pydantic_key = key.replace('.', '_')
            return getattr(self.settings, pydantic_key, default)
        except AttributeError:
            return default

    def set(self, key: str, value: Any, source: str = "runtime", persist: bool = False) -> bool:
        """
        设置配置值

        Args:
            key: 配置键
            value: 配置值
            source: 配置源 ("runtime", "yaml")
            persist: 是否持久化到文件

        Returns:
            是否设置成功
        """
        old_value = self.get(key)

        try:
            if source == "runtime":
                self.runtime_config[key] = value
            elif source == "yaml":
                self._set_yaml_value(key, value)
                if persist:
                    self._save_yaml_config()
            else:
                raise ValueError(f"不支持的配置源: {source}")

            # 记录配置变更
            change_event = ConfigChangeEvent(
                key=key,
                old_value=old_value,
                new_value=value,
                source=source
            )
            self.change_history.append(change_event)

            # 通知监听器
            self._notify_change_listeners(change_event)

            logger.info(f"配置已更新: {key} = {value} (source: {source})")
            return True

        except Exception as e:
            logger.error(f"设置配置失败: {key} = {value}, 错误: {str(e)}")
            return False

    def _set_yaml_value(self, key: str, value: Any):
        """设置YAML配置值"""
        keys = key.split('.')
        config = self.config_data

        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        # 设置值
        config[keys[-1]] = value

    def _save_yaml_config(self):
        """保存YAML配置到文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)

            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, allow_unicode=True)

            logger.info(f"配置已保存到文件: {self.config_file}")
        except Exception as e:
            raise ConfigurationError(
                f"保存配置文件失败: {str(e)}",
                context=ErrorContext(operation="save_yaml_config")
            )

    def reload_config(self):
        """重新加载配置"""
        try:
            old_config = self.config_data.copy()
            self._load_all_configs()

            # 重新初始化设置
            self.settings = AresSettings()

            logger.info("配置已重新加载")

            # 通知配置重载
            change_event = ConfigChangeEvent(
                key="__reload__",
                old_value=old_config,
                new_value=self.config_data,
                source="reload"
            )
            self._notify_change_listeners(change_event)

        except Exception as e:
            raise ConfigurationError(
                f"重新加载配置失败: {str(e)}",
                context=ErrorContext(operation="reload_config")
            )

    def encrypt_value(self, value: str) -> str:
        """加密敏感值"""
        try:
            return self.cipher.encrypt(value.encode()).decode()
        except Exception as e:
            raise ConfigurationError(
                f"加密失败: {str(e)}",
                context=ErrorContext(operation="encrypt_value")
            )

    def decrypt_value(self, encrypted_value: str) -> str:
        """解密敏感值"""
        try:
            return self.cipher.decrypt(encrypted_value.encode()).decode()
        except Exception as e:
            raise ConfigurationError(
                f"解密失败: {str(e)}",
                context=ErrorContext(operation="decrypt_value")
            )

    def add_change_listener(self, listener: callable):
        """添加配置变更监听器"""
        self.change_listeners.append(listener)

    def remove_change_listener(self, listener: callable):
        """移除配置变更监听器"""
        if listener in self.change_listeners:
            self.change_listeners.remove(listener)

    def _notify_change_listeners(self, change_event: ConfigChangeEvent):
        """通知配置变更监听器"""
        for listener in self.change_listeners:
            try:
                listener(change_event)
            except Exception as e:
                logger.error(f"配置变更监听器执行失败: {str(e)}")

    def get_all_config(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """获取所有配置"""
        config = {}

        # 合并所有配置源
        config.update(self.config_data)
        config.update(self.runtime_config)

        # 添加Pydantic设置
        pydantic_dict = self.settings.dict()
        config.update(pydantic_dict)

        # 过滤敏感信息
        if not include_sensitive:
            sensitive_keys = [
                'steamdt_api_key', 'encryption_key', 'secret_key',
                'discord_webhook_url', 'telegram_bot_token', 'redis_password'
            ]
            for key in sensitive_keys:
                if key in config:
                    config[key] = "***HIDDEN***"

        return config

    def validate_config(self) -> Dict[str, Any]:
        """验证配置完整性"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }

        try:
            # 验证必需的配置项
            required_configs = [
                'steamdt_api_key',
                'encryption_key',
                'secret_key'
            ]

            for config_key in required_configs:
                value = self.get(config_key)
                if not value:
                    validation_result['errors'].append(f"缺少必需配置: {config_key}")
                    validation_result['valid'] = False

            # 验证数据库连接
            database_url = self.get('database_url')
            if database_url and not database_url.startswith(('sqlite://', 'postgresql://', 'mysql://')):
                validation_result['warnings'].append(f"数据库URL格式可能不正确: {database_url}")

            # 验证端口号
            ui_port = self.get('ui_port')
            if ui_port and (ui_port < 1024 or ui_port > 65535):
                validation_result['warnings'].append(f"UI端口号可能不合适: {ui_port}")

            # 验证API限制配置
            api_calls_per_minute = self.get('api_calls_per_minute')
            if api_calls_per_minute and api_calls_per_minute > 60:
                validation_result['warnings'].append(f"API调用频率可能过高: {api_calls_per_minute}/分钟")

        except Exception as e:
            validation_result['errors'].append(f"配置验证异常: {str(e)}")
            validation_result['valid'] = False

        return validation_result

    def export_config(self, file_path: str, format: str = "yaml", include_sensitive: bool = False):
        """导出配置到文件"""
        config = self.get_all_config(include_sensitive=include_sensitive)

        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            if format.lower() == "yaml":
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            elif format.lower() == "json":
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False, default=str)
            else:
                raise ValueError(f"不支持的导出格式: {format}")

            logger.info(f"配置已导出到: {file_path}")

        except Exception as e:
            raise ConfigurationError(
                f"导出配置失败: {str(e)}",
                context=ErrorContext(operation="export_config")
            )

    def get_change_history(self, limit: int = 100) -> List[ConfigChangeEvent]:
        """获取配置变更历史"""
        return self.change_history[-limit:]

    def clear_change_history(self):
        """清空配置变更历史"""
        self.change_history.clear()
        logger.info("配置变更历史已清空")


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config(key: str, default: Any = None) -> Any:
    """快捷方式：获取配置值"""
    return get_config_manager().get(key, default)


def set_config(key: str, value: Any, persist: bool = False) -> bool:
    """快捷方式：设置配置值"""
    return get_config_manager().set(key, value, persist=persist)
