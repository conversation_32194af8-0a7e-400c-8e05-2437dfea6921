#!/usr/bin/env python3
"""
Ares系统监控仪表板
提供系统健康状态、指标统计和错误监控的命令行界面
"""

import os
import sys
import asyncio
import argparse
import json
import time
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.health_check import get_health_monitor, setup_health_monitoring, HealthStatus
from core.metrics import get_metrics_collector, get_business_metrics
from core.error_handler import get_error_handler
from core.config import get_config_manager


def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_section(title: str):
    """打印章节标题"""
    print(f"\n--- {title} ---")


def format_status(status: str) -> str:
    """格式化状态显示"""
    status_colors = {
        'healthy': '\033[92m✓ HEALTHY\033[0m',
        'warning': '\033[93m⚠ WARNING\033[0m',
        'critical': '\033[91m✗ CRITICAL\033[0m',
        'unknown': '\033[90m? UNKNOWN\033[0m'
    }
    return status_colors.get(status.lower(), status.upper())


def format_bytes(bytes_value: float) -> str:
    """格式化字节数"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} PB"


def format_duration(seconds: float) -> str:
    """格式化时间间隔"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        return f"{seconds/60:.1f}m"
    elif seconds < 86400:
        return f"{seconds/3600:.1f}h"
    else:
        return f"{seconds/86400:.1f}d"


async def show_health_status():
    """显示健康状态"""
    print_section("System Health Status")
    
    health_monitor = get_health_monitor()
    
    # 执行健康检查
    print("Performing health checks...")
    results = await health_monitor.check_all_health()
    
    if not results:
        print("No health checkers configured")
        return
    
    # 显示整体状态
    overall_status = health_monitor.get_overall_status()
    print(f"\nOverall Status: {format_status(overall_status.value)}")
    
    # 显示各组件状态
    print("\nComponent Status:")
    for component, result in results.items():
        status_str = format_status(result.status.value)
        response_time = f"{result.response_time*1000:.1f}ms"
        print(f"  {component:20} {status_str:20} {response_time:>8} - {result.message}")
        
        # 显示详细信息（如果有警告或错误）
        if result.status in [HealthStatus.WARNING, HealthStatus.CRITICAL] and result.details:
            for key, value in result.details.items():
                if key != 'error':
                    print(f"    {key}: {value}")


def show_metrics_summary():
    """显示指标摘要"""
    print_section("Metrics Summary")
    
    metrics_collector = get_metrics_collector()
    summary = metrics_collector.get_metrics_summary()
    
    print(f"Total Counters:   {summary['total_counters']}")
    print(f"Total Gauges:     {summary['total_gauges']}")
    print(f"Total Histograms: {summary['total_histograms']}")
    print(f"Total Timers:     {summary['total_timers']}")
    print(f"Data Points:      {summary['data_points']}")
    
    if summary['last_update']:
        last_update = datetime.fromtimestamp(summary['last_update'])
        print(f"Last Update:      {last_update.strftime('%Y-%m-%d %H:%M:%S')}")


def show_business_metrics():
    """显示业务指标"""
    print_section("Business Metrics")
    
    metrics_collector = get_metrics_collector()
    
    # API调用统计
    api_calls_total = metrics_collector.get_counter('api_calls_total')
    api_calls_success = metrics_collector.get_counter('api_calls_success')
    api_calls_error = metrics_collector.get_counter('api_calls_error')
    
    if api_calls_total > 0:
        success_rate = (api_calls_success / api_calls_total) * 100
        print(f"API Calls:")
        print(f"  Total:        {api_calls_total:.0f}")
        print(f"  Success:      {api_calls_success:.0f}")
        print(f"  Errors:       {api_calls_error:.0f}")
        print(f"  Success Rate: {success_rate:.1f}%")
    
    # 投资组合指标
    portfolio_value = metrics_collector.get_gauge('portfolio_total_value')
    portfolio_items = metrics_collector.get_gauge('portfolio_item_count')
    portfolio_pl = metrics_collector.get_gauge('portfolio_profit_loss')
    
    if portfolio_value > 0:
        print(f"\nPortfolio:")
        print(f"  Total Value:  ${portfolio_value:.2f}")
        print(f"  Item Count:   {portfolio_items:.0f}")
        print(f"  P&L:          ${portfolio_pl:.2f}")
    
    # 数据库操作统计
    db_ops_total = metrics_collector.get_counter('db_operations_total')
    if db_ops_total > 0:
        print(f"\nDatabase Operations: {db_ops_total:.0f}")


def show_error_statistics():
    """显示错误统计"""
    print_section("Error Statistics")
    
    error_handler = get_error_handler()
    stats = error_handler.get_error_stats()
    
    print(f"Total Errors: {stats['total_errors']}")
    
    if stats['errors_by_severity']:
        print("\nBy Severity:")
        for severity, count in stats['errors_by_severity'].items():
            print(f"  {severity.capitalize():10} {count}")
    
    if stats['errors_by_category']:
        print("\nBy Category:")
        for category, count in stats['errors_by_category'].items():
            print(f"  {category.replace('_', ' ').title():20} {count}")
    
    if stats['last_error_time']:
        last_error = datetime.fromisoformat(stats['last_error_time'])
        time_since = datetime.utcnow() - last_error
        print(f"\nLast Error: {format_duration(time_since.total_seconds())} ago")
    
    # 显示断路器状态
    circuit_breakers = stats.get('circuit_breakers', {})
    if circuit_breakers:
        print("\nCircuit Breakers:")
        for name, cb_info in circuit_breakers.items():
            state = cb_info['state'].upper()
            failures = cb_info['failure_count']
            print(f"  {name:20} {state:8} (failures: {failures})")


def show_system_resources():
    """显示系统资源"""
    print_section("System Resources")
    
    try:
        import psutil
        
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        print(f"CPU Usage:    {cpu_percent:.1f}% ({cpu_count} cores)")
        
        # 内存信息
        memory = psutil.virtual_memory()
        print(f"Memory Usage: {memory.percent:.1f}% ({format_bytes(memory.used)}/{format_bytes(memory.total)})")
        print(f"Memory Free:  {format_bytes(memory.available)}")
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        print(f"Disk Usage:   {disk.percent:.1f}% ({format_bytes(disk.used)}/{format_bytes(disk.total)})")
        print(f"Disk Free:    {format_bytes(disk.free)}")
        
        # 网络信息
        net_io = psutil.net_io_counters()
        print(f"Network I/O:  Sent: {format_bytes(net_io.bytes_sent)}, Recv: {format_bytes(net_io.bytes_recv)}")
        
    except ImportError:
        print("psutil not available - install with: pip install psutil")


def show_configuration():
    """显示配置信息"""
    print_section("Configuration")
    
    try:
        config_manager = get_config_manager()
        
        # 显示关键配置
        key_configs = [
            'app_name',
            'app_version',
            'app_environment',
            'database_url',
            'redis_url',
            'api_calls_per_minute',
            'core_pool_size',
            'main_pool_size'
        ]
        
        for key in key_configs:
            value = config_manager.get(key)
            if value:
                # 隐藏敏感信息
                if 'password' in key.lower() or 'key' in key.lower() or 'token' in key.lower():
                    value = "***HIDDEN***"
                print(f"  {key:25} {value}")
    
    except Exception as e:
        print(f"Failed to load configuration: {str(e)}")


async def export_metrics(output_file: str, format: str = "json"):
    """导出指标数据"""
    print_section(f"Exporting Metrics to {output_file}")
    
    try:
        # 收集所有数据
        health_monitor = get_health_monitor()
        metrics_collector = get_metrics_collector()
        error_handler = get_error_handler()
        
        # 执行健康检查
        health_results = await health_monitor.check_all_health()
        
        export_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'health_status': {
                'overall': health_monitor.get_overall_status().value,
                'components': {name: result.to_dict() for name, result in health_results.items()}
            },
            'metrics': metrics_collector.get_all_metrics(),
            'error_stats': error_handler.get_error_stats()
        }
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            if format.lower() == 'json':
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            else:
                f.write(str(export_data))
        
        print(f"Metrics exported to {output_file}")
        
    except Exception as e:
        print(f"Failed to export metrics: {str(e)}")


async def monitor_realtime(interval: int = 5):
    """实时监控模式"""
    print_header("Real-time Monitoring")
    print(f"Monitoring interval: {interval} seconds")
    print("Press Ctrl+C to stop...")
    
    try:
        while True:
            # 清屏
            os.system('cls' if os.name == 'nt' else 'clear')
            
            print_header(f"Ares System Monitor - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 显示健康状态
            await show_health_status()
            
            # 显示指标摘要
            show_metrics_summary()
            
            # 显示错误统计
            show_error_statistics()
            
            print(f"\nNext update in {interval} seconds...")
            await asyncio.sleep(interval)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Ares系统监控仪表板')
    parser.add_argument('--health', action='store_true', help='显示健康状态')
    parser.add_argument('--metrics', action='store_true', help='显示指标统计')
    parser.add_argument('--errors', action='store_true', help='显示错误统计')
    parser.add_argument('--resources', action='store_true', help='显示系统资源')
    parser.add_argument('--config', action='store_true', help='显示配置信息')
    parser.add_argument('--export', help='导出指标到文件')
    parser.add_argument('--format', choices=['json', 'text'], default='json', help='导出格式')
    parser.add_argument('--monitor', action='store_true', help='实时监控模式')
    parser.add_argument('--interval', type=int, default=5, help='监控间隔（秒）')
    
    args = parser.parse_args()
    
    # 设置健康监控
    setup_health_monitoring()
    
    if args.monitor:
        await monitor_realtime(args.interval)
    elif args.export:
        await export_metrics(args.export, args.format)
    else:
        # 显示指定的信息
        if args.health or not any([args.metrics, args.errors, args.resources, args.config]):
            await show_health_status()
        
        if args.metrics or not any([args.health, args.errors, args.resources, args.config]):
            show_metrics_summary()
            show_business_metrics()
        
        if args.errors:
            show_error_statistics()
        
        if args.resources:
            show_system_resources()
        
        if args.config:
            show_configuration()


if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
