"""
CS2饰品业务逻辑类
包含CS2饰品特定的业务逻辑和评分算法
"""

import logging
import math
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from core.database import Item, Price
from services.steamdt_api import ItemPriceData, PlatformPrice


class CS2Rarity(Enum):
    """CS2饰品稀有度枚举"""
    CONSUMER = "消费级"
    INDUSTRIAL = "工业级"
    MIL_SPEC = "军规级"
    RESTRICTED = "受限"
    CLASSIFIED = "保密"
    COVERT = "隐秘"
    CONTRABAND = "违禁品"


class CS2WeaponType(Enum):
    """CS2武器类型枚举"""
    RIFLE = "步枪"
    SNIPER = "狙击枪"
    PISTOL = "手枪"
    SMG = "冲锋枪"
    SHOTGUN = "霰弹枪"
    MACHINEGUN = "机枪"
    KNIFE = "刀具"
    GLOVES = "手套"
    STICKER = "贴纸"
    MUSIC_KIT = "音乐盒"


class CS2Quality(Enum):
    """CS2饰品品质枚举"""
    NORMAL = "普通"
    STATTRAK = "StatTrak™"
    SOUVENIR = "纪念品"


@dataclass
class CS2ItemAnalysis:
    """CS2饰品分析结果"""
    investment_score: float
    rarity_multiplier: float
    popularity_score: float
    liquidity_score: float
    price_trend_score: float
    risk_level: str
    recommendation: str
    analysis_time: datetime


class CS2Item:
    """CS2饰品业务逻辑类"""
    
    # 稀有度权重映射
    RARITY_WEIGHTS = {
        CS2Rarity.CONSUMER: 1.0,
        CS2Rarity.INDUSTRIAL: 1.2,
        CS2Rarity.MIL_SPEC: 1.5,
        CS2Rarity.RESTRICTED: 2.0,
        CS2Rarity.CLASSIFIED: 3.0,
        CS2Rarity.COVERT: 5.0,
        CS2Rarity.CONTRABAND: 10.0
    }
    
    # 武器类型权重映射
    WEAPON_TYPE_WEIGHTS = {
        CS2WeaponType.KNIFE: 3.0,
        CS2WeaponType.GLOVES: 2.5,
        CS2WeaponType.RIFLE: 2.0,
        CS2WeaponType.SNIPER: 1.8,
        CS2WeaponType.PISTOL: 1.5,
        CS2WeaponType.SMG: 1.3,
        CS2WeaponType.SHOTGUN: 1.2,
        CS2WeaponType.MACHINEGUN: 1.1,
        CS2WeaponType.STICKER: 1.0,
        CS2WeaponType.MUSIC_KIT: 0.8
    }
    
    # 品质权重映射
    QUALITY_WEIGHTS = {
        CS2Quality.NORMAL: 1.0,
        CS2Quality.STATTRAK: 1.5,
        CS2Quality.SOUVENIR: 1.3
    }
    
    def __init__(self, item: Item):
        """
        初始化CS2饰品业务对象
        
        Args:
            item: 数据库Item实例
        """
        self.item = item
        self.logger = logging.getLogger(__name__)
    
    def calculate_investment_score(self, price_data: Optional[ItemPriceData] = None) -> float:
        """
        计算综合投资评分
        
        Args:
            price_data: 价格数据，用于计算流动性和趋势
            
        Returns:
            float: 投资评分 (0-100)
        """
        try:
            # 基础评分组件
            rarity_score = self.get_rarity_multiplier() * 10
            weapon_type_score = self.get_weapon_type_multiplier() * 10
            quality_score = self.get_quality_multiplier() * 10
            wear_score = self.calculate_wear_score()
            
            # 市场数据评分
            liquidity_score = self.calculate_liquidity_score(price_data)
            price_trend_score = self.analyze_price_trend(price_data)
            
            # 加权计算最终评分
            weights = {
                'rarity': 0.25,
                'weapon_type': 0.20,
                'quality': 0.15,
                'wear': 0.15,
                'liquidity': 0.15,
                'price_trend': 0.10
            }
            
            final_score = (
                rarity_score * weights['rarity'] +
                weapon_type_score * weights['weapon_type'] +
                quality_score * weights['quality'] +
                wear_score * weights['wear'] +
                liquidity_score * weights['liquidity'] +
                price_trend_score * weights['price_trend']
            )
            
            # 确保评分在0-100范围内
            return max(0, min(100, final_score))
            
        except Exception as e:
            self.logger.error(f"Error calculating investment score for {self.item.item_id}: {e}")
            return 0.0
    
    def get_rarity_multiplier(self) -> float:
        """
        获取稀有度权重
        
        Returns:
            float: 稀有度权重倍数
        """
        if not self.item.rarity:
            return 1.0
        
        try:
            rarity = CS2Rarity(self.item.rarity)
            return self.RARITY_WEIGHTS.get(rarity, 1.0)
        except ValueError:
            # 如果稀有度不在枚举中，返回默认值
            return 1.0
    
    def get_weapon_type_multiplier(self) -> float:
        """
        获取武器类型权重
        
        Returns:
            float: 武器类型权重倍数
        """
        if not self.item.weapon_type:
            return 1.0
        
        try:
            weapon_type = CS2WeaponType(self.item.weapon_type)
            return self.WEAPON_TYPE_WEIGHTS.get(weapon_type, 1.0)
        except ValueError:
            return 1.0
    
    def get_quality_multiplier(self) -> float:
        """
        获取品质权重
        
        Returns:
            float: 品质权重倍数
        """
        if not self.item.quality:
            return 1.0
        
        try:
            quality = CS2Quality(self.item.quality)
            return self.QUALITY_WEIGHTS.get(quality, 1.0)
        except ValueError:
            return 1.0
    
    def calculate_wear_score(self) -> float:
        """
        计算磨损度评分
        
        Returns:
            float: 磨损度评分 (0-10)
        """
        if self.item.wear_rating is None:
            return 5.0  # 默认中等评分
        
        # 磨损度越低，评分越高
        # 0.0-0.07: 崭新出厂 (10分)
        # 0.07-0.15: 略有磨损 (8分)
        # 0.15-0.38: 久经沙场 (6分)
        # 0.38-0.45: 破损不堪 (4分)
        # 0.45-1.0: 战痕累累 (2分)
        
        wear = self.item.wear_rating
        if wear <= 0.07:
            return 10.0
        elif wear <= 0.15:
            return 8.0
        elif wear <= 0.38:
            return 6.0
        elif wear <= 0.45:
            return 4.0
        else:
            return 2.0
    
    def calculate_liquidity_score(self, price_data: Optional[ItemPriceData] = None) -> float:
        """
        计算流动性评分
        
        Args:
            price_data: 价格数据
            
        Returns:
            float: 流动性评分 (0-10)
        """
        if not price_data or not price_data.platform_prices:
            return 5.0  # 默认中等流动性
        
        # 基于平台数量和交易量计算流动性
        platform_count = len(price_data.platform_prices)
        total_volume = sum(p.sell_count + p.bidding_count for p in price_data.platform_prices)
        
        # 平台数量评分 (最多5分)
        platform_score = min(5.0, platform_count * 1.0)
        
        # 交易量评分 (最多5分)
        if total_volume >= 1000:
            volume_score = 5.0
        elif total_volume >= 500:
            volume_score = 4.0
        elif total_volume >= 100:
            volume_score = 3.0
        elif total_volume >= 50:
            volume_score = 2.0
        elif total_volume >= 10:
            volume_score = 1.0
        else:
            volume_score = 0.5
        
        return platform_score + volume_score
    
    def analyze_price_trend(self, price_data: Optional[ItemPriceData] = None) -> float:
        """
        分析价格趋势
        
        Args:
            price_data: 价格数据
            
        Returns:
            float: 价格趋势评分 (0-10)
        """
        # 这里需要历史价格数据来计算趋势
        # 暂时基于当前价格稳定性给出评分
        if not price_data or not price_data.platform_prices:
            return 5.0
        
        # 计算价格差异程度
        prices = [p.sell_price for p in price_data.platform_prices if p.sell_price > 0]
        if len(prices) < 2:
            return 5.0
        
        avg_price = sum(prices) / len(prices)
        price_variance = sum((p - avg_price) ** 2 for p in prices) / len(prices)
        price_std = math.sqrt(price_variance)
        
        # 价格越稳定，评分越高
        if avg_price > 0:
            cv = price_std / avg_price  # 变异系数
            if cv <= 0.05:  # 变异系数小于5%
                return 9.0
            elif cv <= 0.10:
                return 7.0
            elif cv <= 0.20:
                return 5.0
            elif cv <= 0.30:
                return 3.0
            else:
                return 1.0
        
        return 5.0
    
    def get_comprehensive_analysis(self, price_data: Optional[ItemPriceData] = None) -> CS2ItemAnalysis:
        """
        获取综合分析结果
        
        Args:
            price_data: 价格数据
            
        Returns:
            CS2ItemAnalysis: 综合分析结果
        """
        investment_score = self.calculate_investment_score(price_data)
        rarity_multiplier = self.get_rarity_multiplier()
        liquidity_score = self.calculate_liquidity_score(price_data)
        price_trend_score = self.analyze_price_trend(price_data)
        
        # 计算流行度评分（基于稀有度和武器类型）
        popularity_score = (
            self.get_rarity_multiplier() * 10 +
            self.get_weapon_type_multiplier() * 10
        ) / 2
        
        # 确定风险等级
        if investment_score >= 80:
            risk_level = "低风险"
        elif investment_score >= 60:
            risk_level = "中等风险"
        elif investment_score >= 40:
            risk_level = "较高风险"
        else:
            risk_level = "高风险"
        
        # 生成投资建议
        if investment_score >= 75:
            recommendation = "强烈推荐"
        elif investment_score >= 60:
            recommendation = "推荐"
        elif investment_score >= 45:
            recommendation = "谨慎考虑"
        else:
            recommendation = "不推荐"
        
        return CS2ItemAnalysis(
            investment_score=investment_score,
            rarity_multiplier=rarity_multiplier,
            popularity_score=popularity_score,
            liquidity_score=liquidity_score,
            price_trend_score=price_trend_score,
            risk_level=risk_level,
            recommendation=recommendation,
            analysis_time=datetime.now()
        )
