#!/bin/bash
# Ares投资系统 Docker 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "${LOG_LEVEL}" = "DEBUG" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 环境变量默认值
export ARES_ENV=${ARES_ENV:-development}
export LOG_LEVEL=${LOG_LEVEL:-INFO}
export REDIS_URL=${REDIS_URL:-redis://localhost:6379/0}
export DATABASE_URL=${DATABASE_URL:-sqlite:///data/ares.db}

# 显示启动信息
log_info "Starting Ares Investment System"
log_info "Environment: ${ARES_ENV}"
log_info "Log Level: ${LOG_LEVEL}"
log_info "Service Mode: ${SERVICE_MODE:-web}"

# 等待依赖服务
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}
    
    log_info "Waiting for ${service_name} at ${host}:${port}..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" 2>/dev/null; then
            log_info "${service_name} is ready!"
            return 0
        fi
        log_debug "Attempt $i/$timeout: ${service_name} not ready, waiting..."
        sleep 1
    done
    
    log_error "Timeout waiting for ${service_name}"
    return 1
}

# 检查Redis连接
check_redis() {
    if [ -n "${REDIS_URL}" ]; then
        local redis_host=$(echo $REDIS_URL | sed -n 's/.*:\/\/\([^:]*\):.*/\1/p')
        local redis_port=$(echo $REDIS_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
        
        if [ -n "$redis_host" ] && [ -n "$redis_port" ]; then
            wait_for_service "$redis_host" "$redis_port" "Redis" 30
        fi
    fi
}

# 初始化数据库
init_database() {
    log_info "Initializing database..."
    
    # 确保数据目录存在
    mkdir -p /app/data
    
    # 运行数据库初始化脚本
    if [ -f "/app/scripts/init_db.py" ]; then
        python /app/scripts/init_db.py
        log_info "Database initialized successfully"
    else
        log_warn "Database initialization script not found"
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "Running database migrations..."
    
    if [ -d "/app/migrations" ]; then
        python -c "
import sys
sys.path.append('/app')
from migrations.migration_runner import run_migrations
run_migrations()
" || log_warn "Migration failed or no migrations to run"
        log_info "Migrations completed"
    else
        log_warn "Migrations directory not found"
    fi
}

# 启动Web服务 (FastAPI + Streamlit)
start_web() {
    log_info "Starting web services..."
    
    # 启动FastAPI (后台)
    log_info "Starting FastAPI server..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --log-level info &
    FASTAPI_PID=$!
    
    # 等待FastAPI启动
    sleep 5
    
    # 启动Streamlit (前台)
    log_info "Starting Streamlit server..."
    streamlit run app/main.py --server.port 8501 --server.address 0.0.0.0 --server.headless true
}

# 启动调度器服务
start_scheduler() {
    log_info "Starting scheduler service..."
    python scripts/run_scheduler.py
}

# 启动追踪器服务
start_tracker() {
    log_info "Starting tracker service..."
    python scripts/run_tracker.py
}

# 启动发现器服务
start_discoverer() {
    log_info "Starting discoverer service..."
    python scripts/run_discoverer.py
}

# 启动监控服务
start_monitoring() {
    log_info "Starting monitoring service..."
    python scripts/monitoring_dashboard.py
}

# 运行健康检查
health_check() {
    log_info "Running health check..."
    
    # 检查FastAPI健康状态
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_info "FastAPI health check passed"
    else
        log_error "FastAPI health check failed"
        return 1
    fi
    
    # 检查Streamlit健康状态
    if curl -f http://localhost:8501/_stcore/health >/dev/null 2>&1; then
        log_info "Streamlit health check passed"
    else
        log_warn "Streamlit health check failed"
    fi
    
    return 0
}

# 优雅停止处理
graceful_shutdown() {
    log_info "Received shutdown signal, stopping services..."
    
    # 停止后台进程
    if [ -n "$FASTAPI_PID" ]; then
        log_info "Stopping FastAPI server..."
        kill -TERM $FASTAPI_PID 2>/dev/null || true
        wait $FASTAPI_PID 2>/dev/null || true
    fi
    
    log_info "Services stopped gracefully"
    exit 0
}

# 设置信号处理
trap graceful_shutdown SIGTERM SIGINT

# 主启动逻辑
main() {
    # 检查依赖服务
    if [ "${ARES_ENV}" = "production" ]; then
        check_redis
    fi
    
    # 初始化数据库（仅在web模式下）
    if [ "${1:-web}" = "web" ]; then
        init_database
        run_migrations
    fi
    
    # 根据参数启动不同服务
    case "${1:-web}" in
        "web")
            start_web
            ;;
        "scheduler")
            start_scheduler
            ;;
        "tracker")
            start_tracker
            ;;
        "discoverer")
            start_discoverer
            ;;
        "monitoring")
            start_monitoring
            ;;
        "health")
            health_check
            ;;
        "bash")
            log_info "Starting interactive bash shell..."
            exec /bin/bash
            ;;
        *)
            log_error "Unknown command: $1"
            log_info "Available commands: web, scheduler, tracker, discoverer, monitoring, health, bash"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
