"""
数据库模块测试
验证数据库模型、查询性能和数据完整性
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import (
    DatabaseManager, OptimizedQueries, Item, Price, WatchlistItem, 
    Holding, AlertRule, MacroStat
)
from core.exceptions import DatabaseConnectionError


class TestDatabaseManager:
    """数据库管理器测试类"""
    
    @pytest.fixture
    def temp_db(self):
        """创建临时数据库"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        database_url = f"sqlite:///{temp_file.name}"
        db_manager = DatabaseManager(database_url)
        db_manager.create_tables()
        
        yield db_manager
        
        # 清理
        db_manager.close()
        os.unlink(temp_file.name)
    
    def test_database_initialization(self, temp_db):
        """测试数据库初始化"""
        assert temp_db.engine is not None
        assert temp_db.SessionLocal is not None
        
        # 测试健康检查
        health = temp_db.health_check()
        assert health['status'] == 'healthy'
        assert health['connection_test'] == True
    
    def test_session_management(self, temp_db):
        """测试会话管理"""
        # 测试上下文管理器
        with temp_db.get_session() as session:
            result = session.execute("SELECT 1").scalar()
            assert result == 1
        
        # 测试直接获取会话
        session = temp_db.get_session_direct()
        assert session is not None
        session.close()
    
    def test_table_creation(self, temp_db):
        """测试表创建"""
        health = temp_db.health_check()
        tables = health['table_stats']
        
        expected_tables = ['items', 'prices', 'watchlist_items', 'holdings']
        for table in expected_tables:
            assert table in tables
            assert tables[table] == 0  # 新建数据库应该为空


class TestDatabaseModels:
    """数据库模型测试类"""
    
    @pytest.fixture
    def temp_db_with_data(self):
        """创建包含测试数据的临时数据库"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        database_url = f"sqlite:///{temp_file.name}"
        db_manager = DatabaseManager(database_url)
        db_manager.create_tables()
        
        # 插入测试数据
        with db_manager.get_session() as session:
            # 创建测试饰品
            item = Item(
                item_id='test_ak47',
                name='AK-47 | Test Skin',
                category='rifle',
                rarity='classified',
                market_hash_name='AK-47 | Test Skin (Field-Tested)',
                float_value=0.25
            )
            session.add(item)
            session.flush()
            
            # 创建测试价格
            price = Price(
                item_id='test_ak47',
                platform='steam',
                ask_price=100.0,
                bid_price=95.0,
                last_price=98.0,
                volume_24h=50,
                timestamp=datetime.utcnow()
            )
            session.add(price)
            
            # 创建测试监控项
            watchlist_item = WatchlistItem(
                item_id='test_ak47',
                pool_type='core',
                priority_score=0.8,
                update_frequency=30,
                user_added=True,
                last_updated=datetime.utcnow()
            )
            session.add(watchlist_item)
            
            # 创建测试持仓
            holding = Holding(
                item_id='test_ak47',
                quantity=2,
                cost_basis=90.0,
                purchase_date=datetime.utcnow() - timedelta(days=10),
                purchase_platform='steam',
                notes='测试持仓'
            )
            session.add(holding)
        
        yield db_manager
        
        # 清理
        db_manager.close()
        os.unlink(temp_file.name)
    
    def test_item_model(self, temp_db_with_data):
        """测试饰品模型"""
        with temp_db_with_data.get_session() as session:
            item = session.query(Item).filter_by(item_id='test_ak47').first()
            
            assert item is not None
            assert item.name == 'AK-47 | Test Skin'
            assert item.category == 'rifle'
            assert item.rarity == 'classified'
            assert item.is_active == True
    
    def test_price_model(self, temp_db_with_data):
        """测试价格模型"""
        with temp_db_with_data.get_session() as session:
            price = session.query(Price).filter_by(item_id='test_ak47').first()
            
            assert price is not None
            assert price.platform == 'steam'
            assert price.ask_price == 100.0
            assert price.bid_price == 95.0
            assert price.volume_24h == 50
    
    def test_watchlist_model(self, temp_db_with_data):
        """测试监控列表模型"""
        with temp_db_with_data.get_session() as session:
            watchlist = session.query(WatchlistItem).filter_by(item_id='test_ak47').first()
            
            assert watchlist is not None
            assert watchlist.pool_type == 'core'
            assert watchlist.priority_score == 0.8
            assert watchlist.user_added == True
            assert watchlist.next_update_time is not None  # 应该由事件监听器设置
    
    def test_holding_model(self, temp_db_with_data):
        """测试持仓模型"""
        with temp_db_with_data.get_session() as session:
            holding = session.query(Holding).filter_by(item_id='test_ak47').first()
            
            assert holding is not None
            assert holding.quantity == 2
            assert holding.cost_basis == 90.0
            assert holding.purchase_platform == 'steam'
            assert holding.is_active == True


class TestOptimizedQueries:
    """优化查询测试类"""
    
    @pytest.fixture
    def db_with_performance_data(self):
        """创建包含性能测试数据的数据库"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        database_url = f"sqlite:///{temp_file.name}"
        db_manager = DatabaseManager(database_url)
        db_manager.create_tables()
        
        # 插入大量测试数据
        with db_manager.get_session() as session:
            # 创建100个测试饰品
            items = []
            for i in range(100):
                item = Item(
                    item_id=f'test_item_{i:03d}',
                    name=f'Test Item {i}',
                    category='rifle' if i % 2 == 0 else 'pistol',
                    rarity='classified',
                    market_hash_name=f'Test Item {i} (Field-Tested)'
                )
                items.append(item)
            
            session.add_all(items)
            session.flush()
            
            # 为每个饰品创建多个价格记录
            prices = []
            platforms = ['steam', 'buff163', 'c5game']
            
            for item in items:
                for platform in platforms:
                    for days_ago in range(7):  # 7天的历史数据
                        price = Price(
                            item_id=item.item_id,
                            platform=platform,
                            ask_price=100.0 + (i % 50),
                            bid_price=95.0 + (i % 50),
                            last_price=98.0 + (i % 50),
                            volume_24h=50 + (i % 100),
                            timestamp=datetime.utcnow() - timedelta(days=days_ago, hours=i % 24)
                        )
                        prices.append(price)
            
            session.add_all(prices)
            
            # 创建监控列表
            watchlist_items = []
            for i in range(50):  # 50个监控项
                watchlist_item = WatchlistItem(
                    item_id=f'test_item_{i:03d}',
                    pool_type='core' if i < 25 else 'main',
                    priority_score=0.5 + (i % 50) / 100,
                    update_frequency=30 if i < 25 else 240,
                    user_added=i < 10,
                    last_updated=datetime.utcnow() - timedelta(minutes=i % 60)
                )
                watchlist_items.append(watchlist_item)
            
            session.add_all(watchlist_items)
        
        yield db_manager
        
        # 清理
        db_manager.close()
        os.unlink(temp_file.name)
    
    def test_get_latest_prices_performance(self, db_with_performance_data):
        """测试获取最新价格的性能"""
        queries = OptimizedQueries(db_with_performance_data)
        
        # 测试查询前10个饰品的最新价格
        item_ids = [f'test_item_{i:03d}' for i in range(10)]
        
        start_time = datetime.now()
        prices = queries.get_latest_prices(item_ids)
        query_time = (datetime.now() - start_time).total_seconds()
        
        assert len(prices) > 0
        assert query_time < 1.0  # 应该在1秒内完成
        
        # 验证返回的数据结构
        for price in prices:
            assert 'item_id' in price
            assert 'platform' in price
            assert 'ask_price' in price
            assert price['item_id'] in item_ids
    
    def test_get_watchlist_with_prices_performance(self, db_with_performance_data):
        """测试获取监控列表的性能"""
        queries = OptimizedQueries(db_with_performance_data)
        
        start_time = datetime.now()
        watchlist = queries.get_watchlist_with_prices(limit=50)
        query_time = (datetime.now() - start_time).total_seconds()
        
        assert len(watchlist) > 0
        assert query_time < 1.0  # 应该在1秒内完成
        
        # 验证返回的数据结构
        for item in watchlist:
            assert 'item_id' in item
            assert 'pool_type' in item
            assert 'priority_score' in item
            assert 'name' in item
    
    def test_get_price_history_performance(self, db_with_performance_data):
        """测试获取价格历史的性能"""
        queries = OptimizedQueries(db_with_performance_data)
        
        start_time = datetime.now()
        history = queries.get_price_history('test_item_001', days=7)
        query_time = (datetime.now() - start_time).total_seconds()
        
        assert len(history) > 0
        assert query_time < 1.0  # 应该在1秒内完成
        
        # 验证返回的数据结构
        for record in history:
            assert 'date' in record
            assert 'avg_ask_price' in record
            assert 'total_volume' in record
    
    def test_portfolio_summary_performance(self, db_with_performance_data):
        """测试投资组合摘要性能"""
        queries = OptimizedQueries(db_with_performance_data)
        
        start_time = datetime.now()
        summary = queries.get_portfolio_summary()
        query_time = (datetime.now() - start_time).total_seconds()
        
        assert 'holdings' in summary
        assert 'watchlist' in summary
        assert query_time < 0.5  # 应该在0.5秒内完成


def test_database_constraints():
    """测试数据库约束"""
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_file.close()
    
    database_url = f"sqlite:///{temp_file.name}"
    db_manager = DatabaseManager(database_url)
    db_manager.create_tables()
    
    try:
        with db_manager.get_session() as session:
            # 测试正数约束
            with pytest.raises(Exception):  # 应该违反正数约束
                invalid_price = Price(
                    item_id='test',
                    platform='steam',
                    ask_price=-10.0,  # 负价格应该被拒绝
                    timestamp=datetime.utcnow()
                )
                session.add(invalid_price)
                session.commit()
    
    finally:
        db_manager.close()
        os.unlink(temp_file.name)


if __name__ == "__main__":
    # 运行基本测试
    print("运行数据库基本测试...")
    
    # 创建临时数据库
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_file.close()
    
    try:
        database_url = f"sqlite:///{temp_file.name}"
        db_manager = DatabaseManager(database_url)
        
        # 测试表创建
        print("测试表创建...")
        db_manager.create_tables()
        
        # 测试健康检查
        print("测试健康检查...")
        health = db_manager.health_check()
        assert health['status'] == 'healthy'
        print("✓ 健康检查通过")
        
        # 测试基本CRUD操作
        print("测试基本CRUD操作...")
        with db_manager.get_session() as session:
            # 创建
            item = Item(
                item_id='test_crud',
                name='Test CRUD Item',
                category='test',
                rarity='common'
            )
            session.add(item)
            session.flush()
            
            # 读取
            retrieved_item = session.query(Item).filter_by(item_id='test_crud').first()
            assert retrieved_item.name == 'Test CRUD Item'
            
            # 更新
            retrieved_item.name = 'Updated Test Item'
            session.flush()
            
            # 验证更新
            updated_item = session.query(Item).filter_by(item_id='test_crud').first()
            assert updated_item.name == 'Updated Test Item'
            
            # 删除
            session.delete(updated_item)
        
        print("✓ CRUD操作测试通过")
        
        # 测试查询性能
        print("测试查询性能...")
        queries = OptimizedQueries(db_manager)
        
        start_time = datetime.now()
        summary = queries.get_portfolio_summary()
        query_time = (datetime.now() - start_time).total_seconds()
        print(f"✓ 投资组合查询耗时: {query_time:.3f}秒")
        
        print("所有数据库测试通过！")
        
    finally:
        db_manager.close()
        os.unlink(temp_file.name)
