# Ares系统配置文件
# 版本: 3.0.0
# 最后更新: 2025-07-17

# 应用基础配置
app:
  name: "Ares Investment System"
  version: "1.0.0"
  debug: false
  environment: "development"  # development, testing, staging, production
  description: "CS2饰品投资决策辅助系统"

# 配置管理元信息
config:
  version: "3.0.0"
  last_updated: "2025-07-17T00:00:00Z"
  schema_version: "1.0"
  hot_reload_enabled: true
  validation_enabled: true

# API配置
api:
  steamdt:
    base_url: "https://open.steamdt.com"
    # API密钥通过环境变量STEAMDT_API_KEY设置
    rate_limit:
      calls_per_minute: 10
      burst_capacity: 10
      retry_attempts: 3
      retry_base_delay: 1.0
      retry_max_delay: 60.0
    endpoints:
      rankings:
        hot: "/rankings/hot"
        trending: "/rankings/trending"
        rising: "/rankings/rising"
        falling: "/rankings/falling"
      items:
        info: "/items/{item_id}"
        prices: "/items/{item_id}/prices"
        history: "/items/{item_id}/history"
      markets:
        overview: "/markets/overview"

# 数据库配置
database:
  path: "data/ares.db"
  backup_interval: 24  # hours
  retention_policy:
    price_data: 365  # days
    macro_data: 730  # days
    log_data: 90    # days
  connection_pool:
    size: 20
    max_overflow: 30
    pool_pre_ping: true

# Redis缓存配置
redis:
  url: "redis://localhost:6379"
  # 通过环境变量REDIS_URL可以覆盖
  db: 0
  password: null
  connection_pool:
    max_connections: 20
    retry_on_timeout: true

# 监控配置
monitoring:
  core_pool:
    size: 30
    update_interval: 30  # minutes
  main_pool:
    size: 970
    update_interval: 240  # minutes
    batch_size: 240
  discovery:
    schedule: "0 9,15 * * *"  # 每天9点和15点运行
    max_discoveries: 100

# 评分算法配置
scoring:
  weights:
    spread: 0.4      # 价差权重
    volume: 0.3      # 交易量权重
    volatility: 0.2  # 波动性权重
    trend: 0.1       # 趋势权重
  thresholds:
    min_opportunity_score: 0.3
    min_volume_24h: 10
    min_spread: 2.0
    max_volatility: 50.0

# 通知服务配置
notifications:
  discord:
    enabled: true
    # webhook_url通过环境变量DISCORD_WEBHOOK_URL设置
    rate_limit: 10  # per hour
    retry_attempts: 3
  telegram:
    enabled: false
    # bot_token通过环境变量TELEGRAM_BOT_TOKEN设置
    rate_limit: 20  # per hour
    retry_attempts: 3

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file_path: "logs/ares.log"
  max_size: "100MB"
  backup_count: 5
  loggers:
    api_manager: "DEBUG"
    scheduler: "INFO"
    tracker: "INFO"
    discoverer: "INFO"

# UI配置
ui:
  streamlit:
    port: 8501
    host: "0.0.0.0"
    theme: "dark"
    page_title: "Ares Investment System"
    layout: "wide"
  refresh_intervals:
    dashboard: 30  # seconds
    status_page: 10  # seconds
    charts: 60     # seconds

# 安全配置
security:
  # encryption_key通过环境变量ENCRYPTION_KEY设置
  # secret_key通过环境变量SECRET_KEY设置
  session_timeout: 3600  # seconds
  max_login_attempts: 5
  password_min_length: 8
  enable_encryption: true
  key_rotation_days: 90  # 密钥轮换周期

# 配置验证规则
validation:
  required_env_vars:
    - "STEAMDT_API_KEY"
    - "ENCRYPTION_KEY"
    - "SECRET_KEY"
  optional_env_vars:
    - "DISCORD_WEBHOOK_URL"
    - "TELEGRAM_BOT_TOKEN"
    - "REDIS_URL"
    - "DATABASE_URL"
  config_constraints:
    api_calls_per_minute:
      min: 1
      max: 60
      default: 10
    ui_port:
      min: 1024
      max: 65535
      default: 8501
    core_pool_size:
      min: 10
      max: 100
      default: 30
    main_pool_size:
      min: 100
      max: 2000
      default: 970

# 性能配置
performance:
  cache:
    default_ttl: 1800  # 30 minutes
    hot_data_ttl: 600  # 10 minutes
    query_result_ttl: 300  # 5 minutes
  database:
    query_timeout: 30  # seconds
    batch_size: 1000
  api:
    connection_timeout: 10  # seconds
    read_timeout: 30       # seconds
