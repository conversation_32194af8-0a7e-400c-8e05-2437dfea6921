# Ares系统配置文件
# 版本: 1.0.0

app:
  name: "Ares Investment System"
  version: "1.0.0"
  debug: false
  environment: "development"

# API配置
api:
  steamdt:
    base_url: "https://api.steamdt.com"
    # API密钥通过环境变量STEAMDT_API_KEY设置
    rate_limit:
      calls_per_minute: 10
      burst_capacity: 10
      retry_attempts: 3
      retry_base_delay: 1.0
      retry_max_delay: 60.0
    endpoints:
      rankings:
        hot: "/rankings/hot"
        trending: "/rankings/trending"
        rising: "/rankings/rising"
        falling: "/rankings/falling"
      items:
        info: "/items/{item_id}"
        prices: "/items/{item_id}/prices"
        history: "/items/{item_id}/history"
      markets:
        overview: "/markets/overview"

# 数据库配置
database:
  path: "data/ares.db"
  backup_interval: 24  # hours
  retention_policy:
    price_data: 365  # days
    macro_data: 730  # days
    log_data: 90    # days
  connection_pool:
    size: 20
    max_overflow: 30
    pool_pre_ping: true

# Redis缓存配置
redis:
  url: "redis://localhost:6379"
  # 通过环境变量REDIS_URL可以覆盖
  db: 0
  password: null
  connection_pool:
    max_connections: 20
    retry_on_timeout: true

# 监控配置
monitoring:
  core_pool:
    size: 30
    update_interval: 30  # minutes
  main_pool:
    size: 970
    update_interval: 240  # minutes
    batch_size: 240
  discovery:
    schedule: "0 9,15 * * *"  # 每天9点和15点运行
    max_discoveries: 100

# 评分算法配置
scoring:
  weights:
    spread: 0.4      # 价差权重
    volume: 0.3      # 交易量权重
    volatility: 0.2  # 波动性权重
    trend: 0.1       # 趋势权重
  thresholds:
    min_opportunity_score: 0.3
    min_volume_24h: 10
    min_spread: 2.0
    max_volatility: 50.0

# 通知服务配置
notifications:
  discord:
    enabled: true
    # webhook_url通过环境变量DISCORD_WEBHOOK_URL设置
    rate_limit: 10  # per hour
    retry_attempts: 3
  telegram:
    enabled: false
    # bot_token通过环境变量TELEGRAM_BOT_TOKEN设置
    rate_limit: 20  # per hour
    retry_attempts: 3

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file_path: "logs/ares.log"
  max_size: "100MB"
  backup_count: 5
  loggers:
    api_manager: "DEBUG"
    scheduler: "INFO"
    tracker: "INFO"
    discoverer: "INFO"

# UI配置
ui:
  streamlit:
    port: 8501
    host: "0.0.0.0"
    theme: "dark"
    page_title: "Ares Investment System"
    layout: "wide"
  refresh_intervals:
    dashboard: 30  # seconds
    status_page: 10  # seconds
    charts: 60     # seconds

# 安全配置
security:
  # encryption_key通过环境变量ENCRYPTION_KEY设置
  session_timeout: 3600  # seconds
  max_login_attempts: 5
  password_min_length: 8

# 性能配置
performance:
  cache:
    default_ttl: 1800  # 30 minutes
    hot_data_ttl: 600  # 10 minutes
    query_result_ttl: 300  # 5 minutes
  database:
    query_timeout: 30  # seconds
    batch_size: 1000
  api:
    connection_timeout: 10  # seconds
    read_timeout: 30       # seconds
