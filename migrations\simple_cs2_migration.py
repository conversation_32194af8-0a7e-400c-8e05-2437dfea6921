"""
简化的CS2字段迁移脚本
直接操作数据库，不依赖完整的配置系统
"""

import logging
import sqlite3
import os
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库路径
DB_PATH = Path(__file__).parent.parent / "data" / "ares.db"

# 要添加的字段定义
NEW_FIELDS = [
    ("weapon_type", "VARCHAR(50)", "武器类型：步枪、狙击枪、手枪、刀具等"),
    ("skin_name", "VARCHAR(200)", "皮肤名称"),
    ("wear_rating", "REAL", "磨损度评级 (0.0-1.0)"),
    ("float_min", "REAL", "最小磨损值"),
    ("float_max", "REAL", "最大磨损值"),
    ("quality", "VARCHAR(20)", "品质：普通、StatTrak、纪念品等"),
    ("collection", "VARCHAR(100)", "收藏品系列"),
    ("case_name", "VARCHAR(100)", "来源箱子名称"),
    ("investment_score", "REAL", "投资评分 (0-100)"),
    ("popularity_score", "REAL", "流行度评分 (0-100)"),
    ("liquidity_score", "REAL", "流动性评分 (0-100)")
]

# 要添加的索引定义
NEW_INDEXES = [
    ("idx_item_weapon_type", "weapon_type"),
    ("idx_item_skin_name", "skin_name"),
    ("idx_item_wear_rating", "wear_rating"),
    ("idx_item_investment_score", "investment_score"),
    ("idx_item_weapon_rarity", "weapon_type, rarity"),
    ("idx_item_quality_collection", "quality, collection")
]


def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None


def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    return any(col[1] == column_name for col in columns)


def check_index_exists(cursor, index_name):
    """检查索引是否存在"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name=?
    """, (index_name,))
    return cursor.fetchone() is not None


def add_column(cursor, table_name, column_name, column_type, comment=""):
    """添加列"""
    try:
        if check_column_exists(cursor, table_name, column_name):
            logger.info(f"Column {column_name} already exists in {table_name}")
            return True
        
        sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"
        cursor.execute(sql)
        logger.info(f"Added column {column_name} to {table_name}")
        return True
        
    except sqlite3.Error as e:
        logger.error(f"Error adding column {column_name}: {e}")
        return False


def add_index(cursor, index_name, table_name, columns):
    """添加索引"""
    try:
        if check_index_exists(cursor, index_name):
            logger.info(f"Index {index_name} already exists")
            return True
        
        sql = f"CREATE INDEX {index_name} ON {table_name} ({columns})"
        cursor.execute(sql)
        logger.info(f"Created index {index_name}")
        return True
        
    except sqlite3.Error as e:
        logger.error(f"Error creating index {index_name}: {e}")
        return False


def run_migration():
    """运行迁移"""
    logger.info("Starting CS2 fields migration...")
    
    # 确保数据目录存在
    DB_PATH.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 检查items表是否存在
        if not check_table_exists(cursor, 'items'):
            logger.error("Items table does not exist. Please run database initialization first.")
            return False
        
        success = True
        
        # 添加字段
        logger.info("Adding CS2 specific fields...")
        for field_name, field_type, comment in NEW_FIELDS:
            if not add_column(cursor, 'items', field_name, field_type, comment):
                success = False
        
        # 提交字段更改
        conn.commit()
        
        # 添加索引
        logger.info("Adding indexes...")
        for index_name, columns in NEW_INDEXES:
            if not add_index(cursor, index_name, 'items', columns):
                success = False
        
        # 提交索引更改
        conn.commit()
        
        if success:
            logger.info("CS2 fields migration completed successfully")
        else:
            logger.error("CS2 fields migration completed with errors")
        
        # 验证迁移
        logger.info("Validating migration...")
        validation_success = True
        
        for field_name, _, _ in NEW_FIELDS:
            if not check_column_exists(cursor, 'items', field_name):
                logger.error(f"Validation failed: Column {field_name} not found")
                validation_success = False
        
        for index_name, _ in NEW_INDEXES:
            if not check_index_exists(cursor, index_name):
                logger.error(f"Validation failed: Index {index_name} not found")
                validation_success = False
        
        if validation_success:
            logger.info("Migration validation passed")
        else:
            logger.error("Migration validation failed")
            success = False
        
        return success
        
    except sqlite3.Error as e:
        logger.error(f"Database error: {e}")
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()


def show_table_info():
    """显示表结构信息"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        if check_table_exists(cursor, 'items'):
            logger.info("Current items table structure:")
            cursor.execute("PRAGMA table_info(items)")
            columns = cursor.fetchall()
            for col in columns:
                logger.info(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'}")
            
            logger.info("Current indexes:")
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND tbl_name='items'
            """)
            indexes = cursor.fetchall()
            for idx in indexes:
                logger.info(f"  {idx[0]}")
        else:
            logger.info("Items table does not exist")
        
        conn.close()
        
    except sqlite3.Error as e:
        logger.error(f"Error showing table info: {e}")


if __name__ == "__main__":
    logger.info(f"Database path: {DB_PATH}")
    logger.info(f"Database exists: {DB_PATH.exists()}")
    
    if DB_PATH.exists():
        show_table_info()
    
    success = run_migration()
    
    if success:
        logger.info("Migration completed successfully")
        show_table_info()
    else:
        logger.error("Migration failed")
    
    exit(0 if success else 1)
