"""
Ares系统缓存装饰器
提供函数级缓存装饰器，支持自动缓存函数结果和参数化缓存策略
"""

import asyncio
import functools
import inspect
import hashlib
import json
import time
import logging
from typing import Any, Callable, Optional, Union, Dict, List
from datetime import datetime, timedelta

from .cache import get_cache_manager, CacheConfig
from .metrics import get_metrics_collector

logger = logging.getLogger(__name__)


def _make_cache_key(func: Callable, args: tuple, kwargs: dict, key_prefix: str = None) -> str:
    """
    生成缓存键
    
    Args:
        func: 函数对象
        args: 位置参数
        kwargs: 关键字参数
        key_prefix: 键前缀
        
    Returns:
        缓存键
    """
    # 获取函数名
    func_name = f"{func.__module__}.{func.__qualname__}"
    
    # 序列化参数
    try:
        # 过滤掉不可序列化的参数
        serializable_args = []
        for arg in args:
            if isinstance(arg, (str, int, float, bool, list, dict, tuple)):
                serializable_args.append(arg)
            else:
                serializable_args.append(str(arg))
        
        serializable_kwargs = {}
        for key, value in kwargs.items():
            if isinstance(value, (str, int, float, bool, list, dict, tuple)):
                serializable_kwargs[key] = value
            else:
                serializable_kwargs[key] = str(value)
        
        # 生成参数哈希
        params_str = json.dumps({
            'args': serializable_args,
            'kwargs': serializable_kwargs
        }, sort_keys=True, default=str)
        
        params_hash = hashlib.md5(params_str.encode('utf-8')).hexdigest()[:16]
        
    except Exception as e:
        logger.warning(f"Failed to serialize parameters for {func_name}: {str(e)}")
        params_hash = str(hash((args, tuple(sorted(kwargs.items())))))
    
    # 生成最终键
    if key_prefix:
        return f"{key_prefix}:{func_name}:{params_hash}"
    else:
        return f"func_cache:{func_name}:{params_hash}"


def cache_result(
    ttl: int = 1800,
    key_prefix: str = None,
    use_l1: bool = True,
    use_l2: bool = True,
    cache_none: bool = False,
    cache_exceptions: bool = False,
    condition: Callable = None
):
    """
    缓存函数结果装饰器
    
    Args:
        ttl: 缓存生存时间（秒）
        key_prefix: 缓存键前缀
        use_l1: 是否使用L1缓存
        use_l2: 是否使用L2缓存
        cache_none: 是否缓存None结果
        cache_exceptions: 是否缓存异常
        condition: 缓存条件函数
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        # 检查是否为异步函数
        is_async = inspect.iscoroutinefunction(func)
        
        if is_async:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await _cached_call(
                    func, args, kwargs, ttl, key_prefix, 
                    use_l1, use_l2, cache_none, cache_exceptions, condition, True
                )
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                return asyncio.run(_cached_call(
                    func, args, kwargs, ttl, key_prefix,
                    use_l1, use_l2, cache_none, cache_exceptions, condition, False
                ))
            return sync_wrapper
    
    return decorator


async def _cached_call(
    func: Callable,
    args: tuple,
    kwargs: dict,
    ttl: int,
    key_prefix: str,
    use_l1: bool,
    use_l2: bool,
    cache_none: bool,
    cache_exceptions: bool,
    condition: Callable,
    is_async: bool
) -> Any:
    """执行缓存调用"""
    cache_manager = get_cache_manager()
    metrics = get_metrics_collector()
    
    # 生成缓存键
    cache_key = _make_cache_key(func, args, kwargs, key_prefix)
    
    # 检查缓存条件
    if condition and not condition(*args, **kwargs):
        # 不满足缓存条件，直接调用函数
        if is_async:
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    
    start_time = time.time()
    
    try:
        # 尝试从缓存获取
        cached_result = await cache_manager.cache.get(cache_key, use_l1, use_l2)
        
        if cached_result is not None:
            # 缓存命中
            metrics.increment_counter('function_cache_hits', tags={'function': func.__name__})
            metrics.record_timer('function_cache_get_duration', time.time() - start_time)
            
            # 检查是否为缓存的异常
            if isinstance(cached_result, dict) and cached_result.get('__cached_exception__'):
                exception_data = cached_result['exception_data']
                raise Exception(exception_data['message'])
            
            return cached_result
        
        # 缓存未命中，调用原函数
        metrics.increment_counter('function_cache_misses', tags={'function': func.__name__})
        
        try:
            if is_async:
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 检查是否应该缓存结果
            should_cache = True
            
            if result is None and not cache_none:
                should_cache = False
            
            if should_cache:
                # 缓存结果
                await cache_manager.cache.set(cache_key, result, ttl, use_l1, use_l2)
                metrics.increment_counter('function_cache_sets', tags={'function': func.__name__})
            
            metrics.record_timer('function_call_duration', time.time() - start_time, tags={'function': func.__name__})
            return result
        
        except Exception as e:
            # 处理异常
            if cache_exceptions:
                # 缓存异常信息
                exception_data = {
                    '__cached_exception__': True,
                    'exception_data': {
                        'type': type(e).__name__,
                        'message': str(e),
                        'timestamp': datetime.utcnow().isoformat()
                    }
                }
                await cache_manager.cache.set(cache_key, exception_data, ttl // 10, use_l1, use_l2)  # 异常缓存时间较短
            
            metrics.increment_counter('function_cache_errors', tags={'function': func.__name__})
            raise
    
    except Exception as e:
        logger.error(f"Cache operation failed for {func.__name__}: {str(e)}")
        metrics.increment_counter('function_cache_errors', tags={'function': func.__name__})
        
        # 缓存失败，直接调用函数
        if is_async:
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)


def cache_hot_data(ttl: int = 600, key_prefix: str = "hot_data"):
    """
    热点数据缓存装饰器
    
    Args:
        ttl: 缓存生存时间（秒），默认10分钟
        key_prefix: 缓存键前缀
        
    Returns:
        装饰器函数
    """
    return cache_result(
        ttl=ttl,
        key_prefix=key_prefix,
        use_l1=True,
        use_l2=True,
        cache_none=False
    )


def cache_query_result(ttl: int = 300, key_prefix: str = "query_result"):
    """
    查询结果缓存装饰器
    
    Args:
        ttl: 缓存生存时间（秒），默认5分钟
        key_prefix: 缓存键前缀
        
    Returns:
        装饰器函数
    """
    return cache_result(
        ttl=ttl,
        key_prefix=key_prefix,
        use_l1=True,
        use_l2=True,
        cache_none=True  # 查询结果可能为空
    )


def cache_expensive_operation(ttl: int = 1800, key_prefix: str = "expensive_op"):
    """
    昂贵操作缓存装饰器
    
    Args:
        ttl: 缓存生存时间（秒），默认30分钟
        key_prefix: 缓存键前缀
        
    Returns:
        装饰器函数
    """
    return cache_result(
        ttl=ttl,
        key_prefix=key_prefix,
        use_l1=False,  # 昂贵操作结果可能较大，只存储在L2
        use_l2=True,
        cache_none=False,
        cache_exceptions=True  # 缓存异常避免重复昂贵操作
    )


class CacheInvalidator:
    """缓存失效器"""
    
    def __init__(self):
        self.cache_manager = get_cache_manager()
    
    async def invalidate_by_pattern(self, pattern: str):
        """按模式失效缓存"""
        # TODO: 实现模式匹配的缓存失效
        logger.warning("Pattern-based cache invalidation not implemented yet")
    
    async def invalidate_function_cache(self, func: Callable, *args, **kwargs):
        """失效特定函数调用的缓存"""
        cache_key = _make_cache_key(func, args, kwargs)
        await self.cache_manager.cache.delete(cache_key)
    
    async def invalidate_prefix(self, prefix: str):
        """失效指定前缀的缓存"""
        # TODO: 实现前缀匹配的缓存失效
        logger.warning("Prefix-based cache invalidation not implemented yet")


def cache_invalidate(func: Callable = None, patterns: List[str] = None, prefixes: List[str] = None):
    """
    缓存失效装饰器
    
    Args:
        func: 要失效缓存的函数
        patterns: 要失效的缓存模式列表
        prefixes: 要失效的缓存前缀列表
        
    Returns:
        装饰器函数
    """
    def decorator(target_func: Callable) -> Callable:
        is_async = inspect.iscoroutinefunction(target_func)
        invalidator = CacheInvalidator()
        
        if is_async:
            @functools.wraps(target_func)
            async def async_wrapper(*args, **kwargs):
                result = await target_func(*args, **kwargs)
                
                # 执行缓存失效
                if func:
                    await invalidator.invalidate_function_cache(func, *args, **kwargs)
                
                if patterns:
                    for pattern in patterns:
                        await invalidator.invalidate_by_pattern(pattern)
                
                if prefixes:
                    for prefix in prefixes:
                        await invalidator.invalidate_prefix(prefix)
                
                return result
            return async_wrapper
        else:
            @functools.wraps(target_func)
            def sync_wrapper(*args, **kwargs):
                result = target_func(*args, **kwargs)
                
                # 异步执行缓存失效
                async def invalidate():
                    if func:
                        await invalidator.invalidate_function_cache(func, *args, **kwargs)
                    
                    if patterns:
                        for pattern in patterns:
                            await invalidator.invalidate_by_pattern(pattern)
                    
                    if prefixes:
                        for prefix in prefixes:
                            await invalidator.invalidate_prefix(prefix)
                
                asyncio.create_task(invalidate())
                return result
            return sync_wrapper
    
    return decorator


# 便捷的缓存装饰器实例
cache_item_analysis = cache_result(ttl=1800, key_prefix="item_analysis")
cache_price_data = cache_hot_data(ttl=600, key_prefix="price_data")
cache_user_query = cache_query_result(ttl=300, key_prefix="user_query")
cache_api_call = cache_expensive_operation(ttl=900, key_prefix="api_call")


# 缓存预热功能
class CacheWarmer:
    """缓存预热器"""
    
    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.metrics = get_metrics_collector()
    
    async def warm_up_item_data(self, item_ids: List[str]):
        """预热饰品数据"""
        logger.info(f"Warming up cache for {len(item_ids)} items")
        
        start_time = time.time()
        success_count = 0
        
        for item_id in item_ids:
            try:
                # 这里可以调用实际的数据获取函数
                # 例如：await get_item_analysis(item_id)
                success_count += 1
            except Exception as e:
                logger.error(f"Failed to warm up cache for item {item_id}: {str(e)}")
        
        duration = time.time() - start_time
        self.metrics.record_timer('cache_warmup_duration', duration)
        self.metrics.set_gauge('cache_warmup_success_rate', success_count / len(item_ids) * 100)
        
        logger.info(f"Cache warmup completed: {success_count}/{len(item_ids)} items in {duration:.2f}s")
    
    async def warm_up_hot_data(self, data_keys: List[str]):
        """预热热点数据"""
        logger.info(f"Warming up hot data cache for {len(data_keys)} keys")
        
        for key in data_keys:
            try:
                # 这里可以调用实际的数据获取函数
                pass
            except Exception as e:
                logger.error(f"Failed to warm up hot data for key {key}: {str(e)}")


# 全局缓存预热器实例
_cache_warmer: Optional[CacheWarmer] = None


def get_cache_warmer() -> CacheWarmer:
    """获取全局缓存预热器实例"""
    global _cache_warmer
    if _cache_warmer is None:
        _cache_warmer = CacheWarmer()
    return _cache_warmer
