"""
Ares基础数据收集器API接口
提供基础数据收集器的管理和监控API
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, Body
from fastapi.responses import PlainTextResponse, JSONResponse
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime, timedelta

from services.base_data_collector import (
    get_base_data_collector, 
    start_base_data_collector,
    stop_base_data_collector,
    is_base_data_collector_running
)
from core.exceptions import AresException

# 创建路由器
router = APIRouter(prefix="/api/base-data", tags=["基础数据"])


# 请求模型
class SyncConfigRequest(BaseModel):
    """同步配置请求"""
    sync_time: str = Field(..., pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="同步时间，格式为HH:MM")


class ManualSyncRequest(BaseModel):
    """手动同步请求"""
    force: bool = Field(False, description="是否强制执行，忽略API限制")
    reason: Optional[str] = Field(None, description="手动同步原因")


# 响应模型
class ServiceStatusResponse(BaseModel):
    """服务状态响应"""
    running: bool = Field(..., description="服务是否运行中")
    scheduler_running: bool = Field(..., description="调度器是否运行中")
    sync_time: str = Field(..., description="同步时间")
    next_run_time: Optional[str] = Field(None, description="下次运行时间")
    last_update: Optional[str] = Field(None, description="最后更新时间")


class SyncResultResponse(BaseModel):
    """同步结果响应"""
    success: bool = Field(..., description="是否成功")
    total_items: int = Field(..., description="总饰品数量")
    new_items: int = Field(..., description="新增饰品数量")
    updated_items: int = Field(..., description="更新饰品数量")
    quality_score: float = Field(..., description="数据质量评分")
    sync_duration: float = Field(..., description="同步耗时（秒）")
    error_message: Optional[str] = Field(None, description="错误信息")


# API端点
@router.get("/status", response_model=ServiceStatusResponse)
async def get_service_status():
    """获取基础数据收集器服务状态"""
    try:
        collector = get_base_data_collector()
        scheduler_status = collector.get_scheduler_status()
        
        return ServiceStatusResponse(
            running=collector.running,
            scheduler_running=scheduler_status.get('scheduler_running', False),
            sync_time=collector.sync_time,
            next_run_time=scheduler_status.get('next_run_time'),
            last_update=collector.last_update.isoformat() if collector.last_update else None
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start_service(background_tasks: BackgroundTasks):
    """启动基础数据收集器服务"""
    try:
        if is_base_data_collector_running():
            return {
                "success": True,
                "message": "Base data collector is already running",
                "status": "running"
            }
        
        # 在后台启动服务
        background_tasks.add_task(start_base_data_collector)
        
        return {
            "success": True,
            "message": "Base data collector start initiated",
            "status": "starting",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_service():
    """停止基础数据收集器服务"""
    try:
        if not is_base_data_collector_running():
            return {
                "success": True,
                "message": "Base data collector is not running",
                "status": "stopped"
            }
        
        await stop_base_data_collector()
        
        return {
            "success": True,
            "message": "Base data collector stopped successfully",
            "status": "stopped",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync", response_model=SyncResultResponse)
async def trigger_manual_sync(request: ManualSyncRequest):
    """手动触发基础数据同步"""
    try:
        collector = get_base_data_collector()
        
        # 记录手动同步原因
        if request.reason:
            collector.logger.info(f"手动同步触发，原因: {request.reason}")
        
        # 执行同步
        result = await collector.trigger_manual_sync(force=request.force)
        
        return SyncResultResponse(
            success=result.success,
            total_items=result.total_items,
            new_items=result.new_items,
            updated_items=result.updated_items,
            quality_score=result.data_quality_score,
            sync_duration=result.sync_duration,
            error_message=result.error_message
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_statistics():
    """获取基础数据收集器统计信息"""
    try:
        collector = get_base_data_collector()
        
        # 获取各种统计信息
        api_stats = collector.get_api_statistics()
        db_stats = collector.get_database_statistics()
        quality_stats = collector.get_quality_statistics()
        monitoring_summary = collector.get_monitoring_summary()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "api_statistics": api_stats,
            "database_statistics": db_stats,
            "quality_statistics": quality_stats,
            "monitoring_summary": monitoring_summary
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/execution-history")
async def get_execution_history(
    limit: int = Query(10, ge=1, le=100, description="返回记录数量限制")
):
    """获取定时任务执行历史"""
    try:
        collector = get_base_data_collector()
        history = collector.get_execution_history(limit=limit)
        
        return {
            "success": True,
            "history": history,
            "count": len(history),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/quality-report")
async def get_quality_report():
    """获取最新的数据质量报告"""
    try:
        collector = get_base_data_collector()
        report = collector.get_latest_quality_report()
        
        if report is None:
            return {
                "success": False,
                "message": "No quality report available",
                "report": None
            }
        
        return {
            "success": True,
            "report": report,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config/sync-time")
async def update_sync_time(request: SyncConfigRequest):
    """更新同步时间配置"""
    try:
        collector = get_base_data_collector()
        collector.update_sync_time(request.sync_time)
        
        return {
            "success": True,
            "message": f"Sync time updated to {request.sync_time}",
            "new_sync_time": request.sync_time,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitoring/export")
async def export_monitoring_data(
    format: str = Query("json", regex="^(json|prometheus)$", description="导出格式")
):
    """导出监控数据"""
    try:
        collector = get_base_data_collector()
        data = collector.export_monitoring_data(format_type=format)
        
        if format == "prometheus":
            return PlainTextResponse(content=data, media_type="text/plain")
        else:
            import json
            return JSONResponse(content=json.loads(data))
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/database/cleanup")
async def cleanup_old_items(
    days_threshold: int = Query(30, ge=1, le=365, description="天数阈值")
):
    """清理长时间未同步的饰品数据"""
    try:
        collector = get_base_data_collector()
        cleanup_count = collector.cleanup_old_items(days_threshold=days_threshold)
        
        return {
            "success": True,
            "message": f"Cleaned up {cleanup_count} old items",
            "cleanup_count": cleanup_count,
            "days_threshold": days_threshold,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/statistics/reset")
async def reset_statistics():
    """重置API调用统计"""
    try:
        collector = get_base_data_collector()
        collector.reset_statistics()
        
        return {
            "success": True,
            "message": "API statistics reset successfully",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """基础数据收集器健康检查"""
    try:
        collector = get_base_data_collector()

        # 检查各个组件的健康状态
        api_stats = collector.get_api_statistics()
        db_stats = collector.get_database_statistics()
        scheduler_status = collector.get_scheduler_status()

        # 计算健康评分
        health_score = 100
        issues = []

        # API健康检查
        if api_stats['total_calls'] > 0 and api_stats['success_rate'] < 80:
            health_score -= 30
            issues.append(f"API成功率过低: {api_stats['success_rate']:.1f}%")

        # 数据库健康检查
        if 'error' in db_stats:
            health_score -= 25
            issues.append("数据库连接异常")

        # 调度器健康检查
        if not scheduler_status.get('running', False):
            health_score -= 20
            issues.append("调度器未运行")

        # 数据新鲜度检查
        if collector.last_update:
            from datetime import datetime, timedelta
            if datetime.now() - collector.last_update > timedelta(days=2):
                health_score -= 25
                issues.append("数据更新不及时")

        status = "healthy" if health_score >= 80 else "degraded" if health_score >= 50 else "unhealthy"

        return {
            "status": status,
            "health_score": health_score,
            "issues": issues,
            "components": {
                "api": "healthy" if api_stats['success_rate'] >= 80 or api_stats['total_calls'] == 0 else "unhealthy",
                "database": "healthy" if 'error' not in db_stats else "unhealthy",
                "scheduler": "healthy" if scheduler_status.get('running', False) else "unhealthy",
                "data_freshness": "healthy" if not collector.last_update or datetime.now() - collector.last_update <= timedelta(days=2) else "stale"
            },
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "health_score": 0,
            "issues": [f"健康检查失败: {str(e)}"],
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/metrics/prometheus")
async def get_prometheus_metrics():
    """获取Prometheus格式的监控指标"""
    try:
        collector = get_base_data_collector()
        metrics_data = collector.export_monitoring_data(format_type='prometheus')

        return PlainTextResponse(content=metrics_data, media_type="text/plain")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
