"""
部署配置和容器化测试
验证Docker配置、部署脚本和容器化功能
"""

import pytest
import subprocess
import requests
import time
import json
import yaml
import os
from pathlib import Path
try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent


class TestDockerConfiguration:
    """Docker配置测试"""
    
    def test_dockerfile_exists(self):
        """测试Dockerfile是否存在"""
        dockerfile_path = project_root / "Dockerfile"
        assert dockerfile_path.exists(), "Dockerfile不存在"
    
    def test_dockerfile_syntax(self):
        """测试Dockerfile语法"""
        dockerfile_path = project_root / "Dockerfile"
        
        with open(dockerfile_path, 'r') as f:
            content = f.read()
        
        # 检查必要的指令
        assert "FROM python:" in content, "缺少Python基础镜像"
        assert "WORKDIR /app" in content, "缺少工作目录设置"
        assert "COPY requirements.txt" in content, "缺少依赖文件复制"
        assert "RUN pip install" in content, "缺少依赖安装"
        assert "EXPOSE" in content, "缺少端口暴露"
        assert "ENTRYPOINT" in content or "CMD" in content, "缺少启动命令"
    
    def test_docker_compose_exists(self):
        """测试docker-compose.yml是否存在"""
        compose_path = project_root / "docker-compose.yml"
        assert compose_path.exists(), "docker-compose.yml不存在"
    
    def test_docker_compose_syntax(self):
        """测试docker-compose.yml语法"""
        compose_path = project_root / "docker-compose.yml"
        
        try:
            with open(compose_path, 'r') as f:
                compose_config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            pytest.fail(f"docker-compose.yml语法错误: {e}")
        
        # 检查基本结构
        assert "version" in compose_config, "缺少版本声明"
        assert "services" in compose_config, "缺少服务定义"
        assert "networks" in compose_config, "缺少网络定义"
        assert "volumes" in compose_config, "缺少卷定义"
    
    def test_production_compose_exists(self):
        """测试生产环境compose文件是否存在"""
        prod_compose_path = project_root / "docker-compose.prod.yml"
        assert prod_compose_path.exists(), "docker-compose.prod.yml不存在"
    
    def test_production_compose_syntax(self):
        """测试生产环境compose文件语法"""
        prod_compose_path = project_root / "docker-compose.prod.yml"
        
        try:
            with open(prod_compose_path, 'r') as f:
                prod_config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            pytest.fail(f"docker-compose.prod.yml语法错误: {e}")
        
        # 检查生产环境特定配置
        assert "services" in prod_config, "缺少服务定义"
        
        services = prod_config["services"]
        
        # 检查关键服务
        expected_services = ["postgres", "redis-master", "app1", "app2", "nginx"]
        for service in expected_services:
            assert service in services, f"缺少服务: {service}"


class TestEnvironmentConfiguration:
    """环境配置测试"""
    
    def test_env_example_exists(self):
        """测试环境变量示例文件是否存在"""
        env_example_path = project_root / ".env.example"
        assert env_example_path.exists(), ".env.example不存在"
    
    def test_env_example_content(self):
        """测试环境变量示例文件内容"""
        env_example_path = project_root / ".env.example"
        
        with open(env_example_path, 'r') as f:
            content = f.read()
        
        # 检查必要的环境变量
        required_vars = [
            "ARES_ENV",
            "DATABASE_URL",
            "REDIS_URL",
            "API_STEAMDT_API_KEY",
            "LOG_LEVEL"
        ]
        
        for var in required_vars:
            assert var in content, f"缺少环境变量: {var}"
    
    def test_nginx_config_exists(self):
        """测试Nginx配置文件是否存在"""
        nginx_config_path = project_root / "config" / "nginx" / "nginx.conf"
        assert nginx_config_path.exists(), "Nginx配置文件不存在"
    
    def test_nginx_config_syntax(self):
        """测试Nginx配置文件语法"""
        nginx_config_path = project_root / "config" / "nginx" / "nginx.conf"
        
        with open(nginx_config_path, 'r') as f:
            content = f.read()
        
        # 检查基本配置
        assert "upstream ares_backend" in content, "缺少后端上游配置"
        assert "upstream ares_streamlit" in content, "缺少Streamlit上游配置"
        assert "server {" in content, "缺少服务器配置块"
        assert "location /" in content, "缺少根路径配置"
        assert "location /api/" in content, "缺少API路径配置"


class TestDeploymentScripts:
    """部署脚本测试"""
    
    def test_deploy_script_exists(self):
        """测试部署脚本是否存在"""
        deploy_script_path = project_root / "scripts" / "deploy.sh"
        assert deploy_script_path.exists(), "部署脚本不存在"
    
    def test_deploy_script_executable(self):
        """测试部署脚本是否可执行"""
        deploy_script_path = project_root / "scripts" / "deploy.sh"
        assert os.access(deploy_script_path, os.X_OK), "部署脚本不可执行"
    
    def test_docker_entrypoint_exists(self):
        """测试Docker入口脚本是否存在"""
        entrypoint_path = project_root / "scripts" / "docker-entrypoint.sh"
        assert entrypoint_path.exists(), "Docker入口脚本不存在"
    
    def test_docker_entrypoint_syntax(self):
        """测试Docker入口脚本语法"""
        entrypoint_path = project_root / "scripts" / "docker-entrypoint.sh"
        
        with open(entrypoint_path, 'r') as f:
            content = f.read()
        
        # 检查脚本结构
        assert "#!/bin/bash" in content, "缺少shebang"
        assert "set -e" in content, "缺少错误处理"
        assert "main()" in content, "缺少主函数"
        assert "case" in content, "缺少命令分支处理"


class TestContainerFunctionality:
    """容器功能测试"""
    
    @pytest.fixture(scope="class")
    def docker_client(self):
        """Docker客户端fixture"""
        if not DOCKER_AVAILABLE:
            pytest.skip("Docker模块不可用")
        try:
            client = docker.from_env()
            return client
        except Exception as e:
            pytest.skip(f"Docker不可用: {e}")
    
    def test_docker_available(self, docker_client):
        """测试Docker是否可用"""
        assert docker_client.ping(), "Docker服务不可用"
    
    def test_build_image(self, docker_client):
        """测试构建Docker镜像"""
        try:
            # 构建镜像
            image, logs = docker_client.images.build(
                path=str(project_root),
                tag="ares-test:latest",
                rm=True,
                forcerm=True
            )
            
            assert image is not None, "镜像构建失败"
            
            # 清理测试镜像
            docker_client.images.remove(image.id, force=True)
            
        except docker.errors.BuildError as e:
            pytest.fail(f"镜像构建失败: {e}")
    
    def test_requirements_file(self):
        """测试requirements.txt文件"""
        requirements_path = project_root / "requirements.txt"
        assert requirements_path.exists(), "requirements.txt不存在"
        
        with open(requirements_path, 'r') as f:
            content = f.read()
        
        # 检查关键依赖
        required_packages = [
            "streamlit",
            "fastapi",
            "uvicorn",
            "pandas",
            "redis",
            "sqlalchemy"
        ]
        
        for package in required_packages:
            assert package in content.lower(), f"缺少依赖包: {package}"


class TestHealthChecks:
    """健康检查测试"""
    
    def test_health_check_endpoint_defined(self):
        """测试健康检查端点是否定义"""
        # 检查FastAPI应用中是否定义了健康检查端点
        # 这里可以通过静态分析或运行时检查来验证
        pass
    
    def test_docker_healthcheck_defined(self):
        """测试Docker健康检查是否定义"""
        dockerfile_path = project_root / "Dockerfile"
        
        with open(dockerfile_path, 'r') as f:
            content = f.read()
        
        assert "HEALTHCHECK" in content, "Dockerfile中缺少健康检查定义"


class TestSecurityConfiguration:
    """安全配置测试"""
    
    def test_non_root_user(self):
        """测试是否使用非root用户"""
        dockerfile_path = project_root / "Dockerfile"
        
        with open(dockerfile_path, 'r') as f:
            content = f.read()
        
        assert "USER " in content, "Dockerfile中缺少非root用户设置"
        assert "USER ares" in content, "未设置正确的用户名"
    
    def test_security_headers_in_nginx(self):
        """测试Nginx安全头配置"""
        nginx_config_path = project_root / "config" / "nginx" / "nginx.conf"
        
        with open(nginx_config_path, 'r') as f:
            content = f.read()
        
        # 检查安全头
        security_headers = [
            "X-Frame-Options",
            "X-Content-Type-Options",
            "X-XSS-Protection",
            "Strict-Transport-Security"
        ]
        
        for header in security_headers:
            assert header in content, f"缺少安全头: {header}"
    
    def test_ssl_configuration(self):
        """测试SSL配置"""
        nginx_config_path = project_root / "config" / "nginx" / "nginx.conf"
        
        with open(nginx_config_path, 'r') as f:
            content = f.read()
        
        assert "ssl_protocols" in content, "缺少SSL协议配置"
        assert "ssl_ciphers" in content, "缺少SSL密码套件配置"


class TestResourceLimits:
    """资源限制测试"""
    
    def test_memory_limits_defined(self):
        """测试内存限制是否定义"""
        prod_compose_path = project_root / "docker-compose.prod.yml"
        
        with open(prod_compose_path, 'r') as f:
            prod_config = yaml.safe_load(f)
        
        services = prod_config.get("services", {})
        
        # 检查关键服务是否有资源限制
        critical_services = ["app1", "app2", "postgres", "redis-master"]
        
        for service_name in critical_services:
            if service_name in services:
                service = services[service_name]
                deploy = service.get("deploy", {})
                resources = deploy.get("resources", {})
                limits = resources.get("limits", {})
                
                assert "memory" in limits, f"服务 {service_name} 缺少内存限制"


class TestDocumentation:
    """文档测试"""
    
    def test_deployment_docs_exist(self):
        """测试部署文档是否存在"""
        docs_path = project_root / "docs" / "DEPLOYMENT.md"
        assert docs_path.exists(), "部署文档不存在"
    
    def test_deployment_docs_content(self):
        """测试部署文档内容"""
        docs_path = project_root / "docs" / "DEPLOYMENT.md"
        
        with open(docs_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文档结构
        required_sections = [
            "系统要求",
            "快速开始",
            "环境配置",
            "部署方式",
            "监控和日志",
            "故障排除"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少文档章节: {section}"


def test_integration_deployment():
    """集成部署测试"""
    # 这个测试需要在CI/CD环境中运行
    # 可以测试完整的部署流程
    pass


if __name__ == "__main__":
    # 运行基本测试
    print("运行部署配置和容器化基本测试...")
    
    # 测试文件存在性
    print("测试配置文件...")
    dockerfile_path = project_root / "Dockerfile"
    assert dockerfile_path.exists()
    print("✓ Dockerfile存在")
    
    compose_path = project_root / "docker-compose.yml"
    assert compose_path.exists()
    print("✓ docker-compose.yml存在")
    
    prod_compose_path = project_root / "docker-compose.prod.yml"
    assert prod_compose_path.exists()
    print("✓ docker-compose.prod.yml存在")
    
    # 测试脚本文件
    print("测试部署脚本...")
    deploy_script_path = project_root / "scripts" / "deploy.sh"
    assert deploy_script_path.exists()
    print("✓ 部署脚本存在")
    
    entrypoint_path = project_root / "scripts" / "docker-entrypoint.sh"
    assert entrypoint_path.exists()
    print("✓ Docker入口脚本存在")
    
    # 测试配置文件
    print("测试配置文件...")
    env_example_path = project_root / ".env.example"
    assert env_example_path.exists()
    print("✓ 环境变量示例文件存在")
    
    nginx_config_path = project_root / "config" / "nginx" / "nginx.conf"
    assert nginx_config_path.exists()
    print("✓ Nginx配置文件存在")
    
    # 测试文档
    print("测试文档...")
    docs_path = project_root / "docs" / "DEPLOYMENT.md"
    assert docs_path.exists()
    print("✓ 部署文档存在")
    
    print("所有部署配置和容器化测试通过！")
