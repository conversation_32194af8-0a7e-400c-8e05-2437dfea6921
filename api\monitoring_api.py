"""
Ares监控API接口
提供系统监控、健康检查和告警管理的RESTful API
"""

from fastapi import APIRouter, HTTPException, Query, Body, BackgroundTasks
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

from monitoring.health_check import get_health_checker, HealthStatus
from monitoring.system_monitor import get_system_monitor, AlertRule, Alert

# 创建路由器
router = APIRouter(prefix="/api/monitoring", tags=["监控"])


# 请求模型
class AlertRuleRequest(BaseModel):
    """告警规则请求模型"""
    name: str = Field(..., description="规则名称")
    metric: str = Field(..., description="监控指标")
    condition: str = Field(..., description="条件操作符")
    threshold: float = Field(..., description="阈值")
    duration: int = Field(300, description="持续时间(秒)")
    severity: str = Field("medium", description="严重程度")
    enabled: bool = Field(True, description="是否启用")


class MetricQueryRequest(BaseModel):
    """指标查询请求模型"""
    metric_name: str = Field(..., description="指标名称")
    duration_minutes: int = Field(60, description="查询时长(分钟)")


# 响应模型
class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    success: bool = Field(..., description="是否成功")
    overall_status: str = Field(..., description="整体状态")
    checks: Dict[str, Any] = Field(..., description="检查结果")
    timestamp: str = Field(..., description="检查时间")


class SystemOverviewResponse(BaseModel):
    """系统概览响应模型"""
    success: bool = Field(..., description="是否成功")
    data: Dict[str, Any] = Field(..., description="系统概览数据")


class AlertsResponse(BaseModel):
    """告警响应模型"""
    success: bool = Field(..., description="是否成功")
    alerts: List[Dict[str, Any]] = Field(..., description="告警列表")
    total_count: int = Field(..., description="总数量")


class MetricsResponse(BaseModel):
    """指标响应模型"""
    success: bool = Field(..., description="是否成功")
    metric_name: str = Field(..., description="指标名称")
    data_points: List[Dict[str, Any]] = Field(..., description="数据点")
    latest_value: Optional[float] = Field(None, description="最新值")


# API端点
@router.get("/health", response_model=HealthCheckResponse)
async def get_health_status(force: bool = Query(False, description="强制重新检查")):
    """获取系统健康状态"""
    try:
        health_checker = get_health_checker()
        
        # 运行健康检查
        results = await health_checker.run_health_checks(force=force)
        
        # 获取健康摘要
        summary = health_checker.get_health_summary(results)
        
        return HealthCheckResponse(
            success=True,
            overall_status=summary["overall_status"],
            checks=summary["checks"],
            timestamp=summary["timestamp"]
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health/{check_name}")
async def get_specific_health_check(check_name: str):
    """获取特定健康检查结果"""
    try:
        health_checker = get_health_checker()
        
        # 检查是否存在该检查
        if check_name not in health_checker.checks:
            raise HTTPException(status_code=404, detail=f"Health check '{check_name}' not found")
        
        # 运行特定检查
        check_func = health_checker.checks[check_name]
        result = await check_func()
        
        return {
            "success": True,
            "check_name": check_name,
            "result": result.to_dict()
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/overview", response_model=SystemOverviewResponse)
async def get_system_overview():
    """获取系统概览"""
    try:
        system_monitor = get_system_monitor()
        overview = system_monitor.get_system_overview()
        
        return SystemOverviewResponse(
            success=True,
            data=overview
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/{metric_name}", response_model=MetricsResponse)
async def get_metric_history(
    metric_name: str,
    duration_minutes: int = Query(60, description="查询时长(分钟)")
):
    """获取指标历史数据"""
    try:
        system_monitor = get_system_monitor()
        
        # 获取历史数据
        history = system_monitor.get_metric_history(metric_name, duration_minutes)
        
        # 获取最新值
        latest_metric = system_monitor.get_latest_metric(metric_name)
        latest_value = latest_metric.value if latest_metric else None
        
        # 转换为字典格式
        data_points = [point.to_dict() for point in history]
        
        return MetricsResponse(
            success=True,
            metric_name=metric_name,
            data_points=data_points,
            latest_value=latest_value
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/metrics")
async def add_custom_metric(
    metric_name: str = Body(..., description="指标名称"),
    value: float = Body(..., description="指标值"),
    tags: Optional[Dict[str, str]] = Body(None, description="标签")
):
    """添加自定义指标"""
    try:
        system_monitor = get_system_monitor()
        
        # 添加指标
        system_monitor.add_metric(metric_name, value, tags=tags)
        
        return {
            "success": True,
            "message": f"Metric '{metric_name}' added successfully",
            "value": value,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts", response_model=AlertsResponse)
async def get_alerts(
    active_only: bool = Query(False, description="仅显示活跃告警"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取告警列表"""
    try:
        system_monitor = get_system_monitor()
        
        if active_only:
            alerts = system_monitor.get_active_alerts()
        else:
            alerts = system_monitor.get_alert_history(limit)
        
        alert_dicts = [alert.to_dict() for alert in alerts]
        
        return AlertsResponse(
            success=True,
            alerts=alert_dicts,
            total_count=len(alert_dicts)
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/alerts/rules")
async def create_alert_rule(request: AlertRuleRequest):
    """创建告警规则"""
    try:
        system_monitor = get_system_monitor()
        
        # 验证条件操作符
        valid_conditions = ['>', '<', '>=', '<=', '==']
        if request.condition not in valid_conditions:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid condition. Must be one of: {valid_conditions}"
            )
        
        # 验证严重程度
        valid_severities = ['low', 'medium', 'high', 'critical']
        if request.severity not in valid_severities:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid severity. Must be one of: {valid_severities}"
            )
        
        # 创建告警规则
        rule = AlertRule(
            name=request.name,
            metric=request.metric,
            condition=request.condition,
            threshold=request.threshold,
            duration=request.duration,
            severity=request.severity,
            enabled=request.enabled
        )
        
        system_monitor.add_alert_rule(rule)
        
        return {
            "success": True,
            "message": f"Alert rule '{request.name}' created successfully",
            "rule_name": request.name
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/alerts/rules/{rule_name}")
async def delete_alert_rule(rule_name: str):
    """删除告警规则"""
    try:
        system_monitor = get_system_monitor()
        
        success = system_monitor.remove_alert_rule(rule_name)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Alert rule '{rule_name}' not found")
        
        return {
            "success": True,
            "message": f"Alert rule '{rule_name}' deleted successfully"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts/rules")
async def get_alert_rules():
    """获取告警规则列表"""
    try:
        system_monitor = get_system_monitor()
        
        rules = []
        for rule in system_monitor.alert_rules:
            rules.append({
                "name": rule.name,
                "metric": rule.metric,
                "condition": rule.condition,
                "threshold": rule.threshold,
                "duration": rule.duration,
                "severity": rule.severity,
                "enabled": rule.enabled
            })
        
        return {
            "success": True,
            "rules": rules,
            "total_count": len(rules)
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api-request")
async def record_api_request(
    endpoint: str = Body(..., description="API端点"),
    success: bool = Body(..., description="是否成功"),
    response_time: float = Body(..., description="响应时间"),
    rate_limited: bool = Body(False, description="是否被限流")
):
    """记录API请求统计"""
    try:
        system_monitor = get_system_monitor()
        
        # 记录API请求
        system_monitor.record_api_request(endpoint, success, response_time, rate_limited)
        
        return {
            "success": True,
            "message": "API request recorded successfully"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api-stats")
async def get_api_statistics(endpoint: Optional[str] = Query(None, description="特定端点")):
    """获取API统计信息"""
    try:
        system_monitor = get_system_monitor()
        
        stats = system_monitor.get_api_stats(endpoint)
        
        return {
            "success": True,
            "endpoint": endpoint,
            "stats": stats
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/start")
async def start_monitoring(
    background_tasks: BackgroundTasks,
    interval: int = Body(60, description="监控间隔(秒)")
):
    """启动系统监控"""
    try:
        system_monitor = get_system_monitor()
        
        # 启动监控
        system_monitor.start_monitoring(interval)
        
        return {
            "success": True,
            "message": f"System monitoring started with {interval}s interval",
            "monitoring_active": system_monitor.monitoring_active
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/stop")
async def stop_monitoring():
    """停止系统监控"""
    try:
        system_monitor = get_system_monitor()
        
        # 停止监控
        system_monitor.stop_monitoring()
        
        return {
            "success": True,
            "message": "System monitoring stopped",
            "monitoring_active": system_monitor.monitoring_active
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitoring/status")
async def get_monitoring_status():
    """获取监控状态"""
    try:
        system_monitor = get_system_monitor()
        
        return {
            "success": True,
            "monitoring_active": system_monitor.monitoring_active,
            "total_metrics": len(system_monitor.metrics),
            "active_alerts": len(system_monitor.get_active_alerts()),
            "alert_rules": len(system_monitor.alert_rules)
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/available-metrics")
async def get_available_metrics():
    """获取可用的监控指标"""
    try:
        system_monitor = get_system_monitor()
        
        # 获取所有已记录的指标
        available_metrics = []
        for metric_name, metric_data in system_monitor.metrics.items():
            latest_point = metric_data[-1] if metric_data else None
            
            available_metrics.append({
                "name": metric_name,
                "data_points": len(metric_data),
                "latest_value": latest_point.value if latest_point else None,
                "latest_timestamp": latest_point.timestamp.isoformat() if latest_point else None
            })
        
        return {
            "success": True,
            "metrics": available_metrics,
            "total_count": len(available_metrics)
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
