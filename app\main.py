"""
Ares投资系统主应用入口
支持FastAPI和Streamlit两种模式
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 检查是否为FastAPI模式
if os.getenv('ARES_MODE') == 'api' or 'uvicorn' in sys.modules:
    # FastAPI模式
    from app.api_main import app
else:
    # Streamlit模式
    import streamlit as st
    from app.components.layout import setup_page_config, render_main_layout
    from app.utils.state_manager import StateManager
    from app.utils.navigation import NavigationManager
    from core.config import get_config_manager


def main():
    """主应用入口函数 - Streamlit模式"""
    try:
        # 设置页面配置
        setup_page_config()

        # 初始化状态管理器
        state_manager = StateManager()

        # 初始化导航管理器
        nav_manager = NavigationManager()

        # 加载配置
        try:
            config_manager = get_config_manager()
            app_config = {
                'app_name': config_manager.get('app_name', 'Ares Investment System'),
                'app_version': config_manager.get('app_version', '1.0.0'),
                'debug_mode': config_manager.get('debug_mode', False)
            }
        except Exception as e:
            st.warning(f"配置加载失败，使用默认配置: {str(e)}")
            app_config = {
                'app_name': 'Ares Investment System',
                'app_version': '1.0.0',
                'debug_mode': False
            }

        # 渲染主布局
        render_main_layout(state_manager, nav_manager, app_config)

    except Exception as e:
        st.error(f"应用启动失败: {str(e)}")
        if st.button("重新加载"):
            st.rerun()


if __name__ == "__main__":
    # 检查运行模式
    if os.getenv('ARES_MODE') == 'api':
        # FastAPI模式
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8000)
    else:
        # Streamlit模式
        main()
