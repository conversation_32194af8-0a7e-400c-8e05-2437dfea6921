"""
SteamDT API集成测试
验证与steamdt.com API的真实集成
"""

import pytest
import asyncio
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.steamdt_api import (
    SteamdtAPIManager, 
    PlatformPrice, 
    ItemPriceData,
    get_steamdt_api_manager,
    close_steamdt_api_manager
)
from core.api_manager import APIManager, APIResponse, APICallResult
from core.config import get_config_manager


# 测试数据
TEST_MARKET_HASH_NAME = "AK-47 | Redline (Field-Tested)"
TEST_INSPECT_LINK = "steam://rungame/730/76561202255233023/+csgo_econ_action_preview%20S76561198123456789A12345678D1234567890123456"


class MockAPIManager:
    """模拟API管理器，用于单元测试"""
    
    def __init__(self, mock_responses: Dict[str, Any] = None):
        self.mock_responses = mock_responses or {}
        self.calls = []
    
    async def call_api(self, endpoint: str, method: str = 'GET', **kwargs) -> APIResponse:
        """记录API调用并返回模拟响应"""
        self.calls.append({
            'endpoint': endpoint,
            'method': method,
            'kwargs': kwargs
        })
        
        # 查找匹配的模拟响应
        response_key = f"{method}:{endpoint}"
        if response_key in self.mock_responses:
            mock_data = self.mock_responses[response_key]
            return APIResponse(
                status=APICallResult.SUCCESS,
                data=mock_data,
                error_message=None
            )
        
        # 默认返回空成功响应
        return APIResponse(
            status=APICallResult.SUCCESS,
            data={'data': []},
            error_message=None
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """返回模拟指标"""
        return {
            'total_calls': len(self.calls),
            'endpoints': {call['endpoint']: 1 for call in self.calls}
        }
    
    async def start_session(self):
        """模拟会话启动"""
        pass
    
    async def close_session(self):
        """模拟会话关闭"""
        pass


class TestPlatformPrice:
    """平台价格数据模型测试"""
    
    def test_from_dict(self):
        """测试从字典创建PlatformPrice实例"""
        data = {
            'platform': 'steam',
            'platformItemId': '123456',
            'sellPrice': '10.50',
            'sellCount': '100',
            'biddingPrice': '9.75',
            'biddingCount': '50',
            'updateTime': '1625097600'
        }
        
        price = PlatformPrice.from_dict(data)
        
        assert price.platform == 'steam'
        assert price.platform_item_id == '123456'
        assert price.sell_price == 10.50
        assert price.sell_count == 100
        assert price.bidding_price == 9.75
        assert price.bidding_count == 50
        assert price.update_time == 1625097600


class TestItemPriceData:
    """饰品价格数据模型测试"""
    
    def test_get_steam_price(self):
        """测试获取Steam平台价格"""
        steam_price = PlatformPrice(
            platform='steam',
            platform_item_id='123',
            sell_price=10.0,
            sell_count=100,
            bidding_price=9.0,
            bidding_count=50,
            update_time=1625097600
        )
        
        buff_price = PlatformPrice(
            platform='buff',
            platform_item_id='456',
            sell_price=9.5,
            sell_count=200,
            bidding_price=9.2,
            bidding_count=80,
            update_time=1625097600
        )
        
        price_data = ItemPriceData(
            market_hash_name="AK-47 | Redline",
            platform_prices=[steam_price, buff_price],
            query_time=datetime.utcnow()
        )
        
        result = price_data.get_steam_price()
        assert result is not None
        assert result.platform == 'steam'
        assert result.sell_price == 10.0
    
    def test_get_best_sell_price(self):
        """测试获取最低售价"""
        steam_price = PlatformPrice(
            platform='steam',
            platform_item_id='123',
            sell_price=10.0,
            sell_count=100,
            bidding_price=9.0,
            bidding_count=50,
            update_time=1625097600
        )
        
        buff_price = PlatformPrice(
            platform='buff',
            platform_item_id='456',
            sell_price=9.5,
            sell_count=200,
            bidding_price=9.2,
            bidding_count=80,
            update_time=1625097600
        )
        
        price_data = ItemPriceData(
            market_hash_name="AK-47 | Redline",
            platform_prices=[steam_price, buff_price],
            query_time=datetime.utcnow()
        )
        
        result = price_data.get_best_sell_price()
        assert result is not None
        assert result.platform == 'buff'
        assert result.sell_price == 9.5


class TestSteamdtAPIManager:
    """SteamDT API管理器测试"""
    
    @pytest.fixture
    def mock_api_manager(self):
        """创建模拟API管理器"""
        # 模拟价格响应
        price_response = {
            'code': 0,
            'message': 'success',
            'data': [
                {
                    'platform': 'steam',
                    'platformItemId': '123456',
                    'sellPrice': '10.50',
                    'sellCount': '100',
                    'biddingPrice': '9.75',
                    'biddingCount': '50',
                    'updateTime': '1625097600'
                },
                {
                    'platform': 'buff',
                    'platformItemId': '789012',
                    'sellPrice': '9.80',
                    'sellCount': '200',
                    'biddingPrice': '9.50',
                    'biddingCount': '80',
                    'updateTime': '1625097600'
                }
            ]
        }
        
        # 模拟批量价格响应
        batch_price_response = {
            'code': 0,
            'message': 'success',
            'data': [
                {
                    'marketHashName': 'AK-47 | Redline (Field-Tested)',
                    'prices': [
                        {
                            'platform': 'steam',
                            'platformItemId': '123456',
                            'sellPrice': '10.50',
                            'sellCount': '100',
                            'biddingPrice': '9.75',
                            'biddingCount': '50',
                            'updateTime': '1625097600'
                        }
                    ]
                },
                {
                    'marketHashName': 'AWP | Asiimov (Field-Tested)',
                    'prices': [
                        {
                            'platform': 'steam',
                            'platformItemId': '654321',
                            'sellPrice': '45.00',
                            'sellCount': '50',
                            'biddingPrice': '43.50',
                            'biddingCount': '20',
                            'updateTime': '1625097600'
                        }
                    ]
                }
            ]
        }
        
        # 模拟磨损度响应
        wear_response = {
            'code': 0,
            'message': 'success',
            'data': {
                'itemid': '12345678',
                'defindex': '7',
                'paintindex': '282',
                'rarity': '4',
                'quality': '4',
                'paintwear': '0.25',
                'paintseed': '661'
            }
        }
        
        mock_responses = {
            'GET:/open/cs2/v1/price/single': price_response,
            'POST:/open/cs2/v1/price/batch': batch_price_response,
            'POST:/open/cs2/v1/wear': wear_response
        }
        
        return MockAPIManager(mock_responses)
    
    @pytest.fixture
    def api_manager(self, mock_api_manager):
        """创建SteamDT API管理器"""
        return SteamdtAPIManager(mock_api_manager)
    
    @pytest.mark.asyncio
    async def test_get_item_price(self, api_manager):
        """测试获取单个饰品价格"""
        result = await api_manager.get_item_price(TEST_MARKET_HASH_NAME)
        
        assert result is not None
        assert result.market_hash_name == TEST_MARKET_HASH_NAME
        assert len(result.platform_prices) == 2
        
        # 验证Steam价格
        steam_price = result.get_steam_price()
        assert steam_price is not None
        assert steam_price.platform == 'steam'
        assert steam_price.sell_price == 10.50
        
        # 验证最佳售价
        best_sell = result.get_best_sell_price()
        assert best_sell is not None
        assert best_sell.platform == 'buff'
        assert best_sell.sell_price == 9.80
    
    @pytest.mark.asyncio
    async def test_get_batch_prices(self, api_manager):
        """测试批量获取饰品价格"""
        market_hash_names = [
            "AK-47 | Redline (Field-Tested)",
            "AWP | Asiimov (Field-Tested)"
        ]
        
        result = await api_manager.get_batch_prices(market_hash_names)
        
        assert len(result) == 2
        assert "AK-47 | Redline (Field-Tested)" in result
        assert "AWP | Asiimov (Field-Tested)" in result
        
        # 验证第一个饰品的价格
        ak_price = result["AK-47 | Redline (Field-Tested)"]
        assert ak_price.market_hash_name == "AK-47 | Redline (Field-Tested)"
        assert len(ak_price.platform_prices) == 1
        
        # 验证第二个饰品的价格
        awp_price = result["AWP | Asiimov (Field-Tested)"]
        assert awp_price.market_hash_name == "AWP | Asiimov (Field-Tested)"
        assert len(awp_price.platform_prices) == 1
        assert awp_price.platform_prices[0].sell_price == 45.00
    
    @pytest.mark.asyncio
    async def test_get_item_wear_data(self, api_manager):
        """测试获取饰品磨损度数据"""
        result = await api_manager.get_item_wear_data(TEST_INSPECT_LINK)
        
        assert result is not None
        assert 'data' in result
        assert result['data']['paintwear'] == '0.25'
        assert result['data']['paintseed'] == '661'


@pytest.mark.skipif(
    not os.environ.get('API_STEAMDT_API_KEY'),
    reason="需要设置API_STEAMDT_API_KEY环境变量进行集成测试"
)
class TestSteamdtAPIIntegration:
    """SteamDT API集成测试（需要真实API密钥）"""
    
    @pytest.fixture(scope="class")
    async def api_manager(self):
        """创建真实的SteamDT API管理器"""
        manager = await get_steamdt_api_manager()
        yield manager
        await close_steamdt_api_manager()
    
    @pytest.mark.asyncio
    async def test_get_item_price_integration(self, api_manager):
        """测试获取单个饰品价格（集成测试）"""
        result = await api_manager.get_item_price(TEST_MARKET_HASH_NAME)
        
        assert result is not None
        assert result.market_hash_name == TEST_MARKET_HASH_NAME
        assert len(result.platform_prices) > 0
        
        # 验证至少有一个平台的价格
        assert any(p.sell_price > 0 for p in result.platform_prices)
    
    @pytest.mark.asyncio
    async def test_get_base_item_info_integration(self, api_manager):
        """测试获取基础饰品信息（集成测试）"""
        result = await api_manager.get_base_item_info()
        
        assert result is not None
        assert 'data' in result
        # 验证返回了饰品数据
        assert len(result['data']) > 0


if __name__ == "__main__":
    # 运行单元测试
    pytest.main(["-xvs", __file__])
