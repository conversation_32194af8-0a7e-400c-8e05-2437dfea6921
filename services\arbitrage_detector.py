"""
套利机会识别器
基于跨平台价差识别真实的套利机会，考虑风险和流动性
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

from services.price_comparator import PlatformPriceComparator, PriceComparison, Platform
from services.item_discovery import get_item_discovery_service, ItemValueTier
from core.config import get_config_manager


class ArbitrageRiskLevel(Enum):
    """套利风险等级"""
    LOW = "low"           # 低风险
    MEDIUM = "medium"     # 中等风险
    HIGH = "high"         # 高风险
    VERY_HIGH = "very_high"  # 极高风险


@dataclass
class ArbitrageOpportunity:
    """套利机会"""
    item_name: str
    market_hash_name: str
    buy_platform: Platform
    sell_platform: Platform
    buy_price: float
    sell_price: float
    gross_profit: float
    net_profit: float
    profit_margin_percent: float
    risk_level: ArbitrageRiskLevel
    liquidity_score: float
    time_to_profit_days: int
    confidence_score: float
    reasons: List[str]
    detected_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_name': self.item_name,
            'market_hash_name': self.market_hash_name,
            'buy_platform': self.buy_platform.value,
            'sell_platform': self.sell_platform.value,
            'buy_price': self.buy_price,
            'sell_price': self.sell_price,
            'gross_profit': self.gross_profit,
            'net_profit': self.net_profit,
            'profit_margin_percent': self.profit_margin_percent,
            'risk_level': self.risk_level.value,
            'liquidity_score': self.liquidity_score,
            'time_to_profit_days': self.time_to_profit_days,
            'confidence_score': self.confidence_score,
            'reasons': self.reasons,
            'detected_at': self.detected_at.isoformat()
        }


@dataclass
class ArbitrageConfig:
    """套利配置"""
    min_profit_margin: float = 5.0      # 最小利润率（%）
    min_profit_amount: float = 5.0      # 最小利润金额（元）
    max_investment: float = 1000.0      # 最大投资金额（元）
    min_liquidity_score: float = 30.0   # 最小流动性评分
    max_risk_level: ArbitrageRiskLevel = ArbitrageRiskLevel.HIGH
    min_confidence_score: float = 60.0  # 最小置信度评分


class ArbitrageDetector:
    """套利机会识别器"""
    
    def __init__(self):
        """初始化套利识别器"""
        self.logger = logging.getLogger(__name__)
        self.config = get_config_manager()
        self.price_comparator = PlatformPriceComparator()
        
        # 套利配置
        self.arbitrage_config = ArbitrageConfig(
            min_profit_margin=self.config.get('arbitrage.min_profit_margin', 5.0),
            min_profit_amount=self.config.get('arbitrage.min_profit_amount', 5.0),
            max_investment=self.config.get('arbitrage.max_investment', 1000.0),
            min_liquidity_score=self.config.get('arbitrage.min_liquidity_score', 30.0),
            min_confidence_score=self.config.get('arbitrage.min_confidence_score', 60.0)
        )
        
        # 检测到的套利机会
        self.detected_opportunities: List[ArbitrageOpportunity] = []
        self.state_file = Path("data/arbitrage_opportunities.json")
        
        # 加载历史机会
        self._load_opportunities()
        
        self.logger.info("Arbitrage Detector initialized")
    
    def _load_opportunities(self):
        """加载历史套利机会"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 只加载最近24小时的机会
                cutoff_time = datetime.now() - timedelta(hours=24)
                
                for opp_data in data.get('opportunities', []):
                    detected_at = datetime.fromisoformat(opp_data['detected_at'])
                    if detected_at > cutoff_time:
                        opportunity = ArbitrageOpportunity(
                            item_name=opp_data['item_name'],
                            market_hash_name=opp_data['market_hash_name'],
                            buy_platform=Platform(opp_data['buy_platform']),
                            sell_platform=Platform(opp_data['sell_platform']),
                            buy_price=opp_data['buy_price'],
                            sell_price=opp_data['sell_price'],
                            gross_profit=opp_data['gross_profit'],
                            net_profit=opp_data['net_profit'],
                            profit_margin_percent=opp_data['profit_margin_percent'],
                            risk_level=ArbitrageRiskLevel(opp_data['risk_level']),
                            liquidity_score=opp_data['liquidity_score'],
                            time_to_profit_days=opp_data['time_to_profit_days'],
                            confidence_score=opp_data['confidence_score'],
                            reasons=opp_data['reasons'],
                            detected_at=detected_at
                        )
                        self.detected_opportunities.append(opportunity)
                
                self.logger.info(f"Loaded {len(self.detected_opportunities)} recent arbitrage opportunities")
                
        except Exception as e:
            self.logger.error(f"Error loading arbitrage opportunities: {e}")
    
    def _save_opportunities(self):
        """保存套利机会"""
        try:
            # 确保目录存在
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'opportunities': [opp.to_dict() for opp in self.detected_opportunities],
                'last_save_time': datetime.now().isoformat(),
                'total_opportunities': len(self.detected_opportunities)
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("Arbitrage opportunities saved")
            
        except Exception as e:
            self.logger.error(f"Error saving arbitrage opportunities: {e}")
    
    async def detect_arbitrage_opportunities(self, market_hash_names: List[str]) -> List[ArbitrageOpportunity]:
        """
        检测套利机会
        
        Args:
            market_hash_names: 要检测的饰品列表
            
        Returns:
            List[ArbitrageOpportunity]: 检测到的套利机会
        """
        self.logger.info(f"Detecting arbitrage opportunities for {len(market_hash_names)} items...")
        
        opportunities = []
        
        for market_hash_name in market_hash_names:
            try:
                # 获取价格比较结果
                comparison = await self.price_comparator.compare_item_prices(market_hash_name)
                
                if not comparison or comparison.max_profit_margin < self.arbitrage_config.min_profit_margin:
                    continue
                
                # 分析套利机会
                opportunity = await self._analyze_arbitrage_opportunity(comparison)
                
                if opportunity and self._is_valid_opportunity(opportunity):
                    opportunities.append(opportunity)
                    self.detected_opportunities.append(opportunity)
                
                # 避免请求过快
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Error detecting arbitrage for {market_hash_name}: {e}")
                continue
        
        # 保存检测结果
        self._save_opportunities()
        
        self.logger.info(f"Detected {len(opportunities)} new arbitrage opportunities")
        return opportunities
    
    async def _analyze_arbitrage_opportunity(self, comparison: PriceComparison) -> Optional[ArbitrageOpportunity]:
        """分析套利机会"""
        try:
            if not comparison.best_buy_platform or not comparison.best_sell_platform:
                return None
            
            buy_platform = comparison.best_buy_platform
            sell_platform = comparison.best_sell_platform
            
            buy_price_data = comparison.platform_prices[buy_platform]
            sell_price_data = comparison.platform_prices[sell_platform]
            
            buy_price = buy_price_data.sell_price
            sell_price = sell_price_data.sell_price
            
            # 计算利润
            gross_profit = sell_price - buy_price
            net_profit = self._calculate_net_profit(buy_platform, sell_platform, buy_price, sell_price)
            profit_margin = (net_profit / buy_price) * 100 if buy_price > 0 else 0
            
            # 评估风险和流动性
            risk_level = self._assess_risk_level(comparison, buy_price_data, sell_price_data)
            liquidity_score = self._calculate_liquidity_score(buy_price_data, sell_price_data)
            time_to_profit = self._estimate_time_to_profit(buy_platform, sell_platform)
            confidence_score = self._calculate_confidence_score(comparison, liquidity_score, risk_level)
            
            # 生成分析原因
            reasons = self._generate_analysis_reasons(
                comparison, profit_margin, liquidity_score, risk_level
            )
            
            return ArbitrageOpportunity(
                item_name=comparison.item_name,
                market_hash_name=comparison.market_hash_name,
                buy_platform=buy_platform,
                sell_platform=sell_platform,
                buy_price=buy_price,
                sell_price=sell_price,
                gross_profit=gross_profit,
                net_profit=net_profit,
                profit_margin_percent=profit_margin,
                risk_level=risk_level,
                liquidity_score=liquidity_score,
                time_to_profit_days=time_to_profit,
                confidence_score=confidence_score,
                reasons=reasons,
                detected_at=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing arbitrage opportunity: {e}")
            return None
    
    def _calculate_net_profit(self, buy_platform: Platform, sell_platform: Platform, 
                             buy_price: float, sell_price: float) -> float:
        """计算净利润（考虑手续费）"""
        try:
            buy_fees = self.price_comparator.platform_fees.get(buy_platform)
            sell_fees = self.price_comparator.platform_fees.get(sell_platform)
            
            if not buy_fees or not sell_fees:
                # 简单计算
                return sell_price - buy_price
            
            actual_buy_cost = buy_fees.calculate_buy_cost(buy_price)
            actual_sell_revenue = sell_fees.calculate_sell_cost(sell_price)
            
            return actual_sell_revenue - actual_buy_cost
            
        except Exception as e:
            self.logger.error(f"Error calculating net profit: {e}")
            return sell_price - buy_price
    
    def _assess_risk_level(self, comparison: PriceComparison, buy_price_data, sell_price_data) -> ArbitrageRiskLevel:
        """评估风险等级"""
        risk_factors = 0
        
        # 价格波动风险
        if comparison.price_spread.get('relative_spread_percent', 0) > 20:
            risk_factors += 1
        
        # 流动性风险
        total_volume = buy_price_data.sell_count + sell_price_data.sell_count
        if total_volume < 20:
            risk_factors += 2
        elif total_volume < 50:
            risk_factors += 1
        
        # 价格水平风险
        avg_price = (buy_price_data.sell_price + sell_price_data.sell_price) / 2
        if avg_price > 1000:
            risk_factors += 1
        elif avg_price < 10:
            risk_factors += 1
        
        # 平台风险
        if buy_price_data.platform == 'steam' or sell_price_data.platform == 'steam':
            risk_factors -= 1  # Steam相对安全
        
        # 确定风险等级
        if risk_factors <= 0:
            return ArbitrageRiskLevel.LOW
        elif risk_factors <= 2:
            return ArbitrageRiskLevel.MEDIUM
        elif risk_factors <= 4:
            return ArbitrageRiskLevel.HIGH
        else:
            return ArbitrageRiskLevel.VERY_HIGH
    
    def _calculate_liquidity_score(self, buy_price_data, sell_price_data) -> float:
        """计算流动性评分"""
        buy_volume = buy_price_data.sell_count + buy_price_data.bidding_count
        sell_volume = sell_price_data.sell_count + sell_price_data.bidding_count
        
        total_volume = buy_volume + sell_volume
        
        if total_volume >= 200:
            return 90.0
        elif total_volume >= 100:
            return 80.0
        elif total_volume >= 50:
            return 70.0
        elif total_volume >= 20:
            return 60.0
        elif total_volume >= 10:
            return 50.0
        else:
            return 30.0
    
    def _estimate_time_to_profit(self, buy_platform: Platform, sell_platform: Platform) -> int:
        """估算获利时间"""
        buy_fees = self.price_comparator.platform_fees.get(buy_platform)
        sell_fees = self.price_comparator.platform_fees.get(sell_platform)
        
        if not buy_fees or not sell_fees:
            return 3  # 默认3天
        
        # 考虑提现时间
        return max(buy_fees.withdraw_time_days, sell_fees.withdraw_time_days) + 1
    
    def _calculate_confidence_score(self, comparison: PriceComparison, 
                                   liquidity_score: float, risk_level: ArbitrageRiskLevel) -> float:
        """计算置信度评分"""
        base_score = 50.0
        
        # 利润率加分
        profit_margin = comparison.max_profit_margin
        if profit_margin >= 20:
            base_score += 30
        elif profit_margin >= 10:
            base_score += 20
        elif profit_margin >= 5:
            base_score += 10
        
        # 流动性加分
        base_score += (liquidity_score - 50) * 0.3
        
        # 风险等级扣分
        risk_penalty = {
            ArbitrageRiskLevel.LOW: 0,
            ArbitrageRiskLevel.MEDIUM: -10,
            ArbitrageRiskLevel.HIGH: -20,
            ArbitrageRiskLevel.VERY_HIGH: -30
        }
        base_score += risk_penalty.get(risk_level, -20)
        
        # 平台数量加分
        platform_count = len(comparison.platform_prices)
        if platform_count >= 3:
            base_score += 10
        
        return max(0, min(100, base_score))
    
    def _generate_analysis_reasons(self, comparison: PriceComparison, profit_margin: float,
                                  liquidity_score: float, risk_level: ArbitrageRiskLevel) -> List[str]:
        """生成分析原因"""
        reasons = []
        
        # 利润率原因
        if profit_margin >= 15:
            reasons.append(f"高利润率：{profit_margin:.1f}%，套利空间大")
        elif profit_margin >= 8:
            reasons.append(f"中等利润率：{profit_margin:.1f}%，有一定套利价值")
        else:
            reasons.append(f"较低利润率：{profit_margin:.1f}%，需谨慎评估")
        
        # 流动性原因
        if liquidity_score >= 80:
            reasons.append("高流动性：交易活跃，容易成交")
        elif liquidity_score >= 60:
            reasons.append("中等流动性：有一定交易量")
        else:
            reasons.append("低流动性：交易量较少，成交可能较慢")
        
        # 风险原因
        if risk_level == ArbitrageRiskLevel.LOW:
            reasons.append("低风险：价格稳定，平台可靠")
        elif risk_level == ArbitrageRiskLevel.MEDIUM:
            reasons.append("中等风险：需要关注市场变化")
        else:
            reasons.append("较高风险：价格波动大或流动性不足")
        
        # 平台原因
        if comparison.best_buy_platform and comparison.best_sell_platform:
            reasons.append(f"建议：在{comparison.best_buy_platform.value}买入，在{comparison.best_sell_platform.value}卖出")
        
        return reasons
    
    def _is_valid_opportunity(self, opportunity: ArbitrageOpportunity) -> bool:
        """验证套利机会是否有效"""
        config = self.arbitrage_config
        
        # 检查利润率
        if opportunity.profit_margin_percent < config.min_profit_margin:
            return False
        
        # 检查利润金额
        if opportunity.net_profit < config.min_profit_amount:
            return False
        
        # 检查投资金额
        if opportunity.buy_price > config.max_investment:
            return False
        
        # 检查流动性
        if opportunity.liquidity_score < config.min_liquidity_score:
            return False
        
        # 检查置信度
        if opportunity.confidence_score < config.min_confidence_score:
            return False
        
        return True
    
    def get_top_opportunities(self, limit: int = 10, 
                             risk_level: Optional[ArbitrageRiskLevel] = None) -> List[ArbitrageOpportunity]:
        """获取顶级套利机会"""
        opportunities = self.detected_opportunities.copy()
        
        # 按风险等级筛选
        if risk_level:
            opportunities = [opp for opp in opportunities if opp.risk_level == risk_level]
        
        # 按置信度和利润率排序
        opportunities.sort(
            key=lambda x: (x.confidence_score, x.profit_margin_percent), 
            reverse=True
        )
        
        return opportunities[:limit]
    
    def get_opportunities_summary(self) -> Dict[str, Any]:
        """获取套利机会摘要"""
        if not self.detected_opportunities:
            return {
                'total_opportunities': 0,
                'avg_profit_margin': 0,
                'risk_distribution': {},
                'platform_distribution': {}
            }
        
        # 风险分布
        risk_distribution = {}
        for risk in ArbitrageRiskLevel:
            count = len([opp for opp in self.detected_opportunities if opp.risk_level == risk])
            risk_distribution[risk.value] = count
        
        # 平台分布
        platform_distribution = {}
        for opp in self.detected_opportunities:
            key = f"{opp.buy_platform.value}->{opp.sell_platform.value}"
            platform_distribution[key] = platform_distribution.get(key, 0) + 1
        
        return {
            'total_opportunities': len(self.detected_opportunities),
            'avg_profit_margin': sum(opp.profit_margin_percent for opp in self.detected_opportunities) / len(self.detected_opportunities),
            'avg_confidence_score': sum(opp.confidence_score for opp in self.detected_opportunities) / len(self.detected_opportunities),
            'risk_distribution': risk_distribution,
            'platform_distribution': platform_distribution,
            'last_detection': max(opp.detected_at for opp in self.detected_opportunities).isoformat() if self.detected_opportunities else None
        }


# 全局套利检测器实例
_arbitrage_detector: Optional[ArbitrageDetector] = None


def get_arbitrage_detector() -> ArbitrageDetector:
    """获取全局套利检测器实例"""
    global _arbitrage_detector
    
    if _arbitrage_detector is None:
        _arbitrage_detector = ArbitrageDetector()
    
    return _arbitrage_detector
