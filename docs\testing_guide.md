# 基础数据收集器测试指南

## 概述

本文档描述了基础数据收集器的完整测试体系，包括单元测试、集成测试、API测试等。

## 测试架构

### 测试文件结构

```
tests/
├── conftest.py                    # pytest配置和通用fixtures
├── test_base_data_collector.py    # 基础数据收集器单元测试
├── test_base_data_api.py          # API接口测试
└── test_base_data_integration.py  # 集成测试
```

### 测试分类

1. **单元测试** (`test_base_data_collector.py`)
   - 数据解析测试
   - 数据验证测试
   - API调用逻辑测试
   - 数据库操作测试
   - 调度器集成测试
   - 统计和监控测试
   - 状态管理测试

2. **API测试** (`test_base_data_api.py`)
   - 服务管理API测试
   - 数据同步API测试
   - 监控统计API测试
   - 健康检查API测试
   - 数据导出API测试

3. **集成测试** (`test_base_data_integration.py`)
   - 完整工作流测试
   - 服务生命周期测试
   - 数据库集成测试
   - 错误恢复测试

## 运行测试

### 使用测试脚本 (推荐)

```bash
# 运行所有测试
python scripts/run_tests.py

# 运行特定类型的测试
python scripts/run_tests.py --type unit
python scripts/run_tests.py --type api
python scripts/run_tests.py --type integration
python scripts/run_tests.py --type performance

# 生成覆盖率报告
python scripts/run_tests.py --coverage --html-report

# 详细输出
python scripts/run_tests.py --verbose

# 生成完整测试报告
python scripts/run_tests.py --report

# 检查测试依赖
python scripts/run_tests.py --check-deps
```

### 直接使用pytest

```bash
# 运行所有基础数据收集器测试
pytest tests/test_base_data_collector.py tests/test_base_data_api.py tests/test_base_data_integration.py -v

# 运行单元测试
pytest tests/test_base_data_collector.py -m unit -v

# 运行API测试
pytest tests/test_base_data_api.py -m api -v

# 运行集成测试
pytest tests/test_base_data_integration.py -m integration -v

# 生成覆盖率报告
pytest --cov=services.base_data_collector --cov=api.base_data_api --cov-report=html

# 运行特定测试
pytest tests/test_base_data_collector.py::TestDataParsing::test_parse_api_response_success -v
```

## 测试覆盖范围

### 功能覆盖

- ✅ **数据收集**: API调用、数据解析、错误处理
- ✅ **数据验证**: 质量检查、格式验证、完整性验证
- ✅ **数据存储**: 数据库操作、新增/更新逻辑
- ✅ **调度管理**: 定时任务、手动触发、状态管理
- ✅ **监控统计**: 性能指标、质量报告、状态监控
- ✅ **API接口**: RESTful API、错误处理、响应格式
- ✅ **状态持久化**: 状态保存/加载、配置管理
- ✅ **错误恢复**: 异常处理、重试机制、优雅降级

### 场景覆盖

- ✅ **正常流程**: 成功的数据收集和存储
- ✅ **异常情况**: API失败、数据库错误、网络超时
- ✅ **边界条件**: 空数据、大数据集、无效格式
- ✅ **并发操作**: 多线程访问、状态同步
- ✅ **性能测试**: 大数据量处理、内存使用、响应时间

## 测试数据

### 模拟数据

测试使用模拟的SteamDT API响应数据：

```json
{
  "success": true,
  "data": [
    {
      "name": "AK-47 | 红线 (久经沙场)",
      "marketHashName": "AK-47 | Redline (Field-Tested)",
      "platformList": [
        {"name": "steam", "itemId": "steam_123"},
        {"name": "buff163", "itemId": "buff_456"}
      ]
    }
  ]
}
```

### 测试环境

- **数据库**: SQLite内存数据库 (`sqlite:///:memory:`)
- **API密钥**: 测试用虚拟密钥
- **状态文件**: 临时文件，测试后自动清理
- **日志级别**: DEBUG（详细日志输出）

## 测试配置

### pytest配置 (`conftest.py`)

- **环境设置**: 自动设置测试环境变量
- **Fixtures**: 提供通用的测试数据和模拟对象
- **标记**: 自动为测试添加适当的标记
- **清理**: 测试后自动清理全局状态

### 测试标记

- `@pytest.mark.unit`: 单元测试
- `@pytest.mark.integration`: 集成测试
- `@pytest.mark.api`: API测试
- `@pytest.mark.slow`: 慢速测试
- `@pytest.mark.database`: 数据库测试
- `@pytest.mark.asyncio`: 异步测试

## 性能测试

### 测试指标

- **处理速度**: 1000个饰品应在5秒内完成解析
- **内存使用**: 处理5000个饰品内存增加应小于100MB
- **并发性能**: 支持10个并发读操作
- **数据库性能**: 批量插入/更新操作

### 性能基准

```python
# 大数据集处理测试
def test_large_dataset_processing():
    # 处理1000个饰品应在5秒内完成
    assert processing_time < 5.0

# 内存使用测试
def test_memory_usage():
    # 内存增加应小于100MB
    assert memory_increase < 100

# 并发操作测试
def test_concurrent_operations():
    # 10个并发操作应无错误
    assert len(errors) == 0
```

## 错误测试

### 错误注入

使用`error_injector` fixture注入各种错误：

```python
def test_api_error_handling(error_injector):
    # 注入API错误
    error_injector.inject_api_error(ConnectionError, "Network error")
    
    # 测试错误处理
    result = await collector.collect_base_data()
    assert not result.success
```

### 错误类型

- **网络错误**: 连接超时、DNS解析失败
- **API错误**: 认证失败、限流、服务不可用
- **数据库错误**: 连接失败、约束违反、磁盘空间不足
- **数据错误**: 格式错误、缺失字段、类型不匹配

## 测试报告

### 覆盖率报告

运行 `python scripts/run_tests.py --report` 生成：

- **HTML报告**: `htmlcov/index.html`
- **XML报告**: `coverage.xml`
- **JUnit报告**: `test-results.xml`

### 报告内容

- 代码覆盖率统计
- 未覆盖代码行
- 测试执行时间
- 失败测试详情
- 性能指标

## 持续集成

### CI/CD集成

```yaml
# GitHub Actions示例
- name: Run Tests
  run: |
    python scripts/run_tests.py --report
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage.xml
```

### 质量门禁

- **覆盖率**: 最低90%
- **测试通过率**: 100%
- **性能回归**: 不超过10%
- **内存泄漏**: 无内存泄漏

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: cannot import name 'BaseDataCollector'
   ```
   解决: 检查PYTHONPATH设置

2. **数据库错误**
   ```
   sqlite3.OperationalError: database is locked
   ```
   解决: 确保测试后正确关闭数据库连接

3. **异步测试错误**
   ```
   RuntimeError: There is no current event loop
   ```
   解决: 使用`@pytest.mark.asyncio`标记

### 调试技巧

- 使用 `-v` 参数获取详细输出
- 使用 `--tb=long` 获取完整错误堆栈
- 使用 `--pdb` 在失败时进入调试器
- 检查测试日志文件

## 最佳实践

### 测试编写

1. **独立性**: 每个测试应该独立运行
2. **可重复**: 测试结果应该一致
3. **快速**: 单元测试应该快速执行
4. **清晰**: 测试名称应该描述测试内容
5. **覆盖**: 覆盖正常和异常情况

### 模拟对象

1. **最小化**: 只模拟必要的依赖
2. **真实性**: 模拟行为应该接近真实情况
3. **验证**: 验证模拟对象的调用
4. **清理**: 测试后清理模拟状态

### 数据管理

1. **隔离**: 使用独立的测试数据
2. **清理**: 测试后清理数据
3. **一致性**: 使用一致的测试数据格式
4. **版本化**: 测试数据应该版本化管理

## 扩展测试

### 添加新测试

1. 在适当的测试文件中添加测试类
2. 使用合适的fixtures和标记
3. 遵循命名约定
4. 添加必要的文档

### 测试新功能

1. 编写失败的测试（TDD）
2. 实现功能使测试通过
3. 重构代码保持测试通过
4. 添加边界和错误测试

这个测试体系确保了基础数据收集器的高质量和可靠性，为系统的稳定运行提供了强有力的保障。
