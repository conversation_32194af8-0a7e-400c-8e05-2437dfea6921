"""
Ares系统数据表格组件
提供可排序、可筛选、可分页的数据表格功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go


class DataTable:
    """数据表格组件"""
    
    def __init__(self, 
                 data: pd.DataFrame,
                 key: str,
                 title: str = "数据表格",
                 page_size: int = 20,
                 sortable: bool = True,
                 filterable: bool = True,
                 selectable: bool = False,
                 actions: Optional[List[Dict[str, Any]]] = None):
        """
        初始化数据表格
        
        Args:
            data: 数据DataFrame
            key: 组件唯一标识
            title: 表格标题
            page_size: 每页显示数量
            sortable: 是否可排序
            filterable: 是否可筛选
            selectable: 是否可选择行
            actions: 操作按钮列表
        """
        self.data = data
        self.key = key
        self.title = title
        self.page_size = page_size
        self.sortable = sortable
        self.filterable = filterable
        self.selectable = selectable
        self.actions = actions or []
        
        # 初始化会话状态
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if f"{self.key}_current_page" not in st.session_state:
            st.session_state[f"{self.key}_current_page"] = 0
        
        if f"{self.key}_sort_column" not in st.session_state:
            st.session_state[f"{self.key}_sort_column"] = None
        
        if f"{self.key}_sort_ascending" not in st.session_state:
            st.session_state[f"{self.key}_sort_ascending"] = True
        
        if f"{self.key}_filters" not in st.session_state:
            st.session_state[f"{self.key}_filters"] = {}
        
        if f"{self.key}_selected_rows" not in st.session_state:
            st.session_state[f"{self.key}_selected_rows"] = []
    
    def render(self) -> pd.DataFrame:
        """渲染数据表格"""
        st.markdown(f'<h3 class="section-title">{self.title}</h3>', unsafe_allow_html=True)
        
        # 渲染筛选器
        if self.filterable:
            filtered_data = self._render_filters()
        else:
            filtered_data = self.data.copy()
        
        # 渲染排序控件
        if self.sortable:
            sorted_data = self._apply_sorting(filtered_data)
        else:
            sorted_data = filtered_data
        
        # 渲染分页控件
        paginated_data = self._render_pagination(sorted_data)
        
        # 渲染表格
        displayed_data = self._render_table(paginated_data)
        
        # 渲染操作按钮
        if self.actions:
            self._render_actions()
        
        return displayed_data
    
    def _render_filters(self) -> pd.DataFrame:
        """渲染筛选器"""
        with st.expander("🔍 筛选选项", expanded=False):
            col1, col2, col3 = st.columns(3)
            
            filtered_data = self.data.copy()
            
            # 为每个列创建筛选器
            for i, column in enumerate(self.data.columns):
                col = [col1, col2, col3][i % 3]
                
                with col:
                    if self.data[column].dtype in ['object', 'string']:
                        # 文本筛选
                        unique_values = ['全部'] + sorted(self.data[column].dropna().unique().tolist())
                        selected_value = st.selectbox(
                            f"筛选 {column}",
                            unique_values,
                            key=f"{self.key}_filter_{column}"
                        )
                        
                        if selected_value != '全部':
                            filtered_data = filtered_data[filtered_data[column] == selected_value]
                    
                    elif self.data[column].dtype in ['int64', 'float64']:
                        # 数值范围筛选
                        min_val = float(self.data[column].min())
                        max_val = float(self.data[column].max())
                        
                        if min_val != max_val:
                            range_values = st.slider(
                                f"筛选 {column}",
                                min_val, max_val, (min_val, max_val),
                                key=f"{self.key}_filter_{column}"
                            )
                            
                            filtered_data = filtered_data[
                                (filtered_data[column] >= range_values[0]) & 
                                (filtered_data[column] <= range_values[1])
                            ]
            
            # 重置筛选器按钮
            if st.button("重置筛选器", key=f"{self.key}_reset_filters"):
                for column in self.data.columns:
                    if f"{self.key}_filter_{column}" in st.session_state:
                        del st.session_state[f"{self.key}_filter_{column}"]
                st.rerun()
        
        return filtered_data
    
    def _apply_sorting(self, data: pd.DataFrame) -> pd.DataFrame:
        """应用排序"""
        sort_column = st.session_state[f"{self.key}_sort_column"]
        sort_ascending = st.session_state[f"{self.key}_sort_ascending"]
        
        if sort_column and sort_column in data.columns:
            return data.sort_values(by=sort_column, ascending=sort_ascending)
        
        return data
    
    def _render_pagination(self, data: pd.DataFrame) -> pd.DataFrame:
        """渲染分页控件"""
        total_rows = len(data)
        total_pages = (total_rows - 1) // self.page_size + 1 if total_rows > 0 else 1
        current_page = st.session_state[f"{self.key}_current_page"]
        
        # 确保当前页在有效范围内
        if current_page >= total_pages:
            current_page = max(0, total_pages - 1)
            st.session_state[f"{self.key}_current_page"] = current_page
        
        if total_pages > 1:
            col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])
            
            with col1:
                if st.button("⏮️ 首页", key=f"{self.key}_first_page", disabled=current_page == 0):
                    st.session_state[f"{self.key}_current_page"] = 0
                    st.rerun()
            
            with col2:
                if st.button("⬅️ 上页", key=f"{self.key}_prev_page", disabled=current_page == 0):
                    st.session_state[f"{self.key}_current_page"] = current_page - 1
                    st.rerun()
            
            with col3:
                st.markdown(f"<div style='text-align: center; padding: 0.5rem;'>第 {current_page + 1} 页，共 {total_pages} 页 ({total_rows} 条记录)</div>", 
                           unsafe_allow_html=True)
            
            with col4:
                if st.button("➡️ 下页", key=f"{self.key}_next_page", disabled=current_page >= total_pages - 1):
                    st.session_state[f"{self.key}_current_page"] = current_page + 1
                    st.rerun()
            
            with col5:
                if st.button("⏭️ 末页", key=f"{self.key}_last_page", disabled=current_page >= total_pages - 1):
                    st.session_state[f"{self.key}_current_page"] = total_pages - 1
                    st.rerun()
        
        # 获取当前页数据
        start_idx = current_page * self.page_size
        end_idx = start_idx + self.page_size
        return data.iloc[start_idx:end_idx]
    
    def _render_table(self, data: pd.DataFrame) -> pd.DataFrame:
        """渲染表格"""
        if len(data) == 0:
            st.info("没有数据显示")
            return data
        
        # 创建表格配置
        column_config = {}
        
        for column in data.columns:
            if data[column].dtype in ['int64', 'float64']:
                # 数值列配置
                column_config[column] = st.column_config.NumberColumn(
                    column,
                    help=f"点击排序 {column}",
                    format="%.2f" if data[column].dtype == 'float64' else "%d"
                )
            elif 'date' in column.lower() or 'time' in column.lower():
                # 日期时间列配置
                column_config[column] = st.column_config.DatetimeColumn(
                    column,
                    help=f"点击排序 {column}"
                )
            else:
                # 文本列配置
                column_config[column] = st.column_config.TextColumn(
                    column,
                    help=f"点击排序 {column}"
                )
        
        # 渲染数据表格
        if self.selectable:
            # 可选择的表格
            event = st.dataframe(
                data,
                column_config=column_config,
                use_container_width=True,
                hide_index=True,
                on_select="rerun",
                selection_mode="multi-row",
                key=f"{self.key}_dataframe"
            )
            
            # 处理选择事件
            if hasattr(event, 'selection') and event.selection:
                selected_rows = event.selection.rows
                st.session_state[f"{self.key}_selected_rows"] = selected_rows
        else:
            # 普通表格
            st.dataframe(
                data,
                column_config=column_config,
                use_container_width=True,
                hide_index=True,
                key=f"{self.key}_dataframe"
            )
        
        return data
    
    def _render_actions(self):
        """渲染操作按钮"""
        if not self.actions:
            return
        
        st.markdown("### 操作")
        
        # 创建操作按钮列
        cols = st.columns(len(self.actions))
        
        for i, action in enumerate(self.actions):
            with cols[i]:
                if st.button(
                    action.get('label', '操作'),
                    key=f"{self.key}_action_{i}",
                    help=action.get('help', ''),
                    disabled=action.get('disabled', False),
                    use_container_width=True
                ):
                    # 执行操作回调
                    if 'callback' in action:
                        selected_rows = st.session_state.get(f"{self.key}_selected_rows", [])
                        action['callback'](selected_rows)
    
    def get_selected_rows(self) -> List[int]:
        """获取选中的行索引"""
        return st.session_state.get(f"{self.key}_selected_rows", [])
    
    def clear_selection(self):
        """清除选择"""
        st.session_state[f"{self.key}_selected_rows"] = []


def create_portfolio_table(data: pd.DataFrame, key: str = "portfolio_table") -> DataTable:
    """创建投资组合表格"""
    
    # 定义操作按钮
    actions = [
        {
            'label': '📊 查看详情',
            'help': '查看选中项目的详细信息',
            'callback': lambda rows: st.info(f"查看详情: {len(rows)} 个项目")
        },
        {
            'label': '📈 添加到关注',
            'help': '将选中项目添加到关注列表',
            'callback': lambda rows: st.success(f"已添加 {len(rows)} 个项目到关注列表")
        },
        {
            'label': '🚨 设置预警',
            'help': '为选中项目设置价格预警',
            'callback': lambda rows: st.info(f"为 {len(rows)} 个项目设置预警")
        }
    ]
    
    return DataTable(
        data=data,
        key=key,
        title="💼 投资组合",
        page_size=15,
        sortable=True,
        filterable=True,
        selectable=True,
        actions=actions
    )


def create_watchlist_table(data: pd.DataFrame, key: str = "watchlist_table") -> DataTable:
    """创建监控列表表格"""
    
    actions = [
        {
            'label': '💰 添加到投资组合',
            'help': '将选中项目添加到投资组合',
            'callback': lambda rows: st.success(f"已添加 {len(rows)} 个项目到投资组合")
        },
        {
            'label': '❌ 移除监控',
            'help': '从监控列表中移除选中项目',
            'callback': lambda rows: st.warning(f"已移除 {len(rows)} 个项目")
        }
    ]
    
    return DataTable(
        data=data,
        key=key,
        title="👁️ 监控列表",
        page_size=20,
        sortable=True,
        filterable=True,
        selectable=True,
        actions=actions
    )


def create_discovery_table(data: pd.DataFrame, key: str = "discovery_table") -> DataTable:
    """创建发现机会表格"""
    
    actions = [
        {
            'label': '⭐ 添加到关注',
            'help': '将发现的机会添加到关注列表',
            'callback': lambda rows: st.success(f"已添加 {len(rows)} 个机会到关注列表")
        },
        {
            'label': '📊 详细分析',
            'help': '查看机会的详细分析',
            'callback': lambda rows: st.info(f"分析 {len(rows)} 个机会")
        }
    ]
    
    return DataTable(
        data=data,
        key=key,
        title="🔍 发现机会",
        page_size=10,
        sortable=True,
        filterable=True,
        selectable=True,
        actions=actions
    )
