"""
Ares系统自定义异常类定义
提供统一的异常处理机制和错误分类
"""

from enum import Enum
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime


class ErrorSeverity(Enum):
    """错误严重级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类"""
    API_ERROR = "api_error"
    DATABASE_ERROR = "database_error"
    VALIDATION_ERROR = "validation_error"
    BUSINESS_LOGIC_ERROR = "business_logic_error"
    SYSTEM_ERROR = "system_error"
    CONFIGURATION_ERROR = "configuration_error"


@dataclass
class ErrorContext:
    """错误上下文信息"""
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    item_id: Optional[str] = None
    operation: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class AresException(Exception):
    """Ares系统基础异常类"""
    
    def __init__(
        self,
        message: str,
        category: ErrorCategory,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[ErrorContext] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or ErrorContext()
        self.cause = cause
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录"""
        return {
            'message': self.message,
            'category': self.category.value,
            'severity': self.severity.value,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context.__dict__ if self.context else {},
            'cause': str(self.cause) if self.cause else None
        }


class APILimitExceeded(AresException):
    """API调用频率超限异常"""
    
    def __init__(self, message: str = "API rate limit exceeded", **kwargs):
        super().__init__(message, ErrorCategory.API_ERROR, ErrorSeverity.HIGH, **kwargs)


class APIConnectionError(AresException):
    """API连接错误异常"""
    
    def __init__(self, message: str = "API connection failed", **kwargs):
        super().__init__(message, ErrorCategory.API_ERROR, ErrorSeverity.HIGH, **kwargs)


class APIResponseError(AresException):
    """API响应错误异常"""
    
    def __init__(self, message: str = "Invalid API response", **kwargs):
        super().__init__(message, ErrorCategory.API_ERROR, ErrorSeverity.MEDIUM, **kwargs)


class DataValidationError(AresException):
    """数据验证错误异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.VALIDATION_ERROR, ErrorSeverity.MEDIUM, **kwargs)


class DatabaseConnectionError(AresException):
    """数据库连接错误异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.DATABASE_ERROR, ErrorSeverity.CRITICAL, **kwargs)


class ConfigurationError(AresException):
    """配置错误异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.CONFIGURATION_ERROR, ErrorSeverity.HIGH, **kwargs)


class BusinessLogicError(AresException):
    """业务逻辑错误异常"""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.BUSINESS_LOGIC_ERROR, ErrorSeverity.MEDIUM, **kwargs)


class SchedulerError(AresException):
    """调度器错误异常"""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.SYSTEM_ERROR, ErrorSeverity.HIGH, **kwargs)
