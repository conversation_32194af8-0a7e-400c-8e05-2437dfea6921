"""
Ares调度器API接口
提供智能调度器的管理和监控API
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, Body
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime

from core.scheduler import get_scheduler
from core.priority_calculator import get_priority_calculator, PriorityWeights
from core.database import get_database_manager
from core.exceptions import SchedulerError

# 创建路由器
router = APIRouter(prefix="/api/scheduler", tags=["调度器"])


# 请求模型
class PriorityAdjustmentRequest(BaseModel):
    """优先级调整请求"""
    item_id: str = Field(..., description="饰品ID")
    user_priority: int = Field(..., ge=0, le=10, description="用户优先级(0-10)")


class PoolRebalanceRequest(BaseModel):
    """池重新平衡请求"""
    force: bool = Field(False, description="是否强制重新平衡")


class WeightsUpdateRequest(BaseModel):
    """权重更新请求"""
    spread_weight: float = Field(0.4, ge=0.0, le=1.0, description="价差权重")
    volume_weight: float = Field(0.3, ge=0.0, le=1.0, description="交易量权重")
    volatility_weight: float = Field(0.2, ge=0.0, le=1.0, description="波动率权重")
    user_interest_weight: float = Field(0.1, ge=0.0, le=1.0, description="用户兴趣权重")


# 响应模型
class SchedulerStatusResponse(BaseModel):
    """调度器状态响应"""
    success: bool = Field(..., description="是否成功")
    status: Dict[str, Any] = Field(..., description="调度器状态")


class PoolStatisticsResponse(BaseModel):
    """池统计响应"""
    success: bool = Field(..., description="是否成功")
    statistics: Dict[str, Any] = Field(..., description="池统计信息")


# API端点
@router.get("/status", response_model=SchedulerStatusResponse)
async def get_scheduler_status():
    """获取调度器状态"""
    try:
        scheduler = await get_scheduler()
        status = scheduler.get_schedule_status()
        
        return SchedulerStatusResponse(
            success=True,
            status=status
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start_scheduler(background_tasks: BackgroundTasks):
    """启动调度器"""
    try:
        scheduler = await get_scheduler()
        
        if scheduler.running:
            return {
                "success": True,
                "message": "Scheduler is already running",
                "status": "running"
            }
        
        # 在后台启动调度器
        background_tasks.add_task(scheduler.start)
        
        return {
            "success": True,
            "message": "Scheduler start initiated",
            "status": "starting"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_scheduler():
    """停止调度器"""
    try:
        scheduler = await get_scheduler()
        await scheduler.stop()
        
        return {
            "success": True,
            "message": "Scheduler stopped successfully",
            "status": "stopped"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pools/statistics", response_model=PoolStatisticsResponse)
async def get_pool_statistics():
    """获取池统计信息"""
    try:
        scheduler = await get_scheduler()
        statistics = await scheduler.get_pool_statistics()
        
        return PoolStatisticsResponse(
            success=True,
            statistics=statistics
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pools/rebalance")
async def rebalance_pools(request: PoolRebalanceRequest):
    """重新平衡池分配"""
    try:
        scheduler = await get_scheduler()
        
        if request.force or not scheduler.running:
            await scheduler._rebalance_pools()
            
            return {
                "success": True,
                "message": "Pool rebalancing completed",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "message": "Cannot rebalance while scheduler is running. Use force=true to override.",
                "timestamp": datetime.utcnow().isoformat()
            }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/priority/adjust")
async def adjust_item_priority(request: PriorityAdjustmentRequest):
    """调整饰品优先级"""
    try:
        scheduler = await get_scheduler()
        await scheduler.adjust_item_priority(request.item_id, request.user_priority)
        
        return {
            "success": True,
            "message": f"Priority adjusted for item {request.item_id}",
            "item_id": request.item_id,
            "new_priority": request.user_priority,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/items/{item_id}/force-update")
async def force_update_item(item_id: str):
    """强制更新指定饰品"""
    try:
        scheduler = await get_scheduler()
        await scheduler.force_update_item(item_id)
        
        return {
            "success": True,
            "message": f"Force update initiated for item {item_id}",
            "item_id": item_id,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/items/{item_id}/add")
async def add_item_to_schedule(
    item_id: str,
    pool_type: str = Query("main", description="池类型"),
    priority_score: float = Query(0.0, description="初始优先级评分")
):
    """添加饰品到调度队列"""
    try:
        if pool_type not in ["core", "main"]:
            raise HTTPException(status_code=400, detail="Invalid pool_type. Must be 'core' or 'main'")
        
        scheduler = await get_scheduler()
        await scheduler.add_item_to_schedule(item_id, pool_type, priority_score)
        
        return {
            "success": True,
            "message": f"Item {item_id} added to {pool_type} pool",
            "item_id": item_id,
            "pool_type": pool_type,
            "priority_score": priority_score,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/items/{item_id}")
async def remove_item_from_schedule(item_id: str):
    """从调度队列移除饰品"""
    try:
        scheduler = await get_scheduler()
        await scheduler.remove_item_from_schedule(item_id)
        
        return {
            "success": True,
            "message": f"Item {item_id} removed from schedule",
            "item_id": item_id,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/priority/weights")
async def get_priority_weights():
    """获取当前优先级权重配置"""
    try:
        calculator = get_priority_calculator()
        weights = calculator.weights
        
        return {
            "success": True,
            "weights": {
                "spread_weight": weights.spread_weight,
                "volume_weight": weights.volume_weight,
                "volatility_weight": weights.volatility_weight,
                "user_interest_weight": weights.user_interest_weight,
                "user_priority_multiplier": weights.user_priority_multiplier,
                "failure_penalty": weights.failure_penalty,
                "staleness_penalty": weights.staleness_penalty,
                "max_spread": weights.max_spread,
                "max_volume": weights.max_volume,
                "max_volatility": weights.max_volatility
            }
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/priority/weights")
async def update_priority_weights(request: WeightsUpdateRequest):
    """更新优先级权重配置"""
    try:
        # 验证权重总和
        total_weight = (request.spread_weight + request.volume_weight + 
                       request.volatility_weight + request.user_interest_weight)
        
        if abs(total_weight - 1.0) > 0.01:
            raise HTTPException(
                status_code=400, 
                detail=f"Weights must sum to 1.0, got {total_weight}"
            )
        
        calculator = get_priority_calculator()
        
        # 创建新的权重配置
        new_weights = PriorityWeights(
            spread_weight=request.spread_weight,
            volume_weight=request.volume_weight,
            volatility_weight=request.volatility_weight,
            user_interest_weight=request.user_interest_weight
        )
        
        # 更新权重
        calculator.update_weights(new_weights)
        
        return {
            "success": True,
            "message": "Priority weights updated successfully",
            "new_weights": {
                "spread_weight": new_weights.spread_weight,
                "volume_weight": new_weights.volume_weight,
                "volatility_weight": new_weights.volatility_weight,
                "user_interest_weight": new_weights.user_interest_weight
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/queue/status")
async def get_queue_status():
    """获取调度队列状态"""
    try:
        scheduler = await get_scheduler()
        
        # 获取队列中的前10个任务
        upcoming_tasks = []
        for i, task in enumerate(sorted(scheduler.task_queue)[:10]):
            upcoming_tasks.append({
                "position": i + 1,
                "item_id": task.item_id,
                "pool_type": task.pool_type,
                "priority_score": task.priority_score,
                "next_update_time": task.next_update_time.isoformat(),
                "update_frequency": task.update_frequency,
                "retry_count": task.retry_count
            })
        
        return {
            "success": True,
            "queue_size": len(scheduler.task_queue),
            "upcoming_tasks": upcoming_tasks,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/maintenance")
async def trigger_maintenance():
    """触发定期维护任务"""
    try:
        scheduler = await get_scheduler()
        await scheduler._periodic_maintenance()
        
        return {
            "success": True,
            "message": "Maintenance tasks completed",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
