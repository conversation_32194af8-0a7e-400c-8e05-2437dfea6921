"""
Ares评分算法模块
实现多维度评分模型和机会评分算法
"""

import logging
import math
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class ScoreWeights:
    """评分权重配置"""
    spread_weight: float = 0.25      # 价差权重
    volume_weight: float = 0.20      # 交易量权重
    volatility_weight: float = 0.15  # 波动性权重
    trend_weight: float = 0.20       # 趋势权重
    liquidity_weight: float = 0.10   # 流动性权重
    momentum_weight: float = 0.10    # 动量权重
    
    def normalize(self):
        """归一化权重"""
        total = (self.spread_weight + self.volume_weight + self.volatility_weight + 
                self.trend_weight + self.liquidity_weight + self.momentum_weight)
        
        if total > 0:
            self.spread_weight /= total
            self.volume_weight /= total
            self.volatility_weight /= total
            self.trend_weight /= total
            self.liquidity_weight /= total
            self.momentum_weight /= total


@dataclass
class ItemScore:
    """饰品评分结果"""
    item_id: str
    spread_score: float = 0.0
    volume_score: float = 0.0
    volatility_score: float = 0.0
    trend_score: float = 0.0
    liquidity_score: float = 0.0
    momentum_score: float = 0.0
    total_score: float = 0.0
    confidence: float = 0.0
    risk_level: str = "medium"
    recommendation: str = "hold"


class ScoringEngine:
    """评分引擎"""
    
    def __init__(self):
        """初始化评分引擎"""
        self.default_weights = ScoreWeights()
        
        # 评分参数配置
        self.max_spread_threshold = 50.0      # 最大价差阈值
        self.max_volume_threshold = 1000.0    # 最大交易量阈值
        self.max_volatility_threshold = 0.5   # 最大波动性阈值
        self.max_trend_threshold = 20.0       # 最大趋势阈值
        
        # 风险等级阈值
        self.risk_thresholds = {
            'low': 0.3,
            'medium': 0.6,
            'high': 1.0
        }
    
    def calculate_opportunity_score(
        self,
        item_data: Dict[str, Any],
        weights: Optional[ScoreWeights] = None
    ) -> ItemScore:
        """计算机会评分"""
        if weights is None:
            weights = self.default_weights
        else:
            weights.normalize()
        
        item_id = item_data.get('item_id', 'unknown')
        
        # 计算各维度评分
        spread_score = self._calculate_spread_score(item_data)
        volume_score = self._calculate_volume_score(item_data)
        volatility_score = self._calculate_volatility_score(item_data)
        trend_score = self._calculate_trend_score(item_data)
        liquidity_score = self._calculate_liquidity_score(item_data)
        momentum_score = self._calculate_momentum_score(item_data)
        
        # 计算加权总分
        total_score = (
            spread_score * weights.spread_weight +
            volume_score * weights.volume_weight +
            volatility_score * weights.volatility_weight +
            trend_score * weights.trend_weight +
            liquidity_score * weights.liquidity_weight +
            momentum_score * weights.momentum_weight
        )
        
        # 计算置信度
        confidence = self._calculate_confidence(item_data, total_score)
        
        # 确定风险等级
        risk_level = self._determine_risk_level(volatility_score, total_score)
        
        # 生成推荐
        recommendation = self._generate_recommendation(total_score, risk_level, trend_score)
        
        return ItemScore(
            item_id=item_id,
            spread_score=spread_score,
            volume_score=volume_score,
            volatility_score=volatility_score,
            trend_score=trend_score,
            liquidity_score=liquidity_score,
            momentum_score=momentum_score,
            total_score=total_score,
            confidence=confidence,
            risk_level=risk_level,
            recommendation=recommendation
        )
    
    def _calculate_spread_score(self, item_data: Dict[str, Any]) -> float:
        """计算价差评分"""
        try:
            # 获取买卖价差
            ask_price = item_data.get('ask_price', 0)
            bid_price = item_data.get('bid_price', 0)
            
            if ask_price <= 0 or bid_price <= 0:
                return 0.0
            
            # 计算价差百分比
            spread_percent = ((ask_price - bid_price) / ask_price) * 100
            
            # 价差越大，机会评分越高（但有上限）
            score = min(spread_percent / self.max_spread_threshold, 1.0)
            
            return max(0.0, score)
        
        except Exception as e:
            logger.error(f"Error calculating spread score: {str(e)}")
            return 0.0
    
    def _calculate_volume_score(self, item_data: Dict[str, Any]) -> float:
        """计算交易量评分"""
        try:
            volume_24h = item_data.get('volume_24h', 0)
            
            # 交易量归一化评分
            score = min(volume_24h / self.max_volume_threshold, 1.0)
            
            # 应用对数缩放，避免极值影响
            if score > 0:
                score = math.log(1 + score * 9) / math.log(10)
            
            return max(0.0, score)
        
        except Exception as e:
            logger.error(f"Error calculating volume score: {str(e)}")
            return 0.0
    
    def _calculate_volatility_score(self, item_data: Dict[str, Any]) -> float:
        """计算波动性评分"""
        try:
            volatility = item_data.get('volatility', 0)
            
            if volatility <= 0:
                return 0.0
            
            # 适度波动性获得较高评分
            # 使用倒U型函数，在0.2-0.3之间达到峰值
            optimal_volatility = 0.25
            
            if volatility <= optimal_volatility:
                score = volatility / optimal_volatility
            else:
                # 超过最优值后评分下降
                excess = volatility - optimal_volatility
                score = max(0, 1 - (excess / (self.max_volatility_threshold - optimal_volatility)))
            
            return max(0.0, min(1.0, score))
        
        except Exception as e:
            logger.error(f"Error calculating volatility score: {str(e)}")
            return 0.0
    
    def _calculate_trend_score(self, item_data: Dict[str, Any]) -> float:
        """计算趋势评分"""
        try:
            # 获取不同时间段的价格变化
            price_change_24h = item_data.get('price_change_24h', 0)
            price_change_7d = item_data.get('price_change_7d', 0)
            price_change_30d = item_data.get('price_change_30d', 0)
            
            # 加权计算趋势评分
            # 短期趋势权重更高
            trend_score = (
                price_change_24h * 0.5 +
                price_change_7d * 0.3 +
                price_change_30d * 0.2
            )
            
            # 正向趋势获得正分，但有上限
            if trend_score > 0:
                score = min(trend_score / self.max_trend_threshold, 1.0)
            else:
                # 负向趋势可能是买入机会
                score = max(trend_score / (-self.max_trend_threshold), -1.0)
                # 将负分转换为正分（逆向投资机会）
                score = abs(score) * 0.7  # 降权处理
            
            return max(0.0, min(1.0, score))
        
        except Exception as e:
            logger.error(f"Error calculating trend score: {str(e)}")
            return 0.0
    
    def _calculate_liquidity_score(self, item_data: Dict[str, Any]) -> float:
        """计算流动性评分"""
        try:
            # 基于交易量和价差计算流动性
            volume_24h = item_data.get('volume_24h', 0)
            ask_price = item_data.get('ask_price', 0)
            bid_price = item_data.get('bid_price', 0)
            
            if ask_price <= 0 or bid_price <= 0:
                return 0.0
            
            # 交易量评分
            volume_score = min(volume_24h / 100.0, 1.0)
            
            # 价差评分（价差越小，流动性越好）
            spread_percent = ((ask_price - bid_price) / ask_price) * 100
            spread_score = max(0, 1 - (spread_percent / 10.0))  # 10%价差为临界点
            
            # 综合流动性评分
            liquidity_score = (volume_score * 0.7 + spread_score * 0.3)
            
            return max(0.0, min(1.0, liquidity_score))
        
        except Exception as e:
            logger.error(f"Error calculating liquidity score: {str(e)}")
            return 0.0
    
    def _calculate_momentum_score(self, item_data: Dict[str, Any]) -> float:
        """计算动量评分"""
        try:
            # 基于价格和交易量的变化计算动量
            price_change_24h = item_data.get('price_change_24h', 0)
            volume_change_24h = item_data.get('volume_change_24h', 0)
            
            # 价格动量
            price_momentum = max(0, price_change_24h / 10.0)  # 10%为满分
            
            # 交易量动量
            volume_momentum = max(0, volume_change_24h / 100.0)  # 100%为满分
            
            # 综合动量评分
            momentum_score = (price_momentum * 0.6 + volume_momentum * 0.4)
            
            return max(0.0, min(1.0, momentum_score))
        
        except Exception as e:
            logger.error(f"Error calculating momentum score: {str(e)}")
            return 0.0
    
    def _calculate_confidence(self, item_data: Dict[str, Any], total_score: float) -> float:
        """计算置信度"""
        try:
            # 基于数据完整性和质量计算置信度
            confidence_factors = []
            
            # 数据完整性检查
            required_fields = ['current_price', 'volume_24h', 'price_change_24h']
            completeness = sum(1 for field in required_fields if item_data.get(field) is not None) / len(required_fields)
            confidence_factors.append(completeness)
            
            # 交易量置信度（交易量越高，数据越可靠）
            volume_24h = item_data.get('volume_24h', 0)
            volume_confidence = min(volume_24h / 50.0, 1.0)  # 50笔交易为满分
            confidence_factors.append(volume_confidence)
            
            # 价格稳定性（波动性适中时置信度更高）
            volatility = item_data.get('volatility', 0)
            if volatility > 0:
                stability_confidence = 1 - min(volatility / 0.5, 1.0)
            else:
                stability_confidence = 0.5
            confidence_factors.append(stability_confidence)
            
            # 评分一致性（各维度评分差异不大时置信度更高）
            score_variance = self._calculate_score_variance(item_data)
            consistency_confidence = max(0, 1 - score_variance)
            confidence_factors.append(consistency_confidence)
            
            # 综合置信度
            confidence = sum(confidence_factors) / len(confidence_factors)
            
            return max(0.0, min(1.0, confidence))
        
        except Exception as e:
            logger.error(f"Error calculating confidence: {str(e)}")
            return 0.5
    
    def _calculate_score_variance(self, item_data: Dict[str, Any]) -> float:
        """计算评分方差"""
        try:
            scores = [
                self._calculate_spread_score(item_data),
                self._calculate_volume_score(item_data),
                self._calculate_volatility_score(item_data),
                self._calculate_trend_score(item_data),
                self._calculate_liquidity_score(item_data),
                self._calculate_momentum_score(item_data)
            ]
            
            if not scores:
                return 1.0
            
            mean_score = sum(scores) / len(scores)
            variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
            
            return variance
        
        except Exception as e:
            logger.error(f"Error calculating score variance: {str(e)}")
            return 1.0
    
    def _determine_risk_level(self, volatility_score: float, total_score: float) -> str:
        """确定风险等级"""
        # 基于波动性和总评分确定风险等级
        risk_score = volatility_score * 0.7 + (1 - total_score) * 0.3
        
        if risk_score <= self.risk_thresholds['low']:
            return 'low'
        elif risk_score <= self.risk_thresholds['medium']:
            return 'medium'
        else:
            return 'high'
    
    def _generate_recommendation(self, total_score: float, risk_level: str, trend_score: float) -> str:
        """生成投资建议"""
        if total_score >= 0.8:
            if risk_level == 'low':
                return 'strong_buy'
            elif risk_level == 'medium':
                return 'buy'
            else:
                return 'speculative_buy'
        elif total_score >= 0.6:
            if trend_score >= 0.7:
                return 'buy'
            else:
                return 'hold'
        elif total_score >= 0.4:
            return 'hold'
        elif total_score >= 0.2:
            return 'weak_sell'
        else:
            return 'sell'
    
    def batch_calculate_scores(
        self,
        items: List[Dict[str, Any]],
        weights: Optional[ScoreWeights] = None
    ) -> List[ItemScore]:
        """批量计算评分"""
        scores = []
        
        for item in items:
            try:
                score = self.calculate_opportunity_score(item, weights)
                scores.append(score)
            except Exception as e:
                logger.error(f"Error calculating score for item {item.get('item_id', 'unknown')}: {str(e)}")
                # 创建默认评分
                scores.append(ItemScore(
                    item_id=item.get('item_id', 'unknown'),
                    total_score=0.0,
                    confidence=0.0,
                    risk_level='high',
                    recommendation='hold'
                ))
        
        return scores
    
    def get_scoring_stats(self) -> Dict[str, Any]:
        """获取评分统计"""
        return {
            'default_weights': {
                'spread_weight': self.default_weights.spread_weight,
                'volume_weight': self.default_weights.volume_weight,
                'volatility_weight': self.default_weights.volatility_weight,
                'trend_weight': self.default_weights.trend_weight,
                'liquidity_weight': self.default_weights.liquidity_weight,
                'momentum_weight': self.default_weights.momentum_weight
            },
            'thresholds': {
                'max_spread_threshold': self.max_spread_threshold,
                'max_volume_threshold': self.max_volume_threshold,
                'max_volatility_threshold': self.max_volatility_threshold,
                'max_trend_threshold': self.max_trend_threshold
            },
            'risk_thresholds': self.risk_thresholds
        }


# 全局评分引擎实例
_scoring_engine: Optional[ScoringEngine] = None


def get_scoring_engine() -> ScoringEngine:
    """获取全局评分引擎实例"""
    global _scoring_engine
    if _scoring_engine is None:
        _scoring_engine = ScoringEngine()
    return _scoring_engine
