"""
基础数据API接口测试
测试base_data_api.py中的所有API端点
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
import os
import base64
dummy_key_bytes = b'dummy_key_for_import_only_32byte'
dummy_key = base64.urlsafe_b64encode(dummy_key_bytes).decode()
os.environ.setdefault('STEAMDT_API_KEY', 'dummy_key_for_import')
os.environ.setdefault('ENCRYPTION_KEY', dummy_key)
os.environ.setdefault('SECRET_KEY', 'dummy_secret_key_for_import')
os.environ['DATABASE_URL'] = 'sqlite:///data/ares.db'

from app.api_main import app
from services.base_data_collector import BaseDataResult, UpdateResult


class TestBaseDataAPI:
    """基础数据API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_collector(self):
        """模拟收集器"""
        mock = Mock()
        mock.running = True
        mock.sync_time = "02:00"
        mock.last_update = datetime.now()
        mock.get_scheduler_status.return_value = {
            'running': True,
            'scheduler_running': True,
            'sync_time': '02:00',
            'next_run_time': '2025-01-19T02:00:00'
        }
        mock.get_api_statistics.return_value = {
            'total_calls': 10,
            'successful_calls': 9,
            'failed_calls': 1,
            'success_rate': 90.0,
            'total_items_collected': 1000,
            'last_update': '2025-01-18T02:00:00',
            'can_call_api': False
        }
        mock.get_database_statistics.return_value = {
            'total_items': 1000,
            'steamdt_items': 1000,
            'active_items': 980,
            'recent_synced_items': 50
        }
        mock.get_quality_statistics.return_value = {
            'status': 'available',
            'quality_score': 92.5,
            'total_items_validated': 1000,
            'valid_items': 925,
            'quality_level': '优秀'
        }
        return mock


class TestServiceManagement:
    """服务管理API测试"""
    
    def test_get_service_status(self, client, mock_collector):
        """测试获取服务状态"""
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            response = client.get("/api/base-data/status")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['running'] == True
            assert data['scheduler_running'] == True
            assert data['sync_time'] == '02:00'
            assert 'next_run_time' in data
            assert 'last_update' in data
    
    def test_start_service(self, client, mock_collector):
        """测试启动服务"""
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector), \
             patch('api.base_data_api.is_base_data_collector_running', return_value=False):
            
            response = client.post("/api/base-data/start")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['status'] == 'starting'
            assert 'timestamp' in data
    
    def test_start_service_already_running(self, client, mock_collector):
        """测试启动已运行的服务"""
        with patch('api.base_data_api.is_base_data_collector_running', return_value=True):
            
            response = client.post("/api/base-data/start")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['status'] == 'running'
            assert 'already running' in data['message']
    
    def test_stop_service(self, client, mock_collector):
        """测试停止服务"""
        with patch('api.base_data_api.is_base_data_collector_running', return_value=True), \
             patch('api.base_data_api.stop_base_data_collector') as mock_stop:
            
            response = client.post("/api/base-data/stop")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['status'] == 'stopped'
            mock_stop.assert_called_once()
    
    def test_stop_service_not_running(self, client):
        """测试停止未运行的服务"""
        with patch('api.base_data_api.is_base_data_collector_running', return_value=False):
            
            response = client.post("/api/base-data/stop")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['status'] == 'stopped'
            assert 'not running' in data['message']


class TestDataSync:
    """数据同步API测试"""
    
    def test_trigger_manual_sync_success(self, client, mock_collector):
        """测试成功的手动同步"""
        mock_result = BaseDataResult(
            success=True,
            total_items=100,
            new_items=10,
            updated_items=5,
            data_quality_score=95.0,
            sync_duration=30.5
        )
        mock_collector.trigger_manual_sync.return_value = mock_result
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            sync_request = {
                "force": False,
                "reason": "测试同步"
            }
            response = client.post("/api/base-data/sync", json=sync_request)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['total_items'] == 100
            assert data['new_items'] == 10
            assert data['updated_items'] == 5
            assert data['quality_score'] == 95.0
            assert data['sync_duration'] == 30.5
    
    def test_trigger_manual_sync_failure(self, client, mock_collector):
        """测试失败的手动同步"""
        mock_result = BaseDataResult(
            success=False,
            total_items=0,
            new_items=0,
            updated_items=0,
            error_message="API调用失败"
        )
        mock_collector.trigger_manual_sync.return_value = mock_result
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            sync_request = {
                "force": True,
                "reason": "强制同步测试"
            }
            response = client.post("/api/base-data/sync", json=sync_request)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == False
            assert data['error_message'] == "API调用失败"
    
    def test_update_sync_time(self, client, mock_collector):
        """测试更新同步时间"""
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            config_request = {
                "sync_time": "03:30"
            }
            response = client.put("/api/base-data/config/sync-time", json=config_request)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['new_sync_time'] == "03:30"
            mock_collector.update_sync_time.assert_called_once_with("03:30")
    
    def test_update_sync_time_invalid_format(self, client):
        """测试无效的同步时间格式"""
        config_request = {
            "sync_time": "25:00"  # 无效时间
        }
        response = client.put("/api/base-data/config/sync-time", json=config_request)
        
        assert response.status_code == 422  # 验证错误


class TestMonitoringAndStatistics:
    """监控和统计API测试"""
    
    def test_get_statistics(self, client, mock_collector):
        """测试获取统计信息"""
        mock_monitoring_summary = {
            'service_status': {'running': True},
            'api_performance': {'internal_stats': mock_collector.get_api_statistics.return_value},
            'data_metrics': {
                'database': mock_collector.get_database_statistics.return_value,
                'quality': mock_collector.get_quality_statistics.return_value
            }
        }
        mock_collector.get_monitoring_summary.return_value = mock_monitoring_summary
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/statistics")
            
            assert response.status_code == 200
            data = response.json()
            
            assert 'timestamp' in data
            assert 'api_statistics' in data
            assert 'database_statistics' in data
            assert 'quality_statistics' in data
            assert 'monitoring_summary' in data
    
    def test_get_execution_history(self, client, mock_collector):
        """测试获取执行历史"""
        mock_history = [
            {
                'timestamp': '2025-01-18T02:00:00',
                'success': True,
                'total_items': 100,
                'duration': 30.5
            },
            {
                'timestamp': '2025-01-17T02:00:00',
                'success': True,
                'total_items': 95,
                'duration': 28.2
            }
        ]
        mock_collector.get_execution_history.return_value = mock_history
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/execution-history?limit=5")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert len(data['history']) == 2
            assert data['count'] == 2
    
    def test_get_quality_report(self, client, mock_collector):
        """测试获取质量报告"""
        mock_report = {
            'quality_score': 92.5,
            'total_items': 1000,
            'valid_items': 925,
            'quality_level': '优秀',
            'issues': []
        }
        mock_collector.get_latest_quality_report.return_value = mock_report
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/quality-report")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['report']['quality_score'] == 92.5
    
    def test_get_quality_report_not_available(self, client, mock_collector):
        """测试质量报告不可用"""
        mock_collector.get_latest_quality_report.return_value = None
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/quality-report")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == False
            assert data['report'] is None


class TestHealthCheck:
    """健康检查API测试"""
    
    def test_health_check_healthy(self, client, mock_collector):
        """测试健康的服务"""
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['status'] in ['healthy', 'degraded', 'unhealthy']
            assert 'health_score' in data
            assert 'issues' in data
            assert 'components' in data
            assert 'timestamp' in data
    
    def test_health_check_with_issues(self, client, mock_collector):
        """测试有问题的服务健康检查"""
        # 模拟API成功率低
        mock_collector.get_api_statistics.return_value = {
            'total_calls': 100,
            'success_rate': 60.0  # 低成功率
        }
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/health")
            
            assert response.status_code == 200
            data = response.json()
            
            # 应该检测到问题
            assert data['health_score'] < 100
            assert len(data['issues']) > 0


class TestMonitoringDataExport:
    """监控数据导出API测试"""
    
    def test_export_json_format(self, client, mock_collector):
        """测试导出JSON格式监控数据"""
        mock_data = {'test': 'data', 'metrics': {'value': 123}}
        mock_collector.export_monitoring_data.return_value = json.dumps(mock_data)
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/monitoring/export?format=json")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['test'] == 'data'
            assert data['metrics']['value'] == 123
    
    def test_export_prometheus_format(self, client, mock_collector):
        """测试导出Prometheus格式监控数据"""
        mock_prometheus_data = """# HELP test_metric Test metric
# TYPE test_metric gauge
test_metric 123
"""
        mock_collector.export_monitoring_data.return_value = mock_prometheus_data
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/monitoring/export?format=prometheus")
            
            assert response.status_code == 200
            assert response.headers['content-type'] == 'text/plain; charset=utf-8'
            assert 'test_metric 123' in response.text
    
    def test_get_prometheus_metrics(self, client, mock_collector):
        """测试获取Prometheus指标"""
        mock_prometheus_data = """# HELP base_data_service_running Service running status
# TYPE base_data_service_running gauge
base_data_service_running 1
"""
        mock_collector.export_monitoring_data.return_value = mock_prometheus_data
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.get("/api/base-data/metrics/prometheus")
            
            assert response.status_code == 200
            assert response.headers['content-type'] == 'text/plain; charset=utf-8'
            assert 'base_data_service_running 1' in response.text


class TestDataManagement:
    """数据管理API测试"""
    
    def test_cleanup_old_items(self, client, mock_collector):
        """测试清理旧数据"""
        mock_collector.cleanup_old_items.return_value = 25
        
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.post("/api/base-data/database/cleanup?days_threshold=30")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert data['cleanup_count'] == 25
            assert data['days_threshold'] == 30
            mock_collector.cleanup_old_items.assert_called_once_with(days_threshold=30)
    
    def test_reset_statistics(self, client, mock_collector):
        """测试重置统计数据"""
        with patch('api.base_data_api.get_base_data_collector', return_value=mock_collector):
            
            response = client.post("/api/base-data/statistics/reset")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['success'] == True
            assert 'reset successfully' in data['message']
            mock_collector.reset_statistics.assert_called_once()


class TestErrorHandling:
    """API错误处理测试"""
    
    def test_api_error_handling(self, client):
        """测试API错误处理"""
        with patch('api.base_data_api.get_base_data_collector') as mock_get:
            mock_get.side_effect = Exception("Service unavailable")
            
            response = client.get("/api/base-data/status")
            
            assert response.status_code == 500
            assert 'Service unavailable' in response.json()['detail']
    
    def test_invalid_request_data(self, client):
        """测试无效请求数据"""
        # 测试无效的同步请求
        invalid_request = {
            "force": "invalid_boolean",  # 应该是布尔值
            "reason": None
        }
        
        response = client.post("/api/base-data/sync", json=invalid_request)
        assert response.status_code == 422  # 验证错误
