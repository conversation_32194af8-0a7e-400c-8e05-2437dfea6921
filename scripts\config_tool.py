#!/usr/bin/env python3
"""
配置管理工具
提供配置验证、导出、加密等功能的命令行工具
"""

import os
import sys
import argparse
import json
from pathlib import Path
from cryptography.fernet import Fernet

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import ConfigManager, get_config_manager
from core.exceptions import ConfigurationError


def generate_encryption_key():
    """生成新的加密密钥"""
    key = Fernet.generate_key()
    print(f"新的加密密钥: {key.decode()}")
    print("请将此密钥设置为环境变量 ENCRYPTION_KEY")
    return key.decode()


def generate_secret_key():
    """生成新的会话密钥"""
    import secrets
    key = secrets.token_urlsafe(32)
    print(f"新的会话密钥: {key}")
    print("请将此密钥设置为环境变量 SECRET_KEY")
    return key


def validate_config(config_file: str = None, env_file: str = None):
    """验证配置"""
    try:
        if config_file or env_file:
            config_manager = ConfigManager(
                config_file=config_file or "config/settings.yaml",
                env_file=env_file or ".env"
            )
        else:
            config_manager = get_config_manager()
        
        print("正在验证配置...")
        validation_result = config_manager.validate_config()
        
        if validation_result['valid']:
            print("✓ 配置验证通过")
        else:
            print("✗ 配置验证失败")
            
        if validation_result['errors']:
            print("\n错误:")
            for error in validation_result['errors']:
                print(f"  - {error}")
        
        if validation_result['warnings']:
            print("\n警告:")
            for warning in validation_result['warnings']:
                print(f"  - {warning}")
        
        return validation_result['valid']
        
    except Exception as e:
        print(f"配置验证失败: {str(e)}")
        return False


def export_config(output_file: str, format: str = "yaml", include_sensitive: bool = False):
    """导出配置"""
    try:
        config_manager = get_config_manager()
        config_manager.export_config(output_file, format=format, include_sensitive=include_sensitive)
        print(f"配置已导出到: {output_file}")
        
        if not include_sensitive:
            print("注意: 敏感信息已被隐藏")
        
    except Exception as e:
        print(f"导出配置失败: {str(e)}")


def show_config(key: str = None, source: str = "auto"):
    """显示配置"""
    try:
        config_manager = get_config_manager()
        
        if key:
            value = config_manager.get(key, source=source)
            print(f"{key}: {value}")
        else:
            config = config_manager.get_all_config(include_sensitive=False)
            print(json.dumps(config, indent=2, ensure_ascii=False, default=str))
            
    except Exception as e:
        print(f"显示配置失败: {str(e)}")


def set_config_value(key: str, value: str, persist: bool = False):
    """设置配置值"""
    try:
        config_manager = get_config_manager()
        
        # 尝试解析值类型
        parsed_value = value
        if value.lower() in ['true', 'false']:
            parsed_value = value.lower() == 'true'
        elif value.isdigit():
            parsed_value = int(value)
        elif value.replace('.', '').isdigit():
            parsed_value = float(value)
        
        success = config_manager.set(key, parsed_value, persist=persist)
        
        if success:
            print(f"配置已设置: {key} = {parsed_value}")
            if persist:
                print("配置已持久化到文件")
        else:
            print(f"设置配置失败: {key}")
            
    except Exception as e:
        print(f"设置配置失败: {str(e)}")


def encrypt_value(value: str):
    """加密值"""
    try:
        config_manager = get_config_manager()
        encrypted = config_manager.encrypt_value(value)
        print(f"原始值: {value}")
        print(f"加密值: {encrypted}")
        
    except Exception as e:
        print(f"加密失败: {str(e)}")


def decrypt_value(encrypted_value: str):
    """解密值"""
    try:
        config_manager = get_config_manager()
        decrypted = config_manager.decrypt_value(encrypted_value)
        print(f"加密值: {encrypted_value}")
        print(f"解密值: {decrypted}")
        
    except Exception as e:
        print(f"解密失败: {str(e)}")


def check_environment():
    """检查环境配置"""
    print("检查环境配置...")
    
    required_vars = [
        'STEAMDT_API_KEY',
        'ENCRYPTION_KEY',
        'SECRET_KEY'
    ]
    
    optional_vars = [
        'DATABASE_URL',
        'REDIS_URL',
        'DISCORD_WEBHOOK_URL',
        'TELEGRAM_BOT_TOKEN',
        'APP_ENV',
        'LOG_LEVEL'
    ]
    
    print("\n必需环境变量:")
    all_required_set = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"  ✓ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"  ✗ {var}: 未设置")
            all_required_set = False
    
    print("\n可选环境变量:")
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"  ✓ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"  - {var}: 未设置")
    
    if all_required_set:
        print("\n✓ 所有必需环境变量已设置")
    else:
        print("\n✗ 部分必需环境变量未设置")
    
    return all_required_set


def show_change_history(limit: int = 10):
    """显示配置变更历史"""
    try:
        config_manager = get_config_manager()
        history = config_manager.get_change_history(limit=limit)
        
        if not history:
            print("没有配置变更历史")
            return
        
        print(f"最近 {len(history)} 次配置变更:")
        for event in history:
            print(f"  {event.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {event.key}: {event.old_value} -> {event.new_value} (source: {event.source})")
            
    except Exception as e:
        print(f"显示变更历史失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Ares系统配置管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成密钥命令
    key_parser = subparsers.add_parser('generate-key', help='生成加密密钥')
    key_parser.add_argument('--type', choices=['encryption', 'secret'], default='encryption',
                           help='密钥类型')
    
    # 验证配置命令
    validate_parser = subparsers.add_parser('validate', help='验证配置')
    validate_parser.add_argument('--config-file', help='配置文件路径')
    validate_parser.add_argument('--env-file', help='环境变量文件路径')
    
    # 导出配置命令
    export_parser = subparsers.add_parser('export', help='导出配置')
    export_parser.add_argument('output_file', help='输出文件路径')
    export_parser.add_argument('--format', choices=['yaml', 'json'], default='yaml',
                              help='导出格式')
    export_parser.add_argument('--include-sensitive', action='store_true',
                              help='包含敏感信息')
    
    # 显示配置命令
    show_parser = subparsers.add_parser('show', help='显示配置')
    show_parser.add_argument('--key', help='配置键')
    show_parser.add_argument('--source', choices=['auto', 'yaml', 'env', 'runtime', 'pydantic'],
                            default='auto', help='配置源')
    
    # 设置配置命令
    set_parser = subparsers.add_parser('set', help='设置配置')
    set_parser.add_argument('key', help='配置键')
    set_parser.add_argument('value', help='配置值')
    set_parser.add_argument('--persist', action='store_true', help='持久化到文件')
    
    # 加密命令
    encrypt_parser = subparsers.add_parser('encrypt', help='加密值')
    encrypt_parser.add_argument('value', help='要加密的值')
    
    # 解密命令
    decrypt_parser = subparsers.add_parser('decrypt', help='解密值')
    decrypt_parser.add_argument('encrypted_value', help='要解密的值')
    
    # 检查环境命令
    subparsers.add_parser('check-env', help='检查环境配置')
    
    # 变更历史命令
    history_parser = subparsers.add_parser('history', help='显示配置变更历史')
    history_parser.add_argument('--limit', type=int, default=10, help='显示条数限制')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'generate-key':
            if args.type == 'encryption':
                generate_encryption_key()
            else:
                generate_secret_key()
                
        elif args.command == 'validate':
            success = validate_config(args.config_file, args.env_file)
            sys.exit(0 if success else 1)
            
        elif args.command == 'export':
            export_config(args.output_file, args.format, args.include_sensitive)
            
        elif args.command == 'show':
            show_config(args.key, args.source)
            
        elif args.command == 'set':
            set_config_value(args.key, args.value, args.persist)
            
        elif args.command == 'encrypt':
            encrypt_value(args.value)
            
        elif args.command == 'decrypt':
            decrypt_value(args.encrypted_value)
            
        elif args.command == 'check-env':
            success = check_environment()
            sys.exit(0 if success else 1)
            
        elif args.command == 'history':
            show_change_history(args.limit)
            
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"执行命令失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
