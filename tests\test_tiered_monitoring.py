"""
分层监控系统测试
验证分层监控核心逻辑的正确性
"""

import pytest
import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.tiered_monitoring import (
    TieredMonitoringScheduler, MonitoringItem, PoolType, MonitoringStats
)
from services.api_budget import APIBudgetManager, APIEndpoint, DailyBudget
from services.web_scraper import SteamdtWebScraper, RankingItem
from services.steamdt_api import ItemPriceData, PlatformPrice


class TestAPIBudgetManager:
    """API预算管理器测试"""
    
    @pytest.fixture
    def budget_manager(self):
        """创建预算管理器实例"""
        return APIBudgetManager()
    
    def test_daily_budget_allocation(self, budget_manager):
        """测试每日预算分配"""
        budget = budget_manager.daily_budget
        
        assert budget.total_limit == 14400
        assert budget.get_core_pool_budget() == 8640  # 60%
        assert budget.get_main_pool_budget() == 4320   # 30%
        assert budget.get_discovery_budget() == 1440   # 10%
    
    def test_can_make_call_within_budget(self, budget_manager):
        """测试预算内的API调用检查"""
        # 初始状态应该可以调用
        assert budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "core")
        assert budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "main")
        assert budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "discovery")
    
    def test_can_make_call_budget_exhausted(self, budget_manager):
        """测试预算耗尽时的API调用检查"""
        # 模拟核心池预算耗尽
        budget_manager.budget_status.core_pool_calls = budget_manager.daily_budget.get_core_pool_budget()
        
        assert not budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "core")
        assert budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "main")  # 主池仍可用
    
    def test_record_call(self, budget_manager):
        """测试API调用记录"""
        initial_total = budget_manager.budget_status.total_calls
        initial_core = budget_manager.budget_status.core_pool_calls
        
        budget_manager.record_call(
            APIEndpoint.PRICE_SINGLE, "core", True, 0.5
        )
        
        assert budget_manager.budget_status.total_calls == initial_total + 1
        assert budget_manager.budget_status.core_pool_calls == initial_core + 1
        assert len(budget_manager.call_history) > 0
    
    def test_item_base_daily_limit(self, budget_manager):
        """测试基础信息API的每日限制"""
        # 第一次调用应该可以
        assert budget_manager.can_make_call(APIEndpoint.ITEM_BASE, "discovery")
        
        # 记录一次调用
        budget_manager.record_call(APIEndpoint.ITEM_BASE, "discovery", True, 1.0)
        
        # 第二次调用应该被拒绝
        assert not budget_manager.can_make_call(APIEndpoint.ITEM_BASE, "discovery")
    
    def test_get_budget_summary(self, budget_manager):
        """测试预算摘要获取"""
        summary = budget_manager.get_budget_summary()
        
        assert 'total_calls' in summary
        assert 'total_limit' in summary
        assert 'usage_percentage' in summary
        assert 'core_pool' in summary
        assert 'main_pool' in summary
        assert 'discovery' in summary


class TestTieredMonitoringScheduler:
    """分层监控调度器测试"""
    
    @pytest.fixture
    def scheduler(self):
        """创建调度器实例"""
        return TieredMonitoringScheduler()
    
    @pytest.fixture
    def mock_ranking_items(self):
        """创建模拟排行榜数据"""
        items = []
        for i in range(100):
            item = RankingItem(
                rank=i + 1,
                item_name=f"Test Item {i+1}",
                market_hash_name=f"Test Item {i+1}",
                current_price=10.0 + i,
                price_change=0.5,
                price_change_percent=5.0,
                volume_24h=100 - i,
                icon_url=f"https://example.com/icon{i+1}.png",
                item_url=f"https://example.com/item{i+1}"
            )
            items.append(item)
        return items
    
    def test_calculate_priority_score(self, scheduler):
        """测试优先级评分计算"""
        item = RankingItem(
            rank=1,
            item_name="AK-47 | Redline",
            market_hash_name="AK-47 | Redline (Field-Tested)",
            current_price=50.0,
            price_change=2.5,
            price_change_percent=5.0,
            volume_24h=150,
            icon_url="https://example.com/icon.png",
            item_url="https://example.com/item"
        )
        
        score = scheduler._calculate_priority_score(item)
        
        # 验证评分在合理范围内
        assert 0 <= score <= 100
        assert isinstance(score, float)
    
    def test_deduplicate_and_score(self, scheduler, mock_ranking_items):
        """测试去重和评分"""
        # 添加重复项目
        duplicate_item = RankingItem(
            rank=50,  # 更低的排名
            item_name="Test Item 1",
            market_hash_name="Test Item 1",
            current_price=15.0,
            price_change=1.0,
            price_change_percent=2.0,
            volume_24h=80,
            icon_url="https://example.com/icon1_dup.png",
            item_url="https://example.com/item1_dup"
        )
        mock_ranking_items.append(duplicate_item)
        
        scored_items = scheduler._deduplicate_and_score(mock_ranking_items)
        
        # 验证去重效果
        market_hash_names = [item[0].market_hash_name for item in scored_items]
        assert len(set(market_hash_names)) == len(market_hash_names)  # 无重复
        
        # 验证选择了排名更高的项目
        test_item_1 = next((item for item in scored_items if item[0].market_hash_name == "Test Item 1"), None)
        assert test_item_1 is not None
        assert test_item_1[0].rank == 1  # 应该选择排名更高的
    
    def test_allocate_to_pools(self, scheduler, mock_ranking_items):
        """测试监控池分配"""
        scored_items = scheduler._deduplicate_and_score(mock_ranking_items)
        scheduler._allocate_to_pools(scored_items)
        
        # 验证核心池大小
        assert len(scheduler.core_pool) == min(scheduler.core_pool_size, len(scored_items))
        
        # 验证主池大小
        remaining_items = max(0, len(scored_items) - scheduler.core_pool_size)
        expected_main_size = min(scheduler.main_pool_size, remaining_items)
        assert len(scheduler.main_pool) == expected_main_size
        
        # 验证池类型正确
        for item in scheduler.core_pool.values():
            assert item.pool_type == PoolType.CORE
        
        for item in scheduler.main_pool.values():
            assert item.pool_type == PoolType.MAIN
    
    def test_should_update_core_pool(self, scheduler):
        """测试核心池更新时机判断"""
        current_time = datetime.now()
        
        # 从未更新过，应该更新
        assert scheduler._should_update_core_pool(current_time)
        
        # 刚刚更新过，不应该更新
        scheduler.stats.last_core_update = current_time
        assert not scheduler._should_update_core_pool(current_time)
        
        # 超过更新间隔，应该更新
        old_time = current_time - timedelta(minutes=scheduler.core_update_interval + 1)
        scheduler.stats.last_core_update = old_time
        assert scheduler._should_update_core_pool(current_time)
    
    def test_should_update_main_pool(self, scheduler):
        """测试主池更新时机判断"""
        current_time = datetime.now()
        
        # 从未更新过，应该更新
        assert scheduler._should_update_main_pool(current_time)
        
        # 刚刚更新过，不应该更新
        scheduler.stats.last_main_update = current_time
        assert not scheduler._should_update_main_pool(current_time)
        
        # 超过更新间隔，应该更新
        old_time = current_time - timedelta(minutes=scheduler.main_update_interval + 1)
        scheduler.stats.last_main_update = old_time
        assert scheduler._should_update_main_pool(current_time)
    
    def test_select_main_pool_items_for_update(self, scheduler):
        """测试主池更新项目选择"""
        # 创建测试项目
        now = datetime.now()
        
        # 从未更新的项目
        item1 = MonitoringItem("item1", "Item 1", PoolType.MAIN, 90.0)
        item2 = MonitoringItem("item2", "Item 2", PoolType.MAIN, 80.0)
        
        # 已更新的项目
        item3 = MonitoringItem("item3", "Item 3", PoolType.MAIN, 70.0, now - timedelta(hours=1))
        item4 = MonitoringItem("item4", "Item 4", PoolType.MAIN, 85.0, now - timedelta(hours=2))
        
        scheduler.main_pool = {
            "item1": item1,
            "item2": item2,
            "item3": item3,
            "item4": item4
        }
        
        selected = scheduler._select_main_pool_items_for_update(3)
        
        # 应该优先选择从未更新的项目
        assert len(selected) == 3
        selected_ids = [item.item_id for item in selected]
        assert "item1" in selected_ids
        assert "item2" in selected_ids
        # 第三个应该是更新时间最早的
        assert "item4" in selected_ids
    
    def test_get_monitoring_summary(self, scheduler):
        """测试监控摘要获取"""
        # 添加一些测试数据
        scheduler.core_pool["test1"] = MonitoringItem("test1", "Test 1", PoolType.CORE, 90.0)
        scheduler.main_pool["test2"] = MonitoringItem("test2", "Test 2", PoolType.MAIN, 80.0)
        scheduler.stats.total_updates_today = 100
        scheduler.stats.successful_updates = 95
        scheduler.stats.failed_updates = 5
        
        summary = scheduler.get_monitoring_summary()
        
        assert summary['core_pool']['size'] == 1
        assert summary['main_pool']['size'] == 1
        assert summary['stats']['total_updates_today'] == 100
        assert summary['stats']['successful_updates'] == 95
        assert summary['stats']['failed_updates'] == 5
        assert summary['stats']['success_rate'] == 95.0
        assert 'running' in summary


class TestMonitoringItem:
    """监控项目测试"""
    
    def test_monitoring_item_creation(self):
        """测试监控项目创建"""
        item = MonitoringItem(
            item_id="test_item",
            market_hash_name="Test Item",
            pool_type=PoolType.CORE,
            priority_score=85.5
        )
        
        assert item.item_id == "test_item"
        assert item.market_hash_name == "Test Item"
        assert item.pool_type == PoolType.CORE
        assert item.priority_score == 85.5
        assert item.last_update is None
        assert item.update_count == 0
        assert item.error_count == 0
    
    def test_monitoring_item_to_dict(self):
        """测试监控项目字典转换"""
        now = datetime.now()
        item = MonitoringItem(
            item_id="test_item",
            market_hash_name="Test Item",
            pool_type=PoolType.MAIN,
            priority_score=75.0,
            last_update=now,
            update_count=5,
            error_count=1
        )
        
        item_dict = item.to_dict()
        
        assert item_dict['item_id'] == "test_item"
        assert item_dict['market_hash_name'] == "Test Item"
        assert item_dict['pool_type'] == "main"
        assert item_dict['priority_score'] == 75.0
        assert item_dict['last_update'] == now.isoformat()
        assert item_dict['update_count'] == 5
        assert item_dict['error_count'] == 1


class TestMonitoringStats:
    """监控统计测试"""
    
    def test_monitoring_stats_success_rate(self):
        """测试成功率计算"""
        stats = MonitoringStats()
        
        # 无更新时成功率为0
        assert stats.get_success_rate() == 0.0
        
        # 有更新时计算成功率
        stats.successful_updates = 95
        stats.failed_updates = 5
        assert stats.get_success_rate() == 95.0
        
        # 全部失败时成功率为0
        stats.successful_updates = 0
        stats.failed_updates = 10
        assert stats.get_success_rate() == 0.0


if __name__ == "__main__":
    # 运行基本测试
    print("运行分层监控基本测试...")

    # 测试监控项目（不依赖配置）
    print("测试监控项目...")
    item = MonitoringItem("test", "Test Item", PoolType.CORE, 90.0)
    assert item.pool_type == PoolType.CORE
    assert item.priority_score == 90.0
    print("✓ 监控项目测试通过")

    # 测试监控统计
    print("测试监控统计...")
    stats = MonitoringStats()
    stats.successful_updates = 95
    stats.failed_updates = 5
    assert stats.get_success_rate() == 95.0
    print("✓ 监控统计测试通过")

    print("所有分层监控基本测试通过！")
    print("注意：完整测试需要配置环境变量，请使用 pytest 运行完整测试套件")

    # 运行pytest（如果环境配置正确）
    # pytest.main(["-xvs", __file__])
