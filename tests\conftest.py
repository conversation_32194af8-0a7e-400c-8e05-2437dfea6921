"""
pytest配置文件
为所有测试提供通用的fixtures和配置
"""

import pytest
import asyncio
import tempfile
import os
import sys
from pathlib import Path
import base64

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置测试环境变量
def setup_test_environment():
    """设置测试环境变量"""
    dummy_key_bytes = b'dummy_key_for_import_only_32byte'
    dummy_key = base64.urlsafe_b64encode(dummy_key_bytes).decode()
    
    os.environ.setdefault('STEAMDT_API_KEY', 'test_api_key')
    os.environ.setdefault('ENCRYPTION_KEY', dummy_key)
    os.environ.setdefault('SECRET_KEY', 'test_secret_key')
    os.environ.setdefault('ARES_ENV', 'test')
    
    # 使用内存数据库进行测试
    os.environ['DATABASE_URL'] = 'sqlite:///:memory:'

# 在导入时设置环境
setup_test_environment()


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_database():
    """创建临时数据库"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
        temp_path = Path(f.name)
    
    # 设置临时数据库URL
    original_url = os.environ.get('DATABASE_URL')
    os.environ['DATABASE_URL'] = f'sqlite:///{temp_path}'
    
    yield temp_path
    
    # 清理
    if original_url:
        os.environ['DATABASE_URL'] = original_url
    if temp_path.exists():
        temp_path.unlink()


@pytest.fixture
def temp_state_file():
    """创建临时状态文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_path = Path(f.name)
    yield temp_path
    # 清理
    if temp_path.exists():
        temp_path.unlink()


@pytest.fixture
def mock_config():
    """模拟配置"""
    config = {
        'app_name': 'Ares Test System',
        'app_version': '1.0.0-test',
        'debug_mode': True,
        'base_data_collector': {
            'sync_time': '02:00',
            'api_timeout': 30,
            'max_retries': 3
        },
        'database': {
            'url': 'sqlite:///:memory:',
            'pool_size': 5,
            'max_overflow': 10
        }
    }
    return config


@pytest.fixture
def sample_api_response():
    """示例API响应数据"""
    return {
        'success': True,
        'data': [
            {
                'name': 'AK-47 | 红线 (久经沙场)',
                'marketHashName': 'AK-47 | Redline (Field-Tested)',
                'platformList': [
                    {'name': 'steam', 'itemId': 'steam_123'},
                    {'name': 'buff163', 'itemId': 'buff_456'}
                ]
            },
            {
                'name': 'M4A4 | 龙王 (崭新出厂)',
                'marketHashName': 'M4A4 | Dragon King (Factory New)',
                'platformList': [
                    {'name': 'steam', 'itemId': 'steam_789'},
                    {'name': 'buff163', 'itemId': 'buff_101'}
                ]
            }
        ]
    }


@pytest.fixture
def sample_item_data():
    """示例饰品数据"""
    from services.base_data_collector import ItemBaseInfo, PlatformInfo
    
    return [
        ItemBaseInfo(
            name='AK-47 | 红线 (久经沙场)',
            market_hash_name='AK-47 | Redline (Field-Tested)',
            platform_list=[
                PlatformInfo(name='steam', item_id='steam_123'),
                PlatformInfo(name='buff163', item_id='buff_456')
            ]
        ),
        ItemBaseInfo(
            name='M4A4 | 龙王 (崭新出厂)',
            market_hash_name='M4A4 | Dragon King (Factory New)',
            platform_list=[
                PlatformInfo(name='steam', item_id='steam_789'),
                PlatformInfo(name='buff163', item_id='buff_101')
            ]
        )
    ]


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "api: API测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "database: 数据库测试"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    for item in items:
        # 为异步测试添加标记
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
        
        # 根据文件名添加标记
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "api" in item.nodeid:
            item.add_marker(pytest.mark.api)
        elif "test_base_data_collector" in item.nodeid:
            item.add_marker(pytest.mark.unit)


# 测试会话钩子
@pytest.fixture(scope="session", autouse=True)
def setup_test_session():
    """测试会话设置"""
    print("\n=== 开始基础数据收集器测试会话 ===")
    
    # 确保测试环境干净
    import services.base_data_collector
    services.base_data_collector._base_data_collector = None
    
    yield
    
    print("\n=== 结束基础数据收集器测试会话 ===")


@pytest.fixture(autouse=True)
def reset_global_state():
    """重置全局状态"""
    # 在每个测试前重置全局收集器实例
    import services.base_data_collector
    services.base_data_collector._base_data_collector = None
    
    yield
    
    # 测试后清理
    if hasattr(services.base_data_collector, '_base_data_collector'):
        collector = services.base_data_collector._base_data_collector
        if collector and collector.running:
            asyncio.run(collector.stop())
        services.base_data_collector._base_data_collector = None


# 性能测试辅助
@pytest.fixture
def performance_monitor():
    """性能监控器"""
    import time
    import psutil
    import os
    
    class PerformanceMonitor:
        def __init__(self):
            self.process = psutil.Process(os.getpid())
            self.start_time = None
            self.start_memory = None
        
        def start(self):
            self.start_time = time.time()
            self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        def stop(self):
            end_time = time.time()
            end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            return {
                'duration': end_time - self.start_time if self.start_time else 0,
                'memory_usage': end_memory - self.start_memory if self.start_memory else 0,
                'peak_memory': end_memory
            }
    
    return PerformanceMonitor()


# 数据库测试辅助
@pytest.fixture
def database_helper():
    """数据库测试辅助器"""
    class DatabaseHelper:
        @staticmethod
        def create_test_items(session, count=5):
            """创建测试饰品数据"""
            from core.database import Item
            items = []
            
            for i in range(count):
                item = Item(
                    market_hash_name=f'Test Item {i} (Field-Tested)',
                    name=f'测试饰品 {i}',
                    weapon_type='AK-47',
                    skin_name='Test Skin',
                    data_source='steamdt',
                    is_active=True
                )
                session.add(item)
                items.append(item)
            
            session.commit()
            return items
        
        @staticmethod
        def count_items(session):
            """统计饰品数量"""
            from core.database import Item
            return session.query(Item).count()
        
        @staticmethod
        def get_item_by_hash_name(session, hash_name):
            """根据hash name获取饰品"""
            from core.database import Item
            return session.get(Item, hash_name)
    
    return DatabaseHelper()


# 错误注入辅助
@pytest.fixture
def error_injector():
    """错误注入器"""
    class ErrorInjector:
        def __init__(self):
            self.patches = []
        
        def inject_api_error(self, error_type=Exception, message="Injected error"):
            """注入API错误"""
            from unittest.mock import patch
            
            def side_effect(*args, **kwargs):
                raise error_type(message)
            
            patch_obj = patch('services.base_data_collector.BaseDataCollector._call_api_with_retry', 
                            side_effect=side_effect)
            self.patches.append(patch_obj)
            return patch_obj.start()
        
        def inject_database_error(self, error_type=Exception, message="Database error"):
            """注入数据库错误"""
            from unittest.mock import patch
            
            def side_effect(*args, **kwargs):
                raise error_type(message)
            
            patch_obj = patch('core.database.DatabaseManager.get_session', 
                            side_effect=side_effect)
            self.patches.append(patch_obj)
            return patch_obj.start()
        
        def cleanup(self):
            """清理所有注入的错误"""
            for patch_obj in self.patches:
                try:
                    patch_obj.stop()
                except:
                    pass
            self.patches.clear()
    
    injector = ErrorInjector()
    yield injector
    injector.cleanup()
