最终产品需求文档 (PRD)：CS2混合策略投资助手 "Ares" 系统
版本： Final 2.0 (架构优化版)
日期： 2025年7月17日
产品负责人： (您的名字)
产品经理： Gemini
1.0 项目概述 (Project Overview)
1.1 项目愿景 (Vision)
构建一个自动化的、高效率的、数据驱动的CS2饰品投资决策辅助系统。该系统旨在通过整合高质量的聚合数据源、执行预设的混合投资策略、并以智能化的方式解决规模与频率限制，来提升决策效率、捕捉交易机会、管理长期风险，最终实现超越市场的资产增值。
1.2 核心问题 (Problem Statement)
手动追踪多个平台的饰品价格、计算价差、监控投资组合及宏观市场环境，既耗时又容易出错。更严峻的是，面对成千上万的饰品和数据源（如 steamdt.com）的访问频率限制，任何试图“全面实时监控”的朴素方法在工程上都不可行，会导致机会错失和系统性风险敞口扩大。
1.3 目标用户 (Target User)
一名具备资深开发能力的个人投资者（即您本人）。因此，该系统优先考虑架构的合理性、功能的健壮性、数据的准确性和策略的可扩展性，而非华丽的UI和多用户支持。
2.0 系统架构 (System Architecture)
2.1 核心设计理念 (Core Design Philosophy)
放弃“煮沸海洋”式的蛮力轮询，转而采用**“分层动态目标调度”**的智能架构，将有限的系统资源和请求频率，精准地分配给最高价值的目标。
2.2 核心组件 (Core Components)
“扫描器” (The Scanner)：
职责： 广度优先的侦察兵。
频率： 低频运行（例如，每日一次）。
工作： 缓慢、低压力地遍历一个包含数千个潜在饰品的“大列表”，使用预设标准（如价差、交易量）进行筛选，最终产出一个数量有限的（约50-100个）“动态交易候选池” (watchlist.json)。
“追踪器” (The Tracker)：
职责： 深度优先的狙击手。
频率： 高频、分层运行。
工作： 读取一个数量极少的、手动配置的“静态持仓列表” (holdings.json) 和由“扫描器”每日生成的“动态交易候选池”。对前者进行中低频（如每小时一次）数据更新，对后者进行较高频（如每5分钟一次）数据更新，并将获取到的数据存入主数据库。
2.3 数据流 (Data Flow)
[扫描器] → [动态交易候选池] → [追踪器] ← [静态持仓列表] → [主数据库] → [应用层]
3.0 功能需求 (Functional Requirements)
史诗 1：系统配置与管理 (System Configuration & Management)
1.1 (系统初始化配置): 提供一个系统设置页面，用于配置数据库路径、通知服务Webhook URL等基础信息，并提供测试连接功能。
1.2 (核心列表管理): 在系统设置页面，提供文本区域，允许用户编辑“扫描器”的大列表和“追踪器”的静态持仓列表。
1.3 (系统状态监控): 在系统设置页面或页脚，提供“扫描器”和“追踪器”的状态监控，包括上次运行时间、运行结果、当前追踪目标数，并提供手动触发和日志查看的入口。
史诗 2：数据引擎 (Epic 2: Data Engine)
2.1 (扫描器实现): 开发一个低频、可配置的“扫描器”模块，负责每日一次的大规模饰品筛选，并生成watchlist.json文件。
2.2 (追踪器实现): 开发一个持续运行的“追踪器”模块，能读取watchlist.json和holdings.json，并根据资产类型实施不同的更新频率。
2.3 (数据持久化): 将“追踪器”获取到的所有数据，附加时间戳，稳定地存入本地SQLite数据库。
史诗 3：宏观市场雷达 (Epic 3: Macro Market Radar)
3.1 (生态健康度监控): 自动获取并展示CS2的月/日平均在线玩家数的趋势图。
3.2 (社区活跃度追踪): 追踪关键社区（如r/csgomarketforum）的周发帖/评论量，量化市场热度。
3.3 (官方动态追踪): 聚合CS2官方博客和更新日志，形成一个不错过重大动态的信息流。
史诗 4：投资组合仪表盘 (Epic 4: Portfolio Dashboard)
4.1 (资产视图): 提供一个简洁的界面，将资产清晰地分为“战略储备（持仓）”和“主战部队（交易）”两组。
4.2 (持仓管理): 在“战略储备”面板提供“+ 添加/编辑/删除”持仓的功能，允许用户管理饰品、录入成本、数量和日期。
4.3 (交易看板): 实时展示“主战部队”候选池中每个饰品在各平台的价差、成交量等关键交易数据。
4.4 (持仓看板): 实时展示“战略储备”列表中每个饰品的成本、现值、总价值和浮动盈亏。
4.5 (详情分析页): 点击任一饰品可进入详情页，展示跨平台价格聚合表、历史价格图表（可叠加移动平均线）和数据分析摘要。
4.6 (资产健康度诊断): (未来迭代) 为“战略储备”中的资产提供一个基于多维度（内容物价值、消耗指标、社区热度）的“健康度评分”。
4.7 (投资组合基准对比): (未来迭代) 将“战略储备”组合的收益率与市场基准指数进行对比。
4.8 (长期目标追踪): (未来迭代) 能设定并追踪“战略储备”的长期投资回报目标。
史诗 5：智能预警系统 (Epic 5: Intelligent Alerting System)
5.1 (预警规则配置): 提供一个预警配置页面，允许用户通过表单创建、编辑、启用/禁用预警规则。
5.2 (交易机会预警): 基于买卖价差、跨平台套利等规则，为“主战部队”提供交易机会通知。
5.3 (持仓风险预警): 基于价格跌幅、宏观指标恶化等规则，为“战略储备”提供风险警报。
5.4 (投资组合再平衡提醒): 当资产配置比例偏离预设值时，提醒用户进行再平衡操作。
史诗 6：(未来范围) 交易执行模块 (Epic 6: Future Scope - Execution Module)
6.1 (API密钥管理): 安全地配置和存储第三方交易平台API密钥。
6.2 (一键/自动执行): 在API支持下，实现一键下单或全自动的程序化交易。
4.0 非功能性需求 (Non-Functional Requirements)
性能 (Performance): 追踪器的数据刷新周期应可配置。系统应能稳定处理每日扫描和持续追踪的任务而不造成资源泄漏。
可靠性 (Reliability): 具备对数据源访问失败的重试和日志记录机制。
安全性 (Security): 严禁在代码或配置文件中明文存储任何API密钥或敏感信息。
合规性 (Compliance): “扫描器”和“追踪器”的请求行为必须尊重steamdt.com的robots.txt规则，并设置合理的请求延迟，避免对目标服务器造成不当压力。
5.0 最小可行产品 (MVP) 定义
MVP v1.0 的核心目标是验证“扫描器+追踪器”分层架构的可行性，并建立一个能同时反映两种投资策略的基础信息面板。
必须包含 (Must-Have):
完整的系统配置与管理功能 (Epic 1)。
一个可工作的“扫描器”（可先针对一个较小的、约200个物品的列表进行扫描）。
一个可工作的“追踪器”（能实现对持仓和交易列表的差异化频率更新）。
数据能成功写入SQLite数据库。
一个极简的仪表盘，能分开展示“持仓”和“交易”两个列表，并能显示由“追踪器”实时更新的价格数据（证明整个数据管道已跑通）。
完整的持仓管理功能 (User Story 4.2)。
至少一个宏观指标 (User Story 2.1)，以确立系统的长期分析视角。
可以包含 (Should-Have):
核心的预警功能 (User Stories 5.2, 5.3, 5.4)。
详情分析页及其历史图表 (User Story 4.5)。
暂不包含 (Won't-Have for v1.0):
高级分析功能 (如资产健康度评分、基准对比等)。
所有交易执行相关功能 (Epic 6)。
