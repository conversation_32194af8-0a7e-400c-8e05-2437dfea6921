# 产品需求文档 (PRD)：CS2混合策略投资助手 "Ares" 系统

**版本：** Final 3.0 (API约束优化版)  
**日期：** 2025年7月17日  
**产品负责人：** (您的名字)  
**产品经理：** Gemini  
**技术顾问：** Augment Agent

## 1.0 项目概述 (Project Overview)

### 1.1 项目愿景 (Vision)
构建一个自动化的、高效率的、数据驱动的CS2饰品投资决策辅助系统。该系统旨在通过整合steamdt.com官方API数据源、执行预设的混合投资策略、并以智能化的方式解决API频率限制，来提升决策效率、捕捉交易机会、管理长期风险，最终实现超越市场的资产增值。

### 1.2 核心问题 (Problem Statement)
手动追踪多个平台的饰品价格、计算价差、监控投资组合及宏观市场环境，既耗时又容易出错。更严峻的是，面对约1000个有价值饰品和steamdt.com API每分钟10次的调用限制，需要设计智能的分层监控策略，在有限资源内最大化监控效果，避免机会错失和系统性风险敞口扩大。

### 1.3 目标用户 (Target User)
一名具备资深开发能力的个人投资者（即您本人）。投资规模起步3万元，后期根据收益情况扩展。系统优先考虑架构的合理性、功能的健壮性、数据的准确性和策略的可扩展性，而非华丽的UI和多用户支持。

### 1.4 技术约束 (Technical Constraints)
- **API限制**：steamdt.com官方API每分钟10次调用，每日约14,400次
- **数据时效性**：关注饰品数据需在1小时内更新
- **监控规模**：约1000个经过筛选的有价值饰品
- **容错策略**：数据源不可用时系统暂停，不考虑降级运行

## 2.0 系统架构 (System Architecture)

### 2.1 核心设计理念 (Core Design Philosophy)
采用**"分层智能监控"**架构，将有限的API调用资源精准分配给最高价值的目标。通过用户筛选机制和动态优先级调整，在API约束内实现最大化的监控效果。

### 2.2 核心组件 (Core Components)

#### "发现器" (The Discoverer)
- **职责**：基于排行榜API的机会发现
- **频率**：每日2次（上午/下午各一次）
- **工作**：调用steamdt.com的4个排行榜接口（最热、最新、上涨、下跌），发现潜在投资机会

#### "追踪器" (The Tracker)
- **职责**：分层数据更新和监控
- **频率**：核心关注池每30分钟，主监控池每4小时
- **工作**：
  - **Tier 1 核心关注池**：30个用户重点关注饰品，每30分钟更新
  - **Tier 2 主监控池**：970个有价值饰品，每4小时轮换更新

#### "筛选器" (The Filter)
- **职责**：智能筛选和优先级管理
- **功能**：
  - 多维度筛选（价格区间、交易量、波动性）
  - 用户经验规则配置
  - 从主监控池向核心关注池的智能推荐

### 2.3 API资源管理策略
```
每日API调用预算：14,400次
├── 核心关注池（30个）：30 × 2 × 48 = 2,880次/天 (20%)
├── 主监控池（970个）：970 × 2 × 6 = 11,640次/天 (81%)
├── 排行榜发现：4 × 2 = 8次/天 (<1%)
└── 历史数据按需获取：剩余资源
```

### 2.4 数据流 (Data Flow)
```
[排行榜API] → [发现新目标] → [主监控池] → [用户筛选] → [核心关注池]
     ↓              ↓              ↓              ↓
[每日2次]      [自动添加]      [每4小时]      [每30分钟]
```

## 3.0 功能需求 (Functional Requirements)

### 史诗 1：系统配置与管理 (System Configuration & Management)
- **1.1 系统初始化配置**：提供系统设置页面，配置数据库路径、API密钥、通知服务等
- **1.2 API管理**：统一的API调用管理，包括频率控制、配额监控、自动限流
- **1.3 饰品列表管理**：管理1000个有价值饰品的主列表，支持导入/导出
- **1.4 系统状态监控**：实时显示API使用情况、系统运行状态、错误日志

### 史诗 2：数据引擎 (Data Engine)
- **2.1 发现器实现**：基于排行榜API的自动机会发现
- **2.2 追踪器实现**：分层监控策略，支持不同更新频率
- **2.3 数据持久化**：SQLite数据库存储，支持历史数据查询
- **2.4 智能筛选器**：多维度筛选功能，支持自定义规则

### 史诗 3：宏观市场雷达 (Macro Market Radar)
- **3.1 生态健康度监控**：CS2月/日平均在线玩家数趋势
- **3.2 社区活跃度追踪**：关键社区发帖/评论量统计
- **3.3 官方动态追踪**：CS2官方博客和更新日志聚合

### 史诗 4：投资组合仪表盘 (Portfolio Dashboard)
- **4.1 分层资产视图**：清晰区分"战略储备"和"主战部队"
- **4.2 持仓管理**：完整的CRUD操作，支持成本、数量、日期记录
- **4.3 交易看板**：实时展示核心关注池的价差、成交量等数据
- **4.4 持仓看板**：实时展示战略储备的成本、现值、浮动盈亏
- **4.5 详情分析页**：单饰品深度分析，包括历史图表和技术指标
- **4.6 智能筛选界面**：用户友好的筛选工具，支持条件保存

### 史诗 5：智能预警系统 (Intelligent Alerting System)
- **5.1 预警规则配置**：可视化的规则创建和管理界面
- **5.2 交易机会预警**：基于价差、套利机会的实时通知
- **5.3 持仓风险预警**：基于价格跌幅、宏观指标的风险警报
- **5.4 投资组合再平衡提醒**：资产配置偏离时的自动提醒

## 4.0 非功能性需求 (Non-Functional Requirements)

### 4.1 性能 (Performance)
- 核心关注池数据延迟不超过30分钟
- 主监控池数据延迟不超过4小时
- 系统响应时间不超过3秒
- 支持并发的数据更新和用户查询

### 4.2 可靠性 (Reliability)
- API调用失败自动重试机制（最多3次）
- 完整的错误日志记录和监控
- 数据库定期备份机制
- 系统异常时的优雅降级

### 4.3 安全性 (Security)
- API密钥加密存储，禁止明文保存
- 敏感配置信息的安全管理
- 数据库访问权限控制

### 4.4 合规性 (Compliance)
- 严格遵守steamdt.com API使用条款
- 实现智能限流，避免超出调用限制
- 尊重数据源的robots.txt规则

## 5.0 最小可行产品 (MVP) 定义

### MVP v1.0 核心目标
验证"分层智能监控"架构的可行性，建立完整的数据管道，提供基础的投资决策支持。

### 必须包含 (Must-Have)
1. **完整的系统配置管理** (Epic 1)
2. **30个饰品的核心关注池监控**（验证高频更新）
3. **200个饰品的主监控池**（验证中频更新，为后续扩展做准备）
4. **基础的智能筛选功能**
5. **SQLite数据库和数据持久化**
6. **简化的投资组合仪表盘**
7. **基础的持仓管理功能**
8. **至少一个宏观指标**（CS2玩家在线数）

### 可以包含 (Should-Have)
1. **核心预警功能**（价格变动、交易机会）
2. **详情分析页和历史图表**
3. **排行榜API集成**

### 暂不包含 (Won't-Have for v1.0)
1. **高级分析功能**（资产健康度评分、基准对比）
2. **社区和官方动态追踪**
3. **交易执行相关功能**

---

**文档状态**：已完成核心架构设计，待开发团队评审和实施
