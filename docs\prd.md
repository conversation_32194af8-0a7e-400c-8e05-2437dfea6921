# CS2饰品投资系统产品需求文档 (PRD)

**版本**: v1.0  
**创建日期**: 2025年7月17日  
**产品名称**: CS2饰品投资系统 (CSGOAres)  
**目标用户**: 个人CS2饰品投资者  

## 📋 产品概述

### 1.1 产品定位
CS2饰品投资系统是一个专业的Counter-Strike 2饰品投资决策支持平台，通过自动化数据收集、智能分析和实时监控，帮助投资者在CS2饰品市场中做出更明智的投资决策。

### 1.2 核心价值主张
- **自动化监控**: 7x24小时自动监控1000+饰品价格变化
- **智能发现**: 基于多维度排行榜自动发现投资机会
- **数据驱动**: 提供跨平台价格比较和套利分析
- **风险管理**: 智能预警系统和投资组合管理

### 1.3 目标用户画像
- **主要用户**: 具备技术背景的个人投资者
- **投资规模**: 起步资金3万元，后期可扩展
- **技术水平**: 熟悉开发工具，追求数据全面性
- **使用场景**: 长时间盯盘，需要高信息密度的界面

## 🎯 核心功能模块

### 2.1 发现器 (Discoverer)
**功能描述**: 基于steamdt.com核心排行榜数据，通过多维度分析自动发现高质量投资机会

**核心特性**:
- 集成5种核心排行榜：
  - 在售价涨幅榜/跌幅榜 (价格动量指标)
  - 成交榜 (流动性指标)
  - 热度榜 (市场关注度指标)
  - 热度上升榜 (趋势动量指标)
- 多维度机会评分算法：
  ```
  机会评分 = 价格动量(40%) + 流动性(30%) + 热度动量(20%) + 基础热度(10%)
  ```
- 每日2次自动扫描，目标发现准确率60-70%
- 智能去重和优先级排序
- 趋势强度预测和持续时间评估

**技术要求**:
- API调用频率: 每日10次 (5个榜单 × 2次)
- 数据存储: 保留30天历史排行榜数据用于趋势分析
- 响应时间: 单次发现任务<3分钟
- 准确率目标: 机会识别准确率>60%

### 2.2 追踪器 (Tracker)
**功能描述**: 三层智能监控体系，支持全市场饰品监控，用户关注饰品优先更新

**三层监控架构**:

**第一层: 用户关注池 (User Focus Pool)**
- **定义**: 用户主动添加关注的饰品，无数量限制
- **更新频率**: 每5分钟更新一次
- **API分配**: 30%资源 (约69,120次/天)
- **支持容量**: 500-1000个用户关注饰品

**第二层: 热门发现池 (Hot Discovery Pool)**
- **定义**: 基于排行榜自动发现的热门饰品
- **更新频率**: 每30分钟更新一次
- **API分配**: 50%资源 (约115,200次/天)
- **动态容量**: 2000-3000个热门饰品

**第三层: 全市场扫描池 (Market Scan Pool)**
- **定义**: 对全市场所有饰品进行低频扫描
- **更新频率**: 每4-8小时轮换更新
- **API分配**: 20%资源 (约46,080次/天)
- **覆盖范围**: 全市场所有饰品 (10,000+个)

**API资源能力**:
- 每日API调用能力: 230,400次 (每分钟160个饰品)
  - 单个查询: 60次/分钟
  - 批量查询: 1次/分钟 (100个饰品)
- 饰品基础信息: 每日1次全量更新
- 智能资源分配算法，根据用户关注度动态调整

**智能调度特性**:
- 用户关注饰品优先保障
- 热门饰品自动发现和监控
- 全市场覆盖，不错过任何机会
- 失败重试和降级策略

### 2.3 SteamDT API集成
**功能描述**: 与steamdt.com官方开放平台深度集成，获取全面的饰品数据

**核心API接口**:
- **饰品基础信息**: `/open/cs2/v1/base` - 每日1次，获取全量饰品库
- **单个价格查询**: `/open/cs2/v1/price/single` - 每分钟60次
- **批量价格查询**: `/open/cs2/v1/price/batch` - 每分钟1次，最多100个饰品
- **历史均价查询**: `/open/cs2/v1/price/avg` - 获取7天均价数据

**支持平台数据**:
- Steam市场 (在售价、求购价、数量)
- BUFF163 (在售价、求购价、数量)
- C5Game (在售价、求购价、数量)
- 其他主流交易平台

**API能力优势**:
- **高并发**: 每分钟160个饰品查询能力
- **全覆盖**: 支持全市场所有CS2饰品
- **实时性**: 官方数据源，更新及时
- **准确性**: 直接对接官方API，数据权威

**智能调用策略**:
- 批量优先: 优先使用批量接口提高效率
- 智能缓存: 避免重复查询相同饰品
- 错误重试: 自动重试失败的API调用
- 配额管理: 实时监控API使用情况，智能分配资源

**数据质量保证**:
- 实时数据验证和异常检测
- 多平台数据一致性检查
- 历史数据对比验证

### 2.4 智能筛选器
**功能描述**: 两阶段智能决策系统 - 初步筛选 + AI深度分析

#### **第一阶段: 初步筛选系统**
**目标**: 从全市场10,000+个饰品中快速筛选出候选投资标的

**核心筛选维度**:
- **流动性评分**: 基于成交榜排名、持有人数分布、存世量稀缺度
- **趋势强度**: 结合涨跌幅榜、热度上升榜的综合趋势评估
- **价格动量**: 基于涨跌幅榜的价格变化动量分析
- **市场热度**: 基于热度榜和热度上升榜的关注度分析
- **供需平衡**: 基于新增额/新增量比例的供给结构分析

**初步筛选算法**:
```
初筛评分 = 价格动量(40%) + 流动性(30%) + 热度动量(20%) + 基础热度(10%)
```
**筛选结果**: 每日筛选出100-200个候选饰品进入第二阶段

#### **第二阶段: AI深度分析系统**
**目标**: 对候选饰品进行深度分析，生成最终投资建议

**AI Agent设计**:
- **数据输入**: 完整保留所有原始数据，包括：
  - 多平台价格数据 (在售价、求购价、数量)
  - 历史价格趋势数据
  - 排行榜排名变化数据
  - 宏观市场状态数据
  - 技术指标计算结果
  - 用户历史投资偏好数据

**AI分析维度**:
- **深度趋势分析**: 基于历史数据预测价格走势
- **市场情绪分析**: 结合多维度数据判断市场情绪
- **风险评估**: 综合流动性、波动性、市场环境评估风险
- **时机分析**: 判断最佳买入/卖出时机
- **个性化推荐**: 基于用户投资风格和历史表现

**数据保留策略**:
- **完整性**: 保留所有原始API数据，不做任何删减
- **结构化**: 标准化数据格式，便于AI模型处理
- **时序性**: 保留时间序列数据，支持趋势分析
- **关联性**: 保留饰品间的关联关系数据

**AI决策输出**:
- **投资建议**: 买入/卖出/持有，附带置信度
- **风险评级**: A/B/C/D级风险评估
- **预期收益**: 基于模型预测的收益区间
- **持有建议**: 短期/中期/长期持有策略
- **决策依据**: 详细的分析理由和数据支撑

**预期效果**:
- 初筛效率提升90% (从全市场快速定位候选标的)
- AI分析准确率目标70-80%
- 决策质量显著提升，降低人为判断偏差

### 2.5 数据管理和AI支持系统
**功能描述**: 为AI深度分析提供完整、结构化的数据支持

#### **原始数据完整保留策略**
**核心原则**: 所有从SteamDT API获取的原始数据必须完整保留，不做任何删减或聚合

**数据保留范围**:
- **价格数据**: 每个平台的在售价、求购价、数量、更新时间
- **排行榜数据**: 各榜单的完整排名信息和历史变化
- **宏观数据**: 大盘指数、成交额、成交量、新增数据等
- **饰品基础信息**: 完整的饰品属性和分类信息
- **时间序列数据**: 所有数据的历史变化记录

#### **数据结构化设计**
**为AI模型优化的数据格式**:
```json
{
  "item_data": {
    "market_hash_name": "AK-47 | Redline (Field-Tested)",
    "basic_info": {...},
    "price_data": {
      "platforms": [...],
      "history": [...],
      "technical_indicators": {...}
    },
    "ranking_data": {
      "current_rankings": {...},
      "ranking_history": [...],
      "trend_analysis": {...}
    },
    "market_context": {
      "macro_indicators": {...},
      "market_state": "...",
      "correlation_data": {...}
    }
  }
}
```

#### **AI Agent数据接口**
**标准化数据输入接口**:
- **实时数据API**: 提供最新的饰品数据
- **历史数据API**: 提供指定时间范围的历史数据
- **关联数据API**: 提供相关饰品和市场数据
- **用户数据API**: 提供用户投资历史和偏好数据

**数据质量保证**:
- **完整性检查**: 确保所有必要字段都有数据
- **一致性验证**: 验证跨平台数据的一致性
- **时效性监控**: 监控数据更新的及时性
- **异常检测**: 识别和标记异常数据

#### **AI模型支持功能**
**模型训练数据准备**:
- **特征工程**: 自动生成AI模型所需的特征
- **数据标注**: 基于历史投资结果进行数据标注
- **样本平衡**: 确保训练数据的平衡性
- **数据增强**: 通过数据变换增加训练样本

**模型推理支持**:
- **实时特征计算**: 为实时推理计算最新特征
- **批量预测**: 支持批量饰品的预测分析
- **结果解释**: 提供AI决策的可解释性分析
- **反馈循环**: 收集用户反馈优化模型

## 🌍 宏观市场雷达

### 3.1 市场状态智能识别系统
**基于SteamDT宏观数据的市场状态矩阵**:

| 大盘指数 | 成交额变化 | 成交量变化 | 市场状态 | 投资策略 |
|---------|-----------|-----------|---------|---------|
| ↑ | ↑ | ↑ | 强势牛市 | 积极买入热门品种 |
| ↑ | ↑ | → | 温和牛市 | 选择性买入优质品种 |
| → | ↑ | ↓ | 资金推动 | 关注大额交易品种 |
| ↓ | ↓ | ↓ | 熊市确认 | 保守持仓，等待机会 |
| ↓ | ↑ | ↑ | 抄底机会 | 精选优质品种抄底 |

**实时监控指标**:
- 大盘指数变化趋势
- 成交额/成交量环比分析
- 新增额/新增量比例监控
- 存世量和持有人数统计

### 3.2 价量背离预警系统
**核心逻辑**: 监控成交额增长与成交量增长的背离情况
```
价量背离指数 = (成交额增长率 - 成交量增长率) / 成交量增长率
```
**预警阈值**:
- 轻度背离: 200-500% (关注)
- 中度背离: 500-800% (预警)
- 重度背离: >800% (高度关注)

**当前市场示例**: 成交额+37.12% vs 成交量+4% = 828%重度背离

### 3.3 供需失衡分析
**监控指标**:
- 新增饰品价值变化 (新增额环比)
- 新增饰品数量变化 (新增量环比)
- 供给结构分析和投资机会识别

**分析逻辑**:
- 新增额↓ + 新增量↑ = 低价值饰品供给过剩，高价值饰品稀缺性增强
- 新增额↑ + 新增量↓ = 高价值饰品供给增加，市场可能降温
- 新增额↑ + 新增量↑ = 市场整体供给增加，需谨慎投资

### 3.4 CS2玩家活跃度监控
**数据源**: SteamDT宏观数据 (包含在线玩家数)
- 在线玩家数趋势监控 (基于SteamDT数据)
- 与历史同期对比分析
- 玩家活跃度对市场影响的关联分析
- **说明**: 使用SteamDT提供的玩家数据，虽然可能不如官方API及时，但足够用于趋势分析和投资参考

## 📊 投资组合仪表盘

### 4.1 分层资产视图
**战略储备**: 长期持有的核心资产
- 实时估值和盈亏分析
- 历史表现追踪
- 风险评估指标

**主战部队**: 活跃交易的饰品
- 短期价格波动监控
- 交易机会提醒
- 流动性评分

### 4.2 实时交易看板
- 跨平台价格实时对比
- 套利机会实时提醒
- 交易执行建议

### 4.3 持仓管理
- 饰品详细信息管理
- 成本记录和盈亏计算
- 持仓结构分析和优化建议

### 4.4 历史表现分析
- 投资组合历史收益率
- 基准对比 (大盘指数)
- 风险调整收益分析

## ⚠️ 智能预警系统

### 5.1 价格预警
- 价格突破预设阈值
- 异常价格波动检测
- 跨平台价差预警

### 5.2 交易机会预警
- 套利机会实时提醒
- 热度上升趋势预警
- 流动性变化提醒

### 5.3 风险预警
- 持仓集中度风险
- 市场流动性风险
- 价格下跌风险

### 5.4 投资组合预警
- 再平衡提醒
- 止损建议
- 获利了结提醒

## 🔧 技术架构要求

### 6.1 系统架构
- **前端**: Streamlit Web应用
- **后端**: Python服务架构
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **缓存**: Redis
- **调度**: APScheduler

### 6.2 性能要求
- 核心关注池数据延迟: <30分钟
- 主监控池数据延迟: <4小时
- 系统响应时间: <3秒
- 系统可用性: >99%

### 6.3 安全要求
- API密钥加密存储
- 敏感数据保护
- 访问日志记录
- 错误处理和恢复

## 📈 MVP定义

### MVP v1.0 必须包含:
1. ✅ 完整的系统配置管理和SteamDT API集成
2. ✅ 用户关注池无限制监控 (5分钟更新)
3. ✅ 热门发现池自动监控 (2000-3000个饰品，30分钟更新)
4. ✅ 全市场扫描池覆盖 (10,000+个饰品，4-8小时更新)
5. ✅ 两阶段智能决策系统:
   - 第一阶段: 基于多维度数据的初步筛选
   - 第二阶段: AI Agent深度分析和投资建议
6. ✅ 完整数据保留系统 (支持AI模型的原始数据需求)
7. ✅ SQLite数据库和全饰品数据持久化
8. ✅ 实时投资组合仪表盘 (支持无限制持仓)
9. ✅ 完整的持仓管理功能 (成本追踪、盈亏计算)
10. ✅ 宏观市场雷达 (市场状态识别、价量背离分析)
11. ✅ AI模型数据接口和推理支持系统

### MVP v1.0 暂不包含:
- AI模型的自动训练和优化 (使用预训练模型)
- 高级分析功能 (资产健康度评分、基准对比)
- 社区和官方动态追踪
- 交易执行相关功能
- 移动端支持

### MVP v2.0 规划 (AI增强版):
- AI模型自动训练和持续优化
- 基于强化学习的投资策略优化
- 多模型集成和模型选择
- 用户反馈驱动的模型改进
- 高级AI分析功能 (情绪分析、关联分析等)

## 🎨 用户体验要求

### 7.1 界面设计
- **主题**: CS2游戏风格的暗色主题
- **信息密度**: 高信息密度，支持长时间盯盘
- **响应式**: 支持不同屏幕尺寸
- **可访问性**: 键盘导航和屏幕阅读器支持

### 7.2 交互设计
- 直观的导航结构
- 快速的数据筛选和排序
- 实时数据更新提示
- 友好的错误处理和反馈

## 📊 成功指标

### 核心业务指标:
- **监控规模突破**: 从1000个饰品扩展到全市场10,000+个饰品 (提升10倍)
- **发现效率提升**: 从手动4-6小时筛选降至30分钟自动筛选 (提升90%)
- **响应速度**: 用户关注饰品5分钟内获得最新价格更新
- **投资准确率**: 机会识别准确率从30-40%提升至70-80%
- **风险控制**: 通过流动性评分避免90%的流动性风险

### 量化收益指标:
- 用户投资收益率 > 市场大盘指数表现
- 机会发现准确率 > 70%
- 用户关注饰品更新及时性 > 95% (5分钟内)
- 全市场覆盖率 > 90% (10,000+个饰品)
- 流动性风险避免率 > 90%

### 技术性能指标:
- API调用成功率 > 99%
- 数据更新及时性 > 95%
- 系统稳定性 > 99%
- 核心功能响应时间 < 3秒

### 用户体验指标:
- 用户日活跃时长 > 2小时
- 功能使用满意度 > 4.5/5
- 投资决策支持有效性 > 80%

## 🚀 发布计划

### Phase 1 (MVP): 4周
- 核心监控功能
- 基础UI界面
- 数据库和API集成

### Phase 2 (增强): 6周
- 智能筛选和推荐
- 完整的预警系统
- 性能优化

### Phase 3 (完善): 4周
- 高级分析功能
- 用户体验优化
- 部署和运维

---

**文档维护**: 本文档将随产品迭代持续更新，确保与实际功能保持一致。
