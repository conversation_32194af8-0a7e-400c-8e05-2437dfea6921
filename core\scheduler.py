"""
Ares系统智能调度器
实现基于优先级的智能调度算法，管理核心关注池和主监控池的差异化更新策略
"""

import asyncio
import json
import logging
import heapq
from typing import Dict, List, Optional, Tuple, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
from contextlib import asynccontextmanager

from .database import DatabaseManager, WatchlistItem, Item, Price
from .priority_calculator import get_priority_calculator, PriorityFactors
from .api_manager import APIManager, APICallResult
from .config import get_config_manager
from .exceptions import SchedulerError, ErrorContext, ErrorSeverity

logger = logging.getLogger(__name__)


@dataclass
class ScheduleTask:
    """调度任务"""
    item_id: str
    pool_type: str  # 'core' 或 'main'
    priority_score: float
    next_update_time: datetime
    update_frequency: int  # 分钟
    retry_count: int = 0
    
    def __lt__(self, other):
        """优先队列排序：优先级高的和更新时间早的优先"""
        if self.next_update_time != other.next_update_time:
            return self.next_update_time < other.next_update_time
        return self.priority_score > other.priority_score


@dataclass
class SchedulerState:
    """调度器状态"""
    last_core_update: Optional[datetime] = None
    last_main_update: Optional[datetime] = None
    core_pool_items: Set[str] = None
    main_pool_items: Set[str] = None
    total_api_calls_today: int = 0
    last_reset_date: Optional[str] = None
    
    def __post_init__(self):
        if self.core_pool_items is None:
            self.core_pool_items = set()
        if self.main_pool_items is None:
            self.main_pool_items = set()


class IntelligentScheduler:
    """智能调度器"""
    
    def __init__(self, db_manager: DatabaseManager, api_manager: APIManager):
        """
        初始化智能调度器
        
        Args:
            db_manager: 数据库管理器
            api_manager: API管理器
        """
        self.db_manager = db_manager
        self.api_manager = api_manager
        self.priority_calculator = get_priority_calculator()
        self.config = get_config_manager()
        
        # 调度配置
        self.core_pool_size = self.config.core_pool_size  # 30
        self.main_pool_size = self.config.main_pool_size  # 970
        self.core_update_interval = self.config.core_update_interval  # 30分钟
        self.main_update_interval = self.config.main_update_interval  # 240分钟
        
        # 调度状态
        self.state = SchedulerState()
        self.task_queue: List[ScheduleTask] = []
        self.running = False
        self.state_file = Path("data/scheduler_state.json")
        
        # 加载持久化状态
        self._load_state()
        
        logger.info("Intelligent scheduler initialized")
    
    async def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("Scheduler is already running")
            return
        
        self.running = True
        logger.info("Starting intelligent scheduler")
        
        try:
            # 初始化调度队列
            await self._initialize_schedule_queue()
            
            # 启动调度循环
            await self._schedule_loop()
            
        except Exception as e:
            logger.error("Error in scheduler: %s", str(e))
            self.running = False
            raise SchedulerError(
                message=f"Scheduler failed to start: {str(e)}",
                context=ErrorContext(operation="scheduler_start"),
                severity=ErrorSeverity.HIGH
            )
    
    async def stop(self):
        """停止调度器"""
        logger.info("Stopping intelligent scheduler")
        self.running = False
        self._save_state()
    
    async def _schedule_loop(self):
        """主调度循环"""
        while self.running:
            try:
                # 检查是否有需要执行的任务
                if self.task_queue and self.task_queue[0].next_update_time <= datetime.utcnow():
                    task = heapq.heappop(self.task_queue)
                    await self._execute_task(task)
                
                # 定期重新评估优先级和调整池分配
                await self._periodic_maintenance()
                
                # 短暂休眠避免CPU占用过高
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error("Error in schedule loop: %s", str(e))
                await asyncio.sleep(60)  # 出错时等待更长时间
    
    async def _initialize_schedule_queue(self):
        """初始化调度队列"""
        logger.info("Initializing schedule queue")
        
        async with self.db_manager.get_session() as session:
            # 获取所有活跃的监控项
            watchlist_items = await self.db_manager.get_active_watchlist_items(session)
            
            for item in watchlist_items:
                # 计算下次更新时间
                if item.last_updated:
                    next_update = item.last_updated + timedelta(minutes=item.update_frequency or self.main_update_interval)
                else:
                    next_update = datetime.utcnow()
                
                # 创建调度任务
                task = ScheduleTask(
                    item_id=item.item_id,
                    pool_type=item.pool_type,
                    priority_score=item.priority_score or 0.0,
                    next_update_time=next_update,
                    update_frequency=item.update_frequency or self.main_update_interval
                )
                
                heapq.heappush(self.task_queue, task)
        
        logger.info("Schedule queue initialized with %d tasks", len(self.task_queue))
    
    async def _execute_task(self, task: ScheduleTask):
        """执行调度任务"""
        logger.debug("Executing task for item %s (pool: %s)", task.item_id, task.pool_type)
        
        try:
            # 检查API限制
            if not await self._check_api_limit():
                # API限制达到，重新调度任务
                task.next_update_time = datetime.utcnow() + timedelta(minutes=10)
                heapq.heappush(self.task_queue, task)
                return
            
            # 调用API更新数据
            success = await self._update_item_data(task.item_id)
            
            if success:
                # 更新成功，重置重试计数
                task.retry_count = 0
                
                # 重新计算优先级
                await self._recalculate_item_priority(task.item_id)
                
                # 更新数据库中的最后更新时间
                await self._update_last_updated(task.item_id)
                
            else:
                # 更新失败，增加重试计数
                task.retry_count += 1
                
                # 如果重试次数过多，降低优先级
                if task.retry_count >= 3:
                    task.priority_score *= 0.8
                    await self._update_failure_count(task.item_id, task.retry_count)
            
            # 计算下次更新时间
            task.next_update_time = datetime.utcnow() + timedelta(minutes=task.update_frequency)
            
            # 重新加入队列
            heapq.heappush(self.task_queue, task)
            
        except Exception as e:
            logger.error("Error executing task for item %s: %s", task.item_id, str(e))
            
            # 出错时延迟重试
            task.next_update_time = datetime.utcnow() + timedelta(minutes=30)
            task.retry_count += 1
            heapq.heappush(self.task_queue, task)
    
    async def _update_item_data(self, item_id: str) -> bool:
        """更新饰品数据"""
        try:
            # 这里应该调用API获取最新数据
            # 暂时模拟API调用
            response = await self.api_manager.get_item_price(item_id)
            
            if response.status == APICallResult.SUCCESS:
                # 更新API调用计数
                self.state.total_api_calls_today += 1
                self._save_state()
                return True
            else:
                logger.warning("API call failed for item %s: %s", item_id, response.error_message)
                return False
                
        except Exception as e:
            logger.error("Error updating item data for %s: %s", item_id, str(e))
            return False
    
    async def _check_api_limit(self) -> bool:
        """检查API限制"""
        # 检查今日API调用次数
        today = datetime.utcnow().date().isoformat()
        if self.state.last_reset_date != today:
            self.state.total_api_calls_today = 0
            self.state.last_reset_date = today
        
        # 每日最大调用次数：10次/分钟 * 60分钟 * 24小时 = 14400次
        daily_limit = self.config.api_calls_per_minute * 60 * 24
        
        return self.state.total_api_calls_today < daily_limit
    
    async def _recalculate_item_priority(self, item_id: str):
        """重新计算饰品优先级"""
        async with self.db_manager.get_session() as session:
            # 获取饰品数据
            item_data = await self._get_item_data_for_priority(session, item_id)
            
            if item_data:
                # 计算新的优先级
                new_priority = self.priority_calculator.calculate_priority_score(
                    self.priority_calculator._extract_factors_from_data(item_data)
                )
                
                # 更新数据库
                await self.db_manager.update_watchlist_priority(session, item_id, new_priority)
    
    async def _periodic_maintenance(self):
        """定期维护任务"""
        now = datetime.utcnow()
        
        # 每小时执行一次维护
        if (not hasattr(self, '_last_maintenance') or 
            now - self._last_maintenance > timedelta(hours=1)):
            
            logger.info("Performing periodic maintenance")
            
            # 重新平衡池分配
            await self._rebalance_pools()
            
            # 清理过期任务
            await self._cleanup_expired_tasks()
            
            # 保存状态
            self._save_state()
            
            self._last_maintenance = now
    
    async def _rebalance_pools(self):
        """重新平衡池分配"""
        async with self.db_manager.get_session() as session:
            # 获取所有监控项按优先级排序
            all_items = await self.db_manager.get_watchlist_items_by_priority(session)
            
            # 重新分配核心池
            core_candidates = all_items[:self.core_pool_size]
            main_candidates = all_items[self.core_pool_size:]
            
            # 更新池分配
            for item in core_candidates:
                if item.pool_type != 'core':
                    await self.db_manager.update_pool_type(session, item.item_id, 'core')
                    await self.db_manager.update_update_frequency(session, item.item_id, self.core_update_interval)
            
            for item in main_candidates:
                if item.pool_type != 'main':
                    await self.db_manager.update_pool_type(session, item.item_id, 'main')
                    await self.db_manager.update_update_frequency(session, item.item_id, self.main_update_interval)
    
    async def _cleanup_expired_tasks(self):
        """清理过期任务"""
        # 移除队列中已经不在监控列表中的任务
        valid_tasks = []
        
        async with self.db_manager.get_session() as session:
            active_item_ids = await self.db_manager.get_active_item_ids(session)
            
            for task in self.task_queue:
                if task.item_id in active_item_ids:
                    valid_tasks.append(task)
        
        self.task_queue = valid_tasks
        heapq.heapify(self.task_queue)
    
    def _load_state(self):
        """加载持久化状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                self.state.last_core_update = (
                    datetime.fromisoformat(state_data['last_core_update'])
                    if state_data.get('last_core_update') else None
                )
                self.state.last_main_update = (
                    datetime.fromisoformat(state_data['last_main_update'])
                    if state_data.get('last_main_update') else None
                )
                self.state.core_pool_items = set(state_data.get('core_pool_items', []))
                self.state.main_pool_items = set(state_data.get('main_pool_items', []))
                self.state.total_api_calls_today = state_data.get('total_api_calls_today', 0)
                self.state.last_reset_date = state_data.get('last_reset_date')
                
                logger.info("Scheduler state loaded from %s", self.state_file)
        except Exception as e:
            logger.warning("Failed to load scheduler state: %s", str(e))
    
    def _save_state(self):
        """保存持久化状态"""
        try:
            # 确保目录存在
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            state_data = {
                'last_core_update': self.state.last_core_update.isoformat() if self.state.last_core_update else None,
                'last_main_update': self.state.last_main_update.isoformat() if self.state.last_main_update else None,
                'core_pool_items': list(self.state.core_pool_items),
                'main_pool_items': list(self.state.main_pool_items),
                'total_api_calls_today': self.state.total_api_calls_today,
                'last_reset_date': self.state.last_reset_date
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            logger.debug("Scheduler state saved to %s", self.state_file)
        except Exception as e:
            logger.error("Failed to save scheduler state: %s", str(e))
    
    async def add_item_to_schedule(self, item_id: str, pool_type: str = 'main', priority_score: float = 0.0):
        """添加饰品到调度队列"""
        update_frequency = self.core_update_interval if pool_type == 'core' else self.main_update_interval
        
        task = ScheduleTask(
            item_id=item_id,
            pool_type=pool_type,
            priority_score=priority_score,
            next_update_time=datetime.utcnow(),
            update_frequency=update_frequency
        )
        
        heapq.heappush(self.task_queue, task)
        logger.info("Added item %s to schedule (pool: %s)", item_id, pool_type)
    
    async def remove_item_from_schedule(self, item_id: str):
        """从调度队列移除饰品"""
        self.task_queue = [task for task in self.task_queue if task.item_id != item_id]
        heapq.heapify(self.task_queue)
        logger.info("Removed item %s from schedule", item_id)
    
    async def force_update_item(self, item_id: str):
        """强制更新指定饰品"""
        # 移除现有任务
        await self.remove_item_from_schedule(item_id)
        
        # 添加立即执行的任务
        async with self.db_manager.get_session() as session:
            watchlist_item = await self.db_manager.get_watchlist_item(session, item_id)
            if watchlist_item:
                await self.add_item_to_schedule(
                    item_id, 
                    watchlist_item.pool_type, 
                    watchlist_item.priority_score or 0.0
                )
    
    async def _get_item_data_for_priority(self, session, item_id: str) -> Optional[Dict[str, Any]]:
        """获取用于优先级计算的饰品数据"""
        try:
            # 获取监控项信息
            watchlist_item = await self.db_manager.get_watchlist_item(session, item_id)
            if not watchlist_item:
                return None

            # 获取最新价格数据
            latest_price = await self.db_manager.get_latest_price(session, item_id)

            # 计算价差和波动率
            price_spread = 0.0
            volatility = 0.0

            if latest_price and latest_price.ask_price and latest_price.bid_price:
                price_spread = ((latest_price.ask_price - latest_price.bid_price) / latest_price.bid_price) * 100

            # 获取历史价格计算波动率
            historical_prices = await self.db_manager.get_price_history(session, item_id, days=7)
            if len(historical_prices) > 1:
                prices = [p.last_price for p in historical_prices if p.last_price]
                if prices:
                    import statistics
                    volatility = statistics.stdev(prices) / statistics.mean(prices) * 100

            return {
                'item_id': item_id,
                'price_spread': price_spread,
                'volume_24h': latest_price.volume_24h if latest_price else 0,
                'volatility': volatility,
                'user_added': watchlist_item.user_added,
                'user_priority': watchlist_item.user_priority,
                'last_updated': watchlist_item.last_updated,
                'failure_count': watchlist_item.failure_count,
                'pool_type': watchlist_item.pool_type
            }

        except Exception as e:
            logger.error("Error getting item data for priority calculation: %s", str(e))
            return None

    async def _update_last_updated(self, item_id: str):
        """更新最后更新时间"""
        async with self.db_manager.get_session() as session:
            await self.db_manager.update_watchlist_last_updated(session, item_id, datetime.utcnow())

    async def _update_failure_count(self, item_id: str, failure_count: int):
        """更新失败计数"""
        async with self.db_manager.get_session() as session:
            await self.db_manager.update_watchlist_failure_count(session, item_id, failure_count)

    async def adjust_item_priority(self, item_id: str, user_priority: int):
        """手动调整饰品优先级"""
        async with self.db_manager.get_session() as session:
            # 更新用户优先级
            await self.db_manager.update_user_priority(session, item_id, user_priority)

            # 重新计算优先级
            await self._recalculate_item_priority(item_id)

            # 如果饰品在队列中，更新其优先级
            for task in self.task_queue:
                if task.item_id == item_id:
                    watchlist_item = await self.db_manager.get_watchlist_item(session, item_id)
                    if watchlist_item:
                        task.priority_score = watchlist_item.priority_score or 0.0
                    break

            # 重新堆化队列
            heapq.heapify(self.task_queue)

            logger.info("Adjusted priority for item %s to %d", item_id, user_priority)

    async def get_pool_statistics(self) -> Dict[str, Any]:
        """获取池统计信息"""
        async with self.db_manager.get_session() as session:
            core_items = await self.db_manager.get_pool_items(session, 'core')
            main_items = await self.db_manager.get_pool_items(session, 'main')

            # 计算平均优先级
            core_avg_priority = sum(item.priority_score or 0 for item in core_items) / len(core_items) if core_items else 0
            main_avg_priority = sum(item.priority_score or 0 for item in main_items) / len(main_items) if main_items else 0

            return {
                'core_pool': {
                    'size': len(core_items),
                    'target_size': self.core_pool_size,
                    'avg_priority': round(core_avg_priority, 2),
                    'update_interval': self.core_update_interval
                },
                'main_pool': {
                    'size': len(main_items),
                    'target_size': self.main_pool_size,
                    'avg_priority': round(main_avg_priority, 2),
                    'update_interval': self.main_update_interval
                }
            }

    def get_schedule_status(self) -> Dict[str, Any]:
        """获取调度状态"""
        return {
            'running': self.running,
            'queue_size': len(self.task_queue),
            'next_task_time': self.task_queue[0].next_update_time.isoformat() if self.task_queue else None,
            'api_calls_today': self.state.total_api_calls_today,
            'core_pool_size': len(self.state.core_pool_items),
            'main_pool_size': len(self.state.main_pool_items),
            'last_maintenance': getattr(self, '_last_maintenance', None)
        }


# 全局调度器实例
_scheduler: Optional[IntelligentScheduler] = None


async def get_scheduler() -> IntelligentScheduler:
    """获取全局调度器实例"""
    global _scheduler
    if _scheduler is None:
        from .database import get_database_manager
        from .api_manager import get_api_manager
        
        db_manager = get_database_manager()
        api_manager = await get_api_manager()
        _scheduler = IntelligentScheduler(db_manager, api_manager)
    
    return _scheduler
