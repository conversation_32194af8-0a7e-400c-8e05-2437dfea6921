"""
Ares投资组合页面
显示投资组合概览、持仓详情和绩效分析
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

from app.utils.state_manager import StateManager


def show_portfolio_page():
    """显示投资组合页面"""
    st.markdown('<h1 class="section-title">💼 投资组合</h1>', unsafe_allow_html=True)
    
    # 获取状态管理器
    state_manager = StateManager()
    
    # 页面选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["📊 概览", "📋 持仓详情", "📈 绩效分析", "⚖️ 资产配置"])
    
    with tab1:
        show_portfolio_overview(state_manager)
    
    with tab2:
        show_holdings_detail(state_manager)
    
    with tab3:
        show_performance_analysis(state_manager)
    
    with tab4:
        show_asset_allocation(state_manager)


def show_portfolio_overview(state_manager: StateManager):
    """显示投资组合概览"""
    st.markdown("### 📊 投资组合概览")
    
    portfolio_data = state_manager.get_portfolio_data()
    
    # 关键指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="总价值",
            value=f"${portfolio_data['total_value']:,.2f}",
            delta=f"${portfolio_data['total_profit']:+,.2f}"
        )
    
    with col2:
        st.metric(
            label="总成本",
            value=f"${portfolio_data['total_cost']:,.2f}",
            delta=None
        )
    
    with col3:
        st.metric(
            label="总盈亏",
            value=f"${portfolio_data['total_profit']:+,.2f}",
            delta=f"{portfolio_data['profit_percentage']:+.2f}%"
        )
    
    with col4:
        st.metric(
            label="持仓数量",
            value=len(portfolio_data['holdings']),
            delta="+1"
        )
    
    st.divider()
    
    # 投资组合分布图
    col1, col2 = st.columns(2)
    
    with col1:
        # 按价值分布
        df = pd.DataFrame(portfolio_data['holdings'])
        
        fig = px.pie(
            df,
            values='total_value',
            names='name',
            title="按价值分布",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 盈亏分布
        fig = px.bar(
            df,
            x='name',
            y='profit_loss',
            color='profit_percentage',
            title="盈亏分布",
            color_continuous_scale='RdYlGn'
        )
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white',
            xaxis_tickangle=-45
        )
        
        st.plotly_chart(fig, use_container_width=True)


def show_holdings_detail(state_manager: StateManager):
    """显示持仓详情"""
    st.markdown("### 📋 持仓详情")
    
    portfolio_data = state_manager.get_portfolio_data()
    holdings_df = pd.DataFrame(portfolio_data['holdings'])
    
    # 添加计算列
    holdings_df['盈亏率'] = holdings_df['profit_percentage'].apply(lambda x: f"{x:+.2f}%")
    holdings_df['当前价格'] = holdings_df['current_price'].apply(lambda x: f"${x:.2f}")
    holdings_df['平均成本'] = holdings_df['avg_cost'].apply(lambda x: f"${x:.2f}")
    holdings_df['总价值'] = holdings_df['total_value'].apply(lambda x: f"${x:,.2f}")
    holdings_df['盈亏金额'] = holdings_df['profit_loss'].apply(lambda x: f"${x:+,.2f}")
    
    # 重命名列
    display_df = holdings_df[['name', 'quantity', '平均成本', '当前价格', '总价值', '盈亏金额', '盈亏率']].copy()
    display_df.columns = ['饰品名称', '数量', '平均成本', '当前价格', '总价值', '盈亏金额', '盈亏率']
    
    # 显示表格
    st.dataframe(
        display_df,
        use_container_width=True,
        hide_index=True
    )
    
    # 操作按钮
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 详细分析", use_container_width=True):
            st.info("功能开发中...")
    
    with col2:
        if st.button("💰 添加持仓", use_container_width=True):
            show_add_holding_form()
    
    with col3:
        if st.button("📤 导出数据", use_container_width=True):
            csv = display_df.to_csv(index=False)
            st.download_button(
                label="下载CSV文件",
                data=csv,
                file_name=f"portfolio_{datetime.now().strftime('%Y%m%d')}.csv",
                mime="text/csv"
            )


def show_add_holding_form():
    """显示添加持仓表单"""
    with st.expander("添加新持仓", expanded=True):
        with st.form("add_holding"):
            col1, col2 = st.columns(2)
            
            with col1:
                item_name = st.text_input("饰品名称")
                quantity = st.number_input("数量", min_value=1, value=1)
                purchase_price = st.number_input("购买价格", min_value=0.01, value=100.0, step=0.01)
            
            with col2:
                purchase_date = st.date_input("购买日期", value=datetime.now().date())
                platform = st.selectbox("购买平台", ["Steam市场", "第三方平台", "私人交易"])
                notes = st.text_area("备注", placeholder="可选的备注信息")
            
            submitted = st.form_submit_button("添加持仓")
            
            if submitted:
                if item_name and quantity > 0 and purchase_price > 0:
                    st.success(f"成功添加持仓：{item_name} x{quantity}")
                    st.rerun()
                else:
                    st.error("请填写完整的持仓信息")


def show_performance_analysis(state_manager: StateManager):
    """显示绩效分析"""
    st.markdown("### 📈 绩效分析")
    
    # 时间范围选择
    col1, col2 = st.columns([1, 3])
    
    with col1:
        time_range = st.selectbox(
            "时间范围",
            ["7天", "30天", "90天", "1年", "全部"]
        )
    
    # 生成模拟绩效数据
    days_map = {"7天": 7, "30天": 30, "90天": 90, "1年": 365, "全部": 365}
    days = days_map.get(time_range, 30)
    
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), end=datetime.now(), freq='D')
    
    # 模拟数据
    import random
    portfolio_values = []
    base_value = 10000
    
    for i in range(len(dates)):
        change = random.uniform(-2, 3)
        base_value *= (1 + change / 100)
        portfolio_values.append(base_value)
    
    df = pd.DataFrame({
        'date': dates,
        'portfolio_value': portfolio_values
    })
    
    # 绩效图表
    fig = px.line(
        df,
        x='date',
        y='portfolio_value',
        title=f"投资组合价值趋势 ({time_range})",
        line_shape='spline'
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        xaxis_title="日期",
        yaxis_title="投资组合价值 ($)"
    )
    
    fig.update_traces(line_color='#1f77b4', line_width=3)
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 绩效指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0] * 100
        st.metric("总收益率", f"{total_return:+.2f}%")
    
    with col2:
        annualized_return = total_return * (365 / days)
        st.metric("年化收益率", f"{annualized_return:+.2f}%")
    
    with col3:
        volatility = pd.Series(portfolio_values).pct_change().std() * 100
        st.metric("波动率", f"{volatility:.2f}%")
    
    with col4:
        max_drawdown = -15.2  # 模拟最大回撤
        st.metric("最大回撤", f"{max_drawdown:.2f}%")


def show_asset_allocation(state_manager: StateManager):
    """显示资产配置"""
    st.markdown("### ⚖️ 资产配置")
    
    portfolio_data = state_manager.get_portfolio_data()
    
    # 按类型分类（模拟）
    allocation_data = {
        '步枪': 35.2,
        '狙击枪': 28.5,
        '刀具': 20.1,
        '手枪': 10.8,
        '其他': 5.4
    }
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 当前配置
        st.markdown("#### 当前资产配置")
        
        fig = px.pie(
            values=list(allocation_data.values()),
            names=list(allocation_data.keys()),
            title="按武器类型分布",
            color_discrete_sequence=px.colors.qualitative.Pastel
        )
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 目标配置
        st.markdown("#### 建议资产配置")
        
        target_allocation = {
            '步枪': 30.0,
            '狙击枪': 25.0,
            '刀具': 25.0,
            '手枪': 15.0,
            '其他': 5.0
        }
        
        fig = px.pie(
            values=list(target_allocation.values()),
            names=list(target_allocation.keys()),
            title="建议配置",
            color_discrete_sequence=px.colors.qualitative.Set2
        )
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # 配置偏差分析
    st.markdown("#### 配置偏差分析")
    
    deviation_data = []
    for category in allocation_data.keys():
        current = allocation_data[category]
        target = target_allocation[category]
        deviation = current - target
        
        deviation_data.append({
            '类型': category,
            '当前配置': f"{current:.1f}%",
            '目标配置': f"{target:.1f}%",
            '偏差': f"{deviation:+.1f}%",
            '建议': "减持" if deviation > 2 else ("增持" if deviation < -2 else "保持")
        })
    
    deviation_df = pd.DataFrame(deviation_data)
    st.dataframe(deviation_df, use_container_width=True, hide_index=True)
    
    # 再平衡建议
    if st.button("生成再平衡方案", use_container_width=True):
        st.info("再平衡方案生成功能开发中...")


if __name__ == "__main__":
    show_portfolio_page()
