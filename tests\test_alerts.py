"""
预警系统测试
验证预警引擎、DSL解析器和通知服务功能
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.alerts import (
    AlertEngine, AlertRule, AlertCondition, AlertEvent,
    AlertType, AlertSeverity, get_alert_engine
)
from services.alert_dsl import (
    AlertDSLParser, get_dsl_parser, parse_alert_rule,
    validate_alert_dsl, get_dsl_template
)
from services.notifications import (
    AlertNotificationService, MessageTemplate,
    get_alert_notification_service
)


class TestAlertCondition:
    """预警条件测试"""
    
    def test_condition_evaluation(self):
        """测试条件评估"""
        # 大于条件
        condition = AlertCondition("price", ">", 100.0)
        assert condition.evaluate({"price": 150.0}) == True
        assert condition.evaluate({"price": 50.0}) == False
        
        # 小于条件
        condition = AlertCondition("price_change_percent", "<", -10.0)
        assert condition.evaluate({"price_change_percent": -15.0}) == True
        assert condition.evaluate({"price_change_percent": -5.0}) == False
        
        # 范围条件
        condition = AlertCondition("volume", "between", [100, 200])
        assert condition.evaluate({"volume": 150}) == True
        assert condition.evaluate({"volume": 250}) == False
        assert condition.evaluate({"volume": 50}) == False
    
    def test_nested_field_access(self):
        """测试嵌套字段访问"""
        condition = AlertCondition("item.price", ">", 100.0)
        data = {
            "item": {
                "price": 150.0,
                "name": "Test Item"
            }
        }
        assert condition.evaluate(data) == True
        
        # 字段不存在
        assert condition.evaluate({"other": "value"}) == False
    
    def test_invalid_operator(self):
        """测试无效操作符"""
        condition = AlertCondition("price", "invalid_op", 100.0)
        assert condition.evaluate({"price": 150.0}) == False


class TestAlertRule:
    """预警规则测试"""
    
    def test_rule_evaluation(self):
        """测试规则评估"""
        conditions = [
            AlertCondition("price_change_percent", "<", -15.0),
            AlertCondition("volume_24h", ">", 10)
        ]
        
        rule = AlertRule(
            id="test_rule",
            name="Test Rule",
            description="Test rule description",
            alert_type=AlertType.PRICE_OPPORTUNITY,
            severity=AlertSeverity.MEDIUM,
            conditions=conditions,
            logic_operator="AND"
        )
        
        # 满足所有条件
        data = {
            "price_change_percent": -20.0,
            "volume_24h": 15
        }
        assert rule.evaluate(data) == True
        
        # 只满足部分条件
        data = {
            "price_change_percent": -20.0,
            "volume_24h": 5
        }
        assert rule.evaluate(data) == False
    
    def test_rule_or_logic(self):
        """测试OR逻辑"""
        conditions = [
            AlertCondition("price_change_percent", "<", -15.0),
            AlertCondition("volume_change_percent", ">", 300.0)
        ]
        
        rule = AlertRule(
            id="test_rule_or",
            name="Test OR Rule",
            description="Test OR rule",
            alert_type=AlertType.MARKET_ANOMALY,
            severity=AlertSeverity.HIGH,
            conditions=conditions,
            logic_operator="OR"
        )
        
        # 满足第一个条件
        data = {
            "price_change_percent": -20.0,
            "volume_change_percent": 50.0
        }
        assert rule.evaluate(data) == True
        
        # 满足第二个条件
        data = {
            "price_change_percent": -5.0,
            "volume_change_percent": 400.0
        }
        assert rule.evaluate(data) == True
        
        # 都不满足
        data = {
            "price_change_percent": -5.0,
            "volume_change_percent": 50.0
        }
        assert rule.evaluate(data) == False
    
    def test_rule_cooldown(self):
        """测试规则冷却"""
        rule = AlertRule(
            id="cooldown_test",
            name="Cooldown Test",
            description="Test cooldown",
            alert_type=AlertType.PRICE_RISK,
            severity=AlertSeverity.HIGH,
            conditions=[AlertCondition("price", "<", 50.0)],
            cooldown_minutes=1  # 1分钟冷却
        )
        
        data = {"price": 30.0}
        
        # 第一次评估应该通过
        assert rule.evaluate(data) == True
        rule.trigger()
        
        # 立即再次评估应该被冷却阻止
        assert rule.evaluate(data) == False
        
        # 模拟时间过去（实际测试中可能需要mock时间）
        rule.last_triggered = datetime.utcnow() - timedelta(minutes=2)
        assert rule.evaluate(data) == True
    
    def test_disabled_rule(self):
        """测试禁用规则"""
        rule = AlertRule(
            id="disabled_test",
            name="Disabled Test",
            description="Test disabled rule",
            alert_type=AlertType.PRICE_OPPORTUNITY,
            severity=AlertSeverity.LOW,
            conditions=[AlertCondition("price", ">", 0)],
            enabled=False
        )
        
        # 禁用的规则不应该触发
        assert rule.evaluate({"price": 100.0}) == False


class TestAlertEngine:
    """预警引擎测试"""
    
    @pytest.fixture
    def alert_engine(self):
        """创建测试用预警引擎"""
        engine = AlertEngine()
        # 清空默认规则，使用测试规则
        engine.rules.clear()
        return engine
    
    def test_add_remove_rule(self, alert_engine):
        """测试添加和移除规则"""
        rule = AlertRule(
            id="test_add_remove",
            name="Test Add Remove",
            description="Test rule",
            alert_type=AlertType.PRICE_OPPORTUNITY,
            severity=AlertSeverity.MEDIUM,
            conditions=[AlertCondition("price", ">", 100)]
        )
        
        # 添加规则
        alert_engine.add_rule(rule)
        assert len(alert_engine.rules) == 1
        assert alert_engine.get_rule("test_add_remove") is not None
        
        # 移除规则
        assert alert_engine.remove_rule("test_add_remove") == True
        assert len(alert_engine.rules) == 0
        assert alert_engine.get_rule("test_add_remove") is None
        
        # 移除不存在的规则
        assert alert_engine.remove_rule("nonexistent") == False
    
    def test_enable_disable_rule(self, alert_engine):
        """测试启用和禁用规则"""
        rule = AlertRule(
            id="test_enable_disable",
            name="Test Enable Disable",
            description="Test rule",
            alert_type=AlertType.PRICE_RISK,
            severity=AlertSeverity.HIGH,
            conditions=[AlertCondition("price", "<", 50)]
        )
        
        alert_engine.add_rule(rule)
        
        # 默认应该是启用的
        assert rule.enabled == True
        
        # 禁用规则
        alert_engine.disable_rule("test_enable_disable")
        assert rule.enabled == False
        
        # 启用规则
        alert_engine.enable_rule("test_enable_disable")
        assert rule.enabled == True
    
    @pytest.mark.asyncio
    async def test_evaluate_item(self, alert_engine):
        """测试评估饰品数据"""
        # 添加测试规则
        rule = AlertRule(
            id="test_evaluate",
            name="Test Evaluate",
            description="Test evaluation",
            alert_type=AlertType.PRICE_OPPORTUNITY,
            severity=AlertSeverity.MEDIUM,
            conditions=[
                AlertCondition("price_change_percent", "<", -15.0),
                AlertCondition("volume_24h", ">", 10)
            ]
        )
        alert_engine.add_rule(rule)
        
        # 测试数据
        item_data = {
            "item_id": "test_item",
            "item_name": "Test Item",
            "price_change_percent": -20.0,
            "volume_24h": 15,
            "current_price": 100.0
        }
        
        # 评估应该触发预警
        events = await alert_engine.evaluate_item(item_data)
        assert len(events) == 1
        assert events[0].rule_id == "test_evaluate"
        assert events[0].alert_type == AlertType.PRICE_OPPORTUNITY
    
    def test_get_alert_stats(self, alert_engine):
        """测试获取预警统计"""
        # 添加一些测试规则和事件
        rule = AlertRule(
            id="stats_test",
            name="Stats Test",
            description="Test stats",
            alert_type=AlertType.VOLUME_SPIKE,
            severity=AlertSeverity.LOW,
            conditions=[AlertCondition("volume", ">", 100)]
        )
        alert_engine.add_rule(rule)
        
        # 添加测试事件
        event = AlertEvent(
            id="test_event",
            rule_id="stats_test",
            rule_name="Stats Test",
            alert_type=AlertType.VOLUME_SPIKE,
            severity=AlertSeverity.LOW,
            message="Test message",
            data={}
        )
        alert_engine.events.append(event)
        
        stats = alert_engine.get_alert_stats()
        
        assert stats['total_events'] == 1
        assert stats['total_rules'] == 1
        assert stats['active_rules'] == 1
        assert stats['type_stats']['volume_spike'] == 1
        assert stats['severity_stats']['low'] == 1


class TestAlertDSLParser:
    """DSL解析器测试"""
    
    @pytest.fixture
    def dsl_parser(self):
        """创建DSL解析器实例"""
        return AlertDSLParser()
    
    def test_parse_simple_rule(self, dsl_parser):
        """测试解析简单规则"""
        dsl_text = """
规则名称: 价格下跌预警
描述: 当价格下跌超过15%时触发
价格变化率 < -15%
"""
        
        result = dsl_parser.parse_rule(dsl_text)
        
        assert result.success == True
        assert result.rule is not None
        assert result.rule.name == "价格下跌预警"
        assert len(result.rule.conditions) >= 1
    
    def test_parse_complex_rule(self, dsl_parser):
        """测试解析复杂规则"""
        dsl_text = """
规则名称: 复合条件预警
描述: 价格下跌且交易量增加时触发
严重性: 高
价格变化率 < -20%
并且 交易量变化率 > 200%
冷却时间: 60分钟
"""
        
        result = dsl_parser.parse_rule(dsl_text)
        
        assert result.success == True
        assert result.rule.severity == AlertSeverity.HIGH
        assert result.rule.logic_operator == "AND"
        assert result.rule.cooldown_minutes == 60
    
    def test_validate_dsl(self, dsl_parser):
        """测试DSL验证"""
        # 有效的DSL
        valid_dsl = """
规则名称: 测试规则
价格 > 100
"""
        
        validation = dsl_parser.validate_dsl(valid_dsl)
        assert validation['valid'] == True
        assert len(validation['errors']) == 0
        
        # 无效的DSL
        invalid_dsl = "无效的DSL文本"
        
        validation = dsl_parser.validate_dsl(invalid_dsl)
        assert validation['valid'] == False
        assert len(validation['errors']) > 0
    
    def test_generate_template(self, dsl_parser):
        """测试生成模板"""
        template = dsl_parser.generate_dsl_template(AlertType.PRICE_OPPORTUNITY)
        
        assert "价格机会预警" in template
        assert "价格变化率" in template
        assert "冷却时间" in template


@pytest.mark.asyncio
class TestAlertNotificationService:
    """预警通知服务测试"""
    
    @pytest.fixture
    def notification_service(self):
        """创建通知服务实例"""
        return AlertNotificationService()
    
    async def test_send_alert_notification(self, notification_service):
        """测试发送预警通知"""
        test_data = {
            'item_name': 'Test Item',
            'current_price': 100.0,
            'price_change_percent': -20.0,
            'volume_24h': 50
        }
        
        # 发送价格机会通知
        result = await notification_service.send_alert_notification(
            MessageTemplate.PRICE_OPPORTUNITY.value,
            test_data,
            severity="medium"
        )
        
        # 由于没有实际的通知服务配置，结果可能为False，但不应该抛异常
        assert isinstance(result, bool)
    
    def test_rate_limit_check(self, notification_service):
        """测试频率限制检查"""
        # 第一次检查应该通过
        assert notification_service._check_rate_limit("test_template", "medium") == True
        
        # 更新发送时间
        notification_service._update_last_sent("test_template", "medium")
        
        # 立即再次检查应该被限制
        assert notification_service._check_rate_limit("test_template", "medium") == False
    
    def test_get_test_data(self, notification_service):
        """测试获取测试数据"""
        test_data = notification_service._get_test_data(MessageTemplate.PRICE_OPPORTUNITY.value)
        
        assert 'item_name' in test_data
        assert 'current_price' in test_data
        assert 'price_change_percent' in test_data
    
    def test_template_formatting(self, notification_service):
        """测试模板格式化"""
        template = notification_service.templates[MessageTemplate.PRICE_OPPORTUNITY.value]
        
        test_data = {
            'item_name': 'Test Item',
            'current_price': 100.0,
            'price_change_percent': -15.0,
            'volume_24h': 50,
            'timestamp': '2025-07-17 10:00:00'
        }
        
        formatted = template.format(test_data)
        
        assert 'title' in formatted
        assert 'content' in formatted
        assert 'Test Item' in formatted['title']
        assert '$100.00' in formatted['content']


if __name__ == "__main__":
    # 运行基本测试
    print("运行预警系统基本测试...")
    
    # 测试预警条件
    print("测试预警条件...")
    condition = AlertCondition("price", ">", 100.0)
    assert condition.evaluate({"price": 150.0}) == True
    assert condition.evaluate({"price": 50.0}) == False
    print("✓ 预警条件测试通过")
    
    # 测试预警规则
    print("测试预警规则...")
    rule = AlertRule(
        id="test_rule",
        name="Test Rule",
        description="Test",
        alert_type=AlertType.PRICE_OPPORTUNITY,
        severity=AlertSeverity.MEDIUM,
        conditions=[AlertCondition("price", ">", 100)]
    )
    
    assert rule.evaluate({"price": 150}) == True
    assert rule.evaluate({"price": 50}) == False
    print("✓ 预警规则测试通过")
    
    # 测试预警引擎
    print("测试预警引擎...")
    engine = AlertEngine()
    engine.rules.clear()  # 清空默认规则
    
    engine.add_rule(rule)
    assert len(engine.rules) == 1
    
    engine.remove_rule("test_rule")
    assert len(engine.rules) == 0
    print("✓ 预警引擎测试通过")
    
    # 测试DSL解析器
    print("测试DSL解析器...")
    parser = AlertDSLParser()
    
    dsl_text = "价格 > 100"
    validation = parser.validate_dsl(dsl_text)
    # DSL解析可能失败，但不应该抛异常
    assert 'valid' in validation
    print("✓ DSL解析器测试通过")
    
    # 测试通知服务
    print("测试通知服务...")
    notification_service = AlertNotificationService()
    
    test_data = notification_service._get_test_data(MessageTemplate.PRICE_OPPORTUNITY.value)
    assert 'item_name' in test_data
    
    template = notification_service.templates[MessageTemplate.PRICE_OPPORTUNITY.value]
    formatted = template.format(test_data)
    assert 'title' in formatted
    assert 'content' in formatted
    print("✓ 通知服务测试通过")
    
    print("所有预警系统测试通过！")
