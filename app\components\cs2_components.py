"""
CS2饰品专用UI组件
为CS2饰品投资系统提供专门的用户界面组件
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np

from app.components.data_table import DataTable
from app.components.filter_panel import FilterPanel, FilterType


class CS2ItemCard:
    """CS2饰品卡片组件"""
    
    def __init__(self, item_data: Dict[str, Any], key: str):
        """
        初始化CS2饰品卡片
        
        Args:
            item_data: 饰品数据
            key: 组件唯一标识
        """
        self.item_data = item_data
        self.key = key
    
    def render(self):
        """渲染饰品卡片"""
        with st.container():
            # 卡片样式
            st.markdown("""
            <style>
            .cs2-item-card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 12px;
                margin: 8px 0;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .cs2-item-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }
            .cs2-item-name {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
            }
            .cs2-item-price {
                font-weight: bold;
                font-size: 16px;
                color: #27ae60;
            }
            .cs2-item-change {
                font-size: 12px;
            }
            .cs2-item-change.positive {
                color: #27ae60;
            }
            .cs2-item-change.negative {
                color: #e74c3c;
            }
            .cs2-item-tags {
                display: flex;
                gap: 4px;
                margin-top: 8px;
            }
            .cs2-tag {
                background: #3498db;
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 10px;
            }
            .cs2-tag.rarity {
                background: #9b59b6;
            }
            .cs2-tag.weapon {
                background: #e67e22;
            }
            .cs2-tag.quality {
                background: #f39c12;
            }
            </style>
            """, unsafe_allow_html=True)
            
            # 渲染卡片内容
            st.markdown(f"""
            <div class="cs2-item-card">
                <div class="cs2-item-header">
                    <div class="cs2-item-name">{self.item_data.get('item_name', 'Unknown Item')}</div>
                    <div class="cs2-item-price">¥{self.item_data.get('current_price', 0):.2f}</div>
                </div>
                <div class="cs2-item-change {'positive' if self.item_data.get('change_24h', 0) >= 0 else 'negative'}">
                    24h: {self.item_data.get('change_24h', 0):+.2f}% | 交易量: {self.item_data.get('volume_24h', 0)}
                </div>
                <div class="cs2-item-tags">
                    <span class="cs2-tag rarity">{self.item_data.get('rarity', '未知')}</span>
                    <span class="cs2-tag weapon">{self.item_data.get('weapon_type', '未知')}</span>
                    <span class="cs2-tag quality">{self.item_data.get('quality', '普通')}</span>
                </div>
            </div>
            """, unsafe_allow_html=True)
            
            # 操作按钮
            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button("📊 详情", key=f"{self.key}_details"):
                    self._show_item_details()
            with col2:
                if st.button("⭐ 关注", key=f"{self.key}_watch"):
                    self._add_to_watchlist()
            with col3:
                if st.button("💰 套利", key=f"{self.key}_arbitrage"):
                    self._show_arbitrage_opportunities()
    
    def _show_item_details(self):
        """显示饰品详情"""
        st.session_state[f"{self.key}_show_details"] = True
    
    def _add_to_watchlist(self):
        """添加到关注列表"""
        st.success(f"已添加 {self.item_data.get('item_name')} 到关注列表")
    
    def _show_arbitrage_opportunities(self):
        """显示套利机会"""
        st.session_state[f"{self.key}_show_arbitrage"] = True


class CS2PriceChart:
    """CS2饰品价格图表组件"""
    
    def __init__(self, item_name: str, price_data: List[Dict], key: str):
        """
        初始化价格图表
        
        Args:
            item_name: 饰品名称
            price_data: 价格历史数据
            key: 组件唯一标识
        """
        self.item_name = item_name
        self.price_data = price_data
        self.key = key
    
    def render(self):
        """渲染价格图表"""
        if not self.price_data:
            st.warning("暂无价格数据")
            return
        
        # 转换数据
        df = pd.DataFrame(self.price_data)
        df['date'] = pd.to_datetime(df['date'])
        
        # 创建价格走势图
        fig = go.Figure()
        
        # 添加价格线
        fig.add_trace(go.Scatter(
            x=df['date'],
            y=df['price'],
            mode='lines+markers',
            name='价格',
            line=dict(color='#3498db', width=2),
            marker=dict(size=4)
        ))
        
        # 添加交易量柱状图（副轴）
        fig.add_trace(go.Bar(
            x=df['date'],
            y=df['volume'],
            name='交易量',
            yaxis='y2',
            opacity=0.3,
            marker_color='#95a5a6'
        ))
        
        # 更新布局
        fig.update_layout(
            title=f"{self.item_name} 价格走势",
            xaxis_title="日期",
            yaxis_title="价格 (¥)",
            yaxis2=dict(
                title="交易量",
                overlaying='y',
                side='right'
            ),
            height=400,
            showlegend=True,
            hovermode='x unified'
        )
        
        st.plotly_chart(fig, use_container_width=True)


class CS2ArbitragePanel:
    """CS2套利机会面板"""
    
    def __init__(self, arbitrage_data: List[Dict], key: str):
        """
        初始化套利面板
        
        Args:
            arbitrage_data: 套利机会数据
            key: 组件唯一标识
        """
        self.arbitrage_data = arbitrage_data
        self.key = key
    
    def render(self):
        """渲染套利面板"""
        st.markdown("### 🎯 套利机会")
        
        if not self.arbitrage_data:
            st.info("暂无套利机会")
            return
        
        # 按利润率排序
        sorted_opportunities = sorted(
            self.arbitrage_data, 
            key=lambda x: x.get('profit_margin', 0), 
            reverse=True
        )
        
        for i, opportunity in enumerate(sorted_opportunities[:5]):  # 显示前5个
            with st.expander(f"💰 {opportunity.get('item_name', 'Unknown')} - {opportunity.get('profit_margin', 0):.1f}%"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown("**买入信息**")
                    st.write(f"平台: {opportunity.get('buy_platform', 'Unknown')}")
                    st.write(f"价格: ¥{opportunity.get('buy_price', 0):.2f}")
                    
                with col2:
                    st.markdown("**卖出信息**")
                    st.write(f"平台: {opportunity.get('sell_platform', 'Unknown')}")
                    st.write(f"价格: ¥{opportunity.get('sell_price', 0):.2f}")
                
                # 利润信息
                st.markdown("**利润分析**")
                col3, col4, col5 = st.columns(3)
                with col3:
                    st.metric("毛利润", f"¥{opportunity.get('gross_profit', 0):.2f}")
                with col4:
                    st.metric("净利润", f"¥{opportunity.get('net_profit', 0):.2f}")
                with col5:
                    st.metric("利润率", f"{opportunity.get('profit_margin', 0):.1f}%")
                
                # 风险评估
                risk_level = opportunity.get('risk_level', 'unknown')
                risk_color = {
                    'low': '🟢',
                    'medium': '🟡', 
                    'high': '🟠',
                    'very_high': '🔴'
                }.get(risk_level, '⚪')
                
                st.markdown(f"**风险评估**: {risk_color} {risk_level}")
                st.markdown(f"**置信度**: {opportunity.get('confidence_score', 0):.1f}/100")


class CS2FilterPanel:
    """CS2饰品专用筛选面板"""
    
    def __init__(self, key: str, title: str = "CS2饰品筛选"):
        """
        初始化CS2筛选面板
        
        Args:
            key: 组件唯一标识
            title: 面板标题
        """
        self.key = key
        self.title = title
        self.panel = FilterPanel(key, title)
        self._setup_cs2_filters()
    
    def _setup_cs2_filters(self):
        """设置CS2特有的筛选器"""
        # 基础筛选
        self.panel.add_filter(
            "item_name", FilterType.TEXT, "饰品名称"
        )
        
        # CS2特有筛选
        self.panel.add_filter(
            "weapon_type", FilterType.SELECT, "武器类型",
            options=["全部", "步枪", "狙击枪", "手枪", "冲锋枪", "霰弹枪", "机枪", "刀具", "手套"],
            default_value="全部"
        )
        
        self.panel.add_filter(
            "rarity", FilterType.SELECT, "稀有度",
            options=["全部", "消费级", "工业级", "军规级", "受限", "保密", "隐秘", "违禁品"],
            default_value="全部"
        )
        
        self.panel.add_filter(
            "quality", FilterType.SELECT, "品质",
            options=["全部", "普通", "StatTrak™", "纪念品"],
            default_value="全部"
        )
        
        self.panel.add_filter(
            "current_price", FilterType.NUMBER_RANGE, "价格区间 (¥)",
            min_value=0.0, max_value=10000.0, default_value=(0.0, 10000.0)
        )
        
        self.panel.add_filter(
            "investment_score", FilterType.SLIDER, "投资评分",
            min_value=0.0, max_value=100.0, default_value=50.0
        )
        
        self.panel.add_filter(
            "change_24h", FilterType.NUMBER_RANGE, "24h涨跌 (%)",
            min_value=-50.0, max_value=50.0, default_value=(-50.0, 50.0)
        )
        
        self.panel.add_filter(
            "volume_24h", FilterType.SLIDER, "最低交易量",
            min_value=0, max_value=1000, default_value=10
        )
    
    def render(self):
        """渲染筛选面板"""
        return self.panel.render()
    
    def apply_filters_to_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用筛选器到数据框"""
        return self.panel.apply_filters_to_dataframe(df)


class CS2MetricsOverview:
    """CS2投资指标概览组件"""
    
    def __init__(self, metrics_data: Dict[str, Any], key: str):
        """
        初始化指标概览
        
        Args:
            metrics_data: 指标数据
            key: 组件唯一标识
        """
        self.metrics_data = metrics_data
        self.key = key
    
    def render(self):
        """渲染指标概览"""
        st.markdown("### 📊 CS2投资概览")
        
        # 第一行指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="💼 投资组合总值",
                value=f"¥{self.metrics_data.get('total_value', 0):,.2f}",
                delta=f"{self.metrics_data.get('total_change_percent', 0):+.2f}%"
            )
        
        with col2:
            st.metric(
                label="💰 总盈亏",
                value=f"¥{self.metrics_data.get('total_profit', 0):+,.2f}",
                delta=f"¥{self.metrics_data.get('daily_profit', 0):+.2f}"
            )
        
        with col3:
            st.metric(
                label="⭐ 核心关注池",
                value=f"{self.metrics_data.get('core_pool_count', 0)}",
                delta=f"+{self.metrics_data.get('core_pool_new', 0)}"
            )
        
        with col4:
            st.metric(
                label="🎯 套利机会",
                value=f"{self.metrics_data.get('arbitrage_count', 0)}",
                delta=f"+{self.metrics_data.get('arbitrage_new', 0)}"
            )
        
        # 第二行指标
        col5, col6, col7, col8 = st.columns(4)
        
        with col5:
            st.metric(
                label="🔍 主监控池",
                value=f"{self.metrics_data.get('main_pool_count', 0)}",
                delta=f"+{self.metrics_data.get('main_pool_new', 0)}"
            )
        
        with col6:
            st.metric(
                label="📈 今日涨幅榜",
                value=f"{self.metrics_data.get('rising_count', 0)}",
                delta=f"{self.metrics_data.get('avg_rise_percent', 0):+.1f}%"
            )
        
        with col7:
            st.metric(
                label="📉 今日跌幅榜", 
                value=f"{self.metrics_data.get('falling_count', 0)}",
                delta=f"{self.metrics_data.get('avg_fall_percent', 0):+.1f}%"
            )
        
        with col8:
            st.metric(
                label="🔥 热门关注",
                value=f"{self.metrics_data.get('hot_count', 0)}",
                delta=f"+{self.metrics_data.get('hot_new', 0)}"
            )


def create_cs2_portfolio_table(data: pd.DataFrame, key: str) -> DataTable:
    """创建CS2投资组合表格"""
    # 定义CS2特有的列配置
    column_config = {
        'item_name': st.column_config.TextColumn('饰品名称', width='medium'),
        'weapon_type': st.column_config.TextColumn('武器类型', width='small'),
        'rarity': st.column_config.TextColumn('稀有度', width='small'),
        'quality': st.column_config.TextColumn('品质', width='small'),
        'quantity': st.column_config.NumberColumn('数量', format='%d'),
        'avg_cost': st.column_config.NumberColumn('平均成本', format='¥%.2f'),
        'current_price': st.column_config.NumberColumn('当前价格', format='¥%.2f'),
        'total_value': st.column_config.NumberColumn('总价值', format='¥%.2f'),
        'profit_loss': st.column_config.NumberColumn('盈亏', format='¥%.2f'),
        'profit_percentage': st.column_config.NumberColumn('盈亏率', format='%.2f%%'),
        'investment_score': st.column_config.NumberColumn('投资评分', format='%.1f')
    }
    
    # 定义操作按钮
    actions = [
        {
            'label': '📊 分析',
            'action': 'analyze',
            'color': 'primary'
        },
        {
            'label': '💰 套利',
            'action': 'arbitrage', 
            'color': 'secondary'
        },
        {
            'label': '📈 趋势',
            'action': 'trend',
            'color': 'secondary'
        }
    ]
    
    return DataTable(
        data=data,
        key=key,
        title="CS2投资组合",
        page_size=15,
        sortable=True,
        filterable=True,
        selectable=True,
        actions=actions
    )


def create_cs2_watchlist_table(data: pd.DataFrame, key: str) -> DataTable:
    """创建CS2关注列表表格"""
    column_config = {
        'item_name': st.column_config.TextColumn('饰品名称', width='medium'),
        'weapon_type': st.column_config.TextColumn('武器类型', width='small'),
        'rarity': st.column_config.TextColumn('稀有度', width='small'),
        'current_price': st.column_config.NumberColumn('当前价格', format='¥%.2f'),
        'change_24h': st.column_config.NumberColumn('24h涨跌', format='%.2f%%'),
        'volume_24h': st.column_config.NumberColumn('24h交易量', format='%d'),
        'investment_score': st.column_config.NumberColumn('投资评分', format='%.1f'),
        'priority_score': st.column_config.NumberColumn('优先级', format='%.1f')
    }
    
    actions = [
        {
            'label': '⭐ 加入组合',
            'action': 'add_to_portfolio',
            'color': 'primary'
        },
        {
            'label': '💰 查看套利',
            'action': 'view_arbitrage',
            'color': 'secondary'
        }
    ]
    
    return DataTable(
        data=data,
        key=key,
        title="CS2关注列表",
        page_size=20,
        sortable=True,
        filterable=True,
        selectable=True,
        actions=actions
    )
