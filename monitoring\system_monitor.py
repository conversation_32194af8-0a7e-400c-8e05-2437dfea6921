"""
Ares系统监控器
实现系统性能指标收集、API使用监控和自动告警
"""

import asyncio
import logging
import time
import json
import psutil
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque, defaultdict
from threading import Lock
import threading

from monitoring.health_check import get_health_checker, HealthStatus

logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: datetime
    value: float
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'value': self.value,
            'tags': self.tags
        }


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric: str
    condition: str  # >, <, >=, <=, ==
    threshold: float
    duration: int  # 持续时间（秒）
    severity: str  # low, medium, high, critical
    enabled: bool = True
    
    def evaluate(self, value: float) -> bool:
        """评估告警条件"""
        if self.condition == '>':
            return value > self.threshold
        elif self.condition == '<':
            return value < self.threshold
        elif self.condition == '>=':
            return value >= self.threshold
        elif self.condition == '<=':
            return value <= self.threshold
        elif self.condition == '==':
            return value == self.threshold
        return False


@dataclass
class Alert:
    """告警"""
    id: str
    rule_name: str
    metric: str
    value: float
    threshold: float
    severity: str
    message: str
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'rule_name': self.rule_name,
            'metric': self.metric,
            'value': self.value,
            'threshold': self.threshold,
            'severity': self.severity,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'resolved': self.resolved,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None
        }


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, max_history_points: int = 1000):
        """初始化系统监控器"""
        self.max_history_points = max_history_points
        
        # 指标存储
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_points))
        self.metrics_lock = Lock()
        
        # API使用统计
        self.api_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'total_requests': 0,
            'success_requests': 0,
            'error_requests': 0,
            'avg_response_time': 0.0,
            'last_request_time': None,
            'rate_limit_hits': 0
        })
        self.api_stats_lock = Lock()
        
        # 告警规则和告警历史
        self.alert_rules: List[AlertRule] = []
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self.alerts_lock = Lock()
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 健康检查器
        self.health_checker = get_health_checker()
        
        # 初始化默认告警规则
        self._init_default_alert_rules()
    
    def _init_default_alert_rules(self):
        """初始化默认告警规则"""
        default_rules = [
            AlertRule("high_cpu_usage", "cpu_usage", ">", 85.0, 300, "high"),
            AlertRule("high_memory_usage", "memory_usage", ">", 90.0, 300, "high"),
            AlertRule("low_disk_space", "disk_usage", ">", 90.0, 600, "critical"),
            AlertRule("high_api_error_rate", "api_error_rate", ">", 10.0, 180, "medium"),
            AlertRule("api_rate_limit_exceeded", "api_rate_limit_hits", ">", 5, 60, "medium")
        ]
        
        self.alert_rules.extend(default_rules)
        logger.info(f"Initialized {len(default_rules)} default alert rules")
    
    def start_monitoring(self, interval: int = 60):
        """启动监控"""
        if self.monitoring_active:
            logger.warning("Monitoring is already active")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"Started system monitoring with {interval}s interval")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Stopped system monitoring")
    
    def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 检查告警
                self._check_alerts()
                
                # 等待下一次收集
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        timestamp = datetime.utcnow()
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.add_metric("cpu_usage", cpu_percent, timestamp)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.add_metric("memory_usage", memory.percent, timestamp)
            self.add_metric("memory_available_gb", memory.available / (1024**3), timestamp)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.add_metric("disk_usage", disk_percent, timestamp)
            self.add_metric("disk_free_gb", disk.free / (1024**3), timestamp)
            
            # 网络IO
            net_io = psutil.net_io_counters()
            self.add_metric("network_bytes_sent", net_io.bytes_sent, timestamp)
            self.add_metric("network_bytes_recv", net_io.bytes_recv, timestamp)
            
            # 进程数
            process_count = len(psutil.pids())
            self.add_metric("process_count", process_count, timestamp)
            
            # API错误率
            api_error_rate = self._calculate_api_error_rate()
            self.add_metric("api_error_rate", api_error_rate, timestamp)
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
    
    def add_metric(self, name: str, value: float, timestamp: datetime = None, tags: Dict[str, str] = None):
        """添加指标数据点"""
        if timestamp is None:
            timestamp = datetime.utcnow()
        
        metric_point = MetricPoint(
            timestamp=timestamp,
            value=value,
            tags=tags or {}
        )
        
        with self.metrics_lock:
            self.metrics[name].append(metric_point)
    
    def get_metric_history(self, name: str, duration_minutes: int = 60) -> List[MetricPoint]:
        """获取指标历史数据"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=duration_minutes)
        
        with self.metrics_lock:
            if name not in self.metrics:
                return []
            
            return [
                point for point in self.metrics[name]
                if point.timestamp >= cutoff_time
            ]
    
    def get_latest_metric(self, name: str) -> Optional[MetricPoint]:
        """获取最新指标值"""
        with self.metrics_lock:
            if name not in self.metrics or not self.metrics[name]:
                return None
            return self.metrics[name][-1]
    
    def record_api_request(self, endpoint: str, success: bool, response_time: float, rate_limited: bool = False):
        """记录API请求"""
        with self.api_stats_lock:
            stats = self.api_stats[endpoint]
            
            stats['total_requests'] += 1
            stats['last_request_time'] = datetime.utcnow()
            
            if success:
                stats['success_requests'] += 1
                # 只有成功请求才更新平均响应时间
                total_success = stats['success_requests']
                current_avg = stats['avg_response_time']
                stats['avg_response_time'] = (current_avg * (total_success - 1) + response_time) / total_success
            else:
                stats['error_requests'] += 1

            if rate_limited:
                stats['rate_limit_hits'] += 1
    
    def get_api_stats(self, endpoint: str = None) -> Dict[str, Any]:
        """获取API统计"""
        with self.api_stats_lock:
            if endpoint:
                return self.api_stats.get(endpoint, {})
            return dict(self.api_stats)
    
    def _calculate_api_error_rate(self) -> float:
        """计算API错误率"""
        with self.api_stats_lock:
            total_requests = 0
            total_errors = 0
            
            for stats in self.api_stats.values():
                total_requests += stats['total_requests']
                total_errors += stats['error_requests']
            
            if total_requests == 0:
                return 0.0
            
            return (total_errors / total_requests) * 100
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        with self.alerts_lock:
            self.alert_rules.append(rule)
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_alert_rule(self, rule_name: str) -> bool:
        """移除告警规则"""
        with self.alerts_lock:
            for i, rule in enumerate(self.alert_rules):
                if rule.name == rule_name:
                    del self.alert_rules[i]
                    logger.info(f"Removed alert rule: {rule_name}")
                    return True
        return False
    
    def _check_alerts(self):
        """检查告警条件"""
        with self.alerts_lock:
            for rule in self.alert_rules:
                if not rule.enabled:
                    continue
                
                # 获取最新指标值
                latest_metric = self.get_latest_metric(rule.metric)
                if not latest_metric:
                    continue
                
                # 评估告警条件
                if rule.evaluate(latest_metric.value):
                    self._trigger_alert(rule, latest_metric.value)
                else:
                    self._resolve_alert(rule.name)
    
    def _trigger_alert(self, rule: AlertRule, value: float):
        """触发告警"""
        alert_id = f"{rule.name}_{int(time.time())}"
        
        # 检查是否已有活跃告警
        if rule.name in self.active_alerts:
            return
        
        alert = Alert(
            id=alert_id,
            rule_name=rule.name,
            metric=rule.metric,
            value=value,
            threshold=rule.threshold,
            severity=rule.severity,
            message=f"{rule.metric} is {value:.2f}, exceeds threshold {rule.threshold}",
            timestamp=datetime.utcnow()
        )
        
        self.active_alerts[rule.name] = alert
        self.alert_history.append(alert)
        
        logger.warning(f"Alert triggered: {alert.message}")
        
        # 这里可以添加通知逻辑
        self._send_alert_notification(alert)
    
    def _resolve_alert(self, rule_name: str):
        """解决告警"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.resolved = True
            alert.resolved_at = datetime.utcnow()
            
            del self.active_alerts[rule_name]
            
            logger.info(f"Alert resolved: {rule_name}")
    
    def _send_alert_notification(self, alert: Alert):
        """发送告警通知"""
        # 这里可以集成邮件、短信、Webhook等通知方式
        # 暂时只记录日志
        logger.warning(f"ALERT [{alert.severity.upper()}]: {alert.message}")
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        with self.alerts_lock:
            return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取告警历史"""
        with self.alerts_lock:
            return list(self.alert_history)[-limit:]
    
    def get_system_overview(self) -> Dict[str, Any]:
        """获取系统概览"""
        # 获取最新的系统指标
        cpu_metric = self.get_latest_metric("cpu_usage")
        memory_metric = self.get_latest_metric("memory_usage")
        disk_metric = self.get_latest_metric("disk_usage")
        
        # 获取API统计
        api_stats = self.get_api_stats()
        total_api_requests = sum(stats['total_requests'] for stats in api_stats.values())
        total_api_errors = sum(stats['error_requests'] for stats in api_stats.values())
        
        # 获取告警信息
        active_alerts = self.get_active_alerts()
        
        return {
            "system_metrics": {
                "cpu_usage": cpu_metric.value if cpu_metric else 0,
                "memory_usage": memory_metric.value if memory_metric else 0,
                "disk_usage": disk_metric.value if disk_metric else 0
            },
            "api_metrics": {
                "total_requests": total_api_requests,
                "total_errors": total_api_errors,
                "error_rate": self._calculate_api_error_rate()
            },
            "alerts": {
                "active_count": len(active_alerts),
                "critical_count": len([a for a in active_alerts if a.severity == "critical"]),
                "high_count": len([a for a in active_alerts if a.severity == "high"])
            },
            "monitoring_status": {
                "active": self.monitoring_active,
                "uptime": self._get_uptime()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def _get_uptime(self) -> str:
        """获取系统运行时间"""
        try:
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            
            days = int(uptime_seconds // 86400)
            hours = int((uptime_seconds % 86400) // 3600)
            minutes = int((uptime_seconds % 3600) // 60)
            
            return f"{days}d {hours}h {minutes}m"
        except Exception:
            return "Unknown"


# 全局系统监控器实例
_system_monitor: Optional[SystemMonitor] = None


def get_system_monitor() -> SystemMonitor:
    """获取全局系统监控器实例"""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor()
    return _system_monitor
