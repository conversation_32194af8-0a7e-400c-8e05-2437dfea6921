# Ares基础数据收集器API文档

## 概述

Ares基础数据收集器API提供了完整的基础数据管理和监控接口，支持服务控制、数据同步、质量监控等功能。

## 基础信息

- **Base URL**: `http://localhost:8000/api/base-data`
- **认证**: 暂无（开发环境）
- **响应格式**: JSON

## API端点

### 1. 服务管理

#### 获取服务状态
```http
GET /api/base-data/status
```

**响应示例**:
```json
{
  "running": true,
  "scheduler_running": true,
  "sync_time": "02:00",
  "next_run_time": "2025-01-19T02:00:00",
  "last_update": "2025-01-18T10:30:00"
}
```

#### 启动服务
```http
POST /api/base-data/start
```

#### 停止服务
```http
POST /api/base-data/stop
```

### 2. 数据同步

#### 手动触发同步
```http
POST /api/base-data/sync
Content-Type: application/json

{
  "force": false,
  "reason": "手动测试同步"
}
```

**响应示例**:
```json
{
  "success": true,
  "total_items": 15420,
  "new_items": 23,
  "updated_items": 156,
  "quality_score": 92.5,
  "sync_duration": 45.2,
  "error_message": null
}
```

#### 更新同步时间
```http
PUT /api/base-data/config/sync-time
Content-Type: application/json

{
  "sync_time": "03:00"
}
```

### 3. 监控和统计

#### 获取统计信息
```http
GET /api/base-data/statistics
```

**响应示例**:
```json
{
  "timestamp": "2025-01-18T10:30:00Z",
  "api_statistics": {
    "total_calls": 45,
    "successful_calls": 43,
    "failed_calls": 2,
    "success_rate": 95.56,
    "total_items_collected": 15420,
    "last_update": "2025-01-18T02:00:00",
    "can_call_api": false
  },
  "database_statistics": {
    "total_items": 15420,
    "steamdt_items": 15420,
    "active_items": 15380,
    "recent_synced_items": 179,
    "weapon_type_distribution": {
      "AK-47": 234,
      "M4A4": 189,
      "AWP": 156
    }
  },
  "quality_statistics": {
    "status": "available",
    "quality_score": 92.5,
    "total_items_validated": 15420,
    "valid_items": 15201,
    "quality_level": "优秀"
  }
}
```

#### 获取执行历史
```http
GET /api/base-data/execution-history?limit=10
```

#### 获取质量报告
```http
GET /api/base-data/quality-report
```

### 4. 健康检查

#### 服务健康检查
```http
GET /api/base-data/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "health_score": 95,
  "issues": [],
  "components": {
    "api": "healthy",
    "database": "healthy",
    "scheduler": "healthy",
    "data_freshness": "healthy"
  },
  "timestamp": "2025-01-18T10:30:00Z"
}
```

### 5. 监控数据导出

#### 导出JSON格式监控数据
```http
GET /api/base-data/monitoring/export?format=json
```

#### 导出Prometheus格式指标
```http
GET /api/base-data/metrics/prometheus
```

**响应示例**:
```
# HELP base_data_service_running Service running status
# TYPE base_data_service_running gauge
base_data_service_running 1

# HELP base_data_api_success_rate API success rate percentage
# TYPE base_data_api_success_rate gauge
base_data_api_success_rate 95.56

# HELP base_data_quality_score Data quality score
# TYPE base_data_quality_score gauge
base_data_quality_score 92.5
```

### 6. 数据管理

#### 清理旧数据
```http
POST /api/base-data/database/cleanup?days_threshold=30
```

#### 重置统计数据
```http
POST /api/base-data/statistics/reset
```

## 错误处理

所有API端点都使用标准的HTTP状态码：

- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `500 Internal Server Error`: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 使用示例

### Python客户端示例

```python
import requests
import json

# 基础URL
BASE_URL = "http://localhost:8000/api/base-data"

# 获取服务状态
response = requests.get(f"{BASE_URL}/status")
status = response.json()
print(f"服务运行状态: {status['running']}")

# 手动触发同步
sync_data = {
    "force": False,
    "reason": "定期数据更新"
}
response = requests.post(f"{BASE_URL}/sync", json=sync_data)
result = response.json()
print(f"同步结果: {result['success']}, 处理了{result['total_items']}个饰品")

# 获取统计信息
response = requests.get(f"{BASE_URL}/statistics")
stats = response.json()
print(f"API成功率: {stats['api_statistics']['success_rate']:.1f}%")
print(f"数据质量评分: {stats['quality_statistics']['quality_score']}")
```

### curl示例

```bash
# 获取服务状态
curl -X GET "http://localhost:8000/api/base-data/status"

# 手动触发同步
curl -X POST "http://localhost:8000/api/base-data/sync" \
  -H "Content-Type: application/json" \
  -d '{"force": false, "reason": "手动测试"}'

# 获取Prometheus指标
curl -X GET "http://localhost:8000/api/base-data/metrics/prometheus"
```

## 注意事项

1. **API限制**: SteamDT基础数据API每日只能调用1次，请谨慎使用`force=true`参数
2. **同步时间**: 默认每日凌晨2点自动同步，可通过API修改
3. **数据质量**: 系统会自动验证数据质量，低质量数据会被过滤
4. **监控集成**: 支持Prometheus监控集成，可用于告警和仪表板

## 更新日志

- **v1.0.0**: 初始版本，包含基础的服务管理和监控功能
