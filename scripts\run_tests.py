#!/usr/bin/env python3
"""
基础数据收集器测试运行脚本
提供多种测试运行选项和报告生成
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def setup_test_environment():
    """设置测试环境"""
    import base64
    
    # 设置必要的环境变量
    dummy_key_bytes = b'dummy_key_for_import_only_32byte'
    dummy_key = base64.urlsafe_b64encode(dummy_key_bytes).decode()
    
    os.environ.setdefault('STEAMDT_API_KEY', 'test_api_key')
    os.environ.setdefault('ENCRYPTION_KEY', dummy_key)
    os.environ.setdefault('SECRET_KEY', 'test_secret_key')
    os.environ.setdefault('ARES_ENV', 'test')
    os.environ['DATABASE_URL'] = 'sqlite:///:memory:'
    
    print("✅ 测试环境已设置")


def run_pytest(args_list):
    """运行pytest"""
    cmd = ['python', '-m', 'pytest'] + args_list
    
    print(f"🚀 运行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    start_time = time.time()
    result = subprocess.run(cmd, cwd=project_root)
    end_time = time.time()
    
    print("=" * 60)
    print(f"⏱️  测试耗时: {end_time - start_time:.2f}秒")
    
    return result.returncode


def run_unit_tests(verbose=False, coverage=False):
    """运行单元测试"""
    print("🧪 运行单元测试...")
    
    args = [
        'tests/test_base_data_collector.py',
        '-m', 'unit',
        '--tb=short'
    ]
    
    if verbose:
        args.append('-v')
    
    if coverage:
        args.extend(['--cov=services.base_data_collector', '--cov-report=term-missing'])
    
    return run_pytest(args)


def run_api_tests(verbose=False):
    """运行API测试"""
    print("🌐 运行API测试...")
    
    args = [
        'tests/test_base_data_api.py',
        '-m', 'api',
        '--tb=short'
    ]
    
    if verbose:
        args.append('-v')
    
    return run_pytest(args)


def run_integration_tests(verbose=False):
    """运行集成测试"""
    print("🔗 运行集成测试...")
    
    args = [
        'tests/test_base_data_integration.py',
        '-m', 'integration',
        '--tb=short'
    ]
    
    if verbose:
        args.append('-v')
    
    return run_pytest(args)


def run_all_tests(verbose=False, coverage=False, html_report=False):
    """运行所有测试"""
    print("🎯 运行所有基础数据收集器测试...")
    
    args = [
        'tests/test_base_data_collector.py',
        'tests/test_base_data_api.py',
        'tests/test_base_data_integration.py',
        '--tb=short'
    ]
    
    if verbose:
        args.append('-v')
    
    if coverage:
        args.extend([
            '--cov=services.base_data_collector',
            '--cov=api.base_data_api',
            '--cov-report=term-missing'
        ])
        
        if html_report:
            args.append('--cov-report=html:htmlcov')
    
    return run_pytest(args)


def run_performance_tests(verbose=False):
    """运行性能测试"""
    print("⚡ 运行性能测试...")
    
    args = [
        'tests/test_base_data_collector.py::TestPerformanceAndScalability',
        '--tb=short'
    ]
    
    if verbose:
        args.append('-v')
    
    return run_pytest(args)


def run_specific_test(test_path, verbose=False):
    """运行特定测试"""
    print(f"🎯 运行特定测试: {test_path}")
    
    args = [test_path, '--tb=short']
    
    if verbose:
        args.append('-v')
    
    return run_pytest(args)


def generate_test_report():
    """生成测试报告"""
    print("📊 生成测试报告...")
    
    args = [
        'tests/test_base_data_collector.py',
        'tests/test_base_data_api.py',
        'tests/test_base_data_integration.py',
        '--cov=services.base_data_collector',
        '--cov=api.base_data_api',
        '--cov-report=html:htmlcov',
        '--cov-report=xml:coverage.xml',
        '--cov-report=term-missing',
        '--junit-xml=test-results.xml',
        '-v'
    ]
    
    result = run_pytest(args)
    
    if result == 0:
        print("✅ 测试报告生成成功!")
        print("📁 HTML报告: htmlcov/index.html")
        print("📄 XML报告: coverage.xml")
        print("📋 JUnit报告: test-results.xml")
    
    return result


def check_test_dependencies():
    """检查测试依赖"""
    print("🔍 检查测试依赖...")
    
    required_packages = [
        'pytest',
        'pytest-asyncio',
        'pytest-cov',
        'fastapi',
        'httpx'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有测试依赖已满足")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基础数据收集器测试运行器')
    parser.add_argument('--type', choices=['unit', 'api', 'integration', 'all', 'performance'], 
                       default='all', help='测试类型')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--coverage', '-c', action='store_true', help='生成覆盖率报告')
    parser.add_argument('--html-report', action='store_true', help='生成HTML覆盖率报告')
    parser.add_argument('--check-deps', action='store_true', help='检查测试依赖')
    parser.add_argument('--report', action='store_true', help='生成完整测试报告')
    parser.add_argument('--test', help='运行特定测试')
    
    args = parser.parse_args()
    
    print("🧪 基础数据收集器测试运行器")
    print("=" * 60)
    
    # 检查依赖
    if args.check_deps:
        if not check_test_dependencies():
            return 1
        return 0
    
    # 设置测试环境
    setup_test_environment()
    
    # 生成报告
    if args.report:
        return generate_test_report()
    
    # 运行特定测试
    if args.test:
        return run_specific_test(args.test, args.verbose)
    
    # 根据类型运行测试
    if args.type == 'unit':
        return run_unit_tests(args.verbose, args.coverage)
    elif args.type == 'api':
        return run_api_tests(args.verbose)
    elif args.type == 'integration':
        return run_integration_tests(args.verbose)
    elif args.type == 'performance':
        return run_performance_tests(args.verbose)
    elif args.type == 'all':
        return run_all_tests(args.verbose, args.coverage, args.html_report)
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        sys.exit(1)
