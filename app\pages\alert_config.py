"""
Ares预警配置页面
提供预警规则管理、测试和监控界面
"""

import streamlit as st
import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any

from services.alerts import get_alert_engine, AlertType, AlertSeverity, AlertRule, AlertCondition
from services.alert_dsl import get_dsl_parser, parse_alert_rule, validate_alert_dsl, get_dsl_template
from services.notifications import get_alert_notification_service, MessageTemplate


def show_alert_config_page():
    """显示预警配置页面"""
    st.title("🚨 预警系统配置")
    
    # 侧边栏导航
    page = st.sidebar.selectbox(
        "选择功能",
        ["预警规则管理", "规则编辑器", "通知测试", "预警监控", "系统状态"]
    )
    
    if page == "预警规则管理":
        show_rule_management()
    elif page == "规则编辑器":
        show_rule_editor()
    elif page == "通知测试":
        show_notification_test()
    elif page == "预警监控":
        show_alert_monitoring()
    elif page == "系统状态":
        show_system_status()


def show_rule_management():
    """显示规则管理页面"""
    st.header("📋 预警规则管理")
    
    alert_engine = get_alert_engine()
    rules = alert_engine.get_all_rules()
    
    if not rules:
        st.info("暂无预警规则，请创建新规则。")
        return
    
    # 规则列表
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.subheader("规则列表")
        
        # 创建规则数据框
        rule_data = []
        for rule in rules:
            rule_data.append({
                "ID": rule.id,
                "名称": rule.name,
                "类型": rule.alert_type.value,
                "严重性": rule.severity.value,
                "状态": "启用" if rule.enabled else "禁用",
                "触发次数": rule.trigger_count,
                "最后触发": rule.last_triggered.strftime("%Y-%m-%d %H:%M") if rule.last_triggered else "从未",
                "冷却时间": f"{rule.cooldown_minutes}分钟"
            })
        
        df = pd.DataFrame(rule_data)
        
        # 显示规则表格
        selected_indices = st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            selection_mode="single"
        )
        
        # 规则操作
        if selected_indices and len(selected_indices.selection.rows) > 0:
            selected_idx = selected_indices.selection.rows[0]
            selected_rule_id = rule_data[selected_idx]["ID"]
            selected_rule = alert_engine.get_rule(selected_rule_id)
            
            st.subheader("规则详情")
            
            col1_1, col1_2, col1_3 = st.columns(3)
            
            with col1_1:
                if st.button("启用规则" if not selected_rule.enabled else "禁用规则"):
                    if selected_rule.enabled:
                        alert_engine.disable_rule(selected_rule_id)
                        st.success("规则已禁用")
                    else:
                        alert_engine.enable_rule(selected_rule_id)
                        st.success("规则已启用")
                    st.rerun()
            
            with col1_2:
                if st.button("删除规则"):
                    if alert_engine.remove_rule(selected_rule_id):
                        st.success("规则已删除")
                        st.rerun()
                    else:
                        st.error("删除失败")
            
            with col1_3:
                if st.button("测试规则"):
                    test_rule(selected_rule)
            
            # 显示规则详细信息
            st.write("**描述:**", selected_rule.description)
            st.write("**条件:**")
            for i, condition in enumerate(selected_rule.conditions):
                st.write(f"  {i+1}. {condition.field} {condition.operator} {condition.value}")
            st.write("**逻辑操作符:**", selected_rule.logic_operator)
    
    with col2:
        st.subheader("快速操作")
        
        if st.button("创建新规则"):
            st.session_state.show_rule_editor = True
            st.rerun()
        
        if st.button("导入规则"):
            show_rule_import()
        
        if st.button("导出规则"):
            export_rules(rules)
        
        # 规则统计
        st.subheader("规则统计")
        enabled_count = len([r for r in rules if r.enabled])
        st.metric("总规则数", len(rules))
        st.metric("启用规则", enabled_count)
        st.metric("禁用规则", len(rules) - enabled_count)


def show_rule_editor():
    """显示规则编辑器"""
    st.header("✏️ 规则编辑器")
    
    # 编辑模式选择
    edit_mode = st.radio(
        "编辑模式",
        ["可视化编辑器", "DSL编辑器"],
        horizontal=True
    )
    
    if edit_mode == "可视化编辑器":
        show_visual_editor()
    else:
        show_dsl_editor()


def show_visual_editor():
    """显示可视化编辑器"""
    st.subheader("可视化规则编辑器")
    
    with st.form("rule_form"):
        # 基本信息
        col1, col2 = st.columns(2)
        
        with col1:
            rule_name = st.text_input("规则名称", placeholder="输入规则名称")
            alert_type = st.selectbox(
                "预警类型",
                options=[t.value for t in AlertType],
                format_func=lambda x: {
                    "price_opportunity": "价格机会",
                    "price_risk": "价格风险",
                    "volume_spike": "交易量激增",
                    "portfolio_rebalance": "投资组合再平衡",
                    "holding_risk": "持仓风险",
                    "market_anomaly": "市场异常"
                }.get(x, x)
            )
        
        with col2:
            severity = st.selectbox(
                "严重性",
                options=[s.value for s in AlertSeverity],
                format_func=lambda x: {
                    "low": "低",
                    "medium": "中",
                    "high": "高",
                    "critical": "严重"
                }.get(x, x)
            )
            cooldown_minutes = st.number_input("冷却时间(分钟)", min_value=1, value=60)
        
        description = st.text_area("规则描述", placeholder="描述预警规则的用途和触发条件")
        
        # 条件设置
        st.subheader("预警条件")
        
        num_conditions = st.number_input("条件数量", min_value=1, max_value=5, value=1)
        
        conditions = []
        for i in range(num_conditions):
            st.write(f"**条件 {i+1}**")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                field = st.selectbox(
                    f"字段 {i+1}",
                    options=[
                        "current_price", "price_change_percent", "volume_24h",
                        "volume_change_percent", "holding_quantity", "holding_value",
                        "risk_level", "portfolio_deviation", "volatility"
                    ],
                    key=f"field_{i}"
                )
            
            with col2:
                operator = st.selectbox(
                    f"操作符 {i+1}",
                    options=[">", "<", ">=", "<=", "==", "!="],
                    key=f"operator_{i}"
                )
            
            with col3:
                value = st.number_input(f"值 {i+1}", key=f"value_{i}")
            
            conditions.append(AlertCondition(field, operator, value))
        
        # 逻辑操作符
        if num_conditions > 1:
            logic_operator = st.radio(
                "条件逻辑",
                ["AND", "OR"],
                help="AND: 所有条件都满足时触发; OR: 任一条件满足时触发"
            )
        else:
            logic_operator = "AND"
        
        # 提交按钮
        submitted = st.form_submit_button("创建规则")
        
        if submitted:
            if rule_name and description:
                # 创建规则
                rule = AlertRule(
                    id=f"custom_{int(datetime.now().timestamp())}",
                    name=rule_name,
                    description=description,
                    alert_type=AlertType(alert_type),
                    severity=AlertSeverity(severity),
                    conditions=conditions,
                    logic_operator=logic_operator,
                    cooldown_minutes=cooldown_minutes
                )
                
                # 添加到引擎
                alert_engine = get_alert_engine()
                alert_engine.add_rule(rule)
                
                st.success(f"规则 '{rule_name}' 创建成功！")
            else:
                st.error("请填写规则名称和描述")


def show_dsl_editor():
    """显示DSL编辑器"""
    st.subheader("DSL规则编辑器")
    
    # 模板选择
    template_type = st.selectbox(
        "选择模板",
        options=[t.value for t in AlertType],
        format_func=lambda x: {
            "price_opportunity": "价格机会模板",
            "price_risk": "价格风险模板",
            "volume_spike": "交易量激增模板",
            "portfolio_rebalance": "投资组合再平衡模板"
        }.get(x, x)
    )
    
    if st.button("加载模板"):
        template = get_dsl_template(AlertType(template_type))
        st.session_state.dsl_text = template
    
    # DSL编辑器
    dsl_text = st.text_area(
        "DSL规则",
        value=st.session_state.get('dsl_text', ''),
        height=300,
        help="使用自然语言描述预警规则"
    )
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("验证语法"):
            validation = validate_alert_dsl(dsl_text)
            
            if validation['valid']:
                st.success("✅ DSL语法正确")
            else:
                st.error("❌ DSL语法错误")
                for error in validation['errors']:
                    st.error(f"错误: {error}")
            
            for warning in validation['warnings']:
                st.warning(f"警告: {warning}")
    
    with col2:
        if st.button("创建规则"):
            parse_result = parse_alert_rule(dsl_text)
            
            if parse_result.success:
                alert_engine = get_alert_engine()
                alert_engine.add_rule(parse_result.rule)
                st.success(f"规则 '{parse_result.rule.name}' 创建成功！")
            else:
                st.error(f"创建失败: {parse_result.error}")


def show_notification_test():
    """显示通知测试页面"""
    st.header("📧 通知测试")
    
    notification_service = get_alert_notification_service()
    
    # 模板选择
    template_options = [t.value for t in MessageTemplate]
    selected_template = st.selectbox(
        "选择通知模板",
        options=template_options,
        format_func=lambda x: {
            "price_opportunity": "价格机会通知",
            "price_risk": "价格风险通知",
            "volume_spike": "交易量激增通知",
            "portfolio_rebalance": "投资组合再平衡通知",
            "holding_risk": "持仓风险通知",
            "market_anomaly": "市场异常通知"
        }.get(x, x)
    )
    
    # 测试数据编辑
    st.subheader("测试数据")
    
    test_data = notification_service._get_test_data(selected_template)
    
    # 允许用户编辑测试数据
    edited_data = {}
    for key, value in test_data.items():
        if isinstance(value, (int, float)):
            edited_data[key] = st.number_input(f"{key}", value=value)
        else:
            edited_data[key] = st.text_input(f"{key}", value=str(value))
    
    # 发送测试通知
    if st.button("发送测试通知"):
        with st.spinner("发送中..."):
            try:
                success = asyncio.run(
                    notification_service.send_test_notification(selected_template, edited_data)
                )
                
                if success:
                    st.success("✅ 测试通知发送成功！")
                else:
                    st.error("❌ 测试通知发送失败")
            except Exception as e:
                st.error(f"发送失败: {str(e)}")
    
    # 频率限制状态
    st.subheader("频率限制状态")
    rate_limit_status = notification_service.get_rate_limit_status()
    
    if rate_limit_status:
        for key, status in rate_limit_status.items():
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write(f"**{status['template_type']}** ({status['severity']})")
            
            with col2:
                if status['can_send']:
                    st.success("可发送")
                else:
                    st.warning(f"冷却中 ({status['remaining_cooldown']:.0f}s)")
            
            with col3:
                st.write(f"上次发送: {status['last_sent']}")
    else:
        st.info("暂无发送记录")


def show_alert_monitoring():
    """显示预警监控页面"""
    st.header("📊 预警监控")
    
    alert_engine = get_alert_engine()
    
    # 预警统计
    stats = alert_engine.get_alert_stats()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总预警数", stats['total_events'])
    
    with col2:
        st.metric("待处理", stats['pending_count'])
    
    with col3:
        st.metric("已确认", stats['acknowledged_count'])
    
    with col4:
        st.metric("已解决", stats['resolved_count'])
    
    # 按类型统计
    st.subheader("按类型统计")
    type_stats_df = pd.DataFrame(
        list(stats['type_stats'].items()),
        columns=['预警类型', '数量']
    )
    st.bar_chart(type_stats_df.set_index('预警类型'))
    
    # 按严重性统计
    st.subheader("按严重性统计")
    severity_stats_df = pd.DataFrame(
        list(stats['severity_stats'].items()),
        columns=['严重性', '数量']
    )
    st.bar_chart(severity_stats_df.set_index('严重性'))
    
    # 最近预警事件
    st.subheader("最近预警事件")
    recent_events = alert_engine.get_recent_events(20)
    
    if recent_events:
        event_data = []
        for event in recent_events:
            event_data.append({
                "时间": event.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "规则": event.rule_name,
                "类型": event.alert_type.value,
                "严重性": event.severity.value,
                "消息": event.message[:50] + "..." if len(event.message) > 50 else event.message,
                "状态": "已解决" if event.resolved else ("已确认" if event.acknowledged else "待处理")
            })
        
        events_df = pd.DataFrame(event_data)
        st.dataframe(events_df, use_container_width=True)
    else:
        st.info("暂无预警事件")


def show_system_status():
    """显示系统状态页面"""
    st.header("⚙️ 系统状态")
    
    alert_engine = get_alert_engine()
    notification_service = get_alert_notification_service()
    
    # 预警引擎状态
    st.subheader("预警引擎状态")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**规则统计:**")
        rules = alert_engine.get_all_rules()
        enabled_rules = [r for r in rules if r.enabled]
        
        st.write(f"- 总规则数: {len(rules)}")
        st.write(f"- 启用规则: {len(enabled_rules)}")
        st.write(f"- 禁用规则: {len(rules) - len(enabled_rules)}")
    
    with col2:
        st.write("**事件统计:**")
        stats = alert_engine.get_alert_stats()
        
        st.write(f"- 总事件数: {stats['total_events']}")
        st.write(f"- 待处理: {stats['pending_count']}")
        st.write(f"- 已处理: {stats['acknowledged_count'] + stats['resolved_count']}")
    
    # 通知服务状态
    st.subheader("通知服务状态")
    
    template_list = notification_service.get_template_list()
    st.write(f"**可用模板:** {len(template_list)}")
    
    for template in template_list:
        st.write(f"- {template['emoji']} {template['name']}")


def test_rule(rule: AlertRule):
    """测试规则"""
    st.subheader(f"测试规则: {rule.name}")
    
    # 生成测试数据
    test_data = {
        'item_id': 'test_item_001',
        'item_name': 'Test Item',
        'current_price': 100.0,
        'price_change_percent': -20.0,
        'volume_24h': 50,
        'volume_change_percent': 200.0,
        'holding_quantity': 1,
        'holding_value': 100.0,
        'risk_level': 'high',
        'portfolio_deviation': 15.0,
        'volatility': 0.25
    }
    
    # 测试规则
    result = rule.evaluate(test_data)
    
    if result:
        st.success("✅ 规则测试通过 - 条件满足，会触发预警")
    else:
        st.info("ℹ️ 规则测试完成 - 条件不满足，不会触发预警")
    
    # 显示测试详情
    st.write("**测试数据:**")
    st.json(test_data)
    
    st.write("**条件评估:**")
    for i, condition in enumerate(rule.conditions):
        field_value = test_data.get(condition.field, "N/A")
        condition_result = condition.evaluate(test_data)
        
        status = "✅" if condition_result else "❌"
        st.write(f"{status} 条件 {i+1}: {condition.field} {condition.operator} {condition.value} (实际值: {field_value})")


def show_rule_import():
    """显示规则导入"""
    st.subheader("导入规则")
    
    uploaded_file = st.file_uploader("选择规则文件", type=['json'])
    
    if uploaded_file is not None:
        try:
            import json
            rules_data = json.load(uploaded_file)
            
            st.write(f"发现 {len(rules_data)} 个规则:")
            for rule_data in rules_data:
                st.write(f"- {rule_data.get('name', 'Unknown')}")
            
            if st.button("确认导入"):
                # TODO: 实现规则导入逻辑
                st.success("规则导入成功！")
        
        except Exception as e:
            st.error(f"文件格式错误: {str(e)}")


def export_rules(rules: List[AlertRule]):
    """导出规则"""
    rules_data = []
    
    for rule in rules:
        rule_data = {
            'id': rule.id,
            'name': rule.name,
            'description': rule.description,
            'alert_type': rule.alert_type.value,
            'severity': rule.severity.value,
            'conditions': [
                {
                    'field': c.field,
                    'operator': c.operator,
                    'value': c.value
                }
                for c in rule.conditions
            ],
            'logic_operator': rule.logic_operator,
            'cooldown_minutes': rule.cooldown_minutes,
            'enabled': rule.enabled
        }
        rules_data.append(rule_data)
    
    import json
    rules_json = json.dumps(rules_data, indent=2, ensure_ascii=False)
    
    st.download_button(
        label="下载规则文件",
        data=rules_json,
        file_name=f"alert_rules_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )


if __name__ == "__main__":
    show_alert_config_page()
