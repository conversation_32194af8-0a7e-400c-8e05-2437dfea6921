#!/usr/bin/env python3
"""
Ares系统缓存管理工具
提供缓存状态查看、清理、预热和性能分析功能
"""

import os
import sys
import asyncio
import argparse
import json
import time
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.cache import get_cache_manager, setup_cache, CacheConfig
from core.decorators import get_cache_warmer
from core.config import get_config_manager


def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_section(title: str):
    """打印章节标题"""
    print(f"\n--- {title} ---")


def format_bytes(bytes_value: int) -> str:
    """格式化字节数"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_value < 1024:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024
    return f"{bytes_value:.1f} TB"


def format_percentage(value: float) -> str:
    """格式化百分比"""
    return f"{value:.1f}%"


async def show_cache_status():
    """显示缓存状态"""
    print_section("Cache Status")
    
    try:
        cache_manager = get_cache_manager()
        stats = await cache_manager.get_cache_stats()
        
        # 显示整体统计
        print(f"Total Operations: {stats.get('total_operations', 0)}")
        print(f"Overall Hit Rate: {format_percentage(stats.get('overall_hit_rate', 0))}")
        
        # L1缓存统计
        l1_stats = stats.get('l1_cache', {})
        if l1_stats:
            print_section("L1 Cache (Memory)")
            print(f"  Total Items:    {l1_stats.get('total_items', 0)}")
            print(f"  Hits:           {l1_stats.get('hits', 0)}")
            print(f"  Misses:         {l1_stats.get('misses', 0)}")
            print(f"  Sets:           {l1_stats.get('sets', 0)}")
            print(f"  Deletes:        {l1_stats.get('deletes', 0)}")
            print(f"  Evictions:      {l1_stats.get('evictions', 0)}")
            print(f"  Hit Rate:       {format_percentage(l1_stats.get('hit_rate', 0))}")
            print(f"  Memory Usage:   {format_bytes(l1_stats.get('memory_usage', 0))}")
        
        # L2缓存统计（Redis）
        l2_stats = stats.get('l2_cache')
        if l2_stats:
            print_section("L2 Cache (Redis)")
            print(f"  Hits:                    {l2_stats.get('hits', 0)}")
            print(f"  Misses:                  {l2_stats.get('misses', 0)}")
            print(f"  Sets:                    {l2_stats.get('sets', 0)}")
            print(f"  Deletes:                 {l2_stats.get('deletes', 0)}")
            print(f"  Errors:                  {l2_stats.get('errors', 0)}")
            print(f"  Hit Rate:                {format_percentage(l2_stats.get('hit_rate', 0))}")
            print(f"  Memory Used:             {format_bytes(l2_stats.get('redis_memory_used', 0))}")
            print(f"  Connected Clients:       {l2_stats.get('redis_connected_clients', 0)}")
            print(f"  Total Commands:          {l2_stats.get('redis_total_commands_processed', 0)}")
            print(f"  Redis Keyspace Hits:     {l2_stats.get('redis_keyspace_hits', 0)}")
            print(f"  Redis Keyspace Misses:   {l2_stats.get('redis_keyspace_misses', 0)}")
        else:
            print_section("L2 Cache (Redis)")
            print("  Status: Not available or not connected")
    
    except Exception as e:
        print(f"Failed to get cache status: {str(e)}")


async def clear_cache(cache_type: str = "all"):
    """清空缓存"""
    print_section(f"Clearing Cache ({cache_type})")
    
    try:
        cache_manager = get_cache_manager()
        
        if cache_type == "all":
            success = await cache_manager.clear_cache()
            if success:
                print("✓ All cache cleared successfully")
            else:
                print("✗ Failed to clear cache")
        
        elif cache_type == "l1":
            success = await cache_manager.cache.clear(use_l1=True, use_l2=False)
            if success:
                print("✓ L1 cache cleared successfully")
            else:
                print("✗ Failed to clear L1 cache")
        
        elif cache_type == "l2":
            success = await cache_manager.cache.clear(use_l1=False, use_l2=True)
            if success:
                print("✓ L2 cache cleared successfully")
            else:
                print("✗ Failed to clear L2 cache")
        
        else:
            print(f"Unknown cache type: {cache_type}")
    
    except Exception as e:
        print(f"Failed to clear cache: {str(e)}")


async def test_cache_performance(operations: int = 1000):
    """测试缓存性能"""
    print_section(f"Cache Performance Test ({operations} operations)")
    
    try:
        cache_manager = get_cache_manager()
        
        # 测试数据
        test_data = {f"test_key_{i}": f"test_value_{i}" for i in range(operations)}
        
        # 测试写入性能
        print("Testing write performance...")
        start_time = time.time()
        
        for key, value in test_data.items():
            await cache_manager.cache.set(key, value, ttl=300)
        
        write_duration = time.time() - start_time
        write_ops_per_sec = operations / write_duration
        
        print(f"  Write Operations: {operations}")
        print(f"  Write Duration:   {write_duration:.3f}s")
        print(f"  Write Ops/sec:    {write_ops_per_sec:.1f}")
        
        # 测试读取性能
        print("\nTesting read performance...")
        start_time = time.time()
        
        hit_count = 0
        for key in test_data.keys():
            value = await cache_manager.cache.get(key)
            if value is not None:
                hit_count += 1
        
        read_duration = time.time() - start_time
        read_ops_per_sec = operations / read_duration
        hit_rate = (hit_count / operations) * 100
        
        print(f"  Read Operations:  {operations}")
        print(f"  Read Duration:    {read_duration:.3f}s")
        print(f"  Read Ops/sec:     {read_ops_per_sec:.1f}")
        print(f"  Hit Rate:         {format_percentage(hit_rate)}")
        
        # 清理测试数据
        print("\nCleaning up test data...")
        for key in test_data.keys():
            await cache_manager.cache.delete(key)
        
        print("✓ Performance test completed")
    
    except Exception as e:
        print(f"Performance test failed: {str(e)}")


async def warm_up_cache(item_count: int = 100):
    """预热缓存"""
    print_section(f"Cache Warm-up ({item_count} items)")
    
    try:
        cache_warmer = get_cache_warmer()
        
        # 生成测试饰品ID
        item_ids = [f"item_{i:04d}" for i in range(item_count)]
        
        print(f"Warming up cache for {len(item_ids)} items...")
        start_time = time.time()
        
        await cache_warmer.warm_up_item_data(item_ids)
        
        duration = time.time() - start_time
        print(f"✓ Cache warm-up completed in {duration:.2f}s")
    
    except Exception as e:
        print(f"Cache warm-up failed: {str(e)}")


async def analyze_cache_usage():
    """分析缓存使用情况"""
    print_section("Cache Usage Analysis")
    
    try:
        cache_manager = get_cache_manager()
        stats = await cache_manager.get_cache_stats()
        
        # 分析L1缓存
        l1_stats = stats.get('l1_cache', {})
        if l1_stats:
            total_requests = l1_stats.get('hits', 0) + l1_stats.get('misses', 0)
            
            print("L1 Cache Analysis:")
            print(f"  Efficiency Score:     {format_percentage(l1_stats.get('hit_rate', 0))}")
            print(f"  Total Requests:       {total_requests}")
            print(f"  Memory Utilization:   {format_bytes(l1_stats.get('memory_usage', 0))}")
            print(f"  Eviction Rate:        {l1_stats.get('evictions', 0)} evictions")
            
            # 效率评估
            hit_rate = l1_stats.get('hit_rate', 0)
            if hit_rate >= 80:
                efficiency = "Excellent"
            elif hit_rate >= 60:
                efficiency = "Good"
            elif hit_rate >= 40:
                efficiency = "Fair"
            else:
                efficiency = "Poor"
            
            print(f"  Efficiency Rating:    {efficiency}")
        
        # 分析L2缓存
        l2_stats = stats.get('l2_cache')
        if l2_stats:
            print("\nL2 Cache Analysis:")
            print(f"  Redis Memory:         {l2_stats.get('redis_memory_human', 'N/A')}")
            print(f"  Error Rate:           {l2_stats.get('errors', 0)} errors")
            print(f"  Connection Status:    {'Connected' if l2_stats.get('redis_connected_clients', 0) > 0 else 'Disconnected'}")
        
        # 整体建议
        print("\nRecommendations:")
        overall_hit_rate = stats.get('overall_hit_rate', 0)
        
        if overall_hit_rate < 50:
            print("  • Consider increasing cache TTL values")
            print("  • Review cache key strategies")
            print("  • Implement cache warming for frequently accessed data")
        elif overall_hit_rate < 70:
            print("  • Fine-tune cache eviction policies")
            print("  • Monitor cache key patterns")
        else:
            print("  • Cache performance is good")
            print("  • Continue monitoring for optimization opportunities")
    
    except Exception as e:
        print(f"Cache analysis failed: {str(e)}")


async def export_cache_stats(output_file: str):
    """导出缓存统计"""
    print_section(f"Exporting Cache Stats to {output_file}")
    
    try:
        cache_manager = get_cache_manager()
        stats = await cache_manager.get_cache_stats()
        
        # 添加时间戳
        export_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'cache_stats': stats
        }
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✓ Cache stats exported to {output_file}")
    
    except Exception as e:
        print(f"Failed to export cache stats: {str(e)}")


async def monitor_cache(interval: int = 5, duration: int = 60):
    """监控缓存状态"""
    print_section(f"Cache Monitoring (interval: {interval}s, duration: {duration}s)")
    
    try:
        cache_manager = get_cache_manager()
        start_time = time.time()
        
        print("Timestamp           | L1 Hit Rate | L2 Hit Rate | Total Ops | Memory")
        print("-" * 75)
        
        while time.time() - start_time < duration:
            stats = await cache_manager.get_cache_stats()
            
            timestamp = datetime.now().strftime('%H:%M:%S')
            l1_hit_rate = stats.get('l1_cache', {}).get('hit_rate', 0)
            l2_hit_rate = stats.get('l2_cache', {}).get('hit_rate', 0) if stats.get('l2_cache') else 0
            total_ops = stats.get('total_operations', 0)
            memory_usage = stats.get('l1_cache', {}).get('memory_usage', 0)
            
            print(f"{timestamp:19} | {l1_hit_rate:10.1f}% | {l2_hit_rate:10.1f}% | {total_ops:8d} | {format_bytes(memory_usage):>8}")
            
            await asyncio.sleep(interval)
        
        print("\n✓ Monitoring completed")
    
    except KeyboardInterrupt:
        print("\n✓ Monitoring stopped by user")
    except Exception as e:
        print(f"Monitoring failed: {str(e)}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Ares系统缓存管理工具')
    parser.add_argument('--status', action='store_true', help='显示缓存状态')
    parser.add_argument('--clear', choices=['all', 'l1', 'l2'], help='清空缓存')
    parser.add_argument('--test', type=int, metavar='OPS', help='测试缓存性能（指定操作数）')
    parser.add_argument('--warmup', type=int, metavar='COUNT', help='预热缓存（指定项目数）')
    parser.add_argument('--analyze', action='store_true', help='分析缓存使用情况')
    parser.add_argument('--export', metavar='FILE', help='导出缓存统计到文件')
    parser.add_argument('--monitor', action='store_true', help='监控缓存状态')
    parser.add_argument('--interval', type=int, default=5, help='监控间隔（秒）')
    parser.add_argument('--duration', type=int, default=60, help='监控持续时间（秒）')
    
    args = parser.parse_args()
    
    # 设置缓存
    try:
        # 尝试从配置管理器获取Redis URL
        config_manager = get_config_manager()
        redis_url = config_manager.get('redis_url')
        setup_cache(redis_url=redis_url)
    except Exception as e:
        print(f"Warning: Failed to setup cache with config: {str(e)}")
        # 使用默认配置，不依赖配置管理器
        from core.cache import CacheConfig
        config = CacheConfig()
        setup_cache(config=config, redis_url=None)
    
    print_header("Ares Cache Management Tool")
    
    try:
        if args.status:
            await show_cache_status()
        
        elif args.clear:
            await clear_cache(args.clear)
        
        elif args.test:
            await test_cache_performance(args.test)
        
        elif args.warmup:
            await warm_up_cache(args.warmup)
        
        elif args.analyze:
            await analyze_cache_usage()
        
        elif args.export:
            await export_cache_stats(args.export)
        
        elif args.monitor:
            await monitor_cache(args.interval, args.duration)
        
        else:
            # 默认显示状态
            await show_cache_status()
    
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
