"""
多因子筛选算法测试
验证筛选器和评分引擎功能
"""

import pytest
import json
import time
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.filter import (
    IntelligentFilter, FilterCondition, FilterOperator, SortCriteria, SortOrder,
    FilterPreset, get_intelligent_filter
)
from core.scoring import ScoringEngine, ScoreWeights, ItemScore, get_scoring_engine


class TestFilterCondition:
    """筛选条件测试"""
    
    def test_equals_condition(self):
        """测试等于条件"""
        condition = FilterCondition("category", FilterOperator.EQUALS, "rifle")
        
        assert condition.evaluate({"category": "rifle"}) == True
        assert condition.evaluate({"category": "pistol"}) == False
        assert condition.evaluate({"other": "value"}) == False
    
    def test_greater_than_condition(self):
        """测试大于条件"""
        condition = FilterCondition("price", FilterOperator.GREATER_THAN, 100.0)
        
        assert condition.evaluate({"price": 150.0}) == True
        assert condition.evaluate({"price": 50.0}) == False
        assert condition.evaluate({"price": 100.0}) == False
    
    def test_between_condition(self):
        """测试范围条件"""
        condition = FilterCondition("price", FilterOperator.BETWEEN, [50.0, 200.0])
        
        assert condition.evaluate({"price": 100.0}) == True
        assert condition.evaluate({"price": 50.0}) == True
        assert condition.evaluate({"price": 200.0}) == True
        assert condition.evaluate({"price": 30.0}) == False
        assert condition.evaluate({"price": 250.0}) == False
    
    def test_in_condition(self):
        """测试包含条件"""
        condition = FilterCondition("category", FilterOperator.IN, ["rifle", "pistol"])
        
        assert condition.evaluate({"category": "rifle"}) == True
        assert condition.evaluate({"category": "pistol"}) == True
        assert condition.evaluate({"category": "knife"}) == False
    
    def test_nested_field_access(self):
        """测试嵌套字段访问"""
        condition = FilterCondition("item.price", FilterOperator.GREATER_THAN, 100.0)
        
        data = {
            "item": {
                "price": 150.0,
                "name": "AK-47"
            }
        }
        
        assert condition.evaluate(data) == True
        assert condition.evaluate({"item": {"price": 50.0}}) == False
        assert condition.evaluate({"other": "value"}) == False


class TestScoringEngine:
    """评分引擎测试"""
    
    @pytest.fixture
    def scoring_engine(self):
        """创建评分引擎实例"""
        return ScoringEngine()
    
    @pytest.fixture
    def sample_item_data(self):
        """创建样本饰品数据"""
        return {
            "item_id": "test_item_001",
            "item_name": "AK-47 | Redline (Field-Tested)",
            "current_price": 52.30,
            "ask_price": 55.00,
            "bid_price": 50.00,
            "volume_24h": 156,
            "volume_change_24h": 25.5,
            "price_change_24h": 3.2,
            "price_change_7d": -8.5,
            "price_change_30d": 12.1,
            "volatility": 0.25,
            "liquidity_score": 0.75
        }
    
    def test_spread_score_calculation(self, scoring_engine, sample_item_data):
        """测试价差评分计算"""
        score = scoring_engine._calculate_spread_score(sample_item_data)
        
        # 价差 = (55-50)/55 = 9.09%
        expected_score = 9.09 / 50.0  # 相对于最大阈值50%
        
        assert 0.0 <= score <= 1.0
        assert abs(score - expected_score) < 0.01
    
    def test_volume_score_calculation(self, scoring_engine, sample_item_data):
        """测试交易量评分计算"""
        score = scoring_engine._calculate_volume_score(sample_item_data)
        
        assert 0.0 <= score <= 1.0
        assert score > 0  # 有交易量应该有正分
    
    def test_volatility_score_calculation(self, scoring_engine, sample_item_data):
        """测试波动性评分计算"""
        score = scoring_engine._calculate_volatility_score(sample_item_data)
        
        assert 0.0 <= score <= 1.0
        
        # 测试最优波动性
        optimal_data = sample_item_data.copy()
        optimal_data["volatility"] = 0.25  # 最优波动性
        optimal_score = scoring_engine._calculate_volatility_score(optimal_data)
        
        # 过高波动性
        high_vol_data = sample_item_data.copy()
        high_vol_data["volatility"] = 0.8
        high_vol_score = scoring_engine._calculate_volatility_score(high_vol_data)
        
        assert optimal_score >= high_vol_score
    
    def test_trend_score_calculation(self, scoring_engine, sample_item_data):
        """测试趋势评分计算"""
        score = scoring_engine._calculate_trend_score(sample_item_data)
        
        assert 0.0 <= score <= 1.0
    
    def test_opportunity_score_calculation(self, scoring_engine, sample_item_data):
        """测试机会评分计算"""
        score = scoring_engine.calculate_opportunity_score(sample_item_data)
        
        assert isinstance(score, ItemScore)
        assert score.item_id == "test_item_001"
        assert 0.0 <= score.total_score <= 1.0
        assert 0.0 <= score.confidence <= 1.0
        assert score.risk_level in ['low', 'medium', 'high']
        assert score.recommendation in ['strong_buy', 'buy', 'speculative_buy', 'hold', 'weak_sell', 'sell']
    
    def test_custom_weights(self, scoring_engine, sample_item_data):
        """测试自定义权重"""
        # 默认权重
        default_score = scoring_engine.calculate_opportunity_score(sample_item_data)
        
        # 自定义权重（强调价差）
        custom_weights = ScoreWeights(
            spread_weight=0.5,
            volume_weight=0.2,
            volatility_weight=0.1,
            trend_weight=0.1,
            liquidity_weight=0.05,
            momentum_weight=0.05
        )
        
        custom_score = scoring_engine.calculate_opportunity_score(sample_item_data, custom_weights)
        
        # 评分应该不同
        assert custom_score.total_score != default_score.total_score
    
    def test_batch_scoring(self, scoring_engine):
        """测试批量评分"""
        items = [
            {
                "item_id": "item_001",
                "current_price": 100.0,
                "ask_price": 105.0,
                "bid_price": 95.0,
                "volume_24h": 50,
                "volatility": 0.2
            },
            {
                "item_id": "item_002",
                "current_price": 200.0,
                "ask_price": 210.0,
                "bid_price": 190.0,
                "volume_24h": 100,
                "volatility": 0.3
            }
        ]
        
        scores = scoring_engine.batch_calculate_scores(items)
        
        assert len(scores) == 2
        assert all(isinstance(score, ItemScore) for score in scores)
        assert scores[0].item_id == "item_001"
        assert scores[1].item_id == "item_002"


class TestIntelligentFilter:
    """智能筛选器测试"""
    
    @pytest.fixture
    def intelligent_filter(self):
        """创建智能筛选器实例"""
        return IntelligentFilter()
    
    @pytest.fixture
    def sample_items(self):
        """创建样本饰品列表"""
        return [
            {
                "item_id": "item_001",
                "item_name": "AK-47 | Redline",
                "category": "rifle",
                "current_price": 52.30,
                "ask_price": 55.00,
                "bid_price": 50.00,
                "volume_24h": 156,
                "price_change_24h": 3.2,
                "price_change_7d": -8.5,
                "volatility": 0.25
            },
            {
                "item_id": "item_002",
                "item_name": "AWP | Dragon Lore",
                "category": "sniper",
                "current_price": 3150.00,
                "ask_price": 3200.00,
                "bid_price": 3100.00,
                "volume_24h": 23,
                "price_change_24h": 1.5,
                "price_change_7d": 5.2,
                "volatility": 0.15
            },
            {
                "item_id": "item_003",
                "item_name": "Glock-18 | Fade",
                "category": "pistol",
                "current_price": 285.50,
                "ask_price": 290.00,
                "bid_price": 280.00,
                "volume_24h": 67,
                "price_change_24h": -2.1,
                "price_change_7d": 8.3,
                "volatility": 0.35
            }
        ]
    
    def test_basic_filtering(self, intelligent_filter, sample_items):
        """测试基本筛选功能"""
        # 筛选价格大于100的饰品
        conditions = [
            FilterCondition("current_price", FilterOperator.GREATER_THAN, 100.0)
        ]
        
        results = intelligent_filter.filter_items(
            items=sample_items,
            conditions=conditions,
            use_cache=False
        )
        
        # 应该筛选出2个饰品（AWP和Glock）
        assert len(results) == 2
        assert all(item["current_price"] > 100.0 for item in results)
    
    def test_multiple_conditions(self, intelligent_filter, sample_items):
        """测试多条件筛选"""
        conditions = [
            FilterCondition("current_price", FilterOperator.GREATER_THAN, 50.0),
            FilterCondition("volume_24h", FilterOperator.GREATER_THAN, 30),
            FilterCondition("category", FilterOperator.IN, ["rifle", "pistol"])
        ]
        
        results = intelligent_filter.filter_items(
            items=sample_items,
            conditions=conditions,
            use_cache=False
        )
        
        # 应该筛选出符合所有条件的饰品
        assert len(results) >= 1
        for item in results:
            assert item["current_price"] > 50.0
            assert item["volume_24h"] > 30
            assert item["category"] in ["rifle", "pistol"]
    
    def test_sorting(self, intelligent_filter, sample_items):
        """测试排序功能"""
        sort_criteria = [
            SortCriteria("current_price", SortOrder.DESC)
        ]
        
        results = intelligent_filter.filter_items(
            items=sample_items,
            sort_criteria=sort_criteria,
            use_cache=False
        )
        
        # 结果应该按价格降序排列
        prices = [item["current_price"] for item in results]
        assert prices == sorted(prices, reverse=True)
    
    def test_limit_results(self, intelligent_filter, sample_items):
        """测试结果限制"""
        results = intelligent_filter.filter_items(
            items=sample_items,
            limit=2,
            use_cache=False
        )
        
        assert len(results) <= 2
    
    def test_scoring_integration(self, intelligent_filter, sample_items):
        """测试评分集成"""
        results = intelligent_filter.filter_items(
            items=sample_items,
            use_cache=False
        )
        
        # 所有结果都应该有评分
        for item in results:
            assert "_score" in item
            assert "_score_breakdown" in item
            assert 0.0 <= item["_score"] <= 1.0
    
    def test_cache_functionality(self, intelligent_filter, sample_items):
        """测试缓存功能"""
        conditions = [
            FilterCondition("current_price", FilterOperator.GREATER_THAN, 100.0)
        ]
        
        # 第一次调用
        start_time = time.time()
        results1 = intelligent_filter.filter_items(
            items=sample_items,
            conditions=conditions,
            use_cache=True
        )
        first_call_time = time.time() - start_time
        
        # 第二次调用（应该使用缓存）
        start_time = time.time()
        results2 = intelligent_filter.filter_items(
            items=sample_items,
            conditions=conditions,
            use_cache=True
        )
        second_call_time = time.time() - start_time
        
        # 结果应该相同
        assert len(results1) == len(results2)
        
        # 第二次调用应该更快（使用缓存）
        # 注意：在测试环境中时间差异可能很小，这里只做基本检查
        assert second_call_time <= first_call_time * 2


class TestFilterPreset:
    """筛选预设测试"""
    
    @pytest.fixture
    def intelligent_filter(self):
        """创建智能筛选器实例"""
        return IntelligentFilter()
    
    def test_preset_creation(self, intelligent_filter):
        """测试预设创建"""
        preset = FilterPreset(
            id="test_preset",
            name="测试预设",
            description="测试用预设",
            conditions=[
                FilterCondition("current_price", FilterOperator.GREATER_THAN, 100.0)
            ],
            sort_criteria=[
                SortCriteria("current_price", SortOrder.DESC)
            ]
        )
        
        # 保存预设
        success = intelligent_filter.save_preset(preset)
        assert success == True
        
        # 加载预设
        loaded_preset = intelligent_filter.load_preset("test_preset")
        assert loaded_preset is not None
        assert loaded_preset.name == "测试预设"
        assert len(loaded_preset.conditions) == 1
    
    def test_preset_serialization(self):
        """测试预设序列化"""
        preset = FilterPreset(
            id="test_preset",
            name="测试预设",
            description="测试用预设",
            conditions=[
                FilterCondition("current_price", FilterOperator.GREATER_THAN, 100.0)
            ],
            sort_criteria=[
                SortCriteria("current_price", SortOrder.DESC)
            ]
        )
        
        # 转换为字典
        preset_dict = preset.to_dict()
        assert preset_dict["id"] == "test_preset"
        assert preset_dict["name"] == "测试预设"
        assert len(preset_dict["conditions"]) == 1
        
        # 从字典创建
        restored_preset = FilterPreset.from_dict(preset_dict)
        assert restored_preset.id == preset.id
        assert restored_preset.name == preset.name
        assert len(restored_preset.conditions) == len(preset.conditions)
    
    def test_default_presets_loading(self, intelligent_filter):
        """测试默认预设加载"""
        presets = intelligent_filter.get_all_presets()
        
        # 应该有默认预设
        assert len(presets) > 0
        
        # 检查特定预设
        preset_names = [preset.name for preset in presets]
        assert "高价值机会" in preset_names
        assert "低风险稳健" in preset_names
        assert "热门趋势" in preset_names


class TestPerformance:
    """性能测试"""
    
    def test_large_dataset_filtering(self):
        """测试大数据集筛选性能"""
        # 生成大量测试数据
        import random
        
        large_dataset = []
        for i in range(1000):
            item = {
                "item_id": f"item_{i:04d}",
                "current_price": random.uniform(10, 1000),
                "volume_24h": random.randint(1, 200),
                "volatility": random.uniform(0.1, 0.5),
                "price_change_24h": random.uniform(-20, 20)
            }
            large_dataset.append(item)
        
        intelligent_filter = IntelligentFilter()
        
        conditions = [
            FilterCondition("current_price", FilterOperator.BETWEEN, [50, 500]),
            FilterCondition("volume_24h", FilterOperator.GREATER_THAN, 20)
        ]
        
        start_time = time.time()
        results = intelligent_filter.filter_items(
            items=large_dataset,
            conditions=conditions,
            use_cache=False
        )
        execution_time = time.time() - start_time
        
        # 性能要求：1000个饰品的筛选应该在1秒内完成
        assert execution_time < 1.0
        assert len(results) > 0
        
        # 验证筛选结果正确性
        for item in results:
            assert 50 <= item["current_price"] <= 500
            assert item["volume_24h"] > 20


if __name__ == "__main__":
    # 运行基本测试
    print("运行多因子筛选算法基本测试...")
    
    # 测试筛选条件
    print("测试筛选条件...")
    condition = FilterCondition("price", FilterOperator.GREATER_THAN, 100.0)
    assert condition.evaluate({"price": 150.0}) == True
    assert condition.evaluate({"price": 50.0}) == False
    print("✓ 筛选条件测试通过")
    
    # 测试评分引擎
    print("测试评分引擎...")
    scoring_engine = ScoringEngine()
    
    sample_data = {
        "item_id": "test_item",
        "current_price": 100.0,
        "ask_price": 105.0,
        "bid_price": 95.0,
        "volume_24h": 50,
        "volatility": 0.25,
        "price_change_24h": 5.0
    }
    
    score = scoring_engine.calculate_opportunity_score(sample_data)
    assert isinstance(score, ItemScore)
    assert 0.0 <= score.total_score <= 1.0
    print("✓ 评分引擎测试通过")
    
    # 测试智能筛选器
    print("测试智能筛选器...")
    intelligent_filter = IntelligentFilter()
    
    sample_items = [
        {
            "item_id": "item_001",
            "current_price": 52.30,
            "volume_24h": 156,
            "volatility": 0.25
        },
        {
            "item_id": "item_002",
            "current_price": 285.50,
            "volume_24h": 67,
            "volatility": 0.35
        }
    ]
    
    conditions = [FilterCondition("current_price", FilterOperator.GREATER_THAN, 100.0)]
    results = intelligent_filter.filter_items(sample_items, conditions, use_cache=False)
    
    assert len(results) == 1
    assert results[0]["item_id"] == "item_002"
    print("✓ 智能筛选器测试通过")
    
    # 测试预设功能
    print("测试预设功能...")
    presets = intelligent_filter.get_all_presets()
    assert len(presets) > 0
    print("✓ 预设功能测试通过")
    
    print("所有多因子筛选算法测试通过！")
