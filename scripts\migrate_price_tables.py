#!/usr/bin/env python3
"""
价格数据表迁移脚本
扩展现有Price表以支持SteamDT API字段，新增价格历史汇总表和7天均价表
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
import base64
dummy_key_bytes = b'dummy_key_for_import_only_32byte'
dummy_key = base64.urlsafe_b64encode(dummy_key_bytes).decode()
os.environ.setdefault('STEAMDT_API_KEY', 'migration_key')
os.environ.setdefault('ENCRYPTION_KEY', dummy_key)
os.environ.setdefault('SECRET_KEY', 'migration_secret_key')
# 强制使用SQLite数据库
os.environ['DATABASE_URL'] = 'sqlite:///data/ares.db'

from core.database import get_database_manager, Base
from sqlalchemy import text, inspect


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('migrate_price_tables.log')
        ]
    )
    return logging.getLogger(__name__)


def check_table_exists(engine, table_name):
    """检查表是否存在"""
    inspector = inspect(engine)
    return table_name in inspector.get_table_names()


def check_column_exists(engine, table_name, column_name):
    """检查列是否存在"""
    inspector = inspect(engine)
    if not check_table_exists(engine, table_name):
        return False
    
    columns = inspector.get_columns(table_name)
    return any(col['name'] == column_name for col in columns)


def migrate_price_table(engine, logger):
    """迁移Price表，添加新字段"""
    logger.info("开始迁移Price表...")
    
    # 检查Price表是否存在
    if not check_table_exists(engine, 'prices'):
        logger.error("Price表不存在，无法进行迁移")
        return False
    
    # 需要添加的新字段
    new_columns = [
        ("platform_item_id", "VARCHAR(100)"),
        ("steamdt_sell_price", "DECIMAL(10,2)"),
        ("steamdt_sell_count", "INTEGER"),
        ("steamdt_bidding_price", "DECIMAL(10,2)"),
        ("steamdt_bidding_count", "INTEGER"),
        ("steamdt_update_time", "BIGINT"),
        ("query_time", "DATETIME"),
        ("data_tier", "VARCHAR(10) DEFAULT 'warm'"),
        ("cache_status", "VARCHAR(10) DEFAULT 'missing'")
    ]
    
    with engine.connect() as conn:
        for column_name, column_type in new_columns:
            if not check_column_exists(engine, 'prices', column_name):
                try:
                    sql = f"ALTER TABLE prices ADD COLUMN {column_name} {column_type}"
                    conn.execute(text(sql))
                    logger.info(f"成功添加列: {column_name}")
                except Exception as e:
                    logger.error(f"添加列 {column_name} 失败: {e}")
                    return False
            else:
                logger.info(f"列 {column_name} 已存在，跳过")
        
        # 添加新的索引
        new_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_price_steamdt_update_time ON prices(steamdt_update_time)",
            "CREATE INDEX IF NOT EXISTS idx_price_data_tier ON prices(data_tier)",
            "CREATE INDEX IF NOT EXISTS idx_price_cache_status ON prices(cache_status)",
            "CREATE INDEX IF NOT EXISTS idx_price_platform_item_id ON prices(platform, platform_item_id)"
        ]
        
        for index_sql in new_indexes:
            try:
                conn.execute(text(index_sql))
                logger.info(f"成功创建索引: {index_sql.split()[5]}")
            except Exception as e:
                logger.warning(f"创建索引失败（可能已存在）: {e}")
        
        conn.commit()
    
    logger.info("Price表迁移完成")
    return True


def create_new_tables(engine, logger):
    """创建新的汇总表"""
    logger.info("开始创建新的汇总表...")
    
    # 检查表是否已存在
    if check_table_exists(engine, 'item_price_summary'):
        logger.info("item_price_summary表已存在，跳过创建")
    else:
        try:
            # 创建价格历史汇总表 (SQLite兼容)
            create_summary_sql = """
            CREATE TABLE item_price_summary (
                item_id VARCHAR(300) NOT NULL,
                platform VARCHAR(20) NOT NULL,
                date DATETIME NOT NULL,
                open_price DECIMAL(10,2),
                high_price DECIMAL(10,2),
                low_price DECIMAL(10,2),
                close_price DECIMAL(10,2),
                avg_price DECIMAL(10,2),
                total_sell_count INTEGER DEFAULT 0,
                total_bidding_count INTEGER DEFAULT 0,
                price_volatility DECIMAL(8,4),
                price_trend VARCHAR(10),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (item_id, platform, date),
                FOREIGN KEY (item_id) REFERENCES items(market_hash_name),
                CHECK (open_price >= 0),
                CHECK (high_price >= 0),
                CHECK (low_price >= 0),
                CHECK (close_price >= 0),
                CHECK (avg_price >= 0)
            )
            """
            
            with engine.connect() as conn:
                conn.execute(text(create_summary_sql))

                # 创建索引
                summary_indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_summary_item_date ON item_price_summary(item_id, date)",
                    "CREATE INDEX IF NOT EXISTS idx_summary_platform_date ON item_price_summary(platform, date)",
                    "CREATE INDEX IF NOT EXISTS idx_summary_date ON item_price_summary(date)",
                    "CREATE INDEX IF NOT EXISTS idx_summary_price_trend ON item_price_summary(price_trend)"
                ]

                for index_sql in summary_indexes:
                    conn.execute(text(index_sql))

                conn.commit()

            logger.info("成功创建item_price_summary表")
            
        except Exception as e:
            logger.error(f"创建item_price_summary表失败: {e}")
            return False
    
    if check_table_exists(engine, 'item_avg_prices'):
        logger.info("item_avg_prices表已存在，跳过创建")
    else:
        try:
            # 创建7天均价表 (SQLite兼容)
            create_avg_sql = """
            CREATE TABLE item_avg_prices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id VARCHAR(300) NOT NULL,
                platform VARCHAR(20) NOT NULL,
                avg_price_7d DECIMAL(10,2),
                data_source VARCHAR(50) DEFAULT 'steamdt',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (item_id) REFERENCES items(market_hash_name),
                CHECK (avg_price_7d >= 0)
            )
            """
            
            with engine.connect() as conn:
                conn.execute(text(create_avg_sql))

                # 创建索引
                avg_indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_avg_price_item_platform ON item_avg_prices(item_id, platform)",
                    "CREATE INDEX IF NOT EXISTS idx_avg_price_created_at ON item_avg_prices(created_at)",
                    "CREATE INDEX IF NOT EXISTS idx_avg_price_data_source ON item_avg_prices(data_source)",
                    "CREATE UNIQUE INDEX IF NOT EXISTS uq_avg_price_item_platform_time ON item_avg_prices(item_id, platform, created_at)"
                ]

                for index_sql in avg_indexes:
                    conn.execute(text(index_sql))

                conn.commit()

            logger.info("成功创建item_avg_prices表")
            
        except Exception as e:
            logger.error(f"创建item_avg_prices表失败: {e}")
            return False
    
    logger.info("新表创建完成")
    return True


def verify_migration(engine, logger):
    """验证迁移结果"""
    logger.info("开始验证迁移结果...")
    
    inspector = inspect(engine)
    
    # 验证Price表的新字段
    price_columns = inspector.get_columns('prices')
    price_column_names = [col['name'] for col in price_columns]
    
    required_columns = [
        'platform_item_id', 'steamdt_sell_price', 'steamdt_sell_count',
        'steamdt_bidding_price', 'steamdt_bidding_count', 'steamdt_update_time',
        'query_time', 'data_tier', 'cache_status'
    ]
    
    missing_columns = [col for col in required_columns if col not in price_column_names]
    if missing_columns:
        logger.error(f"Price表缺少字段: {missing_columns}")
        return False
    
    logger.info("Price表字段验证通过")
    
    # 验证新表是否存在
    tables = inspector.get_table_names()
    required_tables = ['item_price_summary', 'item_avg_prices']
    
    missing_tables = [table for table in required_tables if table not in tables]
    if missing_tables:
        logger.error(f"缺少表: {missing_tables}")
        return False
    
    logger.info("新表验证通过")
    
    # 验证索引
    price_indexes = inspector.get_indexes('prices')
    price_index_names = [idx['name'] for idx in price_indexes]
    
    required_indexes = [
        'idx_price_steamdt_update_time', 'idx_price_data_tier',
        'idx_price_cache_status', 'idx_price_platform_item_id'
    ]
    
    missing_indexes = [idx for idx in required_indexes if idx not in price_index_names]
    if missing_indexes:
        logger.warning(f"Price表缺少索引: {missing_indexes}")
    else:
        logger.info("Price表索引验证通过")
    
    logger.info("迁移验证完成")
    return True


def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        logger.info("开始价格数据表迁移")
        
        # 获取数据库管理器
        db_manager = get_database_manager()
        engine = db_manager.engine
        
        logger.info(f"数据库连接: {engine.url}")
        
        # 执行迁移
        if not migrate_price_table(engine, logger):
            logger.error("Price表迁移失败")
            return 1
        
        if not create_new_tables(engine, logger):
            logger.error("新表创建失败")
            return 1
        
        if not verify_migration(engine, logger):
            logger.error("迁移验证失败")
            return 1
        
        logger.info("价格数据表迁移成功完成！")
        return 0
        
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
