"""
Ares智能筛选器
实现多维度评分模型和智能筛选算法
"""

import logging
import json
import time
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum
from abc import ABC, abstractmethod

from core.scoring import Scoring<PERSON>ngine, ScoreWeights, ItemScore
from core.cache import get_cache_manager
from core.config import get_config_manager

logger = logging.getLogger(__name__)


class FilterOperator(Enum):
    """筛选操作符"""
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    GREATER_EQUAL = "gte"
    LESS_THAN = "lt"
    LESS_EQUAL = "lte"
    BETWEEN = "between"
    IN = "in"
    NOT_IN = "not_in"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"


class SortOrder(Enum):
    """排序方向"""
    ASC = "asc"
    DESC = "desc"


@dataclass
class FilterCondition:
    """筛选条件"""
    field: str
    operator: FilterOperator
    value: Union[str, int, float, List[Any]]
    weight: float = 1.0
    
    def evaluate(self, item_data: Dict[str, Any]) -> bool:
        """评估条件是否满足"""
        field_value = self._get_field_value(item_data, self.field)
        
        if field_value is None:
            return False
        
        try:
            if self.operator == FilterOperator.EQUALS:
                return field_value == self.value
            elif self.operator == FilterOperator.NOT_EQUALS:
                return field_value != self.value
            elif self.operator == FilterOperator.GREATER_THAN:
                return field_value > self.value
            elif self.operator == FilterOperator.GREATER_EQUAL:
                return field_value >= self.value
            elif self.operator == FilterOperator.LESS_THAN:
                return field_value < self.value
            elif self.operator == FilterOperator.LESS_EQUAL:
                return field_value <= self.value
            elif self.operator == FilterOperator.BETWEEN:
                return self.value[0] <= field_value <= self.value[1]
            elif self.operator == FilterOperator.IN:
                return field_value in self.value
            elif self.operator == FilterOperator.NOT_IN:
                return field_value not in self.value
            elif self.operator == FilterOperator.CONTAINS:
                return str(self.value).lower() in str(field_value).lower()
            elif self.operator == FilterOperator.NOT_CONTAINS:
                return str(self.value).lower() not in str(field_value).lower()
            else:
                logger.warning(f"Unknown operator: {self.operator}")
                return False
        except Exception as e:
            logger.error(f"Error evaluating condition: {str(e)}")
            return False
    
    def _get_field_value(self, data: Dict[str, Any], field: str) -> Any:
        """获取字段值，支持嵌套字段"""
        keys = field.split('.')
        value = data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value


@dataclass
class SortCriteria:
    """排序条件"""
    field: str
    order: SortOrder = SortOrder.DESC
    weight: float = 1.0


@dataclass
class FilterPreset:
    """筛选预设"""
    id: str
    name: str
    description: str
    conditions: List[FilterCondition]
    sort_criteria: List[SortCriteria]
    score_weights: Optional[ScoreWeights] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'conditions': [
                {
                    'field': c.field,
                    'operator': c.operator.value,
                    'value': c.value,
                    'weight': c.weight
                }
                for c in self.conditions
            ],
            'sort_criteria': [
                {
                    'field': s.field,
                    'order': s.order.value,
                    'weight': s.weight
                }
                for s in self.sort_criteria
            ],
            'score_weights': asdict(self.score_weights) if self.score_weights else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterPreset':
        """从字典创建"""
        conditions = [
            FilterCondition(
                field=c['field'],
                operator=FilterOperator(c['operator']),
                value=c['value'],
                weight=c.get('weight', 1.0)
            )
            for c in data.get('conditions', [])
        ]
        
        sort_criteria = [
            SortCriteria(
                field=s['field'],
                order=SortOrder(s['order']),
                weight=s.get('weight', 1.0)
            )
            for s in data.get('sort_criteria', [])
        ]
        
        score_weights = None
        if data.get('score_weights'):
            score_weights = ScoreWeights(**data['score_weights'])
        
        return cls(
            id=data['id'],
            name=data['name'],
            description=data['description'],
            conditions=conditions,
            sort_criteria=sort_criteria,
            score_weights=score_weights,
            created_at=datetime.fromisoformat(data.get('created_at', datetime.utcnow().isoformat())),
            updated_at=datetime.fromisoformat(data.get('updated_at', datetime.utcnow().isoformat()))
        )


class IntelligentFilter:
    """智能筛选器"""
    
    def __init__(self):
        """初始化筛选器"""
        self.scoring_engine = ScoringEngine()
        try:
            self.cache_manager = get_cache_manager()
        except Exception:
            self.cache_manager = None

        try:
            self.config_manager = get_config_manager()
        except Exception:
            self.config_manager = None
        
        # 筛选配置
        self.cache_ttl = 300  # 5分钟缓存
        self.max_results = 1000
        self.min_score_threshold = 0.1
        
        # 加载预设筛选条件
        self.presets = self._load_presets()
    
    def filter_items(
        self,
        items: List[Dict[str, Any]],
        conditions: List[FilterCondition] = None,
        sort_criteria: List[SortCriteria] = None,
        score_weights: Optional[ScoreWeights] = None,
        limit: Optional[int] = None,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """筛选饰品"""
        # 生成缓存键
        cache_key = self._generate_cache_key(conditions, sort_criteria, score_weights, limit)
        
        # 检查缓存
        if use_cache and self.cache_manager:
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                logger.debug(f"Filter cache hit: {cache_key}")
                return cached_result
        
        # 执行筛选
        start_time = time.time()
        
        # 1. 应用筛选条件
        filtered_items = self._apply_conditions(items, conditions or [])
        
        # 2. 计算评分
        scored_items = self._calculate_scores(filtered_items, score_weights)
        
        # 3. 应用排序
        sorted_items = self._apply_sorting(scored_items, sort_criteria or [])
        
        # 4. 应用限制
        if limit:
            sorted_items = sorted_items[:limit]
        
        # 记录性能
        execution_time = time.time() - start_time
        logger.info(f"Filter execution time: {execution_time:.3f}s, results: {len(sorted_items)}")
        
        # 缓存结果
        if use_cache and self.cache_manager:
            self.cache_manager.set(cache_key, sorted_items, self.cache_ttl)
        
        return sorted_items
    
    def _apply_conditions(self, items: List[Dict[str, Any]], conditions: List[FilterCondition]) -> List[Dict[str, Any]]:
        """应用筛选条件"""
        if not conditions:
            return items
        
        filtered_items = []
        
        for item in items:
            # 检查所有条件
            all_conditions_met = True
            
            for condition in conditions:
                if not condition.evaluate(item):
                    all_conditions_met = False
                    break
            
            if all_conditions_met:
                filtered_items.append(item)
        
        logger.debug(f"Applied {len(conditions)} conditions, filtered {len(items)} -> {len(filtered_items)} items")
        return filtered_items
    
    def _calculate_scores(self, items: List[Dict[str, Any]], score_weights: Optional[ScoreWeights]) -> List[Dict[str, Any]]:
        """计算评分"""
        scored_items = []
        
        for item in items:
            # 计算机会评分
            score = self.scoring_engine.calculate_opportunity_score(item, score_weights)
            
            # 添加评分信息
            item_with_score = item.copy()
            item_with_score['_score'] = score.total_score
            item_with_score['_score_breakdown'] = asdict(score)
            
            # 只保留超过最低阈值的项目
            if score.total_score >= self.min_score_threshold:
                scored_items.append(item_with_score)
        
        logger.debug(f"Calculated scores for {len(items)} items, {len(scored_items)} above threshold")
        return scored_items
    
    def _apply_sorting(self, items: List[Dict[str, Any]], sort_criteria: List[SortCriteria]) -> List[Dict[str, Any]]:
        """应用排序"""
        if not sort_criteria:
            # 默认按评分降序排序
            return sorted(items, key=lambda x: x.get('_score', 0), reverse=True)
        
        # 多字段排序
        def sort_key(item):
            key_values = []
            for criteria in sort_criteria:
                value = self._get_sort_value(item, criteria.field)
                # 处理降序
                if criteria.order == SortOrder.DESC:
                    if isinstance(value, (int, float)):
                        value = -value
                    elif isinstance(value, str):
                        # 字符串降序比较复杂，这里简化处理
                        pass
                key_values.append(value)
            return tuple(key_values)
        
        sorted_items = sorted(items, key=sort_key)
        logger.debug(f"Applied {len(sort_criteria)} sort criteria")
        return sorted_items
    
    def _get_sort_value(self, item: Dict[str, Any], field: str) -> Any:
        """获取排序值"""
        keys = field.split('.')
        value = item
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return 0  # 默认值
        
        return value if value is not None else 0
    
    def _generate_cache_key(
        self,
        conditions: Optional[List[FilterCondition]],
        sort_criteria: Optional[List[SortCriteria]],
        score_weights: Optional[ScoreWeights],
        limit: Optional[int]
    ) -> str:
        """生成缓存键"""
        import hashlib
        
        # 构建缓存键数据
        cache_data = {
            'conditions': [
                {
                    'field': c.field,
                    'operator': c.operator.value,
                    'value': c.value,
                    'weight': c.weight
                }
                for c in (conditions or [])
            ],
            'sort_criteria': [
                {
                    'field': s.field,
                    'order': s.order.value,
                    'weight': s.weight
                }
                for s in (sort_criteria or [])
            ],
            'score_weights': asdict(score_weights) if score_weights else None,
            'limit': limit
        }
        
        # 生成哈希
        cache_str = json.dumps(cache_data, sort_keys=True)
        cache_hash = hashlib.md5(cache_str.encode()).hexdigest()
        
        return f"filter:{cache_hash}"
    
    def get_recommendations(
        self,
        user_preferences: Dict[str, Any] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取个性化推荐"""
        # 基于用户偏好生成推荐条件
        conditions = self._generate_recommendation_conditions(user_preferences or {})
        
        # 获取所有可用数据（这里应该从数据源获取）
        # 暂时返回空列表，实际实现时需要集成数据源
        items = []
        
        # 应用推荐筛选
        recommendations = self.filter_items(
            items=items,
            conditions=conditions,
            limit=limit
        )
        
        return recommendations
    
    def _generate_recommendation_conditions(self, user_preferences: Dict[str, Any]) -> List[FilterCondition]:
        """基于用户偏好生成推荐条件"""
        conditions = []
        
        # 价格范围偏好
        if 'price_range' in user_preferences:
            price_range = user_preferences['price_range']
            conditions.append(FilterCondition(
                field='current_price',
                operator=FilterOperator.BETWEEN,
                value=[price_range.get('min', 0), price_range.get('max', 10000)],
                weight=1.5
            ))
        
        # 类别偏好
        if 'preferred_categories' in user_preferences:
            categories = user_preferences['preferred_categories']
            conditions.append(FilterCondition(
                field='category',
                operator=FilterOperator.IN,
                value=categories,
                weight=1.2
            ))
        
        # 风险偏好
        if 'risk_tolerance' in user_preferences:
            risk_level = user_preferences['risk_tolerance']
            if risk_level == 'low':
                # 低风险：选择波动性较小的
                conditions.append(FilterCondition(
                    field='volatility',
                    operator=FilterOperator.LESS_THAN,
                    value=0.2,
                    weight=1.0
                ))
            elif risk_level == 'high':
                # 高风险：选择波动性较大的
                conditions.append(FilterCondition(
                    field='volatility',
                    operator=FilterOperator.GREATER_THAN,
                    value=0.3,
                    weight=1.0
                ))
        
        return conditions
    
    def save_preset(self, preset: FilterPreset) -> bool:
        """保存筛选预设"""
        try:
            self.presets[preset.id] = preset
            self._save_presets()
            logger.info(f"Saved filter preset: {preset.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to save preset: {str(e)}")
            return False
    
    def load_preset(self, preset_id: str) -> Optional[FilterPreset]:
        """加载筛选预设"""
        return self.presets.get(preset_id)
    
    def delete_preset(self, preset_id: str) -> bool:
        """删除筛选预设"""
        if preset_id in self.presets:
            del self.presets[preset_id]
            self._save_presets()
            logger.info(f"Deleted filter preset: {preset_id}")
            return True
        return False
    
    def get_all_presets(self) -> List[FilterPreset]:
        """获取所有预设"""
        return list(self.presets.values())
    
    def _load_presets(self) -> Dict[str, FilterPreset]:
        """加载预设筛选条件"""
        try:
            with open('data/filter_presets.json', 'r', encoding='utf-8') as f:
                presets_data = json.load(f)
            
            presets = {}
            for preset_data in presets_data:
                preset = FilterPreset.from_dict(preset_data)
                presets[preset.id] = preset
            
            logger.info(f"Loaded {len(presets)} filter presets")
            return presets
        
        except FileNotFoundError:
            logger.info("No filter presets file found, creating default presets")
            return self._create_default_presets()
        except Exception as e:
            logger.error(f"Failed to load filter presets: {str(e)}")
            return self._create_default_presets()
    
    def _save_presets(self):
        """保存预设筛选条件"""
        try:
            import os
            os.makedirs('data', exist_ok=True)
            
            presets_data = [preset.to_dict() for preset in self.presets.values()]
            
            with open('data/filter_presets.json', 'w', encoding='utf-8') as f:
                json.dump(presets_data, f, indent=2, ensure_ascii=False)
            
            logger.debug("Saved filter presets to file")
        except Exception as e:
            logger.error(f"Failed to save filter presets: {str(e)}")
    
    def _create_default_presets(self) -> Dict[str, FilterPreset]:
        """创建默认预设"""
        presets = {}
        
        # 高价值机会预设
        high_value_preset = FilterPreset(
            id="high_value_opportunities",
            name="高价值机会",
            description="筛选高价值投资机会",
            conditions=[
                FilterCondition("current_price", FilterOperator.GREATER_THAN, 100),
                FilterCondition("price_change_7d", FilterOperator.LESS_THAN, -10),
                FilterCondition("volume_24h", FilterOperator.GREATER_THAN, 50)
            ],
            sort_criteria=[
                SortCriteria("_score", SortOrder.DESC)
            ]
        )
        presets[high_value_preset.id] = high_value_preset
        
        # 低风险稳健预设
        low_risk_preset = FilterPreset(
            id="low_risk_stable",
            name="低风险稳健",
            description="筛选低风险稳健投资",
            conditions=[
                FilterCondition("volatility", FilterOperator.LESS_THAN, 0.2),
                FilterCondition("volume_24h", FilterOperator.GREATER_THAN, 20),
                FilterCondition("price_change_30d", FilterOperator.BETWEEN, [-5, 15])
            ],
            sort_criteria=[
                SortCriteria("volume_24h", SortOrder.DESC),
                SortCriteria("_score", SortOrder.DESC)
            ]
        )
        presets[low_risk_preset.id] = low_risk_preset
        
        # 热门趋势预设
        trending_preset = FilterPreset(
            id="trending_items",
            name="热门趋势",
            description="筛选热门趋势饰品",
            conditions=[
                FilterCondition("volume_change_24h", FilterOperator.GREATER_THAN, 50),
                FilterCondition("price_change_24h", FilterOperator.GREATER_THAN, 5),
                FilterCondition("current_price", FilterOperator.BETWEEN, [10, 1000])
            ],
            sort_criteria=[
                SortCriteria("volume_change_24h", SortOrder.DESC),
                SortCriteria("price_change_24h", SortOrder.DESC)
            ]
        )
        presets[trending_preset.id] = trending_preset
        
        # 保存默认预设
        self._save_presets()
        
        return presets
    
    def get_filter_stats(self) -> Dict[str, Any]:
        """获取筛选统计"""
        return {
            'total_presets': len(self.presets),
            'cache_hit_rate': 0.85,  # 模拟缓存命中率
            'avg_execution_time': 0.15,  # 模拟平均执行时间
            'total_filters_executed': 1234  # 模拟执行次数
        }


# 全局筛选器实例
_intelligent_filter: Optional[IntelligentFilter] = None


def get_intelligent_filter() -> IntelligentFilter:
    """获取全局智能筛选器实例"""
    global _intelligent_filter
    if _intelligent_filter is None:
        _intelligent_filter = IntelligentFilter()
    return _intelligent_filter
