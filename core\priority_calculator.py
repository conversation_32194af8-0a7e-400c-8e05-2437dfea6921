"""
Ares系统优先级计算器
实现基于多因子评分模型的优先级计算算法
"""

import logging
import math
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class PriorityFactors:
    """优先级计算因子"""
    price_spread: float = 0.0  # 价差百分比
    volume_24h: int = 0  # 24小时交易量
    volatility: float = 0.0  # 价格波动率
    user_added: bool = False  # 用户手动添加
    user_priority: int = 0  # 用户设置优先级
    last_updated: Optional[datetime] = None  # 最后更新时间
    failure_count: int = 0  # 连续失败次数
    pool_type: str = "main"  # 池类型


@dataclass
class PriorityWeights:
    """优先级权重配置"""
    spread_weight: float = 0.4  # 价差权重
    volume_weight: float = 0.3  # 交易量权重
    volatility_weight: float = 0.2  # 波动率权重
    user_interest_weight: float = 0.1  # 用户兴趣权重
    
    # 调整因子
    user_priority_multiplier: float = 1.5  # 用户优先级倍数
    failure_penalty: float = 0.1  # 失败惩罚
    staleness_penalty: float = 0.05  # 数据陈旧惩罚
    
    # 归一化参数
    max_spread: float = 50.0  # 最大价差百分比
    max_volume: float = 1000.0  # 最大交易量
    max_volatility: float = 20.0  # 最大波动率


class PriorityCalculator:
    """优先级计算器"""
    
    def __init__(self, weights: Optional[PriorityWeights] = None):
        """
        初始化优先级计算器
        
        Args:
            weights: 权重配置，默认使用标准权重
        """
        self.weights = weights or PriorityWeights()
        logger.info("Priority calculator initialized with weights: %s", self.weights)
    
    def calculate_priority_score(self, factors: PriorityFactors) -> float:
        """
        计算优先级评分
        
        Args:
            factors: 优先级计算因子
            
        Returns:
            float: 优先级评分 (0.0 - 10.0)
        """
        try:
            # 基础评分计算
            spread_score = self._calculate_spread_score(factors.price_spread)
            volume_score = self._calculate_volume_score(factors.volume_24h)
            volatility_score = self._calculate_volatility_score(factors.volatility)
            user_interest_score = self._calculate_user_interest_score(factors.user_added)
            
            # 加权基础评分
            base_score = (
                spread_score * self.weights.spread_weight +
                volume_score * self.weights.volume_weight +
                volatility_score * self.weights.volatility_weight +
                user_interest_score * self.weights.user_interest_weight
            )
            
            # 应用调整因子
            adjusted_score = self._apply_adjustments(base_score, factors)
            
            # 确保评分在合理范围内
            final_score = max(0.0, min(10.0, adjusted_score))
            
            logger.debug(
                "Priority calculated: spread=%.2f, volume=%.2f, volatility=%.2f, "
                "user_interest=%.2f, base=%.2f, final=%.2f",
                spread_score, volume_score, volatility_score, user_interest_score,
                base_score, final_score
            )
            
            return final_score
            
        except Exception as e:
            logger.error("Error calculating priority score: %s", str(e))
            return 0.0
    
    def _calculate_spread_score(self, price_spread: float) -> float:
        """计算价差评分"""
        if price_spread <= 0:
            return 0.0
        
        # 使用对数函数平滑价差评分
        normalized_spread = min(price_spread / self.weights.max_spread, 1.0)
        return math.log(1 + normalized_spread * 9) / math.log(10)  # 0-1范围
    
    def _calculate_volume_score(self, volume_24h: int) -> float:
        """计算交易量评分"""
        if volume_24h <= 0:
            return 0.0
        
        # 使用平方根函数避免大交易量过度影响
        normalized_volume = min(volume_24h / self.weights.max_volume, 1.0)
        return math.sqrt(normalized_volume)
    
    def _calculate_volatility_score(self, volatility: float) -> float:
        """计算波动率评分"""
        if volatility <= 0:
            return 0.0
        
        # 适度的波动率有利于交易机会
        normalized_volatility = min(volatility / self.weights.max_volatility, 1.0)
        return normalized_volatility
    
    def _calculate_user_interest_score(self, user_added: bool) -> float:
        """计算用户兴趣评分"""
        return 1.0 if user_added else 0.0
    
    def _apply_adjustments(self, base_score: float, factors: PriorityFactors) -> float:
        """应用调整因子"""
        adjusted_score = base_score
        
        # 用户优先级调整
        if factors.user_priority > 0:
            priority_boost = (factors.user_priority / 10.0) * self.weights.user_priority_multiplier
            adjusted_score += priority_boost
        
        # 失败惩罚
        if factors.failure_count > 0:
            failure_penalty = min(factors.failure_count * self.weights.failure_penalty, 0.5)
            adjusted_score -= failure_penalty
        
        # 数据陈旧惩罚
        if factors.last_updated:
            staleness_hours = (datetime.utcnow() - factors.last_updated).total_seconds() / 3600
            if staleness_hours > 24:  # 超过24小时的数据
                staleness_penalty = min((staleness_hours - 24) * self.weights.staleness_penalty, 1.0)
                adjusted_score -= staleness_penalty
        
        return adjusted_score
    
    def calculate_batch_priorities(self, items_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        批量计算优先级评分
        
        Args:
            items_data: 饰品数据列表
            
        Returns:
            Dict[str, float]: 饰品ID到优先级评分的映射
        """
        priorities = {}
        
        for item_data in items_data:
            try:
                factors = self._extract_factors_from_data(item_data)
                priority_score = self.calculate_priority_score(factors)
                item_id = item_data.get('item_id')
                if item_id:
                    priorities[item_id] = priority_score
            except Exception as e:
                logger.error("Error calculating priority for item %s: %s", 
                           item_data.get('item_id', 'unknown'), str(e))
        
        return priorities
    
    def _extract_factors_from_data(self, item_data: Dict[str, Any]) -> PriorityFactors:
        """从饰品数据中提取优先级因子"""
        return PriorityFactors(
            price_spread=item_data.get('price_spread', 0.0),
            volume_24h=item_data.get('volume_24h', 0),
            volatility=item_data.get('volatility', 0.0),
            user_added=item_data.get('user_added', False),
            user_priority=item_data.get('user_priority', 0),
            last_updated=item_data.get('last_updated'),
            failure_count=item_data.get('failure_count', 0),
            pool_type=item_data.get('pool_type', 'main')
        )
    
    def get_pool_priority_threshold(self, pool_type: str) -> float:
        """
        获取不同池的优先级阈值
        
        Args:
            pool_type: 池类型 ('core' 或 'main')
            
        Returns:
            float: 优先级阈值
        """
        if pool_type == 'core':
            return 7.0  # 核心池需要高优先级
        elif pool_type == 'main':
            return 3.0  # 主监控池中等优先级即可
        else:
            return 1.0  # 默认阈值
    
    def should_promote_to_core(self, priority_score: float, current_pool: str) -> bool:
        """
        判断是否应该提升到核心池
        
        Args:
            priority_score: 当前优先级评分
            current_pool: 当前所在池
            
        Returns:
            bool: 是否应该提升
        """
        if current_pool == 'core':
            return False  # 已经在核心池
        
        core_threshold = self.get_pool_priority_threshold('core')
        return priority_score >= core_threshold
    
    def should_demote_from_core(self, priority_score: float, current_pool: str) -> bool:
        """
        判断是否应该从核心池降级
        
        Args:
            priority_score: 当前优先级评分
            current_pool: 当前所在池
            
        Returns:
            bool: 是否应该降级
        """
        if current_pool != 'core':
            return False  # 不在核心池
        
        # 使用较低的阈值避免频繁升降级
        demote_threshold = self.get_pool_priority_threshold('core') - 1.0
        return priority_score < demote_threshold
    
    def update_weights(self, new_weights: PriorityWeights):
        """更新权重配置"""
        self.weights = new_weights
        logger.info("Priority calculator weights updated: %s", self.weights)


# 全局优先级计算器实例
_priority_calculator: Optional[PriorityCalculator] = None


def get_priority_calculator() -> PriorityCalculator:
    """获取全局优先级计算器实例"""
    global _priority_calculator
    if _priority_calculator is None:
        _priority_calculator = PriorityCalculator()
    return _priority_calculator


def calculate_priority_score(item_data: Dict[str, Any]) -> float:
    """
    便捷函数：计算单个饰品的优先级评分
    
    Args:
        item_data: 饰品数据
        
    Returns:
        float: 优先级评分
    """
    calculator = get_priority_calculator()
    factors = calculator._extract_factors_from_data(item_data)
    return calculator.calculate_priority_score(factors)
