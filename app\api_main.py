"""
Ares投资系统FastAPI应用入口
提供RESTful API接口服务
"""

import sys
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import get_config_manager
from core.exceptions import AresException

# 导入API路由
from api.scheduler_api import router as scheduler_router
from api.tracker_api import router as tracker_router
from api.discoverer_api import router as discoverer_router
from api.monitoring_api import router as monitoring_router
from api.filter_api import router as filter_router
from api.base_data_api import router as base_data_router

# 创建FastAPI应用
app = FastAPI(
    title="Ares Investment System API",
    description="CS2饰品投资系统的RESTful API接口",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(scheduler_router)
app.include_router(tracker_router)
app.include_router(discoverer_router)
app.include_router(monitoring_router)
app.include_router(filter_router)
app.include_router(base_data_router)

# 全局异常处理
@app.exception_handler(AresException)
async def ares_exception_handler(request, exc: AresException):
    """处理Ares系统异常"""
    return JSONResponse(
        status_code=400,
        content={
            "success": False,
            "error": exc.message,
            "error_code": exc.error_code,
            "timestamp": exc.timestamp.isoformat() if exc.timestamp else None
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """处理通用异常"""
    logging.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "detail": str(exc)
        }
    )

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 这里可以添加更多的健康检查逻辑
        config_manager = get_config_manager()
        
        return {
            "status": "healthy",
            "timestamp": "2025-01-18T10:30:00Z",
            "version": "1.0.0",
            "environment": config_manager.get('app_environment', 'development')
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Ares Investment System API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc",
        "health": "/health"
    }

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logging.info("Ares API server starting up...")
    
    try:
        # 初始化配置
        config_manager = get_config_manager()
        logging.info(f"Configuration loaded for environment: {config_manager.get('app_environment', 'development')}")
        
    except Exception as e:
        logging.error(f"Failed to initialize during startup: {e}")
        raise

# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logging.info("Ares API server shutting down...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
