"""
CS2宏观数据监控测试
验证CS2玩家在线数据监控集成功能
"""

import pytest
import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.cs2_macro_collector import (
    CS2MacroDataCollector, CS2PlayerData, CS2MacroIndicators,
    get_cs2_macro_collector, collect_cs2_macro_data, get_cs2_macro_indicators
)
from services.cs2_market_analyzer import (
    CS2MarketAnalyzer, MarketAnalysis, MarketSignal, MarketTrend, InvestmentTiming,
    get_cs2_market_analyzer
)


class TestCS2MacroDataCollector:
    """CS2宏观数据采集器测试"""
    
    @pytest.fixture
    def collector(self):
        """创建CS2宏观数据采集器实例"""
        with patch('services.cs2_macro_collector.get_config_manager') as mock_config:
            mock_config.return_value.get.return_value = 'test_api_key'
            return CS2MacroDataCollector()
    
    def test_collector_initialization(self, collector):
        """测试采集器初始化"""
        assert collector.max_history_days == 30
        assert len(collector.player_data_history) == 0
        assert collector.last_update is None
    
    @pytest.mark.asyncio
    async def test_get_player_data_from_steamdt_success(self, collector):
        """测试从SteamDT成功获取玩家数据"""
        with patch.object(collector, '_get_player_data_from_steamdt', return_value=1000000):
            player_count = await collector._get_player_data_from_steamdt()
            assert player_count == 1000000

    @pytest.mark.asyncio
    async def test_get_player_data_from_steamdt_failure(self, collector):
        """测试SteamDT数据获取失败"""
        with patch.object(collector, '_get_player_data_from_steamdt', return_value=None):
            player_count = await collector._get_player_data_from_steamdt()
            assert player_count is None
    
    @pytest.mark.asyncio
    async def test_collect_current_player_data(self, collector):
        """测试收集当前玩家数据"""
        with patch.object(collector, '_get_player_data_from_steamdt', return_value=950000):
            player_data = await collector.collect_current_player_data()

            assert player_data is not None
            assert player_data.player_count == 950000
            assert player_data.data_source == "steamdt"
            assert len(collector.player_data_history) == 1
    
    def test_add_to_history(self, collector):
        """测试添加数据到历史记录"""
        # 创建测试数据
        player_data = CS2PlayerData(
            timestamp=datetime.now(),
            player_count=800000,
            data_source="test"
        )
        
        collector._add_to_history(player_data)
        
        assert len(collector.player_data_history) == 1
        assert collector.player_data_history[0] == player_data
    
    def test_history_cleanup(self, collector):
        """测试历史数据清理"""
        # 添加过期数据
        old_data = CS2PlayerData(
            timestamp=datetime.now() - timedelta(days=35),
            player_count=700000,
            data_source="test"
        )
        
        recent_data = CS2PlayerData(
            timestamp=datetime.now(),
            player_count=800000,
            data_source="test"
        )
        
        collector._add_to_history(old_data)
        collector._add_to_history(recent_data)
        
        # 过期数据应该被清理
        assert len(collector.player_data_history) == 1
        assert collector.player_data_history[0] == recent_data
    
    @pytest.mark.asyncio
    async def test_get_macro_indicators(self, collector):
        """测试获取宏观指标"""
        # 添加一些历史数据
        for i in range(48):  # 48小时数据
            timestamp = datetime.now() - timedelta(hours=i)
            player_data = CS2PlayerData(
                timestamp=timestamp,
                player_count=800000 + (i * 1000),
                data_source="test"
            )
            collector._add_to_history(player_data)
        
        indicators = await collector.get_macro_indicators()
        
        assert indicators is not None
        assert isinstance(indicators, CS2MacroIndicators)
        assert indicators.current_players > 0
        assert 0 <= indicators.market_heat_index <= 100
        assert 0 <= indicators.confidence_score <= 100
    
    def test_calculate_trend(self, collector):
        """测试趋势计算"""
        # 添加上升趋势数据
        base_time = datetime.now()
        for i in range(24):
            timestamp = base_time - timedelta(hours=23-i)
            player_count = 800000 + (i * 5000)  # 递增趋势
            player_data = CS2PlayerData(
                timestamp=timestamp,
                player_count=player_count,
                data_source="test"
            )
            collector._add_to_history(player_data)
        
        trend = collector._calculate_trend(hours=24)
        
        # 应该是正趋势
        assert trend > 0
    
    def test_calculate_market_heat_index(self, collector):
        """测试市场热度指数计算"""
        # 添加测试数据
        base_time = datetime.now()
        for i in range(24):
            timestamp = base_time - timedelta(hours=23-i)
            player_data = CS2PlayerData(
                timestamp=timestamp,
                player_count=800000,
                data_source="test"
            )
            collector._add_to_history(player_data)
        
        heat_index = collector._calculate_market_heat_index()
        
        assert 0 <= heat_index <= 100
    
    def test_export_data(self, collector):
        """测试数据导出"""
        # 添加测试数据
        player_data = CS2PlayerData(
            timestamp=datetime.now(),
            player_count=800000,
            data_source="test"
        )
        collector._add_to_history(player_data)
        
        exported_data = collector.export_data()
        
        assert 'player_data_history' in exported_data
        assert len(exported_data['player_data_history']) == 1
        assert exported_data['player_data_history'][0]['player_count'] == 800000


class TestCS2MarketAnalyzer:
    """CS2市场分析器测试"""
    
    @pytest.fixture
    def analyzer(self):
        """创建CS2市场分析器实例"""
        return CS2MarketAnalyzer()
    
    @pytest.fixture
    def mock_indicators(self):
        """创建模拟宏观指标"""
        return CS2MacroIndicators(
            current_players=900000,
            player_trend_24h=5.2,
            player_trend_7d=8.5,
            market_heat_index=65.0,
            peak_hours_ratio=0.6,
            weekend_effect=3.2,
            update_time=datetime.now(),
            confidence_score=85.0
        )
    
    @pytest.fixture
    def mock_historical_data(self):
        """创建模拟历史数据"""
        data = []
        base_time = datetime.now()
        for i in range(48):  # 48小时数据
            timestamp = base_time - timedelta(hours=47-i)
            player_count = 850000 + (i * 1000)
            data.append(CS2PlayerData(
                timestamp=timestamp,
                player_count=player_count,
                data_source="test"
            ))
        return data
    
    def test_analyzer_initialization(self, analyzer):
        """测试分析器初始化"""
        assert analyzer.volatility_threshold == 15.0
        assert analyzer.momentum_threshold == 10.0
        assert 'cold' in analyzer.heat_thresholds
        assert 'hot' in analyzer.heat_thresholds
    
    def test_analyze_trend_bullish(self, analyzer, mock_indicators, mock_historical_data):
        """测试看涨趋势分析"""
        # 设置看涨指标
        mock_indicators.player_trend_24h = 8.0
        mock_indicators.player_trend_7d = 12.0
        mock_indicators.market_heat_index = 75.0
        
        trend = analyzer._analyze_trend(mock_indicators, mock_historical_data)
        
        assert trend == MarketTrend.BULLISH
    
    def test_analyze_trend_bearish(self, analyzer, mock_indicators, mock_historical_data):
        """测试看跌趋势分析"""
        # 设置看跌指标
        mock_indicators.player_trend_24h = -8.0
        mock_indicators.player_trend_7d = -12.0
        mock_indicators.market_heat_index = 25.0
        
        trend = analyzer._analyze_trend(mock_indicators, mock_historical_data)
        
        assert trend == MarketTrend.BEARISH
    
    def test_analyze_timing_buy(self, analyzer, mock_indicators, mock_historical_data):
        """测试买入时机分析"""
        # 设置买入时机指标
        mock_indicators.market_heat_index = 25.0  # 低热度
        mock_indicators.player_trend_24h = -8.0   # 下跌趋势
        mock_indicators.confidence_score = 80.0   # 高置信度
        
        timing = analyzer._analyze_timing(mock_indicators, mock_historical_data)
        
        assert timing == InvestmentTiming.BUY
    
    def test_analyze_timing_sell(self, analyzer, mock_indicators, mock_historical_data):
        """测试卖出时机分析"""
        # 设置卖出时机指标
        mock_indicators.market_heat_index = 85.0  # 高热度
        mock_indicators.player_trend_24h = 12.0   # 上涨趋势
        mock_indicators.confidence_score = 80.0   # 高置信度
        
        timing = analyzer._analyze_timing(mock_indicators, mock_historical_data)
        
        assert timing == InvestmentTiming.SELL
    
    def test_analyze_timing_wait(self, analyzer, mock_indicators, mock_historical_data):
        """测试等待时机分析"""
        # 设置低置信度
        mock_indicators.confidence_score = 40.0
        
        timing = analyzer._analyze_timing(mock_indicators, mock_historical_data)
        
        assert timing == InvestmentTiming.WAIT
    
    def test_calculate_confidence(self, analyzer, mock_indicators, mock_historical_data):
        """测试置信度计算"""
        confidence = analyzer._calculate_confidence(mock_indicators, mock_historical_data)
        
        assert 0 <= confidence <= 100
        # 有足够历史数据，置信度应该较高
        assert confidence > 50
    
    def test_analyze_heat_level(self, analyzer):
        """测试热度等级分析"""
        assert analyzer._analyze_heat_level(90) == "极热"
        assert analyzer._analyze_heat_level(70) == "热"
        assert analyzer._analyze_heat_level(50) == "温"
        assert analyzer._analyze_heat_level(30) == "冷"
        assert analyzer._analyze_heat_level(10) == "极冷"
    
    def test_calculate_volatility(self, analyzer, mock_historical_data):
        """测试波动性计算"""
        volatility = analyzer._calculate_volatility(mock_historical_data)
        
        assert 0 <= volatility <= 100
    
    def test_calculate_momentum(self, analyzer, mock_indicators, mock_historical_data):
        """测试动量计算"""
        momentum = analyzer._calculate_momentum(mock_indicators, mock_historical_data)
        
        assert -100 <= momentum <= 100
    
    def test_calculate_support_resistance(self, analyzer, mock_historical_data):
        """测试支撑阻力位计算"""
        support, resistance = analyzer._calculate_support_resistance(mock_historical_data)
        
        assert support > 0
        assert resistance > 0
        assert resistance >= support
    
    @pytest.mark.asyncio
    async def test_detect_market_signals(self, analyzer):
        """测试市场信号检测"""
        with patch.object(analyzer.cs2_collector, 'get_macro_indicators') as mock_get_indicators, \
             patch.object(analyzer.cs2_collector, 'get_historical_data') as mock_get_history:
            
            # 模拟数据
            mock_indicators = CS2MacroIndicators(
                current_players=1100000,  # 高玩家数
                player_trend_24h=18.0,    # 强势上涨
                player_trend_7d=12.0,
                market_heat_index=95.0,   # 过热
                peak_hours_ratio=0.6,
                weekend_effect=5.0,
                update_time=datetime.now(),
                confidence_score=85.0
            )
            
            mock_history = []
            for i in range(48):
                timestamp = datetime.now() - timedelta(hours=47-i)
                mock_history.append(CS2PlayerData(
                    timestamp=timestamp,
                    player_count=900000 + (i * 2000),
                    data_source="test"
                ))
            
            mock_get_indicators.return_value = mock_indicators
            mock_get_history.return_value = mock_history
            
            signals = await analyzer.detect_market_signals()
            
            assert isinstance(signals, list)
            # 应该检测到过热和强势上涨信号
            signal_types = [signal.signal_type for signal in signals]
            assert any("过热" in signal_type for signal_type in signal_types)
            assert any("上涨" in signal_type for signal_type in signal_types)


class TestIntegration:
    """集成测试"""
    
    def test_get_cs2_macro_collector_singleton(self):
        """测试CS2宏观数据采集器单例"""
        collector1 = get_cs2_macro_collector()
        collector2 = get_cs2_macro_collector()
        
        assert collector1 is collector2
    
    def test_get_cs2_market_analyzer_singleton(self):
        """测试CS2市场分析器单例"""
        analyzer1 = get_cs2_market_analyzer()
        analyzer2 = get_cs2_market_analyzer()
        
        assert analyzer1 is analyzer2
    
    @pytest.mark.asyncio
    async def test_collect_cs2_macro_data_function(self):
        """测试CS2宏观数据收集便捷函数"""
        with patch('services.cs2_macro_collector.get_cs2_macro_collector') as mock_get_collector:
            mock_collector = Mock()
            mock_collector.collect_current_player_data = AsyncMock(return_value=CS2PlayerData(
                timestamp=datetime.now(),
                player_count=900000,
                data_source="test"
            ))
            mock_get_collector.return_value = mock_collector
            
            result = await collect_cs2_macro_data()
            
            assert result is not None
            assert result.player_count == 900000
    
    @pytest.mark.asyncio
    async def test_get_cs2_macro_indicators_function(self):
        """测试CS2宏观指标获取便捷函数"""
        with patch('services.cs2_macro_collector.get_cs2_macro_collector') as mock_get_collector:
            mock_collector = Mock()
            mock_collector.get_macro_indicators = AsyncMock(return_value=CS2MacroIndicators(
                current_players=900000,
                player_trend_24h=5.0,
                player_trend_7d=8.0,
                market_heat_index=65.0,
                peak_hours_ratio=0.6,
                weekend_effect=3.0,
                update_time=datetime.now(),
                confidence_score=85.0
            ))
            mock_get_collector.return_value = mock_collector
            
            result = await get_cs2_macro_indicators()
            
            assert result is not None
            assert result.current_players == 900000
            assert result.market_heat_index == 65.0


if __name__ == "__main__":
    # 运行基本测试
    print("运行CS2宏观数据监控基本测试...")
    
    # 测试数据结构
    print("测试数据结构...")
    player_data = CS2PlayerData(
        timestamp=datetime.now(),
        player_count=900000,
        data_source="test"
    )
    assert player_data.player_count == 900000
    assert player_data.data_source == "test"
    
    indicators = CS2MacroIndicators(
        current_players=900000,
        player_trend_24h=5.0,
        player_trend_7d=8.0,
        market_heat_index=65.0,
        peak_hours_ratio=0.6,
        weekend_effect=3.0,
        update_time=datetime.now(),
        confidence_score=85.0
    )
    assert indicators.current_players == 900000
    assert 0 <= indicators.market_heat_index <= 100
    print("✓ 数据结构测试通过")
    
    # 测试枚举
    print("测试枚举...")
    assert MarketTrend.BULLISH.value == "bullish"
    assert InvestmentTiming.BUY.value == "buy"
    print("✓ 枚举测试通过")
    
    # 测试单例（使用Mock避免配置问题）
    print("测试单例...")
    with patch('services.cs2_macro_collector.get_config_manager') as mock_config1, \
         patch('services.cs2_market_analyzer.get_config_manager', create=True) as mock_config2:
        mock_config1.return_value.get.return_value = 'test_key'
        mock_config2.return_value.get.return_value = 'test_key'

        collector1 = get_cs2_macro_collector()
        collector2 = get_cs2_macro_collector()
        assert collector1 is collector2

        analyzer1 = get_cs2_market_analyzer()
        analyzer2 = get_cs2_market_analyzer()
        assert analyzer1 is analyzer2
    print("✓ 单例测试通过")
    
    print("所有CS2宏观数据监控基本测试通过！")
    
    # 运行pytest
    pytest.main(["-xvs", __file__])
