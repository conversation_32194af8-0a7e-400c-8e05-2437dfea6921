"""
Ares预警引擎
实现智能预警系统，支持交易机会预警、持仓风险预警和投资组合再平衡提醒
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod

from core.notifications import get_notification_service, NotificationType
from core.metrics import get_business_metrics
from core.config import get_config_manager

logger = logging.getLogger(__name__)


class AlertType(Enum):
    """预警类型"""
    PRICE_OPPORTUNITY = "price_opportunity"      # 价格机会
    PRICE_RISK = "price_risk"                   # 价格风险
    VOLUME_SPIKE = "volume_spike"               # 交易量激增
    PORTFOLIO_REBALANCE = "portfolio_rebalance" # 投资组合再平衡
    HOLDING_RISK = "holding_risk"               # 持仓风险
    MARKET_ANOMALY = "market_anomaly"           # 市场异常


class AlertSeverity(Enum):
    """预警严重性"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """预警状态"""
    ACTIVE = "active"
    TRIGGERED = "triggered"
    RESOLVED = "resolved"
    DISABLED = "disabled"


@dataclass
class AlertCondition:
    """预警条件"""
    field: str                    # 字段名
    operator: str                 # 操作符: >, <, >=, <=, ==, !=, in, not_in, between
    value: Union[float, int, str, List] # 比较值
    
    def evaluate(self, data: Dict[str, Any]) -> bool:
        """评估条件"""
        field_value = self._get_field_value(data, self.field)
        
        if field_value is None:
            return False
        
        try:
            if self.operator == ">":
                return field_value > self.value
            elif self.operator == "<":
                return field_value < self.value
            elif self.operator == ">=":
                return field_value >= self.value
            elif self.operator == "<=":
                return field_value <= self.value
            elif self.operator == "==":
                return field_value == self.value
            elif self.operator == "!=":
                return field_value != self.value
            elif self.operator == "in":
                return field_value in self.value
            elif self.operator == "not_in":
                return field_value not in self.value
            elif self.operator == "between":
                return self.value[0] <= field_value <= self.value[1]
            else:
                logger.warning(f"Unknown operator: {self.operator}")
                return False
        except Exception as e:
            logger.error(f"Error evaluating condition: {str(e)}")
            return False
    
    def _get_field_value(self, data: Dict[str, Any], field: str) -> Any:
        """获取字段值，支持嵌套字段"""
        keys = field.split('.')
        value = data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value


@dataclass
class AlertRule:
    """预警规则"""
    id: str
    name: str
    description: str
    alert_type: AlertType
    severity: AlertSeverity
    conditions: List[AlertCondition]
    logic_operator: str = "AND"  # AND, OR
    cooldown_minutes: int = 60   # 冷却时间（分钟）
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0
    
    def evaluate(self, data: Dict[str, Any]) -> bool:
        """评估规则"""
        if not self.enabled:
            return False
        
        # 检查冷却时间
        if self.last_triggered:
            cooldown_delta = timedelta(minutes=self.cooldown_minutes)
            if datetime.utcnow() - self.last_triggered < cooldown_delta:
                return False
        
        # 评估条件
        if not self.conditions:
            return False
        
        results = [condition.evaluate(data) for condition in self.conditions]
        
        if self.logic_operator == "AND":
            return all(results)
        elif self.logic_operator == "OR":
            return any(results)
        else:
            logger.warning(f"Unknown logic operator: {self.logic_operator}")
            return False
    
    def trigger(self):
        """触发预警"""
        self.last_triggered = datetime.utcnow()
        self.trigger_count += 1


@dataclass
class AlertEvent:
    """预警事件"""
    id: str
    rule_id: str
    rule_name: str
    alert_type: AlertType
    severity: AlertSeverity
    message: str
    data: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.utcnow)
    acknowledged: bool = False
    resolved: bool = False


class AlertEngine:
    """预警引擎"""
    
    def __init__(self):
        """初始化预警引擎"""
        self.rules: Dict[str, AlertRule] = {}
        self.events: List[AlertEvent] = []
        self.notification_service = get_notification_service()
        self.business_metrics = get_business_metrics()
        
        # 预警频率控制
        self.rate_limits = {
            AlertSeverity.LOW: 3600,      # 低级预警：1小时最多1次
            AlertSeverity.MEDIUM: 1800,   # 中级预警：30分钟最多1次
            AlertSeverity.HIGH: 600,      # 高级预警：10分钟最多1次
            AlertSeverity.CRITICAL: 300   # 严重预警：5分钟最多1次
        }
        
        # 初始化默认规则
        self._init_default_rules()
    
    def _init_default_rules(self):
        """初始化默认预警规则"""
        # 价格机会预警
        price_opportunity_rule = AlertRule(
            id="price_opportunity_001",
            name="价格机会预警",
            description="当饰品价格低于历史平均价格15%时触发",
            alert_type=AlertType.PRICE_OPPORTUNITY,
            severity=AlertSeverity.MEDIUM,
            conditions=[
                AlertCondition("price_change_percent", "<", -15.0),
                AlertCondition("volume_24h", ">", 10)  # 确保有足够交易量
            ],
            cooldown_minutes=120
        )
        
        # 价格风险预警
        price_risk_rule = AlertRule(
            id="price_risk_001",
            name="价格风险预警",
            description="当持仓饰品价格下跌超过20%时触发",
            alert_type=AlertType.PRICE_RISK,
            severity=AlertSeverity.HIGH,
            conditions=[
                AlertCondition("price_change_percent", "<", -20.0),
                AlertCondition("holding_quantity", ">", 0)  # 确保有持仓
            ],
            cooldown_minutes=60
        )
        
        # 交易量激增预警
        volume_spike_rule = AlertRule(
            id="volume_spike_001",
            name="交易量激增预警",
            description="当24小时交易量超过平均值300%时触发",
            alert_type=AlertType.VOLUME_SPIKE,
            severity=AlertSeverity.MEDIUM,
            conditions=[
                AlertCondition("volume_change_percent", ">", 300.0)
            ],
            cooldown_minutes=180
        )
        
        # 投资组合再平衡预警
        portfolio_rebalance_rule = AlertRule(
            id="portfolio_rebalance_001",
            name="投资组合再平衡预警",
            description="当投资组合偏离目标配置超过10%时触发",
            alert_type=AlertType.PORTFOLIO_REBALANCE,
            severity=AlertSeverity.LOW,
            conditions=[
                AlertCondition("portfolio_deviation", ">", 10.0)
            ],
            cooldown_minutes=1440  # 24小时
        )
        
        # 添加规则
        for rule in [price_opportunity_rule, price_risk_rule, volume_spike_rule, portfolio_rebalance_rule]:
            self.add_rule(rule)
    
    def add_rule(self, rule: AlertRule):
        """添加预警规则"""
        self.rules[rule.id] = rule
        logger.info(f"Added alert rule: {rule.name} ({rule.id})")
    
    def remove_rule(self, rule_id: str):
        """移除预警规则"""
        if rule_id in self.rules:
            rule = self.rules.pop(rule_id)
            logger.info(f"Removed alert rule: {rule.name} ({rule_id})")
            return True
        return False
    
    def get_rule(self, rule_id: str) -> Optional[AlertRule]:
        """获取预警规则"""
        return self.rules.get(rule_id)
    
    def get_all_rules(self) -> List[AlertRule]:
        """获取所有预警规则"""
        return list(self.rules.values())
    
    def enable_rule(self, rule_id: str):
        """启用预警规则"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = True
            logger.info(f"Enabled alert rule: {rule_id}")
    
    def disable_rule(self, rule_id: str):
        """禁用预警规则"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = False
            logger.info(f"Disabled alert rule: {rule_id}")
    
    async def evaluate_item(self, item_data: Dict[str, Any]) -> List[AlertEvent]:
        """评估单个饰品数据"""
        triggered_events = []
        
        for rule in self.rules.values():
            try:
                if rule.evaluate(item_data):
                    # 创建预警事件
                    event = AlertEvent(
                        id=f"alert_{int(time.time() * 1000)}_{rule.id}",
                        rule_id=rule.id,
                        rule_name=rule.name,
                        alert_type=rule.alert_type,
                        severity=rule.severity,
                        message=self._generate_alert_message(rule, item_data),
                        data=item_data.copy()
                    )
                    
                    # 触发规则
                    rule.trigger()
                    
                    # 添加事件
                    self.events.append(event)
                    triggered_events.append(event)
                    
                    # 发送通知
                    await self._send_notification(event)
                    
                    # 记录指标
                    self.business_metrics.record_alert_triggered(
                        rule.alert_type.value,
                        rule.severity.value
                    )
                    
                    logger.info(f"Alert triggered: {rule.name} for item {item_data.get('item_id', 'unknown')}")
            
            except Exception as e:
                logger.error(f"Error evaluating rule {rule.id}: {str(e)}")
        
        return triggered_events
    
    async def evaluate_portfolio(self, portfolio_data: Dict[str, Any]) -> List[AlertEvent]:
        """评估投资组合数据"""
        triggered_events = []
        
        # 筛选投资组合相关的规则
        portfolio_rules = [
            rule for rule in self.rules.values()
            if rule.alert_type in [AlertType.PORTFOLIO_REBALANCE, AlertType.HOLDING_RISK]
        ]
        
        for rule in portfolio_rules:
            try:
                if rule.evaluate(portfolio_data):
                    event = AlertEvent(
                        id=f"alert_{int(time.time() * 1000)}_{rule.id}",
                        rule_id=rule.id,
                        rule_name=rule.name,
                        alert_type=rule.alert_type,
                        severity=rule.severity,
                        message=self._generate_alert_message(rule, portfolio_data),
                        data=portfolio_data.copy()
                    )
                    
                    rule.trigger()
                    self.events.append(event)
                    triggered_events.append(event)
                    
                    await self._send_notification(event)
                    
                    self.business_metrics.record_alert_triggered(
                        rule.alert_type.value,
                        rule.severity.value
                    )
                    
                    logger.info(f"Portfolio alert triggered: {rule.name}")
            
            except Exception as e:
                logger.error(f"Error evaluating portfolio rule {rule.id}: {str(e)}")
        
        return triggered_events
    
    def _generate_alert_message(self, rule: AlertRule, data: Dict[str, Any]) -> str:
        """生成预警消息"""
        item_name = data.get('item_name', data.get('item_id', 'Unknown Item'))
        
        if rule.alert_type == AlertType.PRICE_OPPORTUNITY:
            price_change = data.get('price_change_percent', 0)
            current_price = data.get('current_price', 0)
            return f"🟢 价格机会：{item_name} 价格下跌 {abs(price_change):.1f}%，当前价格 ${current_price:.2f}"
        
        elif rule.alert_type == AlertType.PRICE_RISK:
            price_change = data.get('price_change_percent', 0)
            current_price = data.get('current_price', 0)
            return f"🔴 价格风险：{item_name} 价格下跌 {abs(price_change):.1f}%，当前价格 ${current_price:.2f}"
        
        elif rule.alert_type == AlertType.VOLUME_SPIKE:
            volume_change = data.get('volume_change_percent', 0)
            volume_24h = data.get('volume_24h', 0)
            return f"📈 交易量激增：{item_name} 24小时交易量增长 {volume_change:.1f}%，当前交易量 {volume_24h}"
        
        elif rule.alert_type == AlertType.PORTFOLIO_REBALANCE:
            deviation = data.get('portfolio_deviation', 0)
            return f"⚖️ 投资组合再平衡：当前配置偏离目标 {deviation:.1f}%，建议重新平衡"
        
        elif rule.alert_type == AlertType.HOLDING_RISK:
            risk_level = data.get('risk_level', 'Unknown')
            return f"⚠️ 持仓风险：{item_name} 风险等级为 {risk_level}"
        
        else:
            return f"🔔 {rule.name}：{item_name} 触发预警条件"
    
    async def _send_notification(self, event: AlertEvent):
        """发送通知"""
        try:
            # 确定通知类型
            if event.severity == AlertSeverity.CRITICAL:
                notification_type = NotificationType.ERROR
            elif event.severity == AlertSeverity.HIGH:
                notification_type = NotificationType.WARNING
            else:
                notification_type = NotificationType.INFO
            
            # 发送通知
            await self.notification_service.send_notification(
                title=f"Ares预警 - {event.rule_name}",
                content=event.message,
                type=notification_type,
                metadata={
                    'alert_id': event.id,
                    'rule_id': event.rule_id,
                    'alert_type': event.alert_type.value,
                    'severity': event.severity.value,
                    'item_id': event.data.get('item_id'),
                    'timestamp': event.timestamp.isoformat()
                }
            )
        
        except Exception as e:
            logger.error(f"Failed to send notification for alert {event.id}: {str(e)}")
    
    def get_recent_events(self, limit: int = 100) -> List[AlertEvent]:
        """获取最近的预警事件"""
        return sorted(self.events, key=lambda x: x.timestamp, reverse=True)[:limit]
    
    def get_events_by_type(self, alert_type: AlertType) -> List[AlertEvent]:
        """按类型获取预警事件"""
        return [event for event in self.events if event.alert_type == alert_type]
    
    def acknowledge_event(self, event_id: str):
        """确认预警事件"""
        for event in self.events:
            if event.id == event_id:
                event.acknowledged = True
                logger.info(f"Alert event acknowledged: {event_id}")
                return True
        return False
    
    def resolve_event(self, event_id: str):
        """解决预警事件"""
        for event in self.events:
            if event.id == event_id:
                event.resolved = True
                logger.info(f"Alert event resolved: {event_id}")
                return True
        return False
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """获取预警统计"""
        total_events = len(self.events)
        
        # 按类型统计
        type_stats = {}
        for alert_type in AlertType:
            type_stats[alert_type.value] = len(self.get_events_by_type(alert_type))
        
        # 按严重性统计
        severity_stats = {}
        for severity in AlertSeverity:
            severity_stats[severity.value] = len([
                event for event in self.events if event.severity == severity
            ])
        
        # 按状态统计
        acknowledged_count = len([event for event in self.events if event.acknowledged])
        resolved_count = len([event for event in self.events if event.resolved])
        
        return {
            'total_events': total_events,
            'total_rules': len(self.rules),
            'active_rules': len([rule for rule in self.rules.values() if rule.enabled]),
            'type_stats': type_stats,
            'severity_stats': severity_stats,
            'acknowledged_count': acknowledged_count,
            'resolved_count': resolved_count,
            'pending_count': total_events - acknowledged_count
        }


# 全局预警引擎实例
_alert_engine: Optional[AlertEngine] = None


def get_alert_engine() -> AlertEngine:
    """获取全局预警引擎实例"""
    global _alert_engine
    if _alert_engine is None:
        _alert_engine = AlertEngine()
    return _alert_engine
