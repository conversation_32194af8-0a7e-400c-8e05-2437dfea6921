version: '3.8'

services:
  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: ares-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ares-network

  # 数据库服务 (SQLite通过卷挂载)
  database:
    image: alpine:latest
    container_name: ares-database
    restart: "no"
    volumes:
      - database_data:/data
    command: ["sh", "-c", "echo 'Database volume initialized' && sleep infinity"]
    networks:
      - ares-network

  # 主应用服务 (FastAPI + Streamlit)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-app
    restart: unless-stopped
    ports:
      - "8000:8000"  # FastAPI
      - "8501:8501"  # Streamlit
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=sqlite:///data/ares.db
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - CACHE_TTL=300
      - SCHEDULER_ENABLED=true
      - TRACKER_ENABLED=true
      - DISCOVERER_ENABLED=true
    volumes:
      - database_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ares-network

  # 调度器服务
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-scheduler
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=sqlite:///data/ares.db
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - SERVICE_MODE=scheduler
    volumes:
      - database_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
      app:
        condition: service_healthy
    command: ["scheduler"]
    networks:
      - ares-network

  # 追踪器服务
  tracker:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-tracker
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=sqlite:///data/ares.db
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - SERVICE_MODE=tracker
    volumes:
      - database_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
      app:
        condition: service_healthy
    command: ["tracker"]
    networks:
      - ares-network

  # 发现器服务
  discoverer:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-discoverer
    restart: unless-stopped
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=sqlite:///data/ares.db
      - API_STEAMDT_BASE_URL=https://open.steamdt.com
      - API_STEAMDT_API_KEY=${API_STEAMDT_API_KEY}
      - LOG_LEVEL=INFO
      - SERVICE_MODE=discoverer
    volumes:
      - database_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
      app:
        condition: service_healthy
    command: ["discoverer"]
    networks:
      - ares-network

  # 监控服务 (可选)
  monitoring:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ares-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"  # Prometheus metrics
    environment:
      - ARES_ENV=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=sqlite:///data/ares.db
      - LOG_LEVEL=INFO
      - SERVICE_MODE=monitoring
    volumes:
      - database_data:/app/data
      - logs_data:/app/logs
      - ./config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
      app:
        condition: service_healthy
    command: ["monitoring"]
    networks:
      - ares-network

  # Nginx反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: ares-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - logs_data:/var/log/nginx
    depends_on:
      - app
    networks:
      - ares-network
    profiles:
      - production

# 网络配置
networks:
  ares-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  redis_data:
    driver: local
  database_data:
    driver: local
  logs_data:
    driver: local
