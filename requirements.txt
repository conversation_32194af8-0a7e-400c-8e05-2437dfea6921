# Ares投资系统依赖包

# 核心框架
streamlit>=1.28.0
fastapi>=0.104.0
uvicorn>=0.24.0

# 异步HTTP客户端
aiohttp>=3.9.0
httpx>=0.25.0

# 数据库相关
sqlalchemy>=2.0.0
alembic>=1.12.0
sqlite3  # Python内置

# 缓存和消息队列
redis>=5.0.0
hiredis>=2.2.0

# 数据处理
pandas>=2.1.0
numpy>=1.24.0
pydantic>=2.5.0

# 图表和可视化
plotly>=5.17.0
matplotlib>=3.7.0
seaborn>=0.12.0

# 任务调度
apscheduler>=3.10.0

# 加密和安全
cryptography>=41.0.0
python-jose>=3.3.0
passlib>=1.7.4

# 配置管理
pyyaml>=6.0
python-dotenv>=1.0.0

# 日志和监控
structlog>=23.2.0
prometheus-client>=0.19.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 开发工具
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0
pre-commit>=3.5.0

# 通知服务
discord-webhook>=1.3.0
python-telegram-bot>=20.7.0

# 数据验证和序列化
marshmallow>=3.20.0
cerberus>=1.3.5

# 时间处理
python-dateutil>=2.8.0
pytz>=2023.3

# 网络请求
requests>=2.31.0
urllib3>=2.0.0

# 数学和统计
scipy>=1.11.0
scikit-learn>=1.3.0

# 文件处理
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# 环境和部署
gunicorn>=21.2.0
docker>=6.1.0

# 性能监控
psutil>=5.9.0
memory-profiler>=0.61.0
