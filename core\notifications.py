"""
Ares系统通知服务
支持Discord、Telegram等多种通知渠道，实现错误告警和业务通知
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """通知类型"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    SUCCESS = "success"
    ALERT = "alert"


@dataclass
class NotificationMessage:
    """通知消息"""
    title: str
    content: str
    type: NotificationType
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class NotificationChannel(ABC):
    """通知渠道抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get('enabled', True)
        self.rate_limit = config.get('rate_limit', 10)  # 每小时限制
        self.sent_count = 0
        self.last_reset_time = datetime.utcnow()
    
    @abstractmethod
    async def send_message(self, message: NotificationMessage) -> bool:
        """发送消息"""
        pass
    
    def _check_rate_limit(self) -> bool:
        """检查频率限制"""
        now = datetime.utcnow()
        
        # 每小时重置计数
        if (now - self.last_reset_time).total_seconds() >= 3600:
            self.sent_count = 0
            self.last_reset_time = now
        
        return self.sent_count < self.rate_limit
    
    def _increment_sent_count(self):
        """增加发送计数"""
        self.sent_count += 1


class DiscordNotificationChannel(NotificationChannel):
    """Discord通知渠道"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.webhook_url = config.get('webhook_url')
        
        if not self.webhook_url:
            logger.warning("Discord webhook URL not configured")
            self.enabled = False
    
    async def send_message(self, message: NotificationMessage) -> bool:
        """发送Discord消息"""
        if not self.enabled or not self._check_rate_limit():
            return False
        
        try:
            embed = self._create_embed(message)
            payload = {
                "embeds": [embed],
                "username": "Ares System"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.status == 204:
                        self._increment_sent_count()
                        logger.debug(f"Discord notification sent: {message.title}")
                        return True
                    else:
                        logger.error(f"Discord notification failed: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Failed to send Discord notification: {str(e)}")
            return False
    
    def _create_embed(self, message: NotificationMessage) -> Dict[str, Any]:
        """创建Discord嵌入消息"""
        color_map = {
            NotificationType.ERROR: 0xFF0000,      # 红色
            NotificationType.WARNING: 0xFFA500,   # 橙色
            NotificationType.INFO: 0x0099FF,      # 蓝色
            NotificationType.SUCCESS: 0x00FF00,   # 绿色
            NotificationType.ALERT: 0xFF69B4      # 粉色
        }
        
        embed = {
            "title": message.title,
            "description": message.content,
            "color": color_map.get(message.type, 0x808080),
            "timestamp": message.timestamp.isoformat(),
            "footer": {
                "text": "Ares Investment System"
            }
        }
        
        # 添加字段
        if message.metadata:
            fields = []
            for key, value in message.metadata.items():
                if key not in ['title', 'content', 'type']:
                    fields.append({
                        "name": key.replace('_', ' ').title(),
                        "value": str(value),
                        "inline": True
                    })
            
            if fields:
                embed["fields"] = fields[:10]  # Discord限制最多25个字段
        
        return embed


class TelegramNotificationChannel(NotificationChannel):
    """Telegram通知渠道"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.bot_token = config.get('bot_token')
        self.chat_id = config.get('chat_id')
        
        if not self.bot_token or not self.chat_id:
            logger.warning("Telegram bot token or chat ID not configured")
            self.enabled = False
    
    async def send_message(self, message: NotificationMessage) -> bool:
        """发送Telegram消息"""
        if not self.enabled or not self._check_rate_limit():
            return False
        
        try:
            text = self._format_message(message)
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            
            payload = {
                "chat_id": self.chat_id,
                "text": text,
                "parse_mode": "Markdown",
                "disable_web_page_preview": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        self._increment_sent_count()
                        logger.debug(f"Telegram notification sent: {message.title}")
                        return True
                    else:
                        logger.error(f"Telegram notification failed: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {str(e)}")
            return False
    
    def _format_message(self, message: NotificationMessage) -> str:
        """格式化Telegram消息"""
        emoji_map = {
            NotificationType.ERROR: "🚨",
            NotificationType.WARNING: "⚠️",
            NotificationType.INFO: "ℹ️",
            NotificationType.SUCCESS: "✅",
            NotificationType.ALERT: "🔔"
        }
        
        emoji = emoji_map.get(message.type, "📢")
        
        text = f"{emoji} *{message.title}*\n\n{message.content}"
        
        if message.metadata:
            text += "\n\n*Details:*"
            for key, value in message.metadata.items():
                if key not in ['title', 'content', 'type']:
                    text += f"\n• {key.replace('_', ' ').title()}: `{value}`"
        
        text += f"\n\n_Time: {message.timestamp.strftime('%Y-%m-%d %H:%M:%S')}_"
        
        return text


class EmailNotificationChannel(NotificationChannel):
    """邮件通知渠道（简化实现）"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.smtp_server = config.get('smtp_server')
        self.smtp_port = config.get('smtp_port', 587)
        self.username = config.get('username')
        self.password = config.get('password')
        self.to_addresses = config.get('to_addresses', [])
        
        if not all([self.smtp_server, self.username, self.password, self.to_addresses]):
            logger.warning("Email configuration incomplete")
            self.enabled = False
    
    async def send_message(self, message: NotificationMessage) -> bool:
        """发送邮件消息"""
        if not self.enabled or not self._check_rate_limit():
            return False
        
        # 这里可以实现SMTP邮件发送
        # 为了简化，暂时只记录日志
        logger.info(f"Email notification would be sent: {message.title}")
        self._increment_sent_count()
        return True


class NotificationService:
    """通知服务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化通知服务
        
        Args:
            config: 通知配置
        """
        self.config = config
        self.channels: Dict[str, NotificationChannel] = {}
        self.filters: List[Callable[[NotificationMessage], bool]] = []
        
        # 初始化通知渠道
        self._init_channels()
    
    def _init_channels(self):
        """初始化通知渠道"""
        # Discord渠道
        discord_config = self.config.get('discord', {})
        if discord_config.get('enabled', False):
            self.channels['discord'] = DiscordNotificationChannel(discord_config)
        
        # Telegram渠道
        telegram_config = self.config.get('telegram', {})
        if telegram_config.get('enabled', False):
            self.channels['telegram'] = TelegramNotificationChannel(telegram_config)
        
        # 邮件渠道
        email_config = self.config.get('email', {})
        if email_config.get('enabled', False):
            self.channels['email'] = EmailNotificationChannel(email_config)
    
    async def send_notification(
        self,
        title: str,
        content: str,
        type: NotificationType = NotificationType.INFO,
        metadata: Dict[str, Any] = None,
        channels: List[str] = None
    ) -> Dict[str, bool]:
        """
        发送通知
        
        Args:
            title: 通知标题
            content: 通知内容
            type: 通知类型
            metadata: 元数据
            channels: 指定渠道列表，None表示所有渠道
            
        Returns:
            各渠道发送结果
        """
        message = NotificationMessage(
            title=title,
            content=content,
            type=type,
            timestamp=datetime.utcnow(),
            metadata=metadata or {}
        )
        
        # 应用过滤器
        for filter_func in self.filters:
            if not filter_func(message):
                logger.debug(f"Notification filtered out: {title}")
                return {}
        
        # 确定发送渠道
        target_channels = channels or list(self.channels.keys())
        
        # 并发发送
        tasks = []
        for channel_name in target_channels:
            if channel_name in self.channels:
                channel = self.channels[channel_name]
                tasks.append(self._send_to_channel(channel_name, channel, message))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        send_results = {}
        for i, channel_name in enumerate(target_channels):
            if i < len(results):
                if isinstance(results[i], Exception):
                    send_results[channel_name] = False
                    logger.error(f"Failed to send to {channel_name}: {results[i]}")
                else:
                    send_results[channel_name] = results[i]
        
        return send_results
    
    async def _send_to_channel(self, channel_name: str, channel: NotificationChannel, message: NotificationMessage) -> bool:
        """发送到指定渠道"""
        try:
            return await channel.send_message(message)
        except Exception as e:
            logger.error(f"Error sending to {channel_name}: {str(e)}")
            return False
    
    def add_filter(self, filter_func: Callable[[NotificationMessage], bool]):
        """添加通知过滤器"""
        self.filters.append(filter_func)
    
    def remove_filter(self, filter_func: Callable[[NotificationMessage], bool]):
        """移除通知过滤器"""
        if filter_func in self.filters:
            self.filters.remove(filter_func)
    
    async def send_error_notification(self, error_data: Dict[str, Any]):
        """发送错误通知"""
        severity = error_data.get('severity', 'medium')
        category = error_data.get('category', 'unknown')
        
        # 根据严重性确定通知类型
        if severity in ['critical', 'high']:
            notification_type = NotificationType.ERROR
        else:
            notification_type = NotificationType.WARNING
        
        title = f"System Error - {severity.upper()}"
        content = error_data.get('message', 'An error occurred')
        
        await self.send_notification(
            title=title,
            content=content,
            type=notification_type,
            metadata=error_data
        )
    
    async def send_business_notification(self, event_type: str, details: Dict[str, Any]):
        """发送业务通知"""
        title = f"Business Event: {event_type}"
        content = details.get('description', f"A {event_type} event occurred")
        
        await self.send_notification(
            title=title,
            content=content,
            type=NotificationType.INFO,
            metadata=details
        )
    
    def get_channel_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取渠道统计信息"""
        stats = {}
        for name, channel in self.channels.items():
            stats[name] = {
                'enabled': channel.enabled,
                'sent_count': channel.sent_count,
                'rate_limit': channel.rate_limit,
                'last_reset_time': channel.last_reset_time.isoformat()
            }
        return stats


# 全局通知服务实例
_notification_service: Optional[NotificationService] = None


def get_notification_service() -> NotificationService:
    """获取全局通知服务实例"""
    global _notification_service
    if _notification_service is None:
        _notification_service = NotificationService({})
    return _notification_service


def setup_notifications(config: Dict[str, Any]):
    """设置通知服务"""
    global _notification_service
    _notification_service = NotificationService(config)


async def send_notification(title: str, content: str, type: NotificationType = NotificationType.INFO, **kwargs):
    """快捷发送通知"""
    service = get_notification_service()
    return await service.send_notification(title, content, type, **kwargs)
