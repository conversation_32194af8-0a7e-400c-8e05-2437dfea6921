#!/usr/bin/env python3
"""
初始化数据库表结构
重新创建所有表以修复外键引用问题
"""

import sys
import os
from pathlib import Path
import base64

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置必要的环境变量
dummy_key_bytes = b'dummy_key_for_import_only_32byte'
dummy_key = base64.urlsafe_b64encode(dummy_key_bytes).decode()

os.environ.setdefault('STEAMDT_API_KEY', 'dummy_key_for_import')
os.environ.setdefault('ENCRYPTION_KEY', dummy_key)
os.environ.setdefault('SECRET_KEY', 'dummy_secret_key_for_import')
os.environ['DATABASE_URL'] = 'sqlite:///data/ares.db'

from core.database import get_database_manager, Base


def main():
    """主函数"""
    print("初始化数据库表结构...")
    
    try:
        # 获取数据库管理器
        db_manager = get_database_manager()
        engine = db_manager.engine

        print("删除现有表...")
        # 删除所有表
        Base.metadata.drop_all(bind=engine)

        print("创建新表...")
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        print("数据库表结构初始化完成！")
        
        # 验证表创建
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print(f"创建的表: {', '.join(tables)}")
        
        # 检查items表结构
        if 'items' in tables:
            columns = inspector.get_columns('items')
            print(f"items表列数: {len(columns)}")
            
            # 检查主键
            pk_columns = inspector.get_pk_constraint('items')
            print(f"items表主键: {pk_columns['constrained_columns']}")
        
        # 检查外键
        if 'prices' in tables:
            fks = inspector.get_foreign_keys('prices')
            print(f"prices表外键: {fks}")
        
        return 0
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"脚本执行失败: {e}")
        sys.exit(1)
