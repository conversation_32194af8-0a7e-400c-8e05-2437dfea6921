"""
Ares持仓管理页面
管理用户持仓、交易记录和成本分析
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta
import random

from app.utils.state_manager import StateManager


def show_holdings_page():
    """显示持仓管理页面"""
    st.markdown('<h1 class="section-title">📋 持仓管理</h1>', unsafe_allow_html=True)
    
    # 获取状态管理器
    state_manager = StateManager()
    
    # 页面选项卡
    tab1, tab2, tab3 = st.tabs(["📦 当前持仓", "📊 交易记录", "💰 成本分析"])
    
    with tab1:
        show_current_holdings(state_manager)
    
    with tab2:
        show_trading_history(state_manager)
    
    with tab3:
        show_cost_analysis(state_manager)


def show_current_holdings(state_manager: StateManager):
    """显示当前持仓"""
    st.markdown("### 📦 当前持仓")
    
    portfolio_data = state_manager.get_portfolio_data()
    
    # 操作按钮
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("➕ 添加持仓", use_container_width=True):
            st.session_state.show_add_form = True
    
    with col2:
        if st.button("📊 批量操作", use_container_width=True):
            st.session_state.show_batch_form = True
    
    with col3:
        if st.button("📤 导出数据", use_container_width=True):
            export_holdings_data(portfolio_data)
    
    with col4:
        if st.button("🔄 刷新数据", use_container_width=True):
            state_manager.clear_cache('portfolio_data')
            st.rerun()
    
    # 显示添加持仓表单
    if st.session_state.get('show_add_form', False):
        show_add_holding_form()
    
    # 显示批量操作表单
    if st.session_state.get('show_batch_form', False):
        show_batch_operations_form()
    
    # 持仓列表
    holdings_df = pd.DataFrame(portfolio_data['holdings'])
    
    # 添加操作列
    holdings_df['操作'] = holdings_df.index
    
    # 格式化显示
    display_df = holdings_df.copy()
    display_df['当前价格'] = display_df['current_price'].apply(lambda x: f"${x:.2f}")
    display_df['平均成本'] = display_df['avg_cost'].apply(lambda x: f"${x:.2f}")
    display_df['总价值'] = display_df['total_value'].apply(lambda x: f"${x:,.2f}")
    display_df['盈亏金额'] = display_df['profit_loss'].apply(lambda x: f"${x:+,.2f}")
    display_df['盈亏率'] = display_df['profit_percentage'].apply(lambda x: f"{x:+.2f}%")
    
    # 选择要显示的列
    columns_to_show = ['name', 'quantity', '平均成本', '当前价格', '总价值', '盈亏金额', '盈亏率']
    display_df = display_df[columns_to_show]
    display_df.columns = ['饰品名称', '数量', '平均成本', '当前价格', '总价值', '盈亏金额', '盈亏率']
    
    # 显示表格
    st.dataframe(
        display_df,
        use_container_width=True,
        hide_index=True
    )
    
    # 持仓统计
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("持仓总数", len(holdings_df))
    
    with col2:
        total_value = holdings_df['total_value'].sum()
        st.metric("总价值", f"${total_value:,.2f}")
    
    with col3:
        total_profit = holdings_df['profit_loss'].sum()
        st.metric("总盈亏", f"${total_profit:+,.2f}")
    
    with col4:
        avg_return = holdings_df['profit_percentage'].mean()
        st.metric("平均收益率", f"{avg_return:+.2f}%")


def show_add_holding_form():
    """显示添加持仓表单"""
    with st.expander("添加新持仓", expanded=True):
        with st.form("add_holding_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                item_name = st.text_input("饰品名称*", placeholder="例：AK-47 | Redline (Field-Tested)")
                quantity = st.number_input("数量*", min_value=1, value=1)
                purchase_price = st.number_input("购买价格*", min_value=0.01, value=100.0, step=0.01)
                purchase_date = st.date_input("购买日期", value=datetime.now().date())
            
            with col2:
                platform = st.selectbox(
                    "购买平台",
                    ["Steam市场", "BUFF", "C5Game", "第三方平台", "私人交易"]
                )
                condition = st.selectbox(
                    "磨损程度",
                    ["崭新出厂", "略有磨损", "久经沙场", "破损不堪", "战痕累累"]
                )
                notes = st.text_area("备注", placeholder="可选的备注信息")
            
            col_submit, col_cancel = st.columns(2)
            
            with col_submit:
                submitted = st.form_submit_button("添加持仓", use_container_width=True)
            
            with col_cancel:
                cancelled = st.form_submit_button("取消", use_container_width=True)
            
            if submitted:
                if item_name and quantity > 0 and purchase_price > 0:
                    # 这里应该调用实际的添加持仓逻辑
                    st.success(f"成功添加持仓：{item_name} x{quantity}")
                    st.session_state.show_add_form = False
                    st.rerun()
                else:
                    st.error("请填写完整的必填信息（标*项）")
            
            if cancelled:
                st.session_state.show_add_form = False
                st.rerun()


def show_batch_operations_form():
    """显示批量操作表单"""
    with st.expander("批量操作", expanded=True):
        operation_type = st.selectbox(
            "操作类型",
            ["批量更新价格", "批量修改数量", "批量删除", "批量导入"]
        )
        
        if operation_type == "批量更新价格":
            st.info("将从市场获取最新价格并更新所有持仓")
            if st.button("执行更新"):
                st.success("价格更新完成")
        
        elif operation_type == "批量修改数量":
            st.warning("请谨慎使用此功能")
            adjustment = st.number_input("数量调整", value=0, step=1)
            if st.button("执行调整"):
                st.success(f"数量调整完成：{adjustment:+d}")
        
        elif operation_type == "批量删除":
            st.error("此操作不可撤销")
            confirm = st.checkbox("我确认要删除选中的持仓")
            if confirm and st.button("执行删除"):
                st.success("删除操作完成")
        
        elif operation_type == "批量导入":
            uploaded_file = st.file_uploader("选择CSV文件", type=['csv'])
            if uploaded_file:
                st.info("文件上传成功，点击导入开始处理")
                if st.button("开始导入"):
                    st.success("导入完成")
        
        if st.button("关闭", use_container_width=True):
            st.session_state.show_batch_form = False
            st.rerun()


def show_trading_history(state_manager: StateManager):
    """显示交易记录"""
    st.markdown("### 📊 交易记录")
    
    # 生成模拟交易记录
    trading_records = generate_mock_trading_records()
    
    # 筛选选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        date_range = st.selectbox(
            "时间范围",
            ["最近7天", "最近30天", "最近90天", "全部"]
        )
    
    with col2:
        transaction_type = st.selectbox(
            "交易类型",
            ["全部", "买入", "卖出"]
        )
    
    with col3:
        item_filter = st.text_input("饰品名称筛选", placeholder="输入饰品名称")
    
    # 过滤数据
    filtered_records = trading_records
    if transaction_type != "全部":
        filtered_records = [r for r in filtered_records if r['type'] == transaction_type]
    
    if item_filter:
        filtered_records = [r for r in filtered_records if item_filter.lower() in r['item_name'].lower()]
    
    # 显示交易记录表格
    if filtered_records:
        df = pd.DataFrame(filtered_records)
        
        # 格式化显示
        df['交易时间'] = pd.to_datetime(df['timestamp']).dt.strftime('%Y-%m-%d %H:%M')
        df['交易类型'] = df['type'].map({'买入': '🟢 买入', '卖出': '🔴 卖出'})
        df['单价'] = df['price'].apply(lambda x: f"${x:.2f}")
        df['总金额'] = df['total_amount'].apply(lambda x: f"${x:.2f}")
        
        display_df = df[['交易时间', '饰品名称', '交易类型', '数量', '单价', '总金额', '平台']]
        display_df.columns = ['交易时间', '饰品名称', '交易类型', '数量', '单价', '总金额', '平台']
        
        st.dataframe(display_df, use_container_width=True, hide_index=True)
        
        # 交易统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("交易笔数", len(filtered_records))
        
        with col2:
            total_amount = sum(r['total_amount'] for r in filtered_records)
            st.metric("总交易额", f"${total_amount:,.2f}")
        
        with col3:
            buy_count = len([r for r in filtered_records if r['type'] == '买入'])
            st.metric("买入笔数", buy_count)
        
        with col4:
            sell_count = len([r for r in filtered_records if r['type'] == '卖出'])
            st.metric("卖出笔数", sell_count)
    
    else:
        st.info("没有找到符合条件的交易记录")


def show_cost_analysis(state_manager: StateManager):
    """显示成本分析"""
    st.markdown("### 💰 成本分析")
    
    portfolio_data = state_manager.get_portfolio_data()
    holdings_df = pd.DataFrame(portfolio_data['holdings'])
    
    # 成本分布图
    col1, col2 = st.columns(2)
    
    with col1:
        # 成本vs当前价值对比
        fig = px.bar(
            holdings_df,
            x='name',
            y=['avg_cost', 'current_price'],
            title="成本 vs 当前价格对比",
            barmode='group'
        )
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white',
            xaxis_tickangle=-45
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 盈亏分布
        fig = px.pie(
            holdings_df,
            values='total_value',
            names='name',
            title="持仓价值分布"
        )
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # 成本分析表格
    st.markdown("#### 详细成本分析")
    
    analysis_df = holdings_df.copy()
    analysis_df['总成本'] = analysis_df['avg_cost'] * analysis_df['quantity']
    analysis_df['成本占比'] = (analysis_df['总成本'] / analysis_df['总成本'].sum() * 100).round(2)
    analysis_df['价值占比'] = (analysis_df['total_value'] / analysis_df['total_value'].sum() * 100).round(2)
    
    # 格式化显示
    display_df = analysis_df[['name', 'quantity', 'avg_cost', 'current_price', '总成本', 'total_value', '成本占比', '价值占比', 'profit_loss', 'profit_percentage']].copy()
    display_df['avg_cost'] = display_df['avg_cost'].apply(lambda x: f"${x:.2f}")
    display_df['current_price'] = display_df['current_price'].apply(lambda x: f"${x:.2f}")
    display_df['总成本'] = display_df['总成本'].apply(lambda x: f"${x:.2f}")
    display_df['total_value'] = display_df['total_value'].apply(lambda x: f"${x:.2f}")
    display_df['成本占比'] = display_df['成本占比'].apply(lambda x: f"{x:.1f}%")
    display_df['价值占比'] = display_df['价值占比'].apply(lambda x: f"{x:.1f}%")
    display_df['profit_loss'] = display_df['profit_loss'].apply(lambda x: f"${x:+.2f}")
    display_df['profit_percentage'] = display_df['profit_percentage'].apply(lambda x: f"{x:+.1f}%")
    
    display_df.columns = ['饰品名称', '数量', '平均成本', '当前价格', '总成本', '当前价值', '成本占比', '价值占比', '盈亏金额', '盈亏率']
    
    st.dataframe(display_df, use_container_width=True, hide_index=True)


def generate_mock_trading_records():
    """生成模拟交易记录"""
    items = [
        "AK-47 | Redline (Field-Tested)",
        "AWP | Dragon Lore (Factory New)",
        "Karambit | Fade (Factory New)",
        "M4A4 | Howl (Factory New)",
        "Glock-18 | Fade (Factory New)"
    ]
    
    platforms = ["Steam市场", "BUFF", "C5Game", "第三方平台"]
    
    records = []
    for i in range(20):
        item = random.choice(items)
        transaction_type = random.choice(['买入', '卖出'])
        quantity = random.randint(1, 3)
        price = random.uniform(50, 500)
        
        records.append({
            'timestamp': datetime.now() - timedelta(days=random.randint(0, 30)),
            'item_name': item,
            'type': transaction_type,
            'quantity': quantity,
            'price': price,
            'total_amount': price * quantity,
            'platform': random.choice(platforms)
        })
    
    return sorted(records, key=lambda x: x['timestamp'], reverse=True)


def export_holdings_data(portfolio_data):
    """导出持仓数据"""
    holdings_df = pd.DataFrame(portfolio_data['holdings'])
    
    # 格式化数据
    export_df = holdings_df[['name', 'quantity', 'avg_cost', 'current_price', 'total_value', 'profit_loss', 'profit_percentage']].copy()
    export_df.columns = ['饰品名称', '数量', '平均成本', '当前价格', '总价值', '盈亏金额', '盈亏率']
    
    csv = export_df.to_csv(index=False, encoding='utf-8-sig')
    
    st.download_button(
        label="📥 下载CSV文件",
        data=csv,
        file_name=f"holdings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
        mime="text/csv",
        use_container_width=True
    )


if __name__ == "__main__":
    show_holdings_page()
