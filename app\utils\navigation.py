"""
Ares系统导航管理器
管理页面路由、导航历史和面包屑导航
"""

import streamlit as st
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class NavigationItem:
    """导航项"""
    key: str
    name: str
    icon: str
    path: str
    description: str = ""
    parent: Optional[str] = None
    children: List[str] = None
    access_level: str = "public"  # public, user, admin
    
    def __post_init__(self):
        if self.children is None:
            self.children = []


@dataclass
class NavigationHistory:
    """导航历史记录"""
    page: str
    timestamp: datetime
    params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.params is None:
            self.params = {}


class NavigationManager:
    """导航管理器"""
    
    def __init__(self):
        """初始化导航管理器"""
        self.pages = self._init_pages()
        self.max_history = 20
        
        # 初始化导航状态
        self._init_navigation_state()
    
    def _init_pages(self) -> Dict[str, NavigationItem]:
        """初始化页面配置"""
        pages = {
            'dashboard': NavigationItem(
                key='dashboard',
                name='仪表板',
                icon='🏠',
                path='/dashboard',
                description='系统概览和快速统计'
            ),
            'portfolio': NavigationItem(
                key='portfolio',
                name='投资组合',
                icon='💼',
                path='/portfolio',
                description='投资组合管理和分析'
            ),
            'market': NavigationItem(
                key='market',
                name='市场监控',
                icon='📈',
                path='/market',
                description='实时市场数据和趋势分析'
            ),
            'holdings': NavigationItem(
                key='holdings',
                name='持仓管理',
                icon='📋',
                path='/holdings',
                description='持仓详情和交易记录'
            ),
            'alerts': NavigationItem(
                key='alerts',
                name='预警中心',
                icon='🚨',
                path='/alerts',
                description='预警规则配置和事件监控'
            ),
            'analytics': NavigationItem(
                key='analytics',
                name='数据分析',
                icon='📊',
                path='/analytics',
                description='深度数据分析和报告'
            ),
            'settings': NavigationItem(
                key='settings',
                name='系统设置',
                icon='⚙️',
                path='/settings',
                description='系统配置和用户偏好'
            ),
            
            # 子页面
            'portfolio_overview': NavigationItem(
                key='portfolio_overview',
                name='组合概览',
                icon='📊',
                path='/portfolio/overview',
                description='投资组合总体概览',
                parent='portfolio'
            ),
            'portfolio_performance': NavigationItem(
                key='portfolio_performance',
                name='绩效分析',
                icon='📈',
                path='/portfolio/performance',
                description='投资绩效分析',
                parent='portfolio'
            ),
            'portfolio_allocation': NavigationItem(
                key='portfolio_allocation',
                name='资产配置',
                icon='🥧',
                path='/portfolio/allocation',
                description='资产配置分析',
                parent='portfolio'
            ),
            
            'market_overview': NavigationItem(
                key='market_overview',
                name='市场概览',
                icon='🌐',
                path='/market/overview',
                description='整体市场概况',
                parent='market'
            ),
            'market_trends': NavigationItem(
                key='market_trends',
                name='趋势分析',
                icon='📈',
                path='/market/trends',
                description='市场趋势分析',
                parent='market'
            ),
            'market_heatmap': NavigationItem(
                key='market_heatmap',
                name='热力图',
                icon='🔥',
                path='/market/heatmap',
                description='市场热力图',
                parent='market'
            )
        }
        
        # 设置父子关系
        for page in pages.values():
            if page.parent and page.parent in pages:
                pages[page.parent].children.append(page.key)
        
        return pages
    
    def _init_navigation_state(self):
        """初始化导航状态"""
        if 'current_page' not in st.session_state:
            st.session_state.current_page = 'dashboard'
        
        if 'navigation_history' not in st.session_state:
            st.session_state.navigation_history = []
        
        if 'navigation_params' not in st.session_state:
            st.session_state.navigation_params = {}
        
        if 'breadcrumb' not in st.session_state:
            st.session_state.breadcrumb = []
    
    def navigate_to(self, page_key: str, params: Dict[str, Any] = None):
        """导航到指定页面"""
        if page_key not in self.pages:
            st.error(f"页面 '{page_key}' 不存在")
            return False
        
        # 记录导航历史
        self._add_to_history(st.session_state.current_page, st.session_state.navigation_params)
        
        # 更新当前页面
        st.session_state.current_page = page_key
        st.session_state.navigation_params = params or {}
        
        # 更新面包屑
        self._update_breadcrumb(page_key)
        
        # 触发页面重新加载
        st.rerun()
        
        return True
    
    def _add_to_history(self, page: str, params: Dict[str, Any]):
        """添加到导航历史"""
        history_item = NavigationHistory(
            page=page,
            timestamp=datetime.now(),
            params=params
        )
        
        st.session_state.navigation_history.append(history_item)
        
        # 限制历史记录数量
        if len(st.session_state.navigation_history) > self.max_history:
            st.session_state.navigation_history.pop(0)
    
    def _update_breadcrumb(self, page_key: str):
        """更新面包屑导航"""
        breadcrumb = []
        current_page = self.pages.get(page_key)
        
        if current_page:
            # 构建面包屑路径
            path = []
            page = current_page
            
            while page:
                path.insert(0, page)
                if page.parent:
                    page = self.pages.get(page.parent)
                else:
                    break
            
            # 总是包含首页
            if path and path[0].key != 'dashboard':
                path.insert(0, self.pages['dashboard'])
            
            breadcrumb = path
        
        st.session_state.breadcrumb = breadcrumb
    
    def get_current_page(self) -> str:
        """获取当前页面"""
        return st.session_state.current_page
    
    def get_current_page_info(self) -> Optional[NavigationItem]:
        """获取当前页面信息"""
        return self.pages.get(st.session_state.current_page)
    
    def get_navigation_params(self) -> Dict[str, Any]:
        """获取导航参数"""
        return st.session_state.navigation_params
    
    def get_param(self, key: str, default: Any = None) -> Any:
        """获取导航参数"""
        return st.session_state.navigation_params.get(key, default)
    
    def set_param(self, key: str, value: Any):
        """设置导航参数"""
        st.session_state.navigation_params[key] = value
    
    def get_breadcrumb(self) -> List[NavigationItem]:
        """获取面包屑导航"""
        return st.session_state.breadcrumb
    
    def get_navigation_history(self) -> List[NavigationHistory]:
        """获取导航历史"""
        return st.session_state.navigation_history
    
    def go_back(self):
        """返回上一页"""
        if st.session_state.navigation_history:
            last_item = st.session_state.navigation_history.pop()
            st.session_state.current_page = last_item.page
            st.session_state.navigation_params = last_item.params
            self._update_breadcrumb(last_item.page)
            st.rerun()
    
    def get_main_pages(self) -> List[NavigationItem]:
        """获取主要页面列表"""
        return [page for page in self.pages.values() if page.parent is None]
    
    def get_child_pages(self, parent_key: str) -> List[NavigationItem]:
        """获取子页面列表"""
        parent = self.pages.get(parent_key)
        if parent and parent.children:
            return [self.pages[child_key] for child_key in parent.children if child_key in self.pages]
        return []
    
    def render_breadcrumb(self):
        """渲染面包屑导航"""
        breadcrumb = self.get_breadcrumb()
        
        if len(breadcrumb) > 1:
            breadcrumb_html = []
            
            for i, item in enumerate(breadcrumb):
                if i == len(breadcrumb) - 1:
                    # 当前页面，不可点击
                    breadcrumb_html.append(f'<span class="breadcrumb-current">{item.icon} {item.name}</span>')
                else:
                    # 可点击的面包屑项
                    breadcrumb_html.append(f'<span class="breadcrumb-item" onclick="navigate_to(\'{item.key}\')">{item.icon} {item.name}</span>')
                
                if i < len(breadcrumb) - 1:
                    breadcrumb_html.append('<span class="breadcrumb-separator">›</span>')
            
            breadcrumb_str = ' '.join(breadcrumb_html)
            
            st.markdown(f"""
            <div class="breadcrumb-container">
                {breadcrumb_str}
            </div>
            <style>
            .breadcrumb-container {{
                padding: 0.5rem 0;
                margin-bottom: 1rem;
                border-bottom: 1px solid #404040;
            }}
            .breadcrumb-item {{
                color: #1f77b4;
                cursor: pointer;
                text-decoration: none;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                transition: background-color 0.2s;
            }}
            .breadcrumb-item:hover {{
                background-color: rgba(31, 119, 180, 0.1);
            }}
            .breadcrumb-current {{
                color: #fafafa;
                font-weight: 600;
                padding: 0.25rem 0.5rem;
            }}
            .breadcrumb-separator {{
                color: #a6a6a6;
                margin: 0 0.5rem;
            }}
            </style>
            """, unsafe_allow_html=True)
    
    def render_navigation_menu(self):
        """渲染导航菜单"""
        main_pages = self.get_main_pages()
        current_page = self.get_current_page()
        
        for page in main_pages:
            # 检查是否为当前页面或其父页面
            is_current = (page.key == current_page)
            is_parent = any(child == current_page for child in page.children)
            
            # 渲染主页面按钮
            button_class = "nav-button-active" if is_current or is_parent else "nav-button"
            
            if st.button(
                f"{page.icon} {page.name}",
                key=f"nav_{page.key}",
                help=page.description,
                use_container_width=True
            ):
                self.navigate_to(page.key)
            
            # 如果有子页面且当前在此分组下，显示子页面
            if page.children and is_parent:
                child_pages = self.get_child_pages(page.key)
                for child in child_pages:
                    child_is_current = (child.key == current_page)
                    
                    if st.button(
                        f"  └ {child.icon} {child.name}",
                        key=f"nav_{child.key}",
                        help=child.description,
                        use_container_width=True
                    ):
                        self.navigate_to(child.key)
    
    def get_page_title(self) -> str:
        """获取当前页面标题"""
        current_page_info = self.get_current_page_info()
        if current_page_info:
            return f"{current_page_info.icon} {current_page_info.name}"
        return "Ares Investment System"
    
    def clear_history(self):
        """清除导航历史"""
        st.session_state.navigation_history.clear()
    
    def get_navigation_stats(self) -> Dict[str, Any]:
        """获取导航统计"""
        history = self.get_navigation_history()
        
        # 统计页面访问次数
        page_visits = {}
        for item in history:
            page_visits[item.page] = page_visits.get(item.page, 0) + 1
        
        # 最常访问的页面
        most_visited = sorted(page_visits.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'total_navigations': len(history),
            'unique_pages_visited': len(page_visits),
            'most_visited_pages': most_visited,
            'current_session_pages': len(set(item.page for item in history[-10:]))  # 最近10次导航的唯一页面数
        }
