"""
Ares系统布局组件
实现响应式三栏布局和页面结构
"""

import streamlit as st
from typing import Dict, Any
from datetime import datetime

from app.utils.state_manager import StateManager
from app.utils.navigation import NavigationManager


def setup_page_config():
    """设置页面配置"""
    st.set_page_config(
        page_title="Ares Investment System",
        page_icon="🎯",
        layout="wide",
        initial_sidebar_state="expanded",
        menu_items={
            'Get Help': 'https://github.com/your-repo/ares',
            'Report a bug': 'https://github.com/your-repo/ares/issues',
            'About': """
            # Ares Investment System
            
            智能化CS:GO饰品投资管理系统
            
            **版本**: 1.0.0
            **作者**: Ares Team
            """
        }
    )
    
    # 加载自定义CSS
    load_custom_css()


def load_custom_css():
    """加载自定义CSS样式"""
    css = """
    <style>
    /* 主题色彩 */
    :root {
        --primary-color: #1f77b4;
        --secondary-color: #ff7f0e;
        --success-color: #2ca02c;
        --warning-color: #ff7f0e;
        --danger-color: #d62728;
        --dark-bg: #0e1117;
        --card-bg: #262730;
        --text-primary: #fafafa;
        --text-secondary: #a6a6a6;
    }
    
    /* 隐藏Streamlit默认元素 */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* 自定义容器样式 */
    .main-container {
        padding: 1rem;
        background-color: var(--dark-bg);
    }
    
    .card {
        background-color: var(--card-bg);
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #404040;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .metric-card {
        background: linear-gradient(135deg, var(--card-bg) 0%, #2d2d3a 100%);
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-online { background-color: var(--success-color); }
    .status-warning { background-color: var(--warning-color); }
    .status-offline { background-color: var(--danger-color); }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .main-container {
            padding: 0.5rem;
        }
        .card {
            padding: 1rem;
        }
    }
    
    /* 表格样式 */
    .dataframe {
        border: none !important;
    }
    
    .dataframe th {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border: 1px solid #404040 !important;
    }
    
    .dataframe td {
        background-color: var(--dark-bg) !important;
        color: var(--text-primary) !important;
        border: 1px solid #404040 !important;
    }
    
    /* 按钮样式 */
    .stButton > button {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        background-color: #1565c0;
        box-shadow: 0 2px 8px rgba(31, 119, 180, 0.3);
    }
    
    /* 侧边栏样式 */
    .css-1d391kg {
        background-color: var(--card-bg);
    }
    
    /* 标题样式 */
    .main-title {
        color: var(--primary-color);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-align: center;
    }
    
    .section-title {
        color: var(--text-primary);
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
    }
    </style>
    """
    st.markdown(css, unsafe_allow_html=True)


def render_header(app_config: Dict[str, Any]):
    """渲染页面头部"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown(
            f'<h1 class="main-title">🎯 {app_config["app_name"]}</h1>',
            unsafe_allow_html=True
        )
        
        # 状态指示器
        col_a, col_b, col_c = st.columns(3)
        
        with col_a:
            st.markdown(
                '<span class="status-indicator status-online"></span>系统在线',
                unsafe_allow_html=True
            )
        
        with col_b:
            st.markdown(
                f'<span class="status-indicator status-online"></span>版本 {app_config["app_version"]}',
                unsafe_allow_html=True
            )
        
        with col_c:
            current_time = datetime.now().strftime("%H:%M:%S")
            st.markdown(
                f'<span class="status-indicator status-online"></span>{current_time}',
                unsafe_allow_html=True
            )


def render_sidebar(nav_manager: NavigationManager, state_manager: StateManager):
    """渲染侧边栏"""
    with st.sidebar:
        st.markdown('<h2 class="section-title">📊 导航菜单</h2>', unsafe_allow_html=True)
        
        # 主要功能页面
        pages = [
            {"name": "CS2饰品", "icon": "🎮", "key": "cs2_dashboard"},
            {"name": "投资组合", "icon": "💼", "key": "portfolio"},
            {"name": "市场监控", "icon": "📈", "key": "market"},
            {"name": "持仓管理", "icon": "📋", "key": "holdings"},
            {"name": "预警中心", "icon": "🚨", "key": "alerts"},
            {"name": "数据分析", "icon": "📊", "key": "analytics"},
            {"name": "系统设置", "icon": "⚙️", "key": "settings"}
        ]
        
        # 渲染导航按钮
        for page in pages:
            if st.button(
                f"{page['icon']} {page['name']}",
                key=f"nav_{page['key']}",
                use_container_width=True
            ):
                nav_manager.navigate_to(page['key'])
        
        st.divider()
        
        # 快速统计
        st.markdown('<h3 class="section-title">📈 快速统计</h3>', unsafe_allow_html=True)
        
        # 模拟数据
        quick_stats = state_manager.get_quick_stats()
        
        st.metric(
            label="总投资价值",
            value=f"${quick_stats.get('total_value', 0):,.2f}",
            delta=f"{quick_stats.get('value_change', 0):+.2f}%"
        )
        
        st.metric(
            label="持仓数量",
            value=quick_stats.get('holdings_count', 0),
            delta=quick_stats.get('holdings_change', 0)
        )
        
        st.metric(
            label="今日盈亏",
            value=f"${quick_stats.get('daily_pnl', 0):+,.2f}",
            delta=f"{quick_stats.get('pnl_change', 0):+.2f}%"
        )
        
        st.divider()
        
        # 系统状态
        st.markdown('<h3 class="section-title">🔧 系统状态</h3>', unsafe_allow_html=True)
        
        system_status = state_manager.get_system_status()
        
        for service, status in system_status.items():
            status_class = "status-online" if status else "status-offline"
            status_text = "正常" if status else "异常"
            st.markdown(
                f'<span class="status-indicator {status_class}"></span>{service}: {status_text}',
                unsafe_allow_html=True
            )


def render_main_content(nav_manager: NavigationManager, state_manager: StateManager):
    """渲染主要内容区域"""
    current_page = nav_manager.get_current_page()

    if current_page == "cs2_dashboard":
        from app.pages.cs2_dashboard import show_cs2_dashboard_page
        show_cs2_dashboard_page()
    elif current_page == "portfolio":
        from app.pages.portfolio import show_portfolio_page
        show_portfolio_page()
    elif current_page == "market":
        from app.pages.market import show_market_page
        show_market_page()
    elif current_page == "holdings":
        from app.pages.holdings import show_holdings_page
        show_holdings_page()
    elif current_page == "alerts":
        from app.pages.alert_config import show_alert_config_page
        show_alert_config_page()
    elif current_page == "analytics":
        from app.pages.analytics import show_analytics_page
        show_analytics_page()
    elif current_page == "settings":
        from app.pages.settings import show_settings_page
        show_settings_page()
    else:
        from app.pages.dashboard import show_dashboard_page
        show_dashboard_page()


def render_main_layout(state_manager: StateManager, nav_manager: NavigationManager, app_config: Dict[str, Any]):
    """渲染主布局"""
    # 渲染头部
    render_header(app_config)
    
    # 渲染侧边栏
    render_sidebar(nav_manager, state_manager)
    
    # 渲染主要内容
    render_main_content(nav_manager, state_manager)
