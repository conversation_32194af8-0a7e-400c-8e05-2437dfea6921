"""
Ares持仓表单组件
提供持仓的添加、编辑和批量操作表单
"""

import streamlit as st
import pandas as pd
import asyncio
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, date
import re

from services.holdings import get_holdings_manager, Holding


class HoldingsForm:
    """持仓表单组件"""
    
    def __init__(self, key: str, title: str = "持仓表单"):
        """
        初始化持仓表单
        
        Args:
            key: 组件唯一标识
            title: 表单标题
        """
        self.key = key
        self.title = title
        self.holdings_manager = get_holdings_manager()
        
        # 初始化会话状态
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if f"{self.key}_form_data" not in st.session_state:
            st.session_state[f"{self.key}_form_data"] = {}
        
        if f"{self.key}_validation_errors" not in st.session_state:
            st.session_state[f"{self.key}_validation_errors"] = {}
        
        if f"{self.key}_current_price" not in st.session_state:
            st.session_state[f"{self.key}_current_price"] = None
    
    def render_add_form(self, on_submit: Optional[Callable] = None) -> bool:
        """
        渲染添加持仓表单
        
        Args:
            on_submit: 提交回调函数
            
        Returns:
            bool: 是否成功提交
        """
        with st.form(f"{self.key}_add_form"):
            st.markdown(f"### {self.title}")
            
            # 基本信息
            col1, col2 = st.columns(2)
            
            with col1:
                item_name = st.text_input(
                    "饰品名称 *",
                    placeholder="例：AK-47 | Redline (Field-Tested)",
                    help="请输入完整的饰品名称"
                )
                
                quantity = st.number_input(
                    "数量 *",
                    min_value=1,
                    value=1,
                    step=1,
                    help="购买的数量"
                )
                
                purchase_price = st.number_input(
                    "购买价格 *",
                    min_value=0.01,
                    value=100.0,
                    step=0.01,
                    format="%.2f",
                    help="单个饰品的购买价格（美元）"
                )
                
                purchase_date = st.date_input(
                    "购买日期",
                    value=date.today(),
                    help="购买日期"
                )
            
            with col2:
                platform = st.selectbox(
                    "购买平台",
                    ["Steam市场", "BUFF", "C5Game", "第三方平台", "私人交易", "其他"],
                    help="购买平台"
                )
                
                condition = st.selectbox(
                    "磨损程度",
                    ["崭新出厂", "略有磨损", "久经沙场", "破损不堪", "战痕累累", "未知"],
                    index=5,
                    help="饰品的磨损程度"
                )
                
                notes = st.text_area(
                    "备注",
                    placeholder="可选的备注信息",
                    help="任何额外的备注信息"
                )
                
                # 显示当前市场价格（如果可用）
                if st.button("🔍 获取当前价格", key=f"{self.key}_get_price"):
                    if item_name:
                        with st.spinner("获取价格中..."):
                            current_price = self._get_current_price_sync(item_name)
                            if current_price:
                                st.session_state[f"{self.key}_current_price"] = current_price
                                st.success(f"当前市场价格: ${current_price:.2f}")
                            else:
                                st.warning("无法获取当前价格")
                    else:
                        st.warning("请先输入饰品名称")
                
                # 显示当前价格
                if st.session_state[f"{self.key}_current_price"]:
                    st.info(f"💰 当前市场价格: ${st.session_state[f'{self.key}_current_price']:.2f}")
            
            # 表单验证和提交
            col_submit, col_cancel = st.columns(2)
            
            with col_submit:
                submitted = st.form_submit_button("✅ 添加持仓", use_container_width=True)
            
            with col_cancel:
                cancelled = st.form_submit_button("❌ 取消", use_container_width=True)
            
            if submitted:
                # 验证表单
                validation_errors = self._validate_add_form(item_name, quantity, purchase_price)
                
                if not validation_errors:
                    try:
                        # 提取饰品ID（简化处理）
                        item_id = self._extract_item_id(item_name)
                        
                        # 创建持仓记录
                        holding_data = {
                            'item_id': item_id,
                            'item_name': item_name,
                            'quantity': quantity,
                            'cost_basis': purchase_price,
                            'purchase_date': purchase_date,
                            'platform': platform,
                            'condition': condition,
                            'notes': notes
                        }
                        
                        # 调用回调函数
                        if on_submit:
                            success = on_submit(holding_data)
                        else:
                            success = self._add_holding_sync(holding_data)
                        
                        if success:
                            st.success(f"✅ 成功添加持仓：{item_name} x{quantity}")
                            # 清除表单数据
                            st.session_state[f"{self.key}_current_price"] = None
                            return True
                        else:
                            st.error("❌ 添加持仓失败，请重试")
                    
                    except Exception as e:
                        st.error(f"❌ 添加持仓时发生错误：{str(e)}")
                
                else:
                    # 显示验证错误
                    for field, error in validation_errors.items():
                        st.error(f"❌ {field}: {error}")
            
            if cancelled:
                st.session_state[f"{self.key}_current_price"] = None
                return False
        
        return False
    
    def render_edit_form(self, holding: Holding, on_submit: Optional[Callable] = None) -> bool:
        """
        渲染编辑持仓表单
        
        Args:
            holding: 要编辑的持仓记录
            on_submit: 提交回调函数
            
        Returns:
            bool: 是否成功提交
        """
        with st.form(f"{self.key}_edit_form"):
            st.markdown(f"### 编辑持仓 - {holding.item_name}")
            
            col1, col2 = st.columns(2)
            
            with col1:
                new_quantity = st.number_input(
                    "数量",
                    min_value=1,
                    value=holding.quantity,
                    step=1
                )
                
                new_avg_cost = st.number_input(
                    "平均成本",
                    min_value=0.01,
                    value=holding.avg_cost,
                    step=0.01,
                    format="%.2f"
                )
                
                new_platform = st.selectbox(
                    "购买平台",
                    ["Steam市场", "BUFF", "C5Game", "第三方平台", "私人交易", "其他"],
                    index=0 if not holding.platform else ["Steam市场", "BUFF", "C5Game", "第三方平台", "私人交易", "其他"].index(holding.platform) if holding.platform in ["Steam市场", "BUFF", "C5Game", "第三方平台", "私人交易", "其他"] else 0
                )
            
            with col2:
                new_condition = st.selectbox(
                    "磨损程度",
                    ["崭新出厂", "略有磨损", "久经沙场", "破损不堪", "战痕累累", "未知"],
                    index=0 if not holding.condition else ["崭新出厂", "略有磨损", "久经沙场", "破损不堪", "战痕累累", "未知"].index(holding.condition) if holding.condition in ["崭新出厂", "略有磨损", "久经沙场", "破损不堪", "战痕累累", "未知"] else 5
                )
                
                new_notes = st.text_area(
                    "备注",
                    value=holding.notes or "",
                    placeholder="可选的备注信息"
                )
                
                # 显示当前信息
                st.info(f"💰 当前价格: ${holding.current_price:.2f}")
                st.info(f"📊 当前盈亏: ${holding.profit_loss:+.2f} ({holding.profit_percentage:+.1f}%)")
            
            col_submit, col_cancel = st.columns(2)
            
            with col_submit:
                submitted = st.form_submit_button("💾 保存修改", use_container_width=True)
            
            with col_cancel:
                cancelled = st.form_submit_button("❌ 取消", use_container_width=True)
            
            if submitted:
                try:
                    # 准备更新数据
                    update_data = {
                        'quantity': new_quantity,
                        'avg_cost': new_avg_cost,
                        'platform': new_platform,
                        'condition': new_condition,
                        'notes': new_notes
                    }
                    
                    # 调用回调函数
                    if on_submit:
                        success = on_submit(holding.id, update_data)
                    else:
                        success = self._update_holding_sync(holding.id, update_data)
                    
                    if success:
                        st.success("✅ 持仓更新成功")
                        return True
                    else:
                        st.error("❌ 更新失败，请重试")
                
                except Exception as e:
                    st.error(f"❌ 更新时发生错误：{str(e)}")
            
            if cancelled:
                return False
        
        return False
    
    def render_batch_operations_form(self, holdings: List[Holding]) -> Dict[str, Any]:
        """
        渲染批量操作表单
        
        Args:
            holdings: 持仓列表
            
        Returns:
            Dict[str, Any]: 批量操作结果
        """
        st.markdown("### 🔧 批量操作")
        
        operation_type = st.selectbox(
            "操作类型",
            ["批量更新价格", "批量修改数量", "批量删除", "批量导出"],
            help="选择要执行的批量操作"
        )
        
        result = {'operation': operation_type, 'success': False, 'message': ''}
        
        if operation_type == "批量更新价格":
            st.info("📈 将从市场获取最新价格并更新所有持仓")
            
            if st.button("🔄 执行价格更新", use_container_width=True):
                with st.spinner("更新价格中..."):
                    try:
                        updated_count = self._batch_update_prices_sync()
                        result['success'] = True
                        result['message'] = f"成功更新 {updated_count} 个持仓的价格"
                        st.success(result['message'])
                    except Exception as e:
                        result['message'] = f"价格更新失败：{str(e)}"
                        st.error(result['message'])
        
        elif operation_type == "批量修改数量":
            st.warning("⚠️ 请谨慎使用此功能")
            
            selected_holdings = st.multiselect(
                "选择要修改的持仓",
                options=[f"{h.item_name} (当前: {h.quantity})" for h in holdings],
                help="选择要修改数量的持仓"
            )
            
            if selected_holdings:
                adjustment_type = st.radio(
                    "调整类型",
                    ["增加", "减少", "设置为"],
                    horizontal=True
                )
                
                adjustment_value = st.number_input(
                    "调整数值",
                    min_value=1,
                    value=1,
                    step=1
                )
                
                if st.button("📊 执行数量调整", use_container_width=True):
                    try:
                        # 这里应该实现实际的批量数量调整逻辑
                        result['success'] = True
                        result['message'] = f"成功调整 {len(selected_holdings)} 个持仓的数量"
                        st.success(result['message'])
                    except Exception as e:
                        result['message'] = f"数量调整失败：{str(e)}"
                        st.error(result['message'])
        
        elif operation_type == "批量删除":
            st.error("🚨 此操作不可撤销")
            
            selected_holdings = st.multiselect(
                "选择要删除的持仓",
                options=[f"{h.item_name} (价值: ${h.total_value:.2f})" for h in holdings],
                help="选择要删除的持仓"
            )
            
            if selected_holdings:
                confirm = st.checkbox("✅ 我确认要删除选中的持仓")
                
                if confirm and st.button("🗑️ 执行删除", use_container_width=True):
                    try:
                        # 这里应该实现实际的批量删除逻辑
                        result['success'] = True
                        result['message'] = f"成功删除 {len(selected_holdings)} 个持仓"
                        st.success(result['message'])
                    except Exception as e:
                        result['message'] = f"删除失败：{str(e)}"
                        st.error(result['message'])
        
        elif operation_type == "批量导出":
            st.info("📤 导出持仓数据到文件")
            
            export_format = st.selectbox(
                "导出格式",
                ["CSV", "Excel", "JSON"],
                help="选择导出文件格式"
            )
            
            include_fields = st.multiselect(
                "包含字段",
                ["饰品名称", "数量", "平均成本", "当前价格", "总价值", "盈亏金额", "盈亏率", "购买日期", "平台", "备注"],
                default=["饰品名称", "数量", "平均成本", "当前价格", "总价值", "盈亏金额", "盈亏率"],
                help="选择要导出的字段"
            )
            
            if st.button("📥 生成导出文件", use_container_width=True):
                try:
                    export_data = self._prepare_export_data(holdings, include_fields, export_format)
                    result['success'] = True
                    result['data'] = export_data
                    result['message'] = f"成功生成 {export_format} 格式的导出文件"
                    st.success(result['message'])
                except Exception as e:
                    result['message'] = f"导出失败：{str(e)}"
                    st.error(result['message'])
        
        return result
    
    def _validate_add_form(self, item_name: str, quantity: int, purchase_price: float) -> Dict[str, str]:
        """验证添加表单"""
        errors = {}
        
        if not item_name or len(item_name.strip()) < 3:
            errors['饰品名称'] = "饰品名称至少需要3个字符"
        
        if quantity <= 0:
            errors['数量'] = "数量必须大于0"
        
        if purchase_price <= 0:
            errors['购买价格'] = "购买价格必须大于0"
        
        # 验证饰品名称格式（简单验证）
        if item_name and not re.match(r'^[A-Za-z0-9\s\-\|\(\)]+$', item_name):
            errors['饰品名称'] = "饰品名称包含无效字符"
        
        return errors
    
    def _extract_item_id(self, item_name: str) -> str:
        """从饰品名称提取ID（简化处理）"""
        # 这里应该实现实际的饰品ID提取逻辑
        # 暂时使用名称的哈希值作为ID
        import hashlib
        return hashlib.md5(item_name.encode()).hexdigest()[:16]
    
    def _get_current_price_sync(self, item_name: str) -> Optional[float]:
        """同步获取当前价格"""
        try:
            # 这里应该实现实际的价格获取逻辑
            # 暂时返回模拟价格
            import random
            return round(random.uniform(50, 500), 2)
        except Exception as e:
            st.error(f"获取价格失败：{str(e)}")
            return None
    
    def _add_holding_sync(self, holding_data: Dict[str, Any]) -> bool:
        """同步添加持仓"""
        try:
            # 这里应该调用异步的添加持仓方法
            # 暂时返回成功
            return True
        except Exception as e:
            st.error(f"添加持仓失败：{str(e)}")
            return False
    
    def _update_holding_sync(self, holding_id: str, update_data: Dict[str, Any]) -> bool:
        """同步更新持仓"""
        try:
            # 这里应该调用异步的更新持仓方法
            # 暂时返回成功
            return True
        except Exception as e:
            st.error(f"更新持仓失败：{str(e)}")
            return False
    
    def _batch_update_prices_sync(self) -> int:
        """同步批量更新价格"""
        try:
            # 这里应该调用异步的批量更新价格方法
            # 暂时返回模拟数量
            import random
            return random.randint(5, 15)
        except Exception as e:
            st.error(f"批量更新价格失败：{str(e)}")
            return 0
    
    def _prepare_export_data(self, holdings: List[Holding], fields: List[str], format_type: str) -> str:
        """准备导出数据"""
        try:
            # 转换为DataFrame
            data = []
            for holding in holdings:
                row = {}
                if "饰品名称" in fields:
                    row["饰品名称"] = holding.item_name
                if "数量" in fields:
                    row["数量"] = holding.quantity
                if "平均成本" in fields:
                    row["平均成本"] = holding.avg_cost
                if "当前价格" in fields:
                    row["当前价格"] = holding.current_price
                if "总价值" in fields:
                    row["总价值"] = holding.total_value
                if "盈亏金额" in fields:
                    row["盈亏金额"] = holding.profit_loss
                if "盈亏率" in fields:
                    row["盈亏率"] = holding.profit_percentage
                if "购买日期" in fields:
                    row["购买日期"] = holding.purchase_date.isoformat() if holding.purchase_date else ""
                if "平台" in fields:
                    row["平台"] = holding.platform
                if "备注" in fields:
                    row["备注"] = holding.notes
                data.append(row)
            
            df = pd.DataFrame(data)
            
            if format_type == "CSV":
                return df.to_csv(index=False, encoding='utf-8-sig')
            elif format_type == "JSON":
                return df.to_json(orient='records', indent=2)
            else:  # Excel
                # 对于Excel，返回字节数据
                import io
                output = io.BytesIO()
                df.to_excel(output, index=False, engine='openpyxl')
                return output.getvalue()
        
        except Exception as e:
            raise Exception(f"准备导出数据失败：{str(e)}")


def create_holdings_add_form(key: str = "holdings_add") -> HoldingsForm:
    """创建添加持仓表单"""
    return HoldingsForm(key, "添加新持仓")


def create_holdings_edit_form(key: str = "holdings_edit") -> HoldingsForm:
    """创建编辑持仓表单"""
    return HoldingsForm(key, "编辑持仓")


def create_holdings_batch_form(key: str = "holdings_batch") -> HoldingsForm:
    """创建批量操作表单"""
    return HoldingsForm(key, "批量操作")
