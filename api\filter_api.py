"""
Ares筛选API接口
提供RESTful API接口用于筛选和评分功能
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

from core.filter import (
    get_intelligent_filter, FilterCondition, FilterOperator, 
    SortCriteria, SortOrder, FilterPreset
)
from core.scoring import ScoreWeights, get_scoring_engine

# 创建路由器
router = APIRouter(prefix="/api/filter", tags=["筛选"])


# 请求模型
class FilterConditionRequest(BaseModel):
    """筛选条件请求模型"""
    field: str = Field(..., description="字段名")
    operator: str = Field(..., description="操作符")
    value: Any = Field(..., description="值")
    weight: float = Field(1.0, description="权重")


class SortCriteriaRequest(BaseModel):
    """排序条件请求模型"""
    field: str = Field(..., description="排序字段")
    order: str = Field("desc", description="排序方向")
    weight: float = Field(1.0, description="权重")


class ScoreWeightsRequest(BaseModel):
    """评分权重请求模型"""
    spread_weight: float = Field(0.25, description="价差权重")
    volume_weight: float = Field(0.20, description="交易量权重")
    volatility_weight: float = Field(0.15, description="波动性权重")
    trend_weight: float = Field(0.20, description="趋势权重")
    liquidity_weight: float = Field(0.10, description="流动性权重")
    momentum_weight: float = Field(0.10, description="动量权重")


class FilterRequest(BaseModel):
    """筛选请求模型"""
    items: List[Dict[str, Any]] = Field(..., description="待筛选的饰品列表")
    conditions: Optional[List[FilterConditionRequest]] = Field(None, description="筛选条件")
    sort_criteria: Optional[List[SortCriteriaRequest]] = Field(None, description="排序条件")
    score_weights: Optional[ScoreWeightsRequest] = Field(None, description="评分权重")
    limit: Optional[int] = Field(None, description="结果数量限制")
    use_cache: bool = Field(True, description="是否使用缓存")


class FilterPresetRequest(BaseModel):
    """筛选预设请求模型"""
    id: str = Field(..., description="预设ID")
    name: str = Field(..., description="预设名称")
    description: str = Field(..., description="预设描述")
    conditions: List[FilterConditionRequest] = Field(..., description="筛选条件")
    sort_criteria: List[SortCriteriaRequest] = Field(..., description="排序条件")
    score_weights: Optional[ScoreWeightsRequest] = Field(None, description="评分权重")


class RecommendationRequest(BaseModel):
    """推荐请求模型"""
    user_preferences: Optional[Dict[str, Any]] = Field(None, description="用户偏好")
    limit: int = Field(20, description="推荐数量")


# 响应模型
class FilterResponse(BaseModel):
    """筛选响应模型"""
    success: bool = Field(..., description="是否成功")
    data: List[Dict[str, Any]] = Field(..., description="筛选结果")
    total_count: int = Field(..., description="总数量")
    execution_time: float = Field(..., description="执行时间(秒)")
    cache_hit: bool = Field(False, description="是否命中缓存")


class ScoreResponse(BaseModel):
    """评分响应模型"""
    success: bool = Field(..., description="是否成功")
    scores: List[Dict[str, Any]] = Field(..., description="评分结果")
    execution_time: float = Field(..., description="执行时间(秒)")


class PresetResponse(BaseModel):
    """预设响应模型"""
    success: bool = Field(..., description="是否成功")
    presets: List[Dict[str, Any]] = Field(..., description="预设列表")


# API端点
@router.post("/filter", response_model=FilterResponse)
async def filter_items(request: FilterRequest):
    """筛选饰品"""
    try:
        import time
        start_time = time.time()
        
        intelligent_filter = get_intelligent_filter()
        
        # 转换筛选条件
        conditions = []
        if request.conditions:
            for cond_req in request.conditions:
                try:
                    operator = FilterOperator(cond_req.operator)
                    condition = FilterCondition(
                        field=cond_req.field,
                        operator=operator,
                        value=cond_req.value,
                        weight=cond_req.weight
                    )
                    conditions.append(condition)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid operator: {cond_req.operator}"
                    )
        
        # 转换排序条件
        sort_criteria = []
        if request.sort_criteria:
            for sort_req in request.sort_criteria:
                try:
                    order = SortOrder(sort_req.order)
                    criteria = SortCriteria(
                        field=sort_req.field,
                        order=order,
                        weight=sort_req.weight
                    )
                    sort_criteria.append(criteria)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid sort order: {sort_req.order}"
                    )
        
        # 转换评分权重
        score_weights = None
        if request.score_weights:
            score_weights = ScoreWeights(
                spread_weight=request.score_weights.spread_weight,
                volume_weight=request.score_weights.volume_weight,
                volatility_weight=request.score_weights.volatility_weight,
                trend_weight=request.score_weights.trend_weight,
                liquidity_weight=request.score_weights.liquidity_weight,
                momentum_weight=request.score_weights.momentum_weight
            )
        
        # 执行筛选
        results = intelligent_filter.filter_items(
            items=request.items,
            conditions=conditions,
            sort_criteria=sort_criteria,
            score_weights=score_weights,
            limit=request.limit,
            use_cache=request.use_cache
        )
        
        execution_time = time.time() - start_time
        
        return FilterResponse(
            success=True,
            data=results,
            total_count=len(results),
            execution_time=execution_time,
            cache_hit=False  # 实际实现中应该从筛选器获取
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/score", response_model=ScoreResponse)
async def calculate_scores(
    items: List[Dict[str, Any]] = Body(..., description="待评分的饰品列表"),
    score_weights: Optional[ScoreWeightsRequest] = Body(None, description="评分权重")
):
    """计算饰品评分"""
    try:
        import time
        start_time = time.time()
        
        scoring_engine = get_scoring_engine()
        
        # 转换评分权重
        weights = None
        if score_weights:
            weights = ScoreWeights(
                spread_weight=score_weights.spread_weight,
                volume_weight=score_weights.volume_weight,
                volatility_weight=score_weights.volatility_weight,
                trend_weight=score_weights.trend_weight,
                liquidity_weight=score_weights.liquidity_weight,
                momentum_weight=score_weights.momentum_weight
            )
        
        # 批量计算评分
        scores = scoring_engine.batch_calculate_scores(items, weights)
        
        # 转换为字典格式
        score_dicts = []
        for score in scores:
            score_dict = {
                "item_id": score.item_id,
                "spread_score": score.spread_score,
                "volume_score": score.volume_score,
                "volatility_score": score.volatility_score,
                "trend_score": score.trend_score,
                "liquidity_score": score.liquidity_score,
                "momentum_score": score.momentum_score,
                "total_score": score.total_score,
                "confidence": score.confidence,
                "risk_level": score.risk_level,
                "recommendation": score.recommendation
            }
            score_dicts.append(score_dict)
        
        execution_time = time.time() - start_time
        
        return ScoreResponse(
            success=True,
            scores=score_dicts,
            execution_time=execution_time
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/presets", response_model=PresetResponse)
async def get_filter_presets():
    """获取筛选预设列表"""
    try:
        intelligent_filter = get_intelligent_filter()
        presets = intelligent_filter.get_all_presets()
        
        preset_dicts = [preset.to_dict() for preset in presets]
        
        return PresetResponse(
            success=True,
            presets=preset_dicts
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/presets/{preset_id}")
async def get_filter_preset(preset_id: str):
    """获取特定筛选预设"""
    try:
        intelligent_filter = get_intelligent_filter()
        preset = intelligent_filter.load_preset(preset_id)
        
        if preset is None:
            raise HTTPException(status_code=404, detail="Preset not found")
        
        return {
            "success": True,
            "preset": preset.to_dict()
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/presets")
async def create_filter_preset(request: FilterPresetRequest):
    """创建筛选预设"""
    try:
        intelligent_filter = get_intelligent_filter()
        
        # 转换筛选条件
        conditions = []
        for cond_req in request.conditions:
            try:
                operator = FilterOperator(cond_req.operator)
                condition = FilterCondition(
                    field=cond_req.field,
                    operator=operator,
                    value=cond_req.value,
                    weight=cond_req.weight
                )
                conditions.append(condition)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid operator: {cond_req.operator}"
                )
        
        # 转换排序条件
        sort_criteria = []
        for sort_req in request.sort_criteria:
            try:
                order = SortOrder(sort_req.order)
                criteria = SortCriteria(
                    field=sort_req.field,
                    order=order,
                    weight=sort_req.weight
                )
                sort_criteria.append(criteria)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid sort order: {sort_req.order}"
                )
        
        # 转换评分权重
        score_weights = None
        if request.score_weights:
            score_weights = ScoreWeights(
                spread_weight=request.score_weights.spread_weight,
                volume_weight=request.score_weights.volume_weight,
                volatility_weight=request.score_weights.volatility_weight,
                trend_weight=request.score_weights.trend_weight,
                liquidity_weight=request.score_weights.liquidity_weight,
                momentum_weight=request.score_weights.momentum_weight
            )
        
        # 创建预设
        preset = FilterPreset(
            id=request.id,
            name=request.name,
            description=request.description,
            conditions=conditions,
            sort_criteria=sort_criteria,
            score_weights=score_weights
        )
        
        # 保存预设
        success = intelligent_filter.save_preset(preset)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save preset")
        
        return {
            "success": True,
            "message": "Preset created successfully",
            "preset_id": request.id
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/presets/{preset_id}")
async def delete_filter_preset(preset_id: str):
    """删除筛选预设"""
    try:
        intelligent_filter = get_intelligent_filter()
        success = intelligent_filter.delete_preset(preset_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Preset not found")
        
        return {
            "success": True,
            "message": "Preset deleted successfully"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recommendations", response_model=FilterResponse)
async def get_recommendations(request: RecommendationRequest):
    """获取个性化推荐"""
    try:
        import time
        start_time = time.time()
        
        intelligent_filter = get_intelligent_filter()
        
        # 获取推荐
        recommendations = intelligent_filter.get_recommendations(
            user_preferences=request.user_preferences,
            limit=request.limit
        )
        
        execution_time = time.time() - start_time
        
        return FilterResponse(
            success=True,
            data=recommendations,
            total_count=len(recommendations),
            execution_time=execution_time,
            cache_hit=False
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_filter_stats():
    """获取筛选统计信息"""
    try:
        intelligent_filter = get_intelligent_filter()
        scoring_engine = get_scoring_engine()
        
        filter_stats = intelligent_filter.get_filter_stats()
        scoring_stats = scoring_engine.get_scoring_stats()
        
        return {
            "success": True,
            "filter_stats": filter_stats,
            "scoring_stats": scoring_stats
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/operators")
async def get_filter_operators():
    """获取可用的筛选操作符"""
    operators = [
        {"value": "eq", "label": "等于", "description": "字段值等于指定值"},
        {"value": "ne", "label": "不等于", "description": "字段值不等于指定值"},
        {"value": "gt", "label": "大于", "description": "字段值大于指定值"},
        {"value": "gte", "label": "大于等于", "description": "字段值大于等于指定值"},
        {"value": "lt", "label": "小于", "description": "字段值小于指定值"},
        {"value": "lte", "label": "小于等于", "description": "字段值小于等于指定值"},
        {"value": "between", "label": "介于", "description": "字段值在指定范围内"},
        {"value": "in", "label": "包含", "description": "字段值在指定列表中"},
        {"value": "not_in", "label": "不包含", "description": "字段值不在指定列表中"},
        {"value": "contains", "label": "包含文本", "description": "字段值包含指定文本"},
        {"value": "not_contains", "label": "不包含文本", "description": "字段值不包含指定文本"}
    ]
    
    return {
        "success": True,
        "operators": operators
    }


@router.get("/fields")
async def get_filter_fields():
    """获取可用的筛选字段"""
    fields = [
        {"name": "current_price", "label": "当前价格", "type": "number", "description": "饰品当前市场价格"},
        {"name": "volume_24h", "label": "24小时交易量", "type": "number", "description": "过去24小时的交易量"},
        {"name": "price_change_24h", "label": "24小时价格变化", "type": "number", "description": "过去24小时的价格变化百分比"},
        {"name": "price_change_7d", "label": "7天价格变化", "type": "number", "description": "过去7天的价格变化百分比"},
        {"name": "price_change_30d", "label": "30天价格变化", "type": "number", "description": "过去30天的价格变化百分比"},
        {"name": "volatility", "label": "波动性", "type": "number", "description": "价格波动性指标"},
        {"name": "category", "label": "类别", "type": "string", "description": "饰品类别"},
        {"name": "rarity", "label": "稀有度", "type": "string", "description": "饰品稀有度"},
        {"name": "condition", "label": "磨损程度", "type": "string", "description": "饰品磨损程度"},
        {"name": "liquidity_score", "label": "流动性评分", "type": "number", "description": "流动性评分(0-1)"},
        {"name": "risk_score", "label": "风险评分", "type": "number", "description": "风险评分(0-1)"}
    ]
    
    return {
        "success": True,
        "fields": fields
    }
