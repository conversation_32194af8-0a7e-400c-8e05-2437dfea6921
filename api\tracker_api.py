"""
Ares追踪器API接口
提供追踪器服务的管理和监控API
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, Body
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime

from services.tracker import get_tracker_service
from core.exceptions import AresException

# 创建路由器
router = APIRouter(prefix="/api/tracker", tags=["追踪器"])


# 请求模型
class ForceUpdateRequest(BaseModel):
    """强制更新请求"""
    item_id: str = Field(..., description="饰品ID")
    reason: Optional[str] = Field(None, description="更新原因")


class BatchUpdateRequest(BaseModel):
    """批量更新请求"""
    item_ids: List[str] = Field(..., description="饰品ID列表")
    priority: bool = Field(False, description="是否优先处理")


class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    batch_size: Optional[int] = Field(None, ge=1, le=20, description="批处理大小")
    cycle_interval: Optional[int] = Field(None, ge=10, le=300, description="循环间隔(秒)")
    health_check_interval: Optional[int] = Field(None, ge=60, le=3600, description="健康检查间隔(秒)")


# 响应模型
class TrackerStatusResponse(BaseModel):
    """追踪器状态响应"""
    success: bool = Field(..., description="是否成功")
    status: Dict[str, Any] = Field(..., description="追踪器状态")


class UpdateResponse(BaseModel):
    """更新响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    item_id: Optional[str] = Field(None, description="饰品ID")
    timestamp: str = Field(..., description="时间戳")


# API端点
@router.get("/status", response_model=TrackerStatusResponse)
async def get_tracker_status():
    """获取追踪器状态"""
    try:
        tracker = get_tracker_service()
        status = tracker.get_status()
        
        return TrackerStatusResponse(
            success=True,
            status=status
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start_tracker(background_tasks: BackgroundTasks):
    """启动追踪器服务"""
    try:
        tracker = get_tracker_service()
        
        if tracker.running:
            return {
                "success": True,
                "message": "Tracker is already running",
                "status": "running"
            }
        
        # 在后台启动追踪器
        background_tasks.add_task(tracker.start)
        
        return {
            "success": True,
            "message": "Tracker start initiated",
            "status": "starting",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_tracker():
    """停止追踪器服务"""
    try:
        tracker = get_tracker_service()
        await tracker.stop()
        
        return {
            "success": True,
            "message": "Tracker stopped successfully",
            "status": "stopped",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/restart")
async def restart_tracker(background_tasks: BackgroundTasks):
    """重启追踪器服务"""
    try:
        tracker = get_tracker_service()
        
        # 停止服务
        if tracker.running:
            await tracker.stop()
        
        # 在后台重新启动
        background_tasks.add_task(tracker.start)
        
        return {
            "success": True,
            "message": "Tracker restart initiated",
            "status": "restarting",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/force-update", response_model=UpdateResponse)
async def force_update_item(request: ForceUpdateRequest):
    """强制更新指定饰品"""
    try:
        tracker = get_tracker_service()
        success = await tracker.force_update_item(request.item_id)
        
        return UpdateResponse(
            success=success,
            message=f"Force update {'successful' if success else 'failed'} for item {request.item_id}",
            item_id=request.item_id,
            timestamp=datetime.utcnow().isoformat()
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-update")
async def batch_update_items(request: BatchUpdateRequest):
    """批量更新饰品"""
    try:
        tracker = get_tracker_service()
        
        results = []
        for item_id in request.item_ids:
            try:
                success = await tracker.force_update_item(item_id)
                results.append({
                    "item_id": item_id,
                    "success": success,
                    "message": "Updated successfully" if success else "Update failed"
                })
            except Exception as e:
                results.append({
                    "item_id": item_id,
                    "success": False,
                    "message": f"Error: {str(e)}"
                })
        
        successful_count = sum(1 for r in results if r["success"])
        
        return {
            "success": True,
            "message": f"Batch update completed: {successful_count}/{len(request.item_ids)} successful",
            "results": results,
            "summary": {
                "total": len(request.item_ids),
                "successful": successful_count,
                "failed": len(request.item_ids) - successful_count
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_tracker_statistics():
    """获取追踪器统计信息"""
    try:
        tracker = get_tracker_service()
        status = tracker.get_status()
        stats = status['stats']
        
        # 计算额外统计信息
        uptime_hours = 0
        if status.get('uptime'):
            uptime_str = status['uptime']
            # 解析运行时间字符串 "Xh Ym"
            if 'h' in uptime_str:
                hours_part = uptime_str.split('h')[0]
                uptime_hours = int(hours_part)
        
        updates_per_hour = stats['total_updates'] / max(uptime_hours, 1) if uptime_hours > 0 else 0
        
        return {
            "success": True,
            "statistics": {
                **stats,
                "uptime_hours": uptime_hours,
                "updates_per_hour": round(updates_per_hour, 2),
                "core_pool_percentage": round(
                    (stats['core_pool_updates'] / max(stats['total_updates'], 1)) * 100, 1
                ),
                "main_pool_percentage": round(
                    (stats['main_pool_updates'] / max(stats['total_updates'], 1)) * 100, 1
                )
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/health-check")
async def perform_health_check():
    """执行健康检查"""
    try:
        tracker = get_tracker_service()
        
        # 执行健康检查
        health_status = await tracker._perform_health_check()
        
        return {
            "success": True,
            "health_status": "healthy" if health_status else "unhealthy",
            "message": "Health check completed",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config")
async def update_tracker_config(request: ConfigUpdateRequest):
    """更新追踪器配置"""
    try:
        tracker = get_tracker_service()
        
        updated_configs = {}
        
        if request.batch_size is not None:
            tracker.batch_size = request.batch_size
            updated_configs['batch_size'] = request.batch_size
        
        if request.cycle_interval is not None:
            tracker.cycle_interval = request.cycle_interval
            updated_configs['cycle_interval'] = request.cycle_interval
        
        if request.health_check_interval is not None:
            tracker.health_check_interval = request.health_check_interval
            updated_configs['health_check_interval'] = request.health_check_interval
        
        return {
            "success": True,
            "message": "Configuration updated successfully",
            "updated_configs": updated_configs,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_tracker_config():
    """获取追踪器配置"""
    try:
        tracker = get_tracker_service()
        
        return {
            "success": True,
            "config": {
                "batch_size": tracker.batch_size,
                "cycle_interval": tracker.cycle_interval,
                "health_check_interval": tracker.health_check_interval,
                "max_retries": tracker.max_retries
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs")
async def get_tracker_logs(
    lines: int = Query(100, ge=1, le=1000, description="日志行数"),
    level: str = Query("INFO", description="日志级别过滤")
):
    """获取追踪器日志"""
    try:
        # 这里应该实现日志读取逻辑
        # 暂时返回模拟数据
        
        logs = [
            {
                "timestamp": datetime.utcnow().isoformat(),
                "level": "INFO",
                "message": "Tracker service is running normally",
                "module": "tracker"
            }
        ]
        
        return {
            "success": True,
            "logs": logs,
            "total_lines": len(logs),
            "filter_level": level,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset-stats")
async def reset_tracker_statistics():
    """重置追踪器统计信息"""
    try:
        tracker = get_tracker_service()
        
        # 重置统计信息
        tracker.stats.total_updates = 0
        tracker.stats.successful_updates = 0
        tracker.stats.failed_updates = 0
        tracker.stats.core_pool_updates = 0
        tracker.stats.main_pool_updates = 0
        tracker.stats.uptime_start = datetime.utcnow()
        
        # 保存状态
        tracker._save_state()
        
        return {
            "success": True,
            "message": "Statistics reset successfully",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
