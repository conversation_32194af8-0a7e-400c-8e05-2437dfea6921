#!/bin/bash
# Ares投资系统部署脚本
# 支持开发、测试、生产环境的一键部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "${DEBUG}" = "true" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Ares投资系统部署脚本

用法: $0 [选项] <环境>

环境:
  dev         开发环境 (默认)
  test        测试环境
  staging     预发布环境
  prod        生产环境

选项:
  -h, --help              显示此帮助信息
  -v, --verbose           详细输出
  -f, --force             强制重新构建
  -c, --clean             清理现有容器和卷
  -b, --backup            部署前备份数据
  -s, --scale <num>       设置应用实例数量
  --no-cache              构建时不使用缓存
  --pull                  拉取最新基础镜像
  --health-check          部署后运行健康检查

示例:
  $0 dev                  部署开发环境
  $0 prod -b -f           备份并强制重新部署生产环境
  $0 test --scale 2       部署测试环境并启动2个应用实例

EOF
}

# 默认参数
ENVIRONMENT="dev"
VERBOSE=false
FORCE=false
CLEAN=false
BACKUP=false
SCALE=""
NO_CACHE=false
PULL=false
HEALTH_CHECK=false
DEBUG=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            DEBUG=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -b|--backup)
            BACKUP=true
            shift
            ;;
        -s|--scale)
            SCALE="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --pull)
            PULL=true
            shift
            ;;
        --health-check)
            HEALTH_CHECK=true
            shift
            ;;
        dev|test|staging|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境
validate_environment() {
    log_step "验证部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi
    
    # 检查环境变量文件
    local env_file=".env"
    if [ "$ENVIRONMENT" != "dev" ]; then
        env_file=".env.${ENVIRONMENT}"
    fi
    
    if [ ! -f "$env_file" ]; then
        log_error "环境变量文件 $env_file 不存在"
        log_info "请复制 .env.example 并配置相应的环境变量"
        exit 1
    fi
    
    log_info "环境验证通过"
}

# 备份数据
backup_data() {
    if [ "$BACKUP" = "true" ]; then
        log_step "备份数据..."
        
        local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        # 备份数据库
        if docker ps | grep -q "ares-postgres"; then
            log_info "备份PostgreSQL数据库..."
            docker exec ares-postgres pg_dump -U ares_user ares > "$backup_dir/database.sql"
        elif docker ps | grep -q "ares-app"; then
            log_info "备份SQLite数据库..."
            docker cp ares-app:/app/data/ares.db "$backup_dir/ares.db" 2>/dev/null || true
        fi
        
        # 备份配置文件
        log_info "备份配置文件..."
        cp -r config "$backup_dir/" 2>/dev/null || true
        
        # 备份日志
        log_info "备份日志文件..."
        docker cp ares-app:/app/logs "$backup_dir/" 2>/dev/null || true
        
        log_info "数据备份完成: $backup_dir"
    fi
}

# 清理现有容器
cleanup_containers() {
    if [ "$CLEAN" = "true" ]; then
        log_step "清理现有容器和卷..."
        
        # 停止并删除容器
        docker-compose -f docker-compose.yml -f docker-compose.${ENVIRONMENT}.yml down -v --remove-orphans 2>/dev/null || true
        
        # 删除未使用的镜像
        docker image prune -f
        
        # 删除未使用的卷
        docker volume prune -f
        
        log_info "清理完成"
    fi
}

# 构建镜像
build_images() {
    log_step "构建Docker镜像..."
    
    local build_args=""
    
    if [ "$NO_CACHE" = "true" ]; then
        build_args="$build_args --no-cache"
    fi
    
    if [ "$PULL" = "true" ]; then
        build_args="$build_args --pull"
    fi
    
    if [ "$FORCE" = "true" ]; then
        build_args="$build_args --force-rm"
    fi
    
    # 构建镜像
    docker-compose -f docker-compose.yml build $build_args
    
    log_info "镜像构建完成"
}

# 部署服务
deploy_services() {
    log_step "部署服务..."
    
    local compose_files="-f docker-compose.yml"
    
    # 根据环境选择compose文件
    if [ "$ENVIRONMENT" = "prod" ]; then
        compose_files="$compose_files -f docker-compose.prod.yml"
    fi
    
    # 启动服务
    local up_args="--detach"
    
    if [ "$SCALE" != "" ]; then
        up_args="$up_args --scale app=$SCALE"
    fi
    
    docker-compose $compose_files up $up_args
    
    log_info "服务部署完成"
}

# 等待服务启动
wait_for_services() {
    log_step "等待服务启动..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_debug "检查服务状态 (尝试 $attempt/$max_attempts)..."
        
        # 检查主应用健康状态
        if curl -f http://localhost:8000/health >/dev/null 2>&1; then
            log_info "服务启动成功"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log_error "服务启动超时"
    return 1
}

# 运行健康检查
run_health_check() {
    if [ "$HEALTH_CHECK" = "true" ]; then
        log_step "运行健康检查..."
        
        # 检查各个服务
        local services=("app" "redis" "scheduler" "tracker" "discoverer")
        
        for service in "${services[@]}"; do
            if docker ps | grep -q "ares-$service"; then
                log_info "✓ $service 服务运行正常"
            else
                log_warn "✗ $service 服务未运行"
            fi
        done
        
        # 检查API端点
        if curl -f http://localhost:8000/health >/dev/null 2>&1; then
            log_info "✓ API健康检查通过"
        else
            log_warn "✗ API健康检查失败"
        fi
        
        # 检查Streamlit
        if curl -f http://localhost:8501/_stcore/health >/dev/null 2>&1; then
            log_info "✓ Streamlit健康检查通过"
        else
            log_warn "✗ Streamlit健康检查失败"
        fi
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息"
    
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}  Ares投资系统部署完成${NC}"
    echo -e "${CYAN}================================${NC}"
    echo -e "环境: ${YELLOW}$ENVIRONMENT${NC}"
    echo -e "时间: ${YELLOW}$(date)${NC}"
    echo ""
    echo -e "${CYAN}访问地址:${NC}"
    echo -e "  Web界面: ${GREEN}http://localhost:8501${NC}"
    echo -e "  API文档: ${GREEN}http://localhost:8000/docs${NC}"
    echo -e "  健康检查: ${GREEN}http://localhost:8000/health${NC}"
    echo ""
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        echo -e "${CYAN}监控地址:${NC}"
        echo -e "  Grafana: ${GREEN}http://localhost:3000${NC}"
        echo -e "  Prometheus: ${GREEN}http://localhost:9090${NC}"
        echo -e "  Kibana: ${GREEN}http://localhost:5601${NC}"
        echo ""
    fi
    
    echo -e "${CYAN}常用命令:${NC}"
    echo -e "  查看日志: ${YELLOW}docker-compose logs -f${NC}"
    echo -e "  停止服务: ${YELLOW}docker-compose down${NC}"
    echo -e "  重启服务: ${YELLOW}docker-compose restart${NC}"
    echo -e "  查看状态: ${YELLOW}docker-compose ps${NC}"
    echo ""
}

# 主函数
main() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}  Ares投资系统部署脚本${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo ""
    
    validate_environment
    backup_data
    cleanup_containers
    build_images
    deploy_services
    wait_for_services
    run_health_check
    show_deployment_info
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"
