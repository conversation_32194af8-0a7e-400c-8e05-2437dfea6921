# Ares投资系统部署指南

本文档详细介绍了Ares投资系统的Docker容器化部署流程，包括开发、测试和生产环境的配置。

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [环境配置](#环境配置)
- [部署方式](#部署方式)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)
- [维护操作](#维护操作)

## 🔧 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) / macOS / Windows 10+

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 稳定的互联网连接

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.0+
- curl (用于健康检查)

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/ares-investment-system.git
cd ares-investment-system
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

**必需配置项**:
```bash
# API密钥
API_STEAMDT_API_KEY=your_steamdt_api_key_here

# 数据库密码 (生产环境)
POSTGRES_PASSWORD=your_secure_password_here

# Grafana密码 (生产环境)
GRAFANA_PASSWORD=your_grafana_password_here
```

### 3. 一键部署
```bash
# 开发环境
./scripts/deploy.sh dev

# 生产环境
./scripts/deploy.sh prod --backup --health-check
```

### 4. 访问系统
- **Web界面**: http://localhost:8501
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## ⚙️ 环境配置

### 开发环境 (dev)
- 使用SQLite数据库
- 单Redis实例
- 简化的日志配置
- 热重载支持

```bash
./scripts/deploy.sh dev
```

### 测试环境 (test)
- 使用PostgreSQL数据库
- Redis缓存
- 完整的监控配置
- 自动化测试集成

```bash
./scripts/deploy.sh test --scale 2
```

### 生产环境 (prod)
- PostgreSQL主从复制
- Redis集群
- 负载均衡
- 完整的监控和日志聚合
- SSL/TLS加密

```bash
./scripts/deploy.sh prod --backup --health-check
```

## 🐳 部署方式

### Docker Compose文件说明

| 文件 | 用途 | 环境 |
|------|------|------|
| `docker-compose.yml` | 基础配置 | 所有环境 |
| `docker-compose.prod.yml` | 生产环境扩展 | 生产环境 |

### 服务架构

```
┌─────────────────┐    ┌─────────────────┐
│   Nginx LB      │    │   Monitoring    │
│   (80/443)      │    │   (3000/9090)   │
└─────────┬───────┘    └─────────────────┘
          │
    ┌─────▼─────┐
    │   App1    │
    │  (8000)   │
    └─────┬─────┘
          │
    ┌─────▼─────┐         ┌─────────────┐
    │   App2    │◄────────┤  PostgreSQL │
    │  (8000)   │         │   (5432)    │
    └─────┬─────┘         └─────────────┘
          │
    ┌─────▼─────┐         ┌─────────────┐
    │  Redis    │         │  Services   │
    │  (6379)   │         │ (Scheduler, │
    └───────────┘         │ Tracker,    │
                          │ Discoverer) │
                          └─────────────┘
```

### 容器说明

| 容器名 | 服务 | 端口 | 说明 |
|--------|------|------|------|
| ares-nginx | 负载均衡器 | 80, 443 | HTTP/HTTPS入口 |
| ares-app1/2 | 主应用 | 8000, 8501 | FastAPI + Streamlit |
| ares-postgres | 数据库 | 5432 | PostgreSQL数据库 |
| ares-redis-master | 缓存 | 6379 | Redis主节点 |
| ares-scheduler | 调度器 | - | 任务调度服务 |
| ares-tracker | 追踪器 | - | 价格追踪服务 |
| ares-discoverer | 发现器 | - | 机会发现服务 |
| ares-prometheus | 监控 | 9090 | 指标收集 |
| ares-grafana | 仪表板 | 3000 | 监控可视化 |

## 📊 监控和日志

### 监控组件

1. **Prometheus** (http://localhost:9090)
   - 指标收集和存储
   - 告警规则配置
   - 数据查询接口

2. **Grafana** (http://localhost:3000)
   - 监控仪表板
   - 告警通知
   - 数据可视化

3. **ELK Stack** (生产环境)
   - Elasticsearch: 日志存储
   - Logstash: 日志处理
   - Kibana: 日志分析

### 关键指标

- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 请求数、响应时间、错误率
- **业务指标**: API调用次数、发现机会数、追踪项目数

### 日志管理

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f app

# 查看最近100行日志
docker-compose logs --tail=100 app
```

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查容器状态
docker-compose ps

# 查看错误日志
docker-compose logs app

# 重启服务
docker-compose restart app
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U ares_user

# 查看数据库日志
docker-compose logs postgres

# 重置数据库
docker-compose down -v
docker-compose up -d postgres
```

#### 3. Redis连接失败
```bash
# 检查Redis状态
docker-compose exec redis redis-cli ping

# 查看Redis日志
docker-compose logs redis

# 清理Redis缓存
docker-compose exec redis redis-cli FLUSHALL
```

#### 4. API调用失败
```bash
# 检查API健康状态
curl http://localhost:8000/health

# 查看API日志
docker-compose logs app | grep "ERROR"

# 重启API服务
docker-compose restart app
```

### 性能优化

#### 1. 内存优化
```bash
# 查看内存使用
docker stats

# 调整容器内存限制
# 在docker-compose.yml中添加:
deploy:
  resources:
    limits:
      memory: 1G
```

#### 2. 数据库优化
```bash
# 查看数据库性能
docker-compose exec postgres psql -U ares_user -d ares -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC LIMIT 10;"
```

#### 3. 缓存优化
```bash
# 查看Redis内存使用
docker-compose exec redis redis-cli INFO memory

# 设置缓存过期策略
docker-compose exec redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

## 🛠️ 维护操作

### 备份和恢复

#### 数据备份
```bash
# 自动备份 (部署时)
./scripts/deploy.sh prod --backup

# 手动备份数据库
docker-compose exec postgres pg_dump -U ares_user ares > backup_$(date +%Y%m%d).sql

# 备份Redis数据
docker-compose exec redis redis-cli BGSAVE
```

#### 数据恢复
```bash
# 恢复数据库
docker-compose exec -T postgres psql -U ares_user ares < backup_20240117.sql

# 恢复Redis数据
docker cp backup.rdb ares-redis:/data/dump.rdb
docker-compose restart redis
```

### 更新部署

#### 滚动更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
./scripts/deploy.sh prod --force --health-check

# 零停机更新 (生产环境)
docker-compose up -d --no-deps app1
# 等待健康检查通过
docker-compose up -d --no-deps app2
```

#### 回滚操作
```bash
# 回滚到上一个版本
git checkout HEAD~1
./scripts/deploy.sh prod --force

# 或使用备份恢复
./scripts/restore.sh backup_20240117
```

### 清理操作

```bash
# 清理未使用的镜像
docker image prune -f

# 清理未使用的卷
docker volume prune -f

# 完全清理 (谨慎使用)
./scripts/deploy.sh prod --clean
```

### 扩容操作

```bash
# 水平扩容应用实例
./scripts/deploy.sh prod --scale 4

# 垂直扩容 (修改docker-compose.yml中的资源限制)
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
```

## 📞 支持和联系

如果遇到部署问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持团队

---

**注意**: 生产环境部署前请务必：
- 修改所有默认密码
- 配置SSL证书
- 设置防火墙规则
- 配置监控告警
- 制定备份策略
