"""
Ares市场监控页面
显示实时市场数据、价格趋势和交易分析
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import random

from app.utils.state_manager import StateManager


def show_market_page():
    """显示市场监控页面"""
    st.markdown('<h1 class="section-title">📈 市场监控</h1>', unsafe_allow_html=True)
    
    # 获取状态管理器
    state_manager = StateManager()
    
    # 页面选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["🌐 市场概览", "📊 价格趋势", "🔥 热门商品", "📈 交易分析"])
    
    with tab1:
        show_market_overview(state_manager)
    
    with tab2:
        show_price_trends(state_manager)
    
    with tab3:
        show_hot_items(state_manager)
    
    with tab4:
        show_trading_analysis(state_manager)


def show_market_overview(state_manager: StateManager):
    """显示市场概览"""
    st.markdown("### 🌐 市场概览")
    
    market_data = state_manager.get_market_data()
    
    # 市场关键指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📦 总商品数",
            value=f"{market_data['total_items']:,}",
            delta="+25"
        )
    
    with col2:
        st.metric(
            label="🏪 活跃挂单",
            value=f"{market_data['active_listings']:,}",
            delta=f"{random.randint(-50, 100):+d}"
        )
    
    with col3:
        st.metric(
            label="📈 平均涨幅",
            value=f"{market_data['avg_price_change']:+.1f}%",
            delta=f"{random.uniform(-1, 1):+.1f}%"
        )
    
    with col4:
        st.metric(
            label="💰 24h交易量",
            value=f"{market_data['volume_24h']:,}",
            delta=f"+{random.randint(50, 200)}"
        )
    
    st.divider()
    
    # 涨跌榜
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 🚀 今日涨幅榜")
        gainers_df = pd.DataFrame(market_data['top_gainers'])
        
        for _, item in gainers_df.iterrows():
            st.markdown(f"""
            <div style="
                padding: 0.5rem;
                margin-bottom: 0.25rem;
                background-color: rgba(44, 160, 44, 0.1);
                border-left: 3px solid #2ca02c;
                border-radius: 0 4px 4px 0;
            ">
                <div style="display: flex; justify-content: space-between;">
                    <span>{item['name']}</span>
                    <span style="color: #2ca02c; font-weight: bold;">+{item['change']:.1f}%</span>
                </div>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("#### 📉 今日跌幅榜")
        losers_df = pd.DataFrame(market_data['top_losers'])
        
        for _, item in losers_df.iterrows():
            st.markdown(f"""
            <div style="
                padding: 0.5rem;
                margin-bottom: 0.25rem;
                background-color: rgba(214, 39, 40, 0.1);
                border-left: 3px solid #d62728;
                border-radius: 0 4px 4px 0;
            ">
                <div style="display: flex; justify-content: space-between;">
                    <span>{item['name']}</span>
                    <span style="color: #d62728; font-weight: bold;">{item['change']:.1f}%</span>
                </div>
            </div>
            """, unsafe_allow_html=True)


def show_price_trends(state_manager: StateManager):
    """显示价格趋势"""
    st.markdown("### 📊 价格趋势分析")
    
    # 时间范围和商品选择
    col1, col2 = st.columns([1, 1])
    
    with col1:
        time_range = st.selectbox(
            "时间范围",
            ["24小时", "7天", "30天", "90天"]
        )
    
    with col2:
        selected_item = st.selectbox(
            "选择商品",
            ["AK-47 | Redline", "AWP | Dragon Lore", "Karambit | Fade", "M4A4 | Howl"]
        )
    
    # 生成模拟价格数据
    days_map = {"24小时": 1, "7天": 7, "30天": 30, "90天": 90}
    days = days_map.get(time_range, 7)
    
    if time_range == "24小时":
        dates = pd.date_range(start=datetime.now() - timedelta(hours=24), end=datetime.now(), freq='H')
    else:
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), end=datetime.now(), freq='D')
    
    # 模拟价格数据
    base_price = random.uniform(50, 500)
    prices = []
    volumes = []
    
    for i in range(len(dates)):
        price_change = random.uniform(-5, 5)
        base_price *= (1 + price_change / 100)
        prices.append(max(base_price, 1))  # 确保价格不为负
        volumes.append(random.randint(10, 100))
    
    df = pd.DataFrame({
        'datetime': dates,
        'price': prices,
        'volume': volumes
    })
    
    # 价格趋势图
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=df['datetime'],
        y=df['price'],
        mode='lines',
        name='价格',
        line=dict(color='#1f77b4', width=2),
        yaxis='y'
    ))
    
    fig.add_trace(go.Bar(
        x=df['datetime'],
        y=df['volume'],
        name='交易量',
        marker_color='rgba(255, 127, 14, 0.6)',
        yaxis='y2'
    ))
    
    fig.update_layout(
        title=f'{selected_item} - {time_range}价格趋势',
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        xaxis_title="时间",
        yaxis=dict(
            title="价格 ($)",
            side="left"
        ),
        yaxis2=dict(
            title="交易量",
            side="right",
            overlaying="y"
        ),
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        )
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 价格统计
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("当前价格", f"${prices[-1]:.2f}")
    
    with col2:
        price_change = (prices[-1] - prices[0]) / prices[0] * 100
        st.metric("期间涨跌", f"{price_change:+.2f}%")
    
    with col3:
        st.metric("最高价", f"${max(prices):.2f}")
    
    with col4:
        st.metric("最低价", f"${min(prices):.2f}")


def show_hot_items(state_manager: StateManager):
    """显示热门商品"""
    st.markdown("### 🔥 热门商品")
    
    # 生成模拟热门商品数据
    hot_items = [
        {
            'name': 'AK-47 | Redline (Field-Tested)',
            'current_price': 52.30,
            'price_change': 15.2,
            'volume_24h': 156,
            'volume_change': 45.8,
            'category': '步枪'
        },
        {
            'name': 'AWP | Dragon Lore (Factory New)',
            'current_price': 3150.00,
            'price_change': 8.7,
            'volume_24h': 23,
            'volume_change': 78.3,
            'category': '狙击枪'
        },
        {
            'name': 'Karambit | Fade (Factory New)',
            'current_price': 1350.00,
            'price_change': 12.4,
            'volume_24h': 45,
            'volume_change': 92.1,
            'category': '刀具'
        },
        {
            'name': 'M4A4 | Howl (Factory New)',
            'current_price': 3200.00,
            'price_change': -5.2,
            'volume_24h': 18,
            'volume_change': 156.7,
            'category': '步枪'
        },
        {
            'name': 'Glock-18 | Fade (Factory New)',
            'current_price': 285.50,
            'price_change': 22.1,
            'volume_24h': 67,
            'volume_change': 234.5,
            'category': '手枪'
        }
    ]
    
    # 筛选选项
    col1, col2 = st.columns(2)
    
    with col1:
        category_filter = st.selectbox(
            "商品类别",
            ["全部", "步枪", "狙击枪", "刀具", "手枪"]
        )
    
    with col2:
        sort_by = st.selectbox(
            "排序方式",
            ["价格涨幅", "交易量变化", "当前价格"]
        )
    
    # 过滤和排序
    filtered_items = hot_items
    if category_filter != "全部":
        filtered_items = [item for item in hot_items if item['category'] == category_filter]
    
    if sort_by == "价格涨幅":
        filtered_items.sort(key=lambda x: x['price_change'], reverse=True)
    elif sort_by == "交易量变化":
        filtered_items.sort(key=lambda x: x['volume_change'], reverse=True)
    elif sort_by == "当前价格":
        filtered_items.sort(key=lambda x: x['current_price'], reverse=True)
    
    # 显示热门商品列表
    for item in filtered_items:
        price_color = "#2ca02c" if item['price_change'] > 0 else "#d62728"
        
        st.markdown(f"""
        <div style="
            padding: 1rem;
            margin-bottom: 0.5rem;
            background-color: rgba(38, 39, 48, 0.8);
            border-radius: 8px;
            border: 1px solid #404040;
        ">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h4 style="margin: 0; color: #fafafa;">{item['name']}</h4>
                    <p style="margin: 0.25rem 0; color: #a6a6a6;">类别: {item['category']}</p>
                </div>
                <div style="text-align: right;">
                    <div style="font-size: 1.2em; font-weight: bold; color: #fafafa;">
                        ${item['current_price']:,.2f}
                    </div>
                    <div style="color: {price_color}; font-weight: bold;">
                        {item['price_change']:+.1f}%
                    </div>
                </div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                <span style="color: #a6a6a6;">24h交易量: {item['volume_24h']}</span>
                <span style="color: #a6a6a6;">交易量变化: +{item['volume_change']:.1f}%</span>
            </div>
        </div>
        """, unsafe_allow_html=True)


def show_trading_analysis(state_manager: StateManager):
    """显示交易分析"""
    st.markdown("### 📈 交易分析")
    
    # 交易热力图
    st.markdown("#### 🔥 交易热力图")
    
    # 生成模拟热力图数据
    categories = ['步枪', '狙击枪', '刀具', '手枪', '其他']
    conditions = ['崭新出厂', '略有磨损', '久经沙场', '破损不堪', '战痕累累']
    
    heatmap_data = []
    for cat in categories:
        row = []
        for cond in conditions:
            row.append(random.randint(10, 100))
        heatmap_data.append(row)
    
    fig = px.imshow(
        heatmap_data,
        x=conditions,
        y=categories,
        color_continuous_scale='Viridis',
        title="各类别商品交易活跃度"
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 交易统计
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📊 交易统计")
        
        # 模拟交易统计数据
        trading_stats = {
            '今日交易笔数': 1234,
            '今日交易金额': 156789.50,
            '平均交易金额': 127.25,
            '最大单笔交易': 5000.00,
            '活跃交易者': 456
        }
        
        for key, value in trading_stats.items():
            if '金额' in key:
                st.metric(key, f"${value:,.2f}")
            else:
                st.metric(key, f"{value:,}")
    
    with col2:
        st.markdown("#### 📈 交易趋势")
        
        # 生成交易趋势数据
        hours = list(range(24))
        trading_volume = [random.randint(20, 100) for _ in hours]
        
        fig = px.bar(
            x=hours,
            y=trading_volume,
            title="24小时交易量分布",
            labels={'x': '小时', 'y': '交易量'}
        )
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white'
        )
        
        st.plotly_chart(fig, use_container_width=True)


if __name__ == "__main__":
    show_market_page()
