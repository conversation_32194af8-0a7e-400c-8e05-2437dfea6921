"""
Ares持仓管理服务
处理持仓的增删改查操作，包括成本记录、盈亏计算、批量操作和数据导入导出
"""

import logging
import json
import csv
import io
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
from decimal import Decimal, ROUND_HALF_UP
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd

from core.database import get_database_manager
from core.api_manager import get_api_manager, get_item_price
from core.config import get_config_manager
from core.exceptions import AresException, ErrorSeverity, ErrorContext
from monitoring.system_monitor import get_system_monitor

logger = logging.getLogger(__name__)


@dataclass
class Holding:
    """持仓记录"""
    id: Optional[str] = None
    item_id: str = ""
    item_name: str = ""
    quantity: int = 0
    avg_cost: float = 0.0
    current_price: float = 0.0
    total_cost: float = 0.0
    total_value: float = 0.0
    profit_loss: float = 0.0
    profit_percentage: float = 0.0
    purchase_date: Optional[date] = None
    platform: str = ""
    condition: str = ""
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """计算衍生字段"""
        self.total_cost = self.avg_cost * self.quantity
        self.total_value = self.current_price * self.quantity
        self.profit_loss = self.total_value - self.total_cost
        self.profit_percentage = (self.profit_loss / self.total_cost * 100) if self.total_cost > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理日期字段
        if self.purchase_date:
            data['purchase_date'] = self.purchase_date.isoformat()
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.updated_at:
            data['updated_at'] = self.updated_at.isoformat()
        return data


@dataclass
class HoldingsSummary:
    """持仓汇总信息"""
    total_holdings: int = 0
    total_cost: float = 0.0
    total_value: float = 0.0
    total_profit_loss: float = 0.0
    avg_profit_percentage: float = 0.0
    profitable_holdings: int = 0
    losing_holdings: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class HoldingsManager:
    """持仓管理器"""
    
    def __init__(self):
        """初始化持仓管理器"""
        self.config = get_config_manager()
        self.db_manager = get_database_manager()
        self.api_manager = None
        self.system_monitor = get_system_monitor()
        
        # 配置参数
        self.price_precision = self.config.get('holdings.price_precision', 2)
        self.auto_update_prices = self.config.get('holdings.auto_update_prices', True)
        self.batch_size = self.config.get('holdings.batch_size', 50)
        
        logger.info("Holdings manager initialized")
    
    async def initialize(self):
        """初始化API管理器"""
        try:
            self.api_manager = await get_api_manager()
            logger.info("Holdings manager API initialized")
        except Exception as e:
            logger.error("Failed to initialize holdings manager API: %s", str(e))
            raise AresException(
                message=f"Holdings manager initialization failed: {str(e)}",
                context=ErrorContext(operation="holdings_init"),
                severity=ErrorSeverity.HIGH
            )
    
    async def add_holding(self, 
                         item_id: str,
                         item_name: str,
                         quantity: int,
                         cost_basis: float,
                         purchase_date: Optional[date] = None,
                         platform: str = "",
                         condition: str = "",
                         notes: str = "") -> Holding:
        """
        添加持仓记录
        
        Args:
            item_id: 饰品ID
            item_name: 饰品名称
            quantity: 数量
            cost_basis: 成本基础（单价）
            purchase_date: 购买日期
            platform: 购买平台
            condition: 磨损程度
            notes: 备注
            
        Returns:
            Holding: 创建的持仓记录
        """
        try:
            # 验证输入
            if not item_id or not item_name:
                raise ValueError("Item ID and name are required")
            if quantity <= 0:
                raise ValueError("Quantity must be positive")
            if cost_basis <= 0:
                raise ValueError("Cost basis must be positive")
            
            # 获取当前价格
            current_price = await self._get_current_price(item_id)
            
            # 检查是否已存在相同的持仓
            existing_holding = await self._get_holding_by_item(item_id)
            
            if existing_holding:
                # 更新现有持仓（加权平均成本）
                return await self._update_existing_holding(existing_holding, quantity, cost_basis)
            else:
                # 创建新持仓
                holding = Holding(
                    item_id=item_id,
                    item_name=item_name,
                    quantity=quantity,
                    avg_cost=cost_basis,
                    current_price=current_price,
                    purchase_date=purchase_date or date.today(),
                    platform=platform,
                    condition=condition,
                    notes=notes,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                # 保存到数据库
                holding_id = await self._save_holding_to_db(holding)
                holding.id = holding_id
                
                logger.info("Added new holding: %s x%d", item_name, quantity)
                self.system_monitor.record_api_request("add_holding", True, 0)
                
                return holding
                
        except Exception as e:
            logger.error("Error adding holding %s: %s", item_name, str(e))
            self.system_monitor.record_api_request("add_holding", False, 0)
            raise AresException(
                message=f"Failed to add holding: {str(e)}",
                context=ErrorContext(operation="add_holding", item_id=item_id),
                severity=ErrorSeverity.MEDIUM
            )
    
    async def update_holding(self, holding_id: str, **kwargs) -> Holding:
        """
        更新持仓记录
        
        Args:
            holding_id: 持仓ID
            **kwargs: 要更新的字段
            
        Returns:
            Holding: 更新后的持仓记录
        """
        try:
            # 获取现有持仓
            holding = await self._get_holding_by_id(holding_id)
            if not holding:
                raise ValueError(f"Holding {holding_id} not found")
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(holding, key):
                    setattr(holding, key, value)
            
            # 重新计算价格相关字段
            if 'quantity' in kwargs or 'avg_cost' in kwargs:
                holding.__post_init__()
            
            # 更新时间戳
            holding.updated_at = datetime.utcnow()
            
            # 保存到数据库
            await self._update_holding_in_db(holding)
            
            logger.info("Updated holding: %s", holding.item_name)
            return holding
            
        except Exception as e:
            logger.error("Error updating holding %s: %s", holding_id, str(e))
            raise AresException(
                message=f"Failed to update holding: {str(e)}",
                context=ErrorContext(operation="update_holding", item_id=holding_id),
                severity=ErrorSeverity.MEDIUM
            )
    
    async def delete_holding(self, holding_id: str) -> bool:
        """
        删除持仓记录
        
        Args:
            holding_id: 持仓ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 检查持仓是否存在
            holding = await self._get_holding_by_id(holding_id)
            if not holding:
                raise ValueError(f"Holding {holding_id} not found")
            
            # 从数据库删除
            success = await self._delete_holding_from_db(holding_id)
            
            if success:
                logger.info("Deleted holding: %s", holding.item_name)
            
            return success
            
        except Exception as e:
            logger.error("Error deleting holding %s: %s", holding_id, str(e))
            raise AresException(
                message=f"Failed to delete holding: {str(e)}",
                context=ErrorContext(operation="delete_holding", item_id=holding_id),
                severity=ErrorSeverity.MEDIUM
            )
    
    async def get_all_holdings(self) -> List[Holding]:
        """获取所有持仓记录"""
        try:
            holdings = await self._get_all_holdings_from_db()
            
            # 如果启用自动更新价格，更新当前价格
            if self.auto_update_prices:
                await self._update_current_prices(holdings)
            
            return holdings
            
        except Exception as e:
            logger.error("Error getting all holdings: %s", str(e))
            raise AresException(
                message=f"Failed to get holdings: {str(e)}",
                context=ErrorContext(operation="get_all_holdings"),
                severity=ErrorSeverity.LOW
            )
    
    async def get_holdings_summary(self) -> HoldingsSummary:
        """获取持仓汇总信息"""
        try:
            holdings = await self.get_all_holdings()
            
            if not holdings:
                return HoldingsSummary()
            
            total_cost = sum(h.total_cost for h in holdings)
            total_value = sum(h.total_value for h in holdings)
            total_profit_loss = total_value - total_cost
            avg_profit_percentage = sum(h.profit_percentage for h in holdings) / len(holdings)
            profitable_holdings = len([h for h in holdings if h.profit_loss > 0])
            losing_holdings = len([h for h in holdings if h.profit_loss < 0])
            
            return HoldingsSummary(
                total_holdings=len(holdings),
                total_cost=total_cost,
                total_value=total_value,
                total_profit_loss=total_profit_loss,
                avg_profit_percentage=avg_profit_percentage,
                profitable_holdings=profitable_holdings,
                losing_holdings=losing_holdings
            )
            
        except Exception as e:
            logger.error("Error getting holdings summary: %s", str(e))
            raise AresException(
                message=f"Failed to get holdings summary: {str(e)}",
                context=ErrorContext(operation="get_holdings_summary"),
                severity=ErrorSeverity.LOW
            )
    
    async def calculate_pnl(self, holding: Holding) -> Tuple[float, float]:
        """
        计算持仓盈亏
        
        Args:
            holding: 持仓记录
            
        Returns:
            Tuple[float, float]: (盈亏金额, 盈亏百分比)
        """
        try:
            # 获取最新价格
            current_price = await self._get_current_price(holding.item_id)
            
            # 计算盈亏
            total_cost = holding.avg_cost * holding.quantity
            total_value = current_price * holding.quantity
            profit_loss = total_value - total_cost
            profit_percentage = (profit_loss / total_cost * 100) if total_cost > 0 else 0.0
            
            return profit_loss, profit_percentage
            
        except Exception as e:
            logger.error("Error calculating PnL for %s: %s", holding.item_name, str(e))
            return 0.0, 0.0
    
    async def batch_update_prices(self) -> int:
        """
        批量更新所有持仓的当前价格
        
        Returns:
            int: 更新成功的数量
        """
        try:
            holdings = await self._get_all_holdings_from_db()
            updated_count = 0
            
            for holding in holdings:
                try:
                    current_price = await self._get_current_price(holding.item_id)
                    if current_price != holding.current_price:
                        holding.current_price = current_price
                        holding.__post_init__()  # 重新计算衍生字段
                        holding.updated_at = datetime.utcnow()
                        
                        await self._update_holding_in_db(holding)
                        updated_count += 1
                        
                except Exception as e:
                    logger.warning("Failed to update price for %s: %s", holding.item_name, str(e))
                    continue
            
            logger.info("Batch updated prices for %d holdings", updated_count)
            return updated_count
            
        except Exception as e:
            logger.error("Error in batch price update: %s", str(e))
            raise AresException(
                message=f"Failed to batch update prices: {str(e)}",
                context=ErrorContext(operation="batch_update_prices"),
                severity=ErrorSeverity.MEDIUM
            )
    
    async def _get_current_price(self, item_id: str) -> float:
        """获取饰品当前价格"""
        try:
            if not self.api_manager:
                await self.initialize()
            
            response = await get_item_price(self.api_manager, item_id)
            
            if response.status.value == "success" and response.data:
                price = response.data.get('price', 0.0)
                return round(price, self.price_precision)
            else:
                logger.warning("Failed to get price for %s: %s", item_id, response.error_message)
                return 0.0
                
        except Exception as e:
            logger.error("Error getting current price for %s: %s", item_id, str(e))
            return 0.0
    
    async def _update_current_prices(self, holdings: List[Holding]):
        """更新持仓列表的当前价格"""
        for holding in holdings:
            try:
                current_price = await self._get_current_price(holding.item_id)
                if current_price > 0:
                    holding.current_price = current_price
                    holding.__post_init__()  # 重新计算衍生字段
            except Exception as e:
                logger.warning("Failed to update price for %s: %s", holding.item_name, str(e))
                continue
    
    async def _update_existing_holding(self, existing: Holding, new_quantity: int, new_cost: float) -> Holding:
        """更新现有持仓（加权平均成本）"""
        # 计算加权平均成本
        total_quantity = existing.quantity + new_quantity
        total_cost = (existing.avg_cost * existing.quantity) + (new_cost * new_quantity)
        new_avg_cost = total_cost / total_quantity
        
        # 更新持仓
        existing.quantity = total_quantity
        existing.avg_cost = round(new_avg_cost, self.price_precision)
        existing.updated_at = datetime.utcnow()
        existing.__post_init__()  # 重新计算衍生字段
        
        # 保存到数据库
        await self._update_holding_in_db(existing)
        
        logger.info("Updated existing holding: %s, new quantity: %d, new avg cost: %.2f", 
                   existing.item_name, total_quantity, new_avg_cost)
        
        return existing
    
    # 数据库操作方法（这些方法需要根据实际数据库实现）
    async def _get_holding_by_item(self, item_id: str) -> Optional[Holding]:
        """根据饰品ID获取持仓"""
        # 这里应该实现实际的数据库查询
        # 暂时返回None表示不存在
        return None
    
    async def _get_holding_by_id(self, holding_id: str) -> Optional[Holding]:
        """根据持仓ID获取持仓"""
        # 这里应该实现实际的数据库查询
        return None
    
    async def _get_all_holdings_from_db(self) -> List[Holding]:
        """从数据库获取所有持仓"""
        # 这里应该实现实际的数据库查询
        # 暂时返回空列表
        return []
    
    async def _save_holding_to_db(self, holding: Holding) -> str:
        """保存持仓到数据库"""
        # 这里应该实现实际的数据库保存
        # 暂时返回模拟ID
        import uuid
        return str(uuid.uuid4())
    
    async def _update_holding_in_db(self, holding: Holding):
        """更新数据库中的持仓"""
        # 这里应该实现实际的数据库更新
        pass
    
    async def _delete_holding_from_db(self, holding_id: str) -> bool:
        """从数据库删除持仓"""
        # 这里应该实现实际的数据库删除
        return True


# 全局持仓管理器实例
_holdings_manager: Optional[HoldingsManager] = None


def get_holdings_manager() -> HoldingsManager:
    """获取全局持仓管理器实例"""
    global _holdings_manager
    if _holdings_manager is None:
        _holdings_manager = HoldingsManager()
    return _holdings_manager
