"""
Ares系统状态管理器
管理应用程序状态、会话数据和缓存
"""

import streamlit as st
import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum


class SessionState(Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    EXPIRED = "expired"
    INVALID = "invalid"


@dataclass
class UserSession:
    """用户会话数据"""
    session_id: str
    user_id: Optional[str] = None
    username: Optional[str] = None
    login_time: Optional[datetime] = None
    last_activity: Optional[datetime] = None
    preferences: Dict[str, Any] = None
    state: SessionState = SessionState.ACTIVE
    
    def __post_init__(self):
        if self.preferences is None:
            self.preferences = {}
        if self.login_time is None:
            self.login_time = datetime.now()
        if self.last_activity is None:
            self.last_activity = datetime.now()


class StateManager:
    """状态管理器"""
    
    def __init__(self):
        """初始化状态管理器"""
        self.session_timeout = 3600  # 1小时会话超时
        
        # 初始化会话状态
        self._init_session_state()
        
        # 初始化应用状态
        self._init_app_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'session_id' not in st.session_state:
            st.session_state.session_id = f"session_{int(time.time() * 1000)}"
        
        if 'user_session' not in st.session_state:
            st.session_state.user_session = UserSession(
                session_id=st.session_state.session_id
            )
        
        # 更新最后活动时间
        st.session_state.user_session.last_activity = datetime.now()
    
    def _init_app_state(self):
        """初始化应用状态"""
        # 导航状态
        if 'current_page' not in st.session_state:
            st.session_state.current_page = 'dashboard'
        
        # 数据缓存
        if 'data_cache' not in st.session_state:
            st.session_state.data_cache = {}
        
        # 缓存时间戳
        if 'cache_timestamps' not in st.session_state:
            st.session_state.cache_timestamps = {}
        
        # 用户偏好设置
        if 'user_preferences' not in st.session_state:
            st.session_state.user_preferences = {
                'theme': 'dark',
                'language': 'zh-CN',
                'timezone': 'Asia/Shanghai',
                'auto_refresh': True,
                'refresh_interval': 30,
                'notifications_enabled': True,
                'sound_enabled': False
            }
        
        # 应用配置
        if 'app_config' not in st.session_state:
            st.session_state.app_config = {
                'debug_mode': False,
                'api_timeout': 30,
                'max_cache_size': 100,
                'auto_save': True
            }
        
        # 临时数据
        if 'temp_data' not in st.session_state:
            st.session_state.temp_data = {}
    
    def get_session(self) -> UserSession:
        """获取当前会话"""
        return st.session_state.user_session
    
    def is_session_valid(self) -> bool:
        """检查会话是否有效"""
        session = self.get_session()
        
        if session.state != SessionState.ACTIVE:
            return False
        
        # 检查会话超时
        if session.last_activity:
            time_diff = datetime.now() - session.last_activity
            if time_diff.total_seconds() > self.session_timeout:
                session.state = SessionState.EXPIRED
                return False
        
        return True
    
    def update_session_activity(self):
        """更新会话活动时间"""
        if self.is_session_valid():
            st.session_state.user_session.last_activity = datetime.now()
    
    def set_user_info(self, user_id: str, username: str):
        """设置用户信息"""
        session = st.session_state.user_session
        session.user_id = user_id
        session.username = username
        session.login_time = datetime.now()
        session.last_activity = datetime.now()
        session.state = SessionState.ACTIVE
    
    def logout(self):
        """用户登出"""
        st.session_state.user_session.state = SessionState.INVALID
        st.session_state.user_session.user_id = None
        st.session_state.user_session.username = None
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """获取用户偏好设置"""
        return st.session_state.user_preferences.get(key, default)
    
    def set_preference(self, key: str, value: Any):
        """设置用户偏好"""
        st.session_state.user_preferences[key] = value
    
    def get_app_config(self, key: str, default: Any = None) -> Any:
        """获取应用配置"""
        return st.session_state.app_config.get(key, default)
    
    def set_app_config(self, key: str, value: Any):
        """设置应用配置"""
        st.session_state.app_config[key] = value
    
    def cache_data(self, key: str, data: Any, ttl: int = 300):
        """缓存数据"""
        st.session_state.data_cache[key] = data
        st.session_state.cache_timestamps[key] = time.time() + ttl
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if key not in st.session_state.data_cache:
            return None
        
        # 检查是否过期
        if key in st.session_state.cache_timestamps:
            if time.time() > st.session_state.cache_timestamps[key]:
                # 数据已过期，删除
                del st.session_state.data_cache[key]
                del st.session_state.cache_timestamps[key]
                return None
        
        return st.session_state.data_cache[key]
    
    def clear_cache(self, pattern: str = None):
        """清除缓存"""
        if pattern is None:
            st.session_state.data_cache.clear()
            st.session_state.cache_timestamps.clear()
        else:
            # 清除匹配模式的缓存
            keys_to_remove = [k for k in st.session_state.data_cache.keys() if pattern in k]
            for key in keys_to_remove:
                st.session_state.data_cache.pop(key, None)
                st.session_state.cache_timestamps.pop(key, None)
    
    def set_temp_data(self, key: str, value: Any):
        """设置临时数据"""
        st.session_state.temp_data[key] = value
    
    def get_temp_data(self, key: str, default: Any = None) -> Any:
        """获取临时数据"""
        return st.session_state.temp_data.get(key, default)
    
    def clear_temp_data(self, key: str = None):
        """清除临时数据"""
        if key is None:
            st.session_state.temp_data.clear()
        else:
            st.session_state.temp_data.pop(key, None)
    
    def get_quick_stats(self) -> Dict[str, Any]:
        """获取快速统计数据"""
        # 检查缓存
        cached_stats = self.get_cached_data('quick_stats')
        if cached_stats:
            return cached_stats
        
        # 生成模拟数据
        stats = {
            'total_value': 15750.50,
            'value_change': 2.35,
            'holdings_count': 23,
            'holdings_change': 1,
            'daily_pnl': 125.75,
            'pnl_change': 1.85
        }
        
        # 缓存5分钟
        self.cache_data('quick_stats', stats, 300)
        return stats
    
    def get_system_status(self) -> Dict[str, bool]:
        """获取系统状态"""
        # 检查缓存
        cached_status = self.get_cached_data('system_status')
        if cached_status:
            return cached_status
        
        # 生成模拟状态
        status = {
            '数据库': True,
            '缓存服务': True,
            'API服务': True,
            '通知服务': True,
            '预警系统': True
        }
        
        # 缓存1分钟
        self.cache_data('system_status', status, 60)
        return status
    
    def get_market_data(self) -> Dict[str, Any]:
        """获取市场数据"""
        cached_data = self.get_cached_data('market_data')
        if cached_data:
            return cached_data
        
        # 生成模拟市场数据
        import random
        
        market_data = {
            'total_items': 1250,
            'active_listings': 890,
            'avg_price_change': random.uniform(-5, 5),
            'volume_24h': random.randint(500, 2000),
            'top_gainers': [
                {'name': 'AK-47 | Redline', 'change': 15.2},
                {'name': 'AWP | Dragon Lore', 'change': 12.8},
                {'name': 'Karambit | Fade', 'change': 8.5}
            ],
            'top_losers': [
                {'name': 'M4A4 | Howl', 'change': -8.3},
                {'name': 'Butterfly Knife', 'change': -6.1},
                {'name': 'Glock-18 | Fade', 'change': -4.7}
            ]
        }
        
        # 缓存2分钟
        self.cache_data('market_data', market_data, 120)
        return market_data
    
    def get_portfolio_data(self) -> Dict[str, Any]:
        """获取投资组合数据"""
        cached_data = self.get_cached_data('portfolio_data')
        if cached_data:
            return cached_data
        
        # 生成模拟投资组合数据
        import random
        
        portfolio_data = {
            'total_value': 15750.50,
            'total_cost': 14200.00,
            'total_profit': 1550.50,
            'profit_percentage': 10.92,
            'holdings': [
                {
                    'name': 'AK-47 | Redline (Field-Tested)',
                    'quantity': 3,
                    'avg_cost': 45.50,
                    'current_price': 52.30,
                    'total_value': 156.90,
                    'profit_loss': 20.40,
                    'profit_percentage': 14.95
                },
                {
                    'name': 'AWP | Dragon Lore (Factory New)',
                    'quantity': 1,
                    'avg_cost': 2800.00,
                    'current_price': 3150.00,
                    'total_value': 3150.00,
                    'profit_loss': 350.00,
                    'profit_percentage': 12.50
                },
                {
                    'name': 'Karambit | Fade (Factory New)',
                    'quantity': 2,
                    'avg_cost': 1200.00,
                    'current_price': 1350.00,
                    'total_value': 2700.00,
                    'profit_loss': 300.00,
                    'profit_percentage': 12.50
                }
            ]
        }
        
        # 缓存5分钟
        self.cache_data('portfolio_data', portfolio_data, 300)
        return portfolio_data
    
    def export_session_data(self) -> Dict[str, Any]:
        """导出会话数据"""
        return {
            'session_id': st.session_state.session_id,
            'user_session': asdict(st.session_state.user_session),
            'user_preferences': st.session_state.user_preferences,
            'app_config': st.session_state.app_config,
            'current_page': st.session_state.current_page
        }
    
    def import_session_data(self, data: Dict[str, Any]):
        """导入会话数据"""
        try:
            if 'user_preferences' in data:
                st.session_state.user_preferences.update(data['user_preferences'])
            
            if 'app_config' in data:
                st.session_state.app_config.update(data['app_config'])
            
            if 'current_page' in data:
                st.session_state.current_page = data['current_page']
            
            return True
        except Exception as e:
            st.error(f"导入会话数据失败: {str(e)}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_items = len(st.session_state.data_cache)
        expired_items = 0
        
        current_time = time.time()
        for key, expire_time in st.session_state.cache_timestamps.items():
            if current_time > expire_time:
                expired_items += 1
        
        return {
            'total_items': total_items,
            'expired_items': expired_items,
            'active_items': total_items - expired_items,
            'cache_hit_rate': 0.85  # 模拟命中率
        }
