# Ares系统环境变量配置示例
# 复制此文件为.env并填入实际值
# 版本: 3.0.0

# ================================
# 必需配置项 (Required)
# ================================

# API配置 - SteamDT官方API密钥
STEAMDT_API_KEY=your_steamdt_api_key_here

# 加密密钥 - 用于敏感数据加密存储
# 生成方法: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
ENCRYPTION_KEY=your_32_byte_base64_encoded_fernet_key_here

# 会话密钥 - 用于用户会话管理
# 生成方法: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your_secret_key_for_sessions_here

# ================================
# 数据库配置 (Database)
# ================================

# 数据库连接URL
# SQLite (默认): sqlite:///data/ares.db
# PostgreSQL: postgresql://user:password@localhost:5432/ares
# MySQL: mysql://user:password@localhost:3306/ares
DATABASE_URL=sqlite:///data/ares.db

# ================================
# 缓存配置 (Cache)
# ================================

# Redis连接配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password_if_needed

# ================================
# 通知服务配置 (Notifications)
# ================================

# Discord Webhook (可选)
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url

# Telegram Bot (可选)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# ================================
# 应用运行配置 (Application)
# ================================

# 运行环境: development, testing, staging, production
APP_ENV=development

# 调试模式
APP_DEBUG=true

# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# UI服务配置
UI_PORT=8501
UI_HOST=0.0.0.0

# ================================
# 外部服务配置 (External Services)
# ================================

# 错误追踪服务 (可选)
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# 监控服务端口 (可选)
PROMETHEUS_PORT=9090

# ================================
# 开发和测试配置 (Development)
# ================================

# 测试数据库 (仅测试环境)
TEST_DATABASE_URL=sqlite:///data/test_ares.db

# 模拟API模式 (开发环境)
MOCK_API_MODE=false

# 配置热重载 (开发环境)
CONFIG_HOT_RELOAD=true

# ================================
# 性能调优配置 (Performance)
# ================================

# 数据库连接池大小
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# 缓存TTL配置 (秒)
CACHE_DEFAULT_TTL=1800
CACHE_HOT_DATA_TTL=600
CACHE_QUERY_RESULT_TTL=300

# API调用限制
API_CALLS_PER_MINUTE=10
API_RETRY_ATTEMPTS=3
