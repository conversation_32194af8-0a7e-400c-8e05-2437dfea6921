# Ares系统环境变量配置示例
# 复制此文件为.env并填入实际值

# API配置
STEAMDT_API_KEY=your_steamdt_api_key_here

# 数据库配置
DATABASE_URL=sqlite:///data/ares.db

# Redis配置
REDIS_URL=redis://localhost:6379

# 加密密钥（用于敏感数据加密）
ENCRYPTION_KEY=your_32_byte_base64_encoded_key_here

# 通知服务配置
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# 应用配置
APP_ENV=development
APP_DEBUG=true
APP_LOG_LEVEL=INFO

# 安全配置
SECRET_KEY=your_secret_key_for_sessions
JWT_SECRET=your_jwt_secret_key

# 外部服务配置（可选）
SENTRY_DSN=your_sentry_dsn_for_error_tracking
PROMETHEUS_PORT=9090
