"""
饰品发现和评估服务
基于排行榜数据和筛选算法发现有价值的CS2饰品
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Set, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

from core.database import DatabaseManager, Item, get_database_manager
from services.web_scraper import get_web_scraper, RankingItem
from services.steamdt_api import get_steamdt_api_manager, ItemPriceData
from services.cs2_item import CS2Item, CS2Rarity, CS2WeaponType, CS2Quality
from services.api_budget import get_api_budget_manager, APIEndpoint
from core.config import get_config_manager
from core.exceptions import SchedulerError, ErrorContext


class ItemValueTier(Enum):
    """饰品价值等级"""
    PREMIUM = "premium"      # 高价值 (>500元)
    HIGH = "high"           # 较高价值 (100-500元)
    MEDIUM = "medium"       # 中等价值 (20-100元)
    LOW = "low"            # 较低价值 (5-20元)
    MICRO = "micro"        # 微价值 (1-5元)


@dataclass
class ItemEvaluation:
    """饰品评估结果"""
    item_name: str
    market_hash_name: str
    current_price: float
    value_tier: ItemValueTier
    investment_score: float
    liquidity_score: float
    volatility_score: float
    trend_score: float
    final_score: float
    reasons: List[str]
    discovered_from: str  # 发现来源
    discovered_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_name': self.item_name,
            'market_hash_name': self.market_hash_name,
            'current_price': self.current_price,
            'value_tier': self.value_tier.value,
            'investment_score': self.investment_score,
            'liquidity_score': self.liquidity_score,
            'volatility_score': self.volatility_score,
            'trend_score': self.trend_score,
            'final_score': self.final_score,
            'reasons': self.reasons,
            'discovered_from': self.discovered_from,
            'discovered_at': self.discovered_at.isoformat()
        }


class ItemDiscoveryService:
    """饰品发现服务"""
    
    def __init__(self):
        """初始化饰品发现服务"""
        self.logger = logging.getLogger(__name__)
        
        # 价值评估标准
        self.price_tiers = {
            ItemValueTier.PREMIUM: (500, float('inf')),
            ItemValueTier.HIGH: (100, 500),
            ItemValueTier.MEDIUM: (20, 100),
            ItemValueTier.LOW: (5, 20),
            ItemValueTier.MICRO: (1, 5)
        }
        
        # 筛选标准
        self.min_price = 1.0        # 最低价格
        self.max_price = 10000.0    # 最高价格
        self.min_volume = 5         # 最低24小时交易量
        self.min_investment_score = 30.0  # 最低投资评分
        
        # 发现状态
        self.discovered_items: Dict[str, ItemEvaluation] = {}
        self.state_file = Path("data/item_discovery_state.json")
        
        # 加载持久化状态
        self._load_state()
        
        self.logger.info("Item Discovery Service initialized")
    
    def _load_state(self):
        """加载持久化状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 恢复发现的饰品
                if 'discovered_items' in data:
                    for item_data in data['discovered_items']:
                        evaluation = ItemEvaluation(
                            item_name=item_data['item_name'],
                            market_hash_name=item_data['market_hash_name'],
                            current_price=item_data['current_price'],
                            value_tier=ItemValueTier(item_data['value_tier']),
                            investment_score=item_data['investment_score'],
                            liquidity_score=item_data['liquidity_score'],
                            volatility_score=item_data['volatility_score'],
                            trend_score=item_data['trend_score'],
                            final_score=item_data['final_score'],
                            reasons=item_data['reasons'],
                            discovered_from=item_data['discovered_from'],
                            discovered_at=datetime.fromisoformat(item_data['discovered_at'])
                        )
                        self.discovered_items[evaluation.market_hash_name] = evaluation
                
                self.logger.info(f"Loaded {len(self.discovered_items)} discovered items from state")
                
        except Exception as e:
            self.logger.error(f"Error loading item discovery state: {e}")
    
    def _save_state(self):
        """保存持久化状态"""
        try:
            # 确保目录存在
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'discovered_items': [item.to_dict() for item in self.discovered_items.values()],
                'last_save_time': datetime.now().isoformat(),
                'total_discovered': len(self.discovered_items)
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("Item discovery state saved")
            
        except Exception as e:
            self.logger.error(f"Error saving item discovery state: {e}")
    
    async def discover_valuable_items(self, target_count: int = 1000) -> List[ItemEvaluation]:
        """
        发现有价值的饰品
        
        Args:
            target_count: 目标发现数量
            
        Returns:
            List[ItemEvaluation]: 发现的有价值饰品列表
        """
        self.logger.info(f"Starting discovery of {target_count} valuable items...")
        
        try:
            # 从排行榜获取候选饰品
            candidates = await self._gather_candidates_from_rankings()
            
            # 评估候选饰品
            evaluations = await self._evaluate_candidates(candidates)
            
            # 筛选和排序
            valuable_items = self._filter_and_rank_items(evaluations, target_count)
            
            # 更新发现状态
            self._update_discovered_items(valuable_items)
            
            # 保存状态
            self._save_state()
            
            self.logger.info(f"Discovery completed: found {len(valuable_items)} valuable items")
            return valuable_items
            
        except Exception as e:
            self.logger.error(f"Error in item discovery: {e}")
            return []
    
    async def _gather_candidates_from_rankings(self) -> List[RankingItem]:
        """从排行榜收集候选饰品"""
        self.logger.info("Gathering candidates from rankings...")
        
        candidates = []
        scraper = await get_web_scraper()
        
        # 从四种排行榜获取数据
        ranking_types = ['hot', 'trending', 'rising', 'falling']
        
        for ranking_type in ranking_types:
            try:
                items = await scraper.get_ranking_data(ranking_type, limit=200)
                candidates.extend(items)
                self.logger.info(f"Gathered {len(items)} candidates from {ranking_type} ranking")
                
                # 避免请求过快
                await asyncio.sleep(2)
                
            except Exception as e:
                self.logger.error(f"Error gathering from {ranking_type} ranking: {e}")
        
        # 去重
        unique_candidates = self._deduplicate_candidates(candidates)
        self.logger.info(f"Total unique candidates: {len(unique_candidates)}")
        
        return unique_candidates
    
    def _deduplicate_candidates(self, candidates: List[RankingItem]) -> List[RankingItem]:
        """去重候选饰品"""
        seen = set()
        unique_candidates = []
        
        for item in candidates:
            key = item.market_hash_name.lower().strip()
            if key not in seen:
                seen.add(key)
                unique_candidates.append(item)
        
        return unique_candidates
    
    async def _evaluate_candidates(self, candidates: List[RankingItem]) -> List[ItemEvaluation]:
        """评估候选饰品"""
        self.logger.info(f"Evaluating {len(candidates)} candidates...")
        
        evaluations = []
        budget_manager = get_api_budget_manager()
        
        for i, candidate in enumerate(candidates):
            try:
                # 检查API预算
                if not budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "discovery"):
                    self.logger.warning(f"Discovery stopped at {i}/{len(candidates)}: API budget exhausted")
                    break
                
                # 基础筛选
                if not self._passes_basic_filter(candidate):
                    continue
                
                # 详细评估
                evaluation = await self._evaluate_single_item(candidate)
                if evaluation and evaluation.final_score >= self.min_investment_score:
                    evaluations.append(evaluation)
                
                # 记录API调用
                budget_manager.record_call(
                    APIEndpoint.WEB_SCRAPING, "discovery", True, 0.1
                )
                
                # 避免请求过快
                if i % 10 == 0:
                    await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error evaluating candidate {candidate.item_name}: {e}")
                continue
        
        self.logger.info(f"Evaluation completed: {len(evaluations)} items passed evaluation")
        return evaluations
    
    def _passes_basic_filter(self, item: RankingItem) -> bool:
        """基础筛选"""
        # 价格筛选
        if item.current_price < self.min_price or item.current_price > self.max_price:
            return False
        
        # 交易量筛选
        if item.volume_24h < self.min_volume:
            return False
        
        # 名称筛选（排除明显的非饰品）
        name_lower = item.item_name.lower()
        if any(keyword in name_lower for keyword in ['sticker', 'music kit', 'graffiti']):
            return False
        
        return True
    
    async def _evaluate_single_item(self, item: RankingItem) -> Optional[ItemEvaluation]:
        """评估单个饰品"""
        try:
            # 确定价值等级
            value_tier = self._determine_value_tier(item.current_price)
            
            # 计算各项评分
            investment_score = self._calculate_investment_score(item)
            liquidity_score = self._calculate_liquidity_score(item)
            volatility_score = self._calculate_volatility_score(item)
            trend_score = self._calculate_trend_score(item)
            
            # 计算最终评分
            final_score = self._calculate_final_score(
                investment_score, liquidity_score, volatility_score, trend_score
            )
            
            # 生成评估原因
            reasons = self._generate_evaluation_reasons(
                item, investment_score, liquidity_score, volatility_score, trend_score
            )
            
            return ItemEvaluation(
                item_name=item.item_name,
                market_hash_name=item.market_hash_name,
                current_price=item.current_price,
                value_tier=value_tier,
                investment_score=investment_score,
                liquidity_score=liquidity_score,
                volatility_score=volatility_score,
                trend_score=trend_score,
                final_score=final_score,
                reasons=reasons,
                discovered_from="ranking_analysis",
                discovered_at=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error evaluating item {item.item_name}: {e}")
            return None
    
    def _determine_value_tier(self, price: float) -> ItemValueTier:
        """确定价值等级"""
        for tier, (min_price, max_price) in self.price_tiers.items():
            if min_price <= price < max_price:
                return tier
        return ItemValueTier.MICRO
    
    def _calculate_investment_score(self, item: RankingItem) -> float:
        """计算投资评分"""
        score = 0.0
        
        # 排名因子
        rank_score = max(0, 100 - item.rank)
        score += rank_score * 0.3
        
        # 价格因子（适中价格更有投资价值）
        if 10 <= item.current_price <= 500:
            price_score = 80
        elif 5 <= item.current_price < 10:
            price_score = 60
        elif 500 < item.current_price <= 2000:
            price_score = 70
        else:
            price_score = 40
        score += price_score * 0.4
        
        # 价格变化因子
        change_score = min(abs(item.price_change_percent), 30)
        score += change_score * 0.3
        
        return min(100, score)
    
    def _calculate_liquidity_score(self, item: RankingItem) -> float:
        """计算流动性评分"""
        # 基于24小时交易量
        if item.volume_24h >= 200:
            return 90.0
        elif item.volume_24h >= 100:
            return 80.0
        elif item.volume_24h >= 50:
            return 70.0
        elif item.volume_24h >= 20:
            return 60.0
        elif item.volume_24h >= 10:
            return 50.0
        else:
            return 30.0
    
    def _calculate_volatility_score(self, item: RankingItem) -> float:
        """计算波动性评分"""
        # 基于价格变化百分比
        abs_change = abs(item.price_change_percent)
        
        if 2 <= abs_change <= 10:
            return 80.0  # 适度波动最好
        elif 1 <= abs_change < 2:
            return 60.0
        elif 10 < abs_change <= 20:
            return 70.0
        elif abs_change > 20:
            return 50.0  # 过度波动风险高
        else:
            return 40.0  # 无波动缺乏机会
    
    def _calculate_trend_score(self, item: RankingItem) -> float:
        """计算趋势评分"""
        # 基于价格变化方向和幅度
        if item.price_change > 0:
            # 上涨趋势
            if item.price_change_percent >= 5:
                return 85.0
            elif item.price_change_percent >= 2:
                return 75.0
            else:
                return 65.0
        elif item.price_change < 0:
            # 下跌趋势（可能是买入机会）
            if item.price_change_percent <= -10:
                return 60.0  # 大跌可能是机会
            elif item.price_change_percent <= -5:
                return 70.0
            else:
                return 55.0
        else:
            return 50.0  # 无变化
    
    def _calculate_final_score(self, investment: float, liquidity: float, 
                              volatility: float, trend: float) -> float:
        """计算最终评分"""
        weights = {
            'investment': 0.4,
            'liquidity': 0.3,
            'volatility': 0.2,
            'trend': 0.1
        }
        
        final_score = (
            investment * weights['investment'] +
            liquidity * weights['liquidity'] +
            volatility * weights['volatility'] +
            trend * weights['trend']
        )
        
        return round(final_score, 2)
    
    def _generate_evaluation_reasons(self, item: RankingItem, investment: float, 
                                   liquidity: float, volatility: float, trend: float) -> List[str]:
        """生成评估原因"""
        reasons = []
        
        # 投资评分原因
        if investment >= 80:
            reasons.append("高投资价值：排名靠前且价格合理")
        elif investment >= 60:
            reasons.append("中等投资价值：具备一定投资潜力")
        
        # 流动性原因
        if liquidity >= 80:
            reasons.append("高流动性：交易活跃，易于买卖")
        elif liquidity >= 60:
            reasons.append("中等流动性：有一定交易量")
        else:
            reasons.append("低流动性：交易量较少，需谨慎")
        
        # 波动性原因
        if volatility >= 70:
            reasons.append("适度波动：价格变化为投资提供机会")
        elif volatility < 50:
            reasons.append("波动性风险：价格变化过大或过小")
        
        # 趋势原因
        if trend >= 70:
            if item.price_change > 0:
                reasons.append("上涨趋势：价格呈现上升态势")
            else:
                reasons.append("下跌机会：价格下跌可能是买入时机")
        
        return reasons
    
    def _filter_and_rank_items(self, evaluations: List[ItemEvaluation], 
                              target_count: int) -> List[ItemEvaluation]:
        """筛选和排序饰品"""
        # 按最终评分排序
        sorted_items = sorted(evaluations, key=lambda x: x.final_score, reverse=True)
        
        # 确保多样性：不同价值等级的平衡
        balanced_items = self._ensure_diversity(sorted_items, target_count)
        
        return balanced_items[:target_count]
    
    def _ensure_diversity(self, items: List[ItemEvaluation], target_count: int) -> List[ItemEvaluation]:
        """确保饰品多样性"""
        # 按价值等级分组
        tier_groups = {}
        for item in items:
            tier = item.value_tier
            if tier not in tier_groups:
                tier_groups[tier] = []
            tier_groups[tier].append(item)
        
        # 分配目标数量
        tier_targets = {
            ItemValueTier.PREMIUM: int(target_count * 0.1),   # 10%
            ItemValueTier.HIGH: int(target_count * 0.25),     # 25%
            ItemValueTier.MEDIUM: int(target_count * 0.35),   # 35%
            ItemValueTier.LOW: int(target_count * 0.25),      # 25%
            ItemValueTier.MICRO: int(target_count * 0.05)     # 5%
        }
        
        # 从每个等级选择饰品
        balanced_items = []
        for tier, target in tier_targets.items():
            if tier in tier_groups:
                tier_items = tier_groups[tier][:target]
                balanced_items.extend(tier_items)
        
        # 如果还没达到目标数量，从剩余的高评分饰品中补充
        if len(balanced_items) < target_count:
            remaining_items = [item for item in items if item not in balanced_items]
            remaining_count = target_count - len(balanced_items)
            balanced_items.extend(remaining_items[:remaining_count])
        
        return balanced_items
    
    def _update_discovered_items(self, new_items: List[ItemEvaluation]):
        """更新发现的饰品"""
        for item in new_items:
            self.discovered_items[item.market_hash_name] = item
        
        self.logger.info(f"Updated discovered items: {len(self.discovered_items)} total")
    
    def get_discovered_items(self, tier: Optional[ItemValueTier] = None) -> List[ItemEvaluation]:
        """获取发现的饰品"""
        items = list(self.discovered_items.values())
        
        if tier:
            items = [item for item in items if item.value_tier == tier]
        
        # 按评分排序
        items.sort(key=lambda x: x.final_score, reverse=True)
        
        return items
    
    def get_discovery_summary(self) -> Dict[str, Any]:
        """获取发现摘要"""
        tier_counts = {}
        for tier in ItemValueTier:
            tier_counts[tier.value] = len([
                item for item in self.discovered_items.values() 
                if item.value_tier == tier
            ])
        
        return {
            'total_discovered': len(self.discovered_items),
            'tier_distribution': tier_counts,
            'average_score': sum(item.final_score for item in self.discovered_items.values()) / len(self.discovered_items) if self.discovered_items else 0,
            'last_discovery': max(item.discovered_at for item in self.discovered_items.values()).isoformat() if self.discovered_items else None
        }


# 全局饰品发现服务实例
_item_discovery_service: Optional[ItemDiscoveryService] = None


def get_item_discovery_service() -> ItemDiscoveryService:
    """获取全局饰品发现服务实例"""
    global _item_discovery_service
    
    if _item_discovery_service is None:
        _item_discovery_service = ItemDiscoveryService()
    
    return _item_discovery_service
