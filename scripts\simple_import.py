#!/usr/bin/env python3
"""
简化版基础数据导入脚本
快速导入SteamDT基础数据到数据库
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import get_database_manager, Item


def main():
    """主函数"""
    print("开始导入SteamDT基础数据...")
    
    # 加载数据文件
    data_file = project_root / 'data' / 'basedata.json'
    print(f"读取数据文件: {data_file}")
    
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if not data.get('success'):
        print("数据文件格式错误")
        return 1
    
    items_data = data.get('data', [])
    print(f"共有 {len(items_data)} 个饰品数据")
    
    # 获取数据库管理器
    db_manager = get_database_manager()
    
    new_count = 0
    updated_count = 0
    error_count = 0
    
    with db_manager.get_session() as session:
        for i, item_data in enumerate(items_data):
            try:
                name = item_data.get('name', '')
                market_hash_name = item_data.get('marketHashName', '')
                
                if not name or not market_hash_name:
                    print(f"跳过不完整数据: {item_data}")
                    continue
                
                # 处理平台数据
                platform_list = item_data.get('platformList', [])
                platform_data = {}
                for platform_info in platform_list:
                    platform_name = platform_info.get('name', '').lower()
                    item_id = platform_info.get('itemId', '')
                    if platform_name and item_id:
                        platform_data[platform_name] = item_id
                
                # 解析武器类型
                weapon_type = ""
                skin_name = ""
                if '|' in market_hash_name:
                    parts = market_hash_name.split('|')
                    weapon_type = parts[0].strip()
                    if len(parts) > 1:
                        skin_part = parts[1].strip()
                        if '(' in skin_part:
                            skin_name = skin_part.split('(')[0].strip()
                        else:
                            skin_name = skin_part
                
                # 检查是否已存在
                existing_item = session.get(Item, market_hash_name)
                
                if existing_item:
                    # 更新现有饰品
                    existing_item.name = name
                    existing_item.weapon_type = weapon_type
                    existing_item.skin_name = skin_name
                    existing_item.platform_data = json.dumps(platform_data, ensure_ascii=False)
                    existing_item.data_source = 'steamdt'
                    existing_item.last_sync_time = datetime.now()
                    existing_item.updated_at = datetime.now()
                    existing_item.is_active = True
                    updated_count += 1
                else:
                    # 创建新饰品
                    new_item = Item(
                        market_hash_name=market_hash_name,
                        name=name,
                        weapon_type=weapon_type,
                        skin_name=skin_name,
                        platform_data=json.dumps(platform_data, ensure_ascii=False),
                        data_source='steamdt',
                        last_sync_time=datetime.now(),
                        is_active=True,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(new_item)
                    new_count += 1
                
                # 每1000条记录提交一次并显示进度
                if (i + 1) % 1000 == 0:
                    session.commit()
                    print(f"已处理 {i + 1}/{len(items_data)} 个饰品 (新增: {new_count}, 更新: {updated_count})")
            
            except Exception as e:
                error_count += 1
                print(f"处理第{i+1}个饰品时出错: {e}")
                continue
        
        # 最终提交
        session.commit()
    
    print("=" * 50)
    print("导入完成！")
    print(f"新增饰品: {new_count}")
    print(f"更新饰品: {updated_count}")
    print(f"错误数量: {error_count}")
    print(f"总计处理: {new_count + updated_count}")
    print("=" * 50)
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"脚本执行失败: {e}")
        sys.exit(1)
