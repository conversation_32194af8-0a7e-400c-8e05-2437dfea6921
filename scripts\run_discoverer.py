#!/usr/bin/env python3
"""
Ares发现器服务运行脚本
启动和管理机会发现服务
"""

import asyncio
import logging
import sys
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.discoverer import get_discoverer_service, DiscovererService
from core.logging_config import setup_logging
from core.config import get_config_manager
from core.exceptions import AresException


def setup_argument_parser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Ares发现器服务运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_discoverer.py                    # 启动发现器服务
  python run_discoverer.py --status           # 查看发现器状态
  python run_discoverer.py --manual-discovery # 手动触发发现
  python run_discoverer.py --history          # 查看发现历史
  python run_discoverer.py --config-check     # 检查配置
        """
    )
    
    parser.add_argument(
        '--status', 
        action='store_true',
        help='查看发现器状态并退出'
    )
    
    parser.add_argument(
        '--manual-discovery',
        action='store_true',
        help='手动触发机会发现'
    )
    
    parser.add_argument(
        '--history',
        action='store_true',
        help='查看发现历史'
    )
    
    parser.add_argument(
        '--config-check',
        action='store_true',
        help='检查配置并退出'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--discovery-limit',
        type=int,
        metavar='N',
        help='每个排行榜的发现数量限制覆盖'
    )
    
    parser.add_argument(
        '--min-score',
        type=float,
        metavar='SCORE',
        help='最小机会评分覆盖'
    )
    
    return parser


async def check_status():
    """检查发现器状态"""
    try:
        discoverer = get_discoverer_service()
        await discoverer.initialize()
        
        status = discoverer.get_status()
        
        print("=== Ares发现器状态 ===")
        print(f"运行状态: {'运行中' if status['running'] else '已停止'}")
        print(f"调度器状态: {'运行中' if status['scheduler_running'] else '已停止'}")
        print(f"已发现项目数: {status['discovered_items_count']}")
        print()
        
        stats = status['stats']
        print("=== 统计信息 ===")
        print(f"总发现次数: {stats['total_discoveries']}")
        print(f"成功添加: {stats['successful_additions']}")
        print(f"重复项目: {stats['duplicate_items']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"API调用次数: {stats['api_calls_made']}")
        print(f"发现会话数: {stats['discovery_sessions']}")
        print(f"最后发现时间: {stats['last_discovery_time'] or '无'}")
        print()
        
        print("=== 配置信息 ===")
        print(f"发现时间: {', '.join(status['discovery_times'])}")
        print(f"最小机会评分: {status['min_opportunity_score']}")
        print()
        
        if status['next_scheduled_jobs']:
            print("=== 计划任务 ===")
            for job in status['next_scheduled_jobs']:
                print(f"- {job['name']}: {job['next_run_time'] or '未计划'}")
        
        return True
        
    except Exception as e:
        print(f"获取状态失败: {str(e)}")
        return False


async def manual_discovery():
    """手动触发发现"""
    try:
        print("手动触发机会发现...")
        
        discoverer = get_discoverer_service()
        await discoverer.initialize()
        
        opportunities = await discoverer.manual_discovery()
        
        print(f"\n发现完成！")
        print(f"发现机会数量: {len(opportunities)}")
        
        if opportunities:
            print("\n发现的机会:")
            for i, opp in enumerate(opportunities[:10], 1):  # 显示前10个
                print(f"{i:2d}. {opp.item_name} ({opp.ranking_type}排行榜第{opp.ranking_position}位)")
                print(f"    评分: {opp.opportunity_score:.2f}")
                print(f"    ID: {opp.item_id}")
                print()
            
            if len(opportunities) > 10:
                print(f"... 还有 {len(opportunities) - 10} 个机会")
        
        return True
        
    except Exception as e:
        print(f"手动发现失败: {str(e)}")
        return False


async def show_history():
    """显示发现历史"""
    try:
        discoverer = get_discoverer_service()
        history = discoverer.get_discovery_history(limit=5)
        
        if not history:
            print("暂无发现历史")
            return True
        
        print("=== 发现历史 (最近5次) ===")
        
        for i, session in enumerate(reversed(history), 1):
            session_time = datetime.fromisoformat(session['session_time'])
            print(f"\n{i}. {session_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   发现机会: {session['opportunities_count']} 个")
            
            if session['opportunities']:
                # 显示前3个机会
                for j, opp in enumerate(session['opportunities'][:3], 1):
                    print(f"   {j}. {opp['item_name']} (评分: {opp['opportunity_score']:.2f})")
                
                if len(session['opportunities']) > 3:
                    print(f"   ... 还有 {len(session['opportunities']) - 3} 个")
        
        return True
        
    except Exception as e:
        print(f"获取历史失败: {str(e)}")
        return False


def check_config():
    """检查配置"""
    try:
        print("=== 配置检查 ===")
        
        config = get_config_manager()
        
        # 检查关键配置
        required_configs = [
            'api.steamdt.base_url',
            'api.steamdt.api_key',
            'database.url',
            'discoverer.limit_per_ranking',
            'discoverer.min_opportunity_score',
            'discoverer.discovery_times'
        ]
        
        missing_configs = []
        for config_key in required_configs:
            value = config.get(config_key)
            if value is None:
                missing_configs.append(config_key)
            else:
                print(f"✓ {config_key}: {value}")
        
        if missing_configs:
            print("\n缺失的配置:")
            for config_key in missing_configs:
                print(f"✗ {config_key}")
            return False
        
        print("\n✓ 所有必需配置都已设置")
        return True
        
    except Exception as e:
        print(f"配置检查失败: {str(e)}")
        return False


async def run_discoverer(args):
    """运行发现器服务"""
    try:
        print("=== 启动Ares发现器服务 ===")
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 创建发现器实例
        discoverer = get_discoverer_service()
        
        # 应用命令行参数覆盖
        if args.discovery_limit:
            discoverer.discovery_limit = args.discovery_limit
            print(f"发现数量限制覆盖: {args.discovery_limit}")
        
        if args.min_score:
            discoverer.min_opportunity_score = args.min_score
            print(f"最小评分覆盖: {args.min_score}")
        
        print("正在初始化服务组件...")
        
        # 启动发现器
        await discoverer.start()
        
        print("发现器服务已启动，按 Ctrl+C 停止服务")
        
        # 保持运行
        try:
            while discoverer.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n收到中断信号，正在停止服务...")
            await discoverer.stop()
            print("服务已停止")
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止服务...")
        if 'discoverer' in locals():
            await discoverer.stop()
        print("服务已停止")
    except Exception as e:
        print(f"发现器运行失败: {str(e)}")
        logging.error("Discoverer service failed", exc_info=True)
        sys.exit(1)


async def main():
    """主函数"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(level=getattr(logging, args.log_level))
    
    try:
        # 根据参数执行不同操作
        if args.config_check:
            success = check_config()
            sys.exit(0 if success else 1)
        
        elif args.status:
            success = await check_status()
            sys.exit(0 if success else 1)
        
        elif args.manual_discovery:
            success = await manual_discovery()
            sys.exit(0 if success else 1)
        
        elif args.history:
            success = await show_history()
            sys.exit(0 if success else 1)
        
        else:
            # 默认启动发现器服务
            await run_discoverer(args)
    
    except AresException as e:
        print(f"Ares系统错误: {e.message}")
        logging.error("Ares system error", exc_info=True)
        sys.exit(1)
    except Exception as e:
        print(f"未知错误: {str(e)}")
        logging.error("Unknown error", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # 在Windows上设置事件循环策略
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主函数
    asyncio.run(main())
