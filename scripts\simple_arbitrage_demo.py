"""
简化的套利机会检测演示
不依赖配置系统，直接展示核心算法
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.price_comparator import Platform, PlatformFees
from services.arbitrage_detector import ArbitrageRiskLevel
from services.steamdt_api import PlatformPrice


def demonstrate_platform_fees():
    """演示平台手续费计算"""
    print("💳 平台手续费演示")
    print("=" * 50)
    
    # 手动创建平台手续费配置
    platform_fees = {
        Platform.STEAM: PlatformFees(
            platform=Platform.STEAM,
            sell_fee_rate=0.13,      # Steam 13%手续费
            buy_fee_rate=0.0,
            withdraw_fee_rate=0.0,
            min_withdraw_amount=0.0,
            withdraw_time_days=0
        ),
        Platform.BUFF: PlatformFees(
            platform=Platform.BUFF,
            sell_fee_rate=0.025,     # BUFF 2.5%手续费
            buy_fee_rate=0.0,
            withdraw_fee_rate=0.01,  # 1%提现费
            min_withdraw_amount=100.0,
            withdraw_time_days=1
        ),
        Platform.C5GAME: PlatformFees(
            platform=Platform.C5GAME,
            sell_fee_rate=0.05,      # C5 5%手续费
            buy_fee_rate=0.0,
            withdraw_fee_rate=0.02,  # 2%提现费
            min_withdraw_amount=50.0,
            withdraw_time_days=2
        )
    }
    
    test_price = 100.0
    print(f"基准价格: ¥{test_price:.2f}")
    print("\n各平台手续费对比:")
    
    for platform, fees in platform_fees.items():
        buy_cost = fees.calculate_buy_cost(test_price)
        sell_revenue = fees.calculate_sell_cost(test_price)
        
        print(f"\n{platform.value.upper()}:")
        print(f"   买入成本: ¥{buy_cost:.2f} (手续费率: {fees.buy_fee_rate*100:.1f}%)")
        print(f"   卖出收益: ¥{sell_revenue:.2f} (手续费率: {(fees.sell_fee_rate + fees.withdraw_fee_rate)*100:.1f}%)")
        print(f"   提现时间: {fees.withdraw_time_days} 天")
        print(f"   最小提现: ¥{fees.min_withdraw_amount:.2f}")
    
    return platform_fees


def demonstrate_price_comparison():
    """演示价格比较功能"""
    print("\n\n💰 跨平台价格比较演示")
    print("=" * 50)
    
    # 创建演示数据
    demo_items = [
        {
            'name': 'AK-47 | Redline (Field-Tested)',
            'platforms': {
                Platform.STEAM: PlatformPrice(
                    platform='steam',
                    platform_item_id='123456',
                    sell_price=120.0,
                    sell_count=45,
                    bidding_price=115.0,
                    bidding_count=20,
                    update_time=1625097600
                ),
                Platform.BUFF: PlatformPrice(
                    platform='buff',
                    platform_item_id='789012',
                    sell_price=95.0,
                    sell_count=80,
                    bidding_price=90.0,
                    bidding_count=35,
                    update_time=1625097600
                ),
                Platform.C5GAME: PlatformPrice(
                    platform='c5game',
                    platform_item_id='345678',
                    sell_price=105.0,
                    sell_count=25,
                    bidding_price=100.0,
                    bidding_count=15,
                    update_time=1625097600
                )
            }
        },
        {
            'name': 'AWP | Asiimov (Field-Tested)',
            'platforms': {
                Platform.STEAM: PlatformPrice(
                    platform='steam',
                    platform_item_id='234567',
                    sell_price=85.0,
                    sell_count=30,
                    bidding_price=80.0,
                    bidding_count=12,
                    update_time=1625097600
                ),
                Platform.BUFF: PlatformPrice(
                    platform='buff',
                    platform_item_id='890123',
                    sell_price=78.0,
                    sell_count=60,
                    bidding_price=75.0,
                    bidding_count=25,
                    update_time=1625097600
                )
            }
        }
    ]
    
    for item_data in demo_items:
        item_name = item_data['name']
        platform_prices = item_data['platforms']
        
        print(f"\n🎯 分析饰品: {item_name}")
        print("-" * 40)
        
        # 显示各平台价格
        print("📊 各平台价格:")
        for platform, price_data in platform_prices.items():
            print(f"   {platform.value:8}: 售价 ¥{price_data.sell_price:7.2f} | 求购 ¥{price_data.bidding_price:7.2f} | 交易量 {price_data.sell_count + price_data.bidding_count:3d}")
        
        # 计算价差
        prices = [price.sell_price for price in platform_prices.values()]
        min_price = min(prices)
        max_price = max(prices)
        avg_price = sum(prices) / len(prices)
        
        print(f"\n📈 价差分析:")
        print(f"   最低价格: ¥{min_price:.2f}")
        print(f"   最高价格: ¥{max_price:.2f}")
        print(f"   平均价格: ¥{avg_price:.2f}")
        print(f"   绝对价差: ¥{max_price - min_price:.2f}")
        print(f"   相对价差: {((max_price - min_price) / min_price) * 100:.1f}%")
        
        # 寻找最佳套利机会
        best_buy_platform = None
        best_sell_platform = None
        max_profit_margin = 0.0
        
        for buy_platform, buy_price_data in platform_prices.items():
            for sell_platform, sell_price_data in platform_prices.items():
                if buy_platform == sell_platform:
                    continue
                
                buy_price = buy_price_data.sell_price
                sell_price = sell_price_data.sell_price
                
                if buy_price <= 0 or sell_price <= 0:
                    continue
                
                # 简单利润率计算
                profit_margin = ((sell_price - buy_price) / buy_price) * 100
                
                if profit_margin > max_profit_margin:
                    max_profit_margin = profit_margin
                    best_buy_platform = buy_platform
                    best_sell_platform = sell_platform
        
        if best_buy_platform and best_sell_platform and max_profit_margin > 0:
            buy_price = platform_prices[best_buy_platform].sell_price
            sell_price = platform_prices[best_sell_platform].sell_price
            
            print(f"\n🎯 最佳套利机会:")
            print(f"   买入平台: {best_buy_platform.value} (¥{buy_price:.2f})")
            print(f"   卖出平台: {best_sell_platform.value} (¥{sell_price:.2f})")
            print(f"   毛利润: ¥{sell_price - buy_price:.2f}")
            print(f"   毛利润率: {max_profit_margin:.1f}%")
        else:
            print("\n❌ 未发现有利可图的套利机会")


def demonstrate_arbitrage_calculation():
    """演示套利计算（考虑手续费）"""
    print("\n\n🔍 套利计算演示（考虑手续费）")
    print("=" * 50)
    
    # 平台手续费配置
    platform_fees = {
        Platform.STEAM: PlatformFees(
            platform=Platform.STEAM,
            sell_fee_rate=0.13,
            buy_fee_rate=0.0,
            withdraw_fee_rate=0.0,
            min_withdraw_amount=0.0,
            withdraw_time_days=0
        ),
        Platform.BUFF: PlatformFees(
            platform=Platform.BUFF,
            sell_fee_rate=0.025,
            buy_fee_rate=0.0,
            withdraw_fee_rate=0.01,
            min_withdraw_amount=100.0,
            withdraw_time_days=1
        )
    }
    
    # 示例：BUFF买入，Steam卖出
    buy_platform = Platform.BUFF
    sell_platform = Platform.STEAM
    buy_price = 95.0
    sell_price = 120.0
    
    print(f"示例套利计算:")
    print(f"买入平台: {buy_platform.value} - ¥{buy_price:.2f}")
    print(f"卖出平台: {sell_platform.value} - ¥{sell_price:.2f}")
    
    # 计算实际成本和收益
    buy_fees = platform_fees[buy_platform]
    sell_fees = platform_fees[sell_platform]
    
    actual_buy_cost = buy_fees.calculate_buy_cost(buy_price)
    actual_sell_revenue = sell_fees.calculate_sell_cost(sell_price)
    
    net_profit = actual_sell_revenue - actual_buy_cost
    profit_margin = (net_profit / actual_buy_cost) * 100 if actual_buy_cost > 0 else 0
    
    print(f"\n💰 详细计算:")
    print(f"实际买入成本: ¥{actual_buy_cost:.2f}")
    print(f"实际卖出收益: ¥{actual_sell_revenue:.2f}")
    print(f"净利润: ¥{net_profit:.2f}")
    print(f"净利润率: {profit_margin:.1f}%")
    
    # 风险评估
    if profit_margin >= 10:
        risk_assessment = "🟢 低风险 - 利润率较高"
    elif profit_margin >= 5:
        risk_assessment = "🟡 中等风险 - 利润率适中"
    elif profit_margin >= 2:
        risk_assessment = "🟠 较高风险 - 利润率较低"
    else:
        risk_assessment = "🔴 高风险 - 利润率过低"
    
    print(f"\n📊 风险评估: {risk_assessment}")
    
    # 投资建议
    if profit_margin >= 8:
        recommendation = "🌟 强烈推荐"
    elif profit_margin >= 5:
        recommendation = "👍 推荐"
    elif profit_margin >= 3:
        recommendation = "🤔 谨慎考虑"
    else:
        recommendation = "❌ 不推荐"
    
    print(f"🎯 投资建议: {recommendation}")


def main():
    """主函数"""
    print("🚀 启动CS2饰品套利机会检测演示")
    print()
    
    try:
        # 演示平台手续费
        demonstrate_platform_fees()
        
        # 演示价格比较
        demonstrate_price_comparison()
        
        # 演示套利计算
        demonstrate_arbitrage_calculation()
        
        # 总结
        print("\n\n📋 演示总结")
        print("=" * 30)
        print("✅ 平台手续费计算 - 完成")
        print("✅ 跨平台价格比较 - 完成")
        print("✅ 套利机会识别 - 完成")
        print("✅ 风险评估和建议 - 完成")
        
        print("\n💡 关键发现:")
        print("• Steam手续费最高(13%)，但价格通常也最高")
        print("• BUFF手续费较低(3.5%)，适合作为买入平台")
        print("• 考虑手续费后，实际利润率会显著降低")
        print("• 需要综合考虑利润率、流动性和风险")
        
        print("\n✅ 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
