"""
Ares预警规则DSL解析器
支持自然语言式的预警规则定义和解析
"""

import re
import time
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

from .alerts import AlertRule, AlertCondition, AlertType, AlertSeverity

logger = logging.getLogger(__name__)


@dataclass
class DSLParseResult:
    """DSL解析结果"""
    success: bool
    rule: Optional[AlertRule] = None
    error: Optional[str] = None


class AlertDSLParser:
    """预警规则DSL解析器"""
    
    def __init__(self):
        """初始化DSL解析器"""
        # 操作符映射
        self.operator_map = {
            '大于': '>',
            '小于': '<',
            '大于等于': '>=',
            '小于等于': '<=',
            '等于': '==',
            '不等于': '!=',
            '包含': 'in',
            '不包含': 'not_in',
            '介于': 'between',
            '>': '>',
            '<': '<',
            '>=': '>=',
            '<=': '<=',
            '==': '==',
            '!=': '!=',
            'in': 'in',
            'not_in': 'not_in',
            'between': 'between'
        }
        
        # 字段映射
        self.field_map = {
            '价格': 'current_price',
            '价格变化': 'price_change_percent',
            '价格变化率': 'price_change_percent',
            '交易量': 'volume_24h',
            '交易量变化': 'volume_change_percent',
            '交易量变化率': 'volume_change_percent',
            '持仓数量': 'holding_quantity',
            '持仓价值': 'holding_value',
            '风险等级': 'risk_level',
            '投资组合偏离': 'portfolio_deviation',
            '波动率': 'volatility',
            '流动性': 'liquidity',
            '市值': 'market_cap'
        }
        
        # 预警类型映射
        self.alert_type_map = {
            '价格机会': AlertType.PRICE_OPPORTUNITY,
            '价格风险': AlertType.PRICE_RISK,
            '交易量激增': AlertType.VOLUME_SPIKE,
            '投资组合再平衡': AlertType.PORTFOLIO_REBALANCE,
            '持仓风险': AlertType.HOLDING_RISK,
            '市场异常': AlertType.MARKET_ANOMALY
        }
        
        # 严重性映射
        self.severity_map = {
            '低': AlertSeverity.LOW,
            '中': AlertSeverity.MEDIUM,
            '高': AlertSeverity.HIGH,
            '严重': AlertSeverity.CRITICAL,
            'low': AlertSeverity.LOW,
            'medium': AlertSeverity.MEDIUM,
            'high': AlertSeverity.HIGH,
            'critical': AlertSeverity.CRITICAL
        }
    
    def parse_rule(self, dsl_text: str) -> DSLParseResult:
        """解析DSL规则文本"""
        try:
            # 清理文本
            dsl_text = dsl_text.strip()
            
            # 解析规则基本信息
            rule_info = self._parse_rule_info(dsl_text)
            if not rule_info:
                return DSLParseResult(False, error="无法解析规则基本信息")
            
            # 解析条件
            conditions = self._parse_conditions(dsl_text)
            if not conditions:
                return DSLParseResult(False, error="无法解析预警条件")
            
            # 创建规则
            rule = AlertRule(
                id=rule_info.get('id', f"dsl_rule_{int(time.time())}"),
                name=rule_info.get('name', '自定义规则'),
                description=rule_info.get('description', dsl_text),
                alert_type=rule_info.get('alert_type', AlertType.MARKET_ANOMALY),
                severity=rule_info.get('severity', AlertSeverity.MEDIUM),
                conditions=conditions,
                logic_operator=rule_info.get('logic_operator', 'AND'),
                cooldown_minutes=rule_info.get('cooldown_minutes', 60),
                enabled=True
            )
            
            return DSLParseResult(True, rule=rule)
        
        except Exception as e:
            logger.error(f"DSL parsing error: {str(e)}")
            return DSLParseResult(False, error=f"解析错误: {str(e)}")
    
    def _parse_rule_info(self, dsl_text: str) -> Optional[Dict[str, Any]]:
        """解析规则基本信息"""
        rule_info = {}
        
        # 解析规则名称
        name_match = re.search(r'规则名称[:：]\s*([^\n]+)', dsl_text)
        if name_match:
            rule_info['name'] = name_match.group(1).strip()
        
        # 解析规则描述
        desc_match = re.search(r'描述[:：]\s*([^\n]+)', dsl_text)
        if desc_match:
            rule_info['description'] = desc_match.group(1).strip()
        
        # 解析预警类型
        for type_name, alert_type in self.alert_type_map.items():
            if type_name in dsl_text:
                rule_info['alert_type'] = alert_type
                break
        
        # 解析严重性
        for severity_name, severity in self.severity_map.items():
            if f'严重性{severity_name}' in dsl_text or f'级别{severity_name}' in dsl_text:
                rule_info['severity'] = severity
                break
        
        # 解析逻辑操作符
        if '并且' in dsl_text or 'AND' in dsl_text.upper():
            rule_info['logic_operator'] = 'AND'
        elif '或者' in dsl_text or 'OR' in dsl_text.upper():
            rule_info['logic_operator'] = 'OR'
        
        # 解析冷却时间
        cooldown_match = re.search(r'冷却时间[:：]\s*(\d+)\s*分钟', dsl_text)
        if cooldown_match:
            rule_info['cooldown_minutes'] = int(cooldown_match.group(1))
        
        return rule_info
    
    def _parse_conditions(self, dsl_text: str) -> List[AlertCondition]:
        """解析预警条件"""
        conditions = []
        
        # 条件模式匹配
        patterns = [
            # 数值比较：价格 > 100
            r'([^><=!]+)\s*([><=!]+)\s*([\d.]+)',
            # 百分比比较：价格变化率 < -15%
            r'([^><=!]+)\s*([><=!]+)\s*(-?[\d.]+)%',
            # 范围比较：价格 介于 100 和 200
            r'([^介于]+)\s*介于\s*([\d.]+)\s*和\s*([\d.]+)',
            # 包含比较：风险等级 包含 高风险
            r'([^包含不]+)\s*(包含|不包含)\s*([^\s]+)'
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, dsl_text)
            for match in matches:
                condition = self._create_condition_from_match(match, pattern)
                if condition:
                    conditions.append(condition)
        
        return conditions
    
    def _create_condition_from_match(self, match, pattern: str) -> Optional[AlertCondition]:
        """从正则匹配创建条件"""
        try:
            groups = match.groups()
            
            if '介于' in pattern:
                # 范围条件
                field_name = groups[0].strip()
                min_value = float(groups[1])
                max_value = float(groups[2])
                
                field = self._map_field_name(field_name)
                if field:
                    return AlertCondition(field, 'between', [min_value, max_value])
            
            elif '包含' in pattern:
                # 包含条件
                field_name = groups[0].strip()
                operator = '不包含' if '不包含' in groups[1] else '包含'
                value = groups[2].strip()
                
                field = self._map_field_name(field_name)
                mapped_operator = self.operator_map.get(operator)
                
                if field and mapped_operator:
                    return AlertCondition(field, mapped_operator, value)
            
            else:
                # 数值比较条件
                field_name = groups[0].strip()
                operator = groups[1].strip()
                value_str = groups[2].strip()
                
                field = self._map_field_name(field_name)
                mapped_operator = self.operator_map.get(operator)
                
                if field and mapped_operator:
                    # 处理百分比
                    if '%' in pattern:
                        value = float(value_str)
                    else:
                        value = float(value_str)
                    
                    return AlertCondition(field, mapped_operator, value)
        
        except Exception as e:
            logger.error(f"Error creating condition from match: {str(e)}")
        
        return None
    
    def _map_field_name(self, field_name: str) -> Optional[str]:
        """映射字段名称"""
        field_name = field_name.strip()
        return self.field_map.get(field_name, field_name)
    
    def generate_dsl_template(self, alert_type: AlertType) -> str:
        """生成DSL模板"""
        templates = {
            AlertType.PRICE_OPPORTUNITY: """
规则名称: 价格机会预警
描述: 当价格下跌超过阈值时触发买入机会预警
预警类型: 价格机会
严重性: 中
条件:
  价格变化率 < -15%
  并且 交易量 > 10
冷却时间: 120分钟
""",
            AlertType.PRICE_RISK: """
规则名称: 价格风险预警  
描述: 当持仓价格大幅下跌时触发风险预警
预警类型: 价格风险
严重性: 高
条件:
  价格变化率 < -20%
  并且 持仓数量 > 0
冷却时间: 60分钟
""",
            AlertType.VOLUME_SPIKE: """
规则名称: 交易量激增预警
描述: 当交易量异常增长时触发
预警类型: 交易量激增
严重性: 中
条件:
  交易量变化率 > 300%
冷却时间: 180分钟
""",
            AlertType.PORTFOLIO_REBALANCE: """
规则名称: 投资组合再平衡预警
描述: 当投资组合偏离目标配置时触发
预警类型: 投资组合再平衡
严重性: 低
条件:
  投资组合偏离 > 10%
冷却时间: 1440分钟
"""
        }
        
        return templates.get(alert_type, "# 自定义预警规则模板\n规则名称: \n描述: \n条件: \n")
    
    def validate_dsl(self, dsl_text: str) -> Dict[str, Any]:
        """验证DSL语法"""
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            parse_result = self.parse_rule(dsl_text)
            
            if not parse_result.success:
                result['valid'] = False
                result['errors'].append(parse_result.error)
            else:
                rule = parse_result.rule
                
                # 检查规则完整性
                if not rule.name:
                    result['warnings'].append("建议设置规则名称")
                
                if not rule.conditions:
                    result['valid'] = False
                    result['errors'].append("至少需要一个预警条件")
                
                # 检查条件合理性
                for condition in rule.conditions:
                    if condition.field not in self.field_map.values():
                        result['warnings'].append(f"字段 '{condition.field}' 可能不存在")
        
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"语法错误: {str(e)}")
        
        return result


# 全局DSL解析器实例
_dsl_parser: Optional[AlertDSLParser] = None


def get_dsl_parser() -> AlertDSLParser:
    """获取全局DSL解析器实例"""
    global _dsl_parser
    if _dsl_parser is None:
        _dsl_parser = AlertDSLParser()
    return _dsl_parser


# 便捷函数
def parse_alert_rule(dsl_text: str) -> DSLParseResult:
    """解析预警规则DSL"""
    parser = get_dsl_parser()
    return parser.parse_rule(dsl_text)


def validate_alert_dsl(dsl_text: str) -> Dict[str, Any]:
    """验证预警规则DSL"""
    parser = get_dsl_parser()
    return parser.validate_dsl(dsl_text)


def get_dsl_template(alert_type: AlertType) -> str:
    """获取DSL模板"""
    parser = get_dsl_parser()
    return parser.generate_dsl_template(alert_type)
