"""
Ares系统指标收集器
收集业务指标、技术指标和性能指标，支持实时监控和历史分析
"""

import time
import threading
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
import json
import logging

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"         # 计数器（只增不减）
    GAUGE = "gauge"            # 仪表（可增可减）
    HISTOGRAM = "histogram"     # 直方图（分布统计）
    TIMER = "timer"            # 计时器（时间统计）


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    value: Union[int, float]
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp,
            'value': self.value,
            'tags': self.tags
        }


@dataclass
class MetricSummary:
    """指标摘要统计"""
    count: int = 0
    sum: float = 0.0
    min: float = float('inf')
    max: float = float('-inf')
    avg: float = 0.0
    last_value: float = 0.0
    last_timestamp: float = 0.0
    
    def update(self, value: float, timestamp: float):
        """更新统计信息"""
        self.count += 1
        self.sum += value
        self.min = min(self.min, value)
        self.max = max(self.max, value)
        self.avg = self.sum / self.count
        self.last_value = value
        self.last_timestamp = timestamp
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'count': self.count,
            'sum': self.sum,
            'min': self.min if self.min != float('inf') else 0,
            'max': self.max if self.max != float('-inf') else 0,
            'avg': self.avg,
            'last_value': self.last_value,
            'last_timestamp': self.last_timestamp
        }


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, retention_seconds: int = 3600, max_points_per_metric: int = 1000):
        """
        初始化指标收集器
        
        Args:
            retention_seconds: 数据保留时间（秒）
            max_points_per_metric: 每个指标的最大数据点数
        """
        self.retention_seconds = retention_seconds
        self.max_points_per_metric = max_points_per_metric
        
        # 存储不同类型的指标
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = defaultdict(float)
        self.histograms: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_points_per_metric))
        self.timers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_points_per_metric))
        
        # 指标摘要统计
        self.summaries: Dict[str, MetricSummary] = defaultdict(MetricSummary)
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 启动清理线程
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """启动数据清理线程"""
        def cleanup_worker():
            while True:
                try:
                    self._cleanup_old_data()
                    time.sleep(300)  # 每5分钟清理一次
                except Exception as e:
                    logger.error(f"Metrics cleanup error: {str(e)}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        cutoff_time = time.time() - self.retention_seconds
        
        with self.lock:
            # 清理直方图数据
            for metric_name, points in self.histograms.items():
                while points and points[0].timestamp < cutoff_time:
                    points.popleft()
            
            # 清理计时器数据
            for metric_name, points in self.timers.items():
                while points and points[0].timestamp < cutoff_time:
                    points.popleft()
    
    def increment_counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None):
        """增加计数器"""
        with self.lock:
            metric_key = self._get_metric_key(name, tags)
            self.counters[metric_key] += value
            
            # 更新摘要统计
            timestamp = time.time()
            self.summaries[metric_key].update(self.counters[metric_key], timestamp)
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """设置仪表值"""
        with self.lock:
            metric_key = self._get_metric_key(name, tags)
            self.gauges[metric_key] = value
            
            # 更新摘要统计
            timestamp = time.time()
            self.summaries[metric_key].update(value, timestamp)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录直方图数据"""
        with self.lock:
            metric_key = self._get_metric_key(name, tags)
            timestamp = time.time()
            
            point = MetricPoint(timestamp=timestamp, value=value, tags=tags or {})
            self.histograms[metric_key].append(point)
            
            # 更新摘要统计
            self.summaries[metric_key].update(value, timestamp)
    
    def record_timer(self, name: str, duration: float, tags: Dict[str, str] = None):
        """记录计时器数据"""
        with self.lock:
            metric_key = self._get_metric_key(name, tags)
            timestamp = time.time()
            
            point = MetricPoint(timestamp=timestamp, value=duration, tags=tags or {})
            self.timers[metric_key].append(point)
            
            # 更新摘要统计
            self.summaries[metric_key].update(duration, timestamp)
    
    def _get_metric_key(self, name: str, tags: Dict[str, str] = None) -> str:
        """生成指标键"""
        if not tags:
            return name
        
        # 按键排序确保一致性
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"
    
    def get_counter(self, name: str, tags: Dict[str, str] = None) -> float:
        """获取计数器值"""
        with self.lock:
            metric_key = self._get_metric_key(name, tags)
            return self.counters.get(metric_key, 0.0)
    
    def get_gauge(self, name: str, tags: Dict[str, str] = None) -> float:
        """获取仪表值"""
        with self.lock:
            metric_key = self._get_metric_key(name, tags)
            return self.gauges.get(metric_key, 0.0)
    
    def get_histogram_stats(self, name: str, tags: Dict[str, str] = None) -> Dict[str, Any]:
        """获取直方图统计"""
        with self.lock:
            metric_key = self._get_metric_key(name, tags)
            points = self.histograms.get(metric_key, deque())
            
            if not points:
                return {'count': 0, 'min': 0, 'max': 0, 'avg': 0, 'p50': 0, 'p95': 0, 'p99': 0}
            
            values = [p.value for p in points]
            values.sort()
            
            count = len(values)
            return {
                'count': count,
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / count,
                'p50': values[int(count * 0.5)] if count > 0 else 0,
                'p95': values[int(count * 0.95)] if count > 0 else 0,
                'p99': values[int(count * 0.99)] if count > 0 else 0
            }
    
    def get_timer_stats(self, name: str, tags: Dict[str, str] = None) -> Dict[str, Any]:
        """获取计时器统计"""
        return self.get_histogram_stats(name, tags)  # 计时器和直方图统计相同
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        with self.lock:
            return {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histograms': {
                    name: [point.to_dict() for point in points]
                    for name, points in self.histograms.items()
                },
                'timers': {
                    name: [point.to_dict() for point in points]
                    for name, points in self.timers.items()
                },
                'summaries': {
                    name: summary.to_dict()
                    for name, summary in self.summaries.items()
                }
            }
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self.lock:
            return {
                'total_counters': len(self.counters),
                'total_gauges': len(self.gauges),
                'total_histograms': len(self.histograms),
                'total_timers': len(self.timers),
                'data_points': sum(len(points) for points in self.histograms.values()) +
                              sum(len(points) for points in self.timers.values()),
                'last_update': max(
                    (summary.last_timestamp for summary in self.summaries.values()),
                    default=0
                )
            }
    
    def reset_metrics(self):
        """重置所有指标"""
        with self.lock:
            self.counters.clear()
            self.gauges.clear()
            self.histograms.clear()
            self.timers.clear()
            self.summaries.clear()
            logger.info("All metrics reset")
    
    def export_metrics(self, format: str = "json") -> str:
        """导出指标数据"""
        metrics_data = self.get_all_metrics()
        
        if format.lower() == "json":
            return json.dumps(metrics_data, indent=2, default=str)
        elif format.lower() == "prometheus":
            return self._export_prometheus_format(metrics_data)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def _export_prometheus_format(self, metrics_data: Dict[str, Any]) -> str:
        """导出Prometheus格式"""
        lines = []
        
        # 导出计数器
        for name, value in metrics_data['counters'].items():
            lines.append(f"# TYPE {name} counter")
            lines.append(f"{name} {value}")
        
        # 导出仪表
        for name, value in metrics_data['gauges'].items():
            lines.append(f"# TYPE {name} gauge")
            lines.append(f"{name} {value}")
        
        return "\n".join(lines)


class TimerContext:
    """计时器上下文管理器"""
    
    def __init__(self, collector: MetricsCollector, name: str, tags: Dict[str, str] = None):
        self.collector = collector
        self.name = name
        self.tags = tags
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.collector.record_timer(self.name, duration, self.tags)


# 业务指标收集器
class BusinessMetricsCollector:
    """业务指标收集器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
    
    def record_api_call(self, endpoint: str, success: bool, duration: float):
        """记录API调用指标"""
        tags = {'endpoint': endpoint, 'success': str(success)}
        
        self.metrics.increment_counter('api_calls_total', tags=tags)
        self.metrics.record_timer('api_call_duration', duration, tags=tags)
        
        if success:
            self.metrics.increment_counter('api_calls_success', tags={'endpoint': endpoint})
        else:
            self.metrics.increment_counter('api_calls_error', tags={'endpoint': endpoint})
    
    def record_database_operation(self, operation: str, duration: float, rows_affected: int = 0):
        """记录数据库操作指标"""
        tags = {'operation': operation}
        
        self.metrics.increment_counter('db_operations_total', tags=tags)
        self.metrics.record_timer('db_operation_duration', duration, tags=tags)
        self.metrics.record_histogram('db_rows_affected', rows_affected, tags=tags)
    
    def record_item_discovery(self, source: str, count: int):
        """记录饰品发现指标"""
        tags = {'source': source}
        
        self.metrics.increment_counter('items_discovered', count, tags=tags)
        self.metrics.set_gauge('discovery_batch_size', count, tags=tags)
    
    def record_price_update(self, pool_type: str, count: int, duration: float):
        """记录价格更新指标"""
        tags = {'pool_type': pool_type}
        
        self.metrics.increment_counter('price_updates_total', count, tags=tags)
        self.metrics.record_timer('price_update_duration', duration, tags=tags)
        self.metrics.set_gauge('price_update_batch_size', count, tags=tags)
    
    def record_user_action(self, action: str, item_id: str = None):
        """记录用户操作指标"""
        tags = {'action': action}
        if item_id:
            tags['item_id'] = item_id
        
        self.metrics.increment_counter('user_actions_total', tags=tags)
    
    def record_alert_triggered(self, alert_type: str, severity: str):
        """记录预警触发指标"""
        tags = {'alert_type': alert_type, 'severity': severity}
        
        self.metrics.increment_counter('alerts_triggered', tags=tags)
    
    def update_portfolio_metrics(self, total_value: float, item_count: int, profit_loss: float):
        """更新投资组合指标"""
        self.metrics.set_gauge('portfolio_total_value', total_value)
        self.metrics.set_gauge('portfolio_item_count', item_count)
        self.metrics.set_gauge('portfolio_profit_loss', profit_loss)


# 全局指标收集器实例
_metrics_collector: Optional[MetricsCollector] = None
_business_metrics: Optional[BusinessMetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """获取全局指标收集器实例"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def get_business_metrics() -> BusinessMetricsCollector:
    """获取业务指标收集器实例"""
    global _business_metrics
    if _business_metrics is None:
        _business_metrics = BusinessMetricsCollector(get_metrics_collector())
    return _business_metrics


def timer(name: str, tags: Dict[str, str] = None) -> TimerContext:
    """创建计时器上下文管理器"""
    return TimerContext(get_metrics_collector(), name, tags)
