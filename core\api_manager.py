"""
Ares系统API管理模块
实现令牌桶限流算法、指数退避重试策略和API调用监控
"""

import time
import asyncio
import aiohttp
import logging
from typing import Optional, Dict, Any, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
from cryptography.fernet import Fernet

from .exceptions import (
    APILimitExceeded, APIConnectionError, APIResponseError,
    ErrorContext, ErrorSeverity
)


class APICallResult(Enum):
    """API调用结果状态"""
    SUCCESS = "success"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    TIMEOUT = "timeout"
    RETRY_EXHAUSTED = "retry_exhausted"


@dataclass
class APIResponse:
    """API响应数据结构"""
    status: APICallResult
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    retry_after: Optional[int] = None
    response_time: Optional[float] = None
    attempts: int = 1


class TokenBucket:
    """令牌桶限流算法实现"""
    
    def __init__(self, capacity: int = 10, refill_rate: float = 10/60):
        """
        初始化令牌桶
        
        Args:
            capacity: 桶容量（最大令牌数）
            refill_rate: 令牌补充速率（令牌/秒）
        """
        self.capacity = capacity
        self.tokens = float(capacity)
        self.refill_rate = refill_rate  # tokens per second
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> bool:
        """
        获取令牌
        
        Args:
            tokens: 需要的令牌数量
            
        Returns:
            bool: 是否成功获取令牌
        """
        async with self._lock:
            now = time.time()
            # 计算需要添加的令牌数
            elapsed = now - self.last_refill
            self.tokens = min(self.capacity, self.tokens + elapsed * self.refill_rate)
            self.last_refill = now
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    async def wait_for_token(self, tokens: int = 1, timeout: float = 60.0) -> bool:
        """
        等待令牌可用
        
        Args:
            tokens: 需要的令牌数量
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否在超时前获取到令牌
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if await self.acquire(tokens):
                return True
            # 计算下一个令牌的等待时间
            wait_time = min(1.0 / self.refill_rate, timeout - (time.time() - start_time))
            if wait_time > 0:
                await asyncio.sleep(wait_time)
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取令牌桶状态"""
        return {
            'capacity': self.capacity,
            'current_tokens': self.tokens,
            'refill_rate': self.refill_rate,
            'last_refill': self.last_refill
        }


class ExponentialBackoff:
    """指数退避重试策略"""
    
    def __init__(self, base_delay: float = 1.0, max_delay: float = 60.0, max_retries: int = 3):
        """
        初始化指数退避策略
        
        Args:
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            max_retries: 最大重试次数
        """
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.max_retries = max_retries
    
    def get_delay(self, attempt: int) -> float:
        """
        计算重试延迟时间
        
        Args:
            attempt: 当前重试次数（从0开始）
            
        Returns:
            float: 延迟时间（秒）
        """
        delay = self.base_delay * (2 ** attempt)
        return min(delay, self.max_delay)
    
    async def execute_with_retry(self, func, *args, **kwargs):
        """
        执行函数并在失败时重试
        
        Args:
            func: 要执行的异步函数
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    delay = self.get_delay(attempt)
                    await asyncio.sleep(delay)
                else:
                    break
        
        raise last_exception


class APIMetrics:
    """API调用指标收集器"""
    
    def __init__(self):
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.rate_limited_calls = 0
        self.total_response_time = 0.0
        self.last_call_time = None
        self.daily_calls = {}
        self._lock = asyncio.Lock()
    
    async def record_call(self, result: APICallResult, response_time: float = 0.0):
        """记录API调用指标"""
        async with self._lock:
            self.total_calls += 1
            self.total_response_time += response_time
            self.last_call_time = datetime.utcnow()
            
            # 记录每日调用次数
            today = datetime.utcnow().date().isoformat()
            self.daily_calls[today] = self.daily_calls.get(today, 0) + 1
            
            if result == APICallResult.SUCCESS:
                self.successful_calls += 1
            elif result == APICallResult.RATE_LIMITED:
                self.rate_limited_calls += 1
            else:
                self.failed_calls += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标摘要"""
        avg_response_time = (
            self.total_response_time / self.total_calls 
            if self.total_calls > 0 else 0.0
        )
        
        success_rate = (
            self.successful_calls / self.total_calls 
            if self.total_calls > 0 else 0.0
        )
        
        today = datetime.utcnow().date().isoformat()
        today_calls = self.daily_calls.get(today, 0)
        
        return {
            'total_calls': self.total_calls,
            'successful_calls': self.successful_calls,
            'failed_calls': self.failed_calls,
            'rate_limited_calls': self.rate_limited_calls,
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'last_call_time': self.last_call_time.isoformat() if self.last_call_time else None,
            'today_calls': today_calls,
            'daily_limit_remaining': max(0, 14400 - today_calls)  # 每日14400次限制
        }


class APIManager:
    """统一的API调用管理器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化API管理器

        Args:
            config: 配置字典，包含API密钥、基础URL等信息
        """
        self.base_url = config.get('base_url', 'https://api.steamdt.com')
        self.api_key = config.get('api_key')
        self.encryption_key = config.get('encryption_key')

        # 初始化限流器和重试策略
        rate_limit_config = config.get('rate_limit', {})
        self.bucket = TokenBucket(
            capacity=rate_limit_config.get('calls_per_minute', 10),
            refill_rate=rate_limit_config.get('calls_per_minute', 10) / 60
        )

        self.backoff = ExponentialBackoff(
            base_delay=rate_limit_config.get('retry_base_delay', 1.0),
            max_delay=rate_limit_config.get('retry_max_delay', 60.0),
            max_retries=rate_limit_config.get('retry_attempts', 3)
        )

        # 初始化指标收集器和会话
        self.metrics = APIMetrics()
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger(__name__)

        # 加密工具
        if self.encryption_key:
            self.cipher = Fernet(self.encryption_key.encode())

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()

    async def start_session(self):
        """启动HTTP会话"""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={'User-Agent': 'Ares-Investment-System/1.0'}
            )

    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None

    def encrypt_api_key(self, api_key: str) -> str:
        """加密API密钥"""
        if not self.cipher:
            raise ConfigurationError("Encryption key not configured")
        return self.cipher.encrypt(api_key.encode()).decode()

    def decrypt_api_key(self, encrypted_key: str) -> str:
        """解密API密钥"""
        if not self.cipher:
            raise ConfigurationError("Encryption key not configured")
        return self.cipher.decrypt(encrypted_key.encode()).decode()

    async def call_api(
        self,
        endpoint: str,
        method: str = 'GET',
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: float = 30.0
    ) -> APIResponse:
        """
        执行API调用

        Args:
            endpoint: API端点
            method: HTTP方法
            params: URL参数
            data: 请求体数据
            headers: 请求头
            timeout: 超时时间

        Returns:
            APIResponse: API响应结果
        """
        if not self.session:
            await self.start_session()

        # 等待令牌
        if not await self.bucket.wait_for_token(timeout=timeout):
            await self.metrics.record_call(APICallResult.RATE_LIMITED)
            return APIResponse(
                status=APICallResult.RATE_LIMITED,
                error_message="Rate limit exceeded, no tokens available"
            )

        # 准备请求
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        request_headers = {'Authorization': f'Bearer {self.api_key}'}
        if headers:
            request_headers.update(headers)

        # 执行重试逻辑
        start_time = time.time()
        attempts = 0

        async def _make_request():
            nonlocal attempts
            attempts += 1

            try:
                async with self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data,
                    headers=request_headers,
                    timeout=aiohttp.ClientTimeout(total=timeout)
                ) as response:
                    response_time = time.time() - start_time

                    if response.status == 429:  # Rate limited
                        retry_after = int(response.headers.get('Retry-After', 60))
                        raise APILimitExceeded(
                            f"Rate limit exceeded, retry after {retry_after} seconds",
                            context=ErrorContext(operation=f"{method} {endpoint}")
                        )

                    if response.status >= 400:
                        error_text = await response.text()
                        raise APIResponseError(
                            f"API error {response.status}: {error_text}",
                            context=ErrorContext(operation=f"{method} {endpoint}")
                        )

                    response_data = await response.json()
                    await self.metrics.record_call(APICallResult.SUCCESS, response_time)

                    return APIResponse(
                        status=APICallResult.SUCCESS,
                        data=response_data,
                        response_time=response_time,
                        attempts=attempts
                    )

            except asyncio.TimeoutError:
                await self.metrics.record_call(APICallResult.TIMEOUT)
                raise APIConnectionError(
                    f"Request timeout for {endpoint}",
                    context=ErrorContext(operation=f"{method} {endpoint}")
                )
            except aiohttp.ClientError as e:
                await self.metrics.record_call(APICallResult.ERROR)
                raise APIConnectionError(
                    f"Connection error: {str(e)}",
                    context=ErrorContext(operation=f"{method} {endpoint}")
                )

        try:
            return await self.backoff.execute_with_retry(_make_request)
        except Exception as e:
            response_time = time.time() - start_time
            await self.metrics.record_call(APICallResult.RETRY_EXHAUSTED, response_time)

            return APIResponse(
                status=APICallResult.RETRY_EXHAUSTED,
                error_message=str(e),
                response_time=response_time,
                attempts=attempts
            )

    def get_metrics(self) -> Dict[str, Any]:
        """获取API调用指标"""
        bucket_status = self.bucket.get_status()
        api_metrics = self.metrics.get_metrics()

        return {
            'bucket_status': bucket_status,
            'api_metrics': api_metrics,
            'session_active': self.session is not None
        }
