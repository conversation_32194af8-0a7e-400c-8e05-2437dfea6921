"""
CS2宏观数据采集器
基于SteamDT宏观数据收集CS2市场指标，帮助判断市场整体趋势和投资时机
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import json

from core.config import get_config_manager


@dataclass
class CS2PlayerData:
    """CS2玩家数据"""
    timestamp: datetime
    player_count: int
    peak_24h: Optional[int] = None
    avg_24h: Optional[float] = None
    trend_7d: Optional[float] = None
    trend_30d: Optional[float] = None
    data_source: str = "steamdt"


@dataclass
class CS2MacroIndicators:
    """CS2宏观指标"""
    current_players: int
    player_trend_24h: float  # 24小时变化百分比
    player_trend_7d: float   # 7天变化百分比
    market_heat_index: float  # 市场热度指数 (0-100)
    peak_hours_ratio: float   # 高峰时段比例
    weekend_effect: float     # 周末效应
    update_time: datetime
    confidence_score: float   # 数据置信度 (0-100)


class CS2MacroDataCollector:
    """CS2宏观数据采集器"""
    
    def __init__(self):
        """初始化CS2宏观数据采集器"""
        self.config_manager = get_config_manager()
        self.logger = logging.getLogger(__name__)
        
        # 数据存储
        self.player_data_history: List[CS2PlayerData] = []
        self.max_history_days = 30

        # 从SteamDT获取宏观数据，无需频繁调用限制
        self.last_update = None
        
        # 缓存
        self.cached_indicators: Optional[CS2MacroIndicators] = None
        self.cache_expiry = 300  # 5分钟缓存
        self.cache_timestamp = 0
    
    async def collect_current_player_data(self) -> Optional[CS2PlayerData]:
        """从SteamDT宏观数据中获取CS2玩家数据"""
        try:
            # 注意：这里应该从SteamDT的宏观数据API获取玩家数据
            # 由于SteamDT包含在线玩家数据，我们直接使用这些数据
            # 实际实现时需要调用SteamDT的宏观数据接口

            # 模拟从SteamDT获取的玩家数据
            # 实际应该调用 steamdt_api.get_macro_data() 或类似接口
            player_count = await self._get_player_data_from_steamdt()

            if player_count is not None:
                # 创建玩家数据对象
                player_data = CS2PlayerData(
                    timestamp=datetime.now(),
                    player_count=player_count,
                    data_source="steamdt"
                )

                # 计算24小时统计
                await self._calculate_24h_stats(player_data)

                # 添加到历史数据
                self._add_to_history(player_data)

                # 更新最后更新时间
                self.last_update = datetime.now()

                self.logger.info(f"成功从SteamDT获取CS2玩家数据: {player_count} 人在线")
                return player_data

            else:
                self.logger.error("无法从SteamDT获取CS2玩家数据")
                return None

        except Exception as e:
            self.logger.error(f"收集CS2玩家数据时出错: {e}")
            return None
    
    async def _get_player_data_from_steamdt(self) -> Optional[int]:
        """从SteamDT宏观数据获取玩家数"""
        try:
            # TODO: 实际实现时需要调用SteamDT的宏观数据API
            # 这里应该集成steamdt_api模块来获取宏观数据
            # 包括在线玩家数、大盘指数等信息

            # 临时返回模拟数据，实际应该从SteamDT API获取
            # 示例：steamdt_api.get_macro_data()

            self.logger.info("从SteamDT获取宏观数据...")

            # 模拟数据 - 实际实现时替换为真实API调用
            # 可以参考steamdt.com首页显示的在线玩家数
            import random
            simulated_player_count = random.randint(800000, 1000000)

            self.logger.info(f"SteamDT数据获取成功 (模拟数据): {simulated_player_count}")
            return simulated_player_count

        except Exception as e:
            self.logger.error(f"从SteamDT获取数据异常: {e}")
            return None
    
    async def _calculate_24h_stats(self, player_data: CS2PlayerData):
        """计算24小时统计数据"""
        try:
            # 获取24小时内的历史数据
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_data = [
                data for data in self.player_data_history 
                if data.timestamp >= cutoff_time
            ]
            
            if recent_data:
                player_counts = [data.player_count for data in recent_data]
                player_data.peak_24h = max(player_counts)
                player_data.avg_24h = sum(player_counts) / len(player_counts)
                
                # 计算7天趋势
                week_cutoff = datetime.now() - timedelta(days=7)
                week_data = [
                    data for data in self.player_data_history 
                    if data.timestamp >= week_cutoff
                ]
                
                if len(week_data) >= 2:
                    recent_avg = sum(data.player_count for data in week_data[-24:]) / min(24, len(week_data))
                    old_avg = sum(data.player_count for data in week_data[:24]) / min(24, len(week_data))
                    if old_avg > 0:
                        player_data.trend_7d = ((recent_avg - old_avg) / old_avg) * 100
                
        except Exception as e:
            self.logger.error(f"计算24小时统计时出错: {e}")
    
    def _add_to_history(self, player_data: CS2PlayerData):
        """添加数据到历史记录"""
        self.player_data_history.append(player_data)
        
        # 清理过期数据
        cutoff_time = datetime.now() - timedelta(days=self.max_history_days)
        self.player_data_history = [
            data for data in self.player_data_history 
            if data.timestamp >= cutoff_time
        ]
        
        # 按时间排序
        self.player_data_history.sort(key=lambda x: x.timestamp)
    
    async def get_macro_indicators(self, force_refresh: bool = False) -> Optional[CS2MacroIndicators]:
        """获取CS2宏观指标"""
        try:
            # 检查缓存
            current_time = time.time()
            if (not force_refresh and 
                self.cached_indicators and 
                current_time - self.cache_timestamp < self.cache_expiry):
                return self.cached_indicators
            
            # 确保有足够的历史数据
            if len(self.player_data_history) < 2:
                await self.collect_current_player_data()
                if len(self.player_data_history) < 2:
                    return None
            
            # 获取最新数据
            latest_data = self.player_data_history[-1]
            current_players = latest_data.player_count
            
            # 计算趋势
            player_trend_24h = self._calculate_trend(hours=24)
            player_trend_7d = self._calculate_trend(days=7)
            
            # 计算市场热度指数
            market_heat_index = self._calculate_market_heat_index()
            
            # 计算高峰时段比例
            peak_hours_ratio = self._calculate_peak_hours_ratio()
            
            # 计算周末效应
            weekend_effect = self._calculate_weekend_effect()
            
            # 计算置信度
            confidence_score = self._calculate_confidence_score()
            
            # 创建宏观指标对象
            indicators = CS2MacroIndicators(
                current_players=current_players,
                player_trend_24h=player_trend_24h,
                player_trend_7d=player_trend_7d,
                market_heat_index=market_heat_index,
                peak_hours_ratio=peak_hours_ratio,
                weekend_effect=weekend_effect,
                update_time=datetime.now(),
                confidence_score=confidence_score
            )
            
            # 更新缓存
            self.cached_indicators = indicators
            self.cache_timestamp = current_time
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"获取宏观指标时出错: {e}")
            return None
    
    def _calculate_trend(self, hours: int = None, days: int = None) -> float:
        """计算趋势百分比"""
        try:
            if hours:
                cutoff_time = datetime.now() - timedelta(hours=hours)
            elif days:
                cutoff_time = datetime.now() - timedelta(days=days)
            else:
                return 0.0
            
            # 获取时间段内的数据
            period_data = [
                data for data in self.player_data_history 
                if data.timestamp >= cutoff_time
            ]
            
            if len(period_data) < 2:
                return 0.0
            
            # 计算开始和结束的平均值
            mid_point = len(period_data) // 2
            start_avg = sum(data.player_count for data in period_data[:mid_point]) / mid_point
            end_avg = sum(data.player_count for data in period_data[mid_point:]) / (len(period_data) - mid_point)
            
            if start_avg > 0:
                return ((end_avg - start_avg) / start_avg) * 100
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"计算趋势时出错: {e}")
            return 0.0
    
    def _calculate_market_heat_index(self) -> float:
        """计算市场热度指数 (0-100)"""
        try:
            if not self.player_data_history:
                return 50.0  # 默认中等热度
            
            # 获取最近24小时数据
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_data = [
                data for data in self.player_data_history 
                if data.timestamp >= cutoff_time
            ]
            
            if not recent_data:
                return 50.0
            
            current_players = recent_data[-1].player_count
            avg_players = sum(data.player_count for data in recent_data) / len(recent_data)
            max_players = max(data.player_count for data in recent_data)
            
            # 基于当前玩家数相对于平均值和峰值的位置计算热度
            if max_players > avg_players:
                heat_ratio = (current_players - avg_players) / (max_players - avg_players)
                heat_index = 50 + (heat_ratio * 50)  # 50-100范围
            else:
                heat_index = 50.0
            
            return max(0.0, min(100.0, heat_index))
            
        except Exception as e:
            self.logger.error(f"计算市场热度指数时出错: {e}")
            return 50.0
    
    def _calculate_peak_hours_ratio(self) -> float:
        """计算高峰时段比例"""
        try:
            # 获取最近7天数据
            cutoff_time = datetime.now() - timedelta(days=7)
            week_data = [
                data for data in self.player_data_history 
                if data.timestamp >= cutoff_time
            ]
            
            if len(week_data) < 24:  # 至少需要24小时数据
                return 0.5  # 默认50%
            
            # 计算每小时的平均玩家数
            hourly_avg = {}
            for data in week_data:
                hour = data.timestamp.hour
                if hour not in hourly_avg:
                    hourly_avg[hour] = []
                hourly_avg[hour].append(data.player_count)
            
            # 计算每小时平均值
            hourly_means = {
                hour: sum(counts) / len(counts) 
                for hour, counts in hourly_avg.items()
            }
            
            if not hourly_means:
                return 0.5
            
            # 找出高峰时段（高于平均值的时段）
            overall_avg = sum(hourly_means.values()) / len(hourly_means)
            peak_hours = sum(1 for avg in hourly_means.values() if avg > overall_avg)
            
            return peak_hours / 24.0
            
        except Exception as e:
            self.logger.error(f"计算高峰时段比例时出错: {e}")
            return 0.5
    
    def _calculate_weekend_effect(self) -> float:
        """计算周末效应"""
        try:
            # 获取最近4周数据
            cutoff_time = datetime.now() - timedelta(weeks=4)
            month_data = [
                data for data in self.player_data_history 
                if data.timestamp >= cutoff_time
            ]
            
            if len(month_data) < 14:  # 至少需要2周数据
                return 0.0
            
            # 分离工作日和周末数据
            weekday_counts = []
            weekend_counts = []
            
            for data in month_data:
                if data.timestamp.weekday() < 5:  # 周一到周五
                    weekday_counts.append(data.player_count)
                else:  # 周六周日
                    weekend_counts.append(data.player_count)
            
            if not weekday_counts or not weekend_counts:
                return 0.0
            
            weekday_avg = sum(weekday_counts) / len(weekday_counts)
            weekend_avg = sum(weekend_counts) / len(weekend_counts)
            
            if weekday_avg > 0:
                return ((weekend_avg - weekday_avg) / weekday_avg) * 100
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"计算周末效应时出错: {e}")
            return 0.0
    
    def _calculate_confidence_score(self) -> float:
        """计算数据置信度"""
        try:
            score = 100.0
            
            # 基于数据量调整置信度
            data_count = len(self.player_data_history)
            if data_count < 24:  # 少于24小时数据
                score *= 0.5
            elif data_count < 168:  # 少于7天数据
                score *= 0.7
            elif data_count < 720:  # 少于30天数据
                score *= 0.9
            
            # 基于数据新鲜度调整
            if self.player_data_history:
                latest_time = self.player_data_history[-1].timestamp
                age_hours = (datetime.now() - latest_time).total_seconds() / 3600
                if age_hours > 1:  # 数据超过1小时
                    score *= max(0.3, 1 - (age_hours / 24))
            
            # 基于API调用成功率调整（简化版）
            # 这里可以根据实际的API调用成功率来调整
            
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            self.logger.error(f"计算置信度时出错: {e}")
            return 50.0
    
    def get_historical_data(self, days: int = 7) -> List[CS2PlayerData]:
        """获取历史数据"""
        cutoff_time = datetime.now() - timedelta(days=days)
        return [
            data for data in self.player_data_history 
            if data.timestamp >= cutoff_time
        ]
    
    def export_data(self) -> Dict[str, Any]:
        """导出数据用于分析"""
        return {
            'player_data_history': [
                {
                    'timestamp': data.timestamp.isoformat(),
                    'player_count': data.player_count,
                    'peak_24h': data.peak_24h,
                    'avg_24h': data.avg_24h,
                    'trend_7d': data.trend_7d,
                    'data_source': data.data_source
                }
                for data in self.player_data_history
            ],
            'current_indicators': (
                {
                    'current_players': self.cached_indicators.current_players,
                    'player_trend_24h': self.cached_indicators.player_trend_24h,
                    'player_trend_7d': self.cached_indicators.player_trend_7d,
                    'market_heat_index': self.cached_indicators.market_heat_index,
                    'peak_hours_ratio': self.cached_indicators.peak_hours_ratio,
                    'weekend_effect': self.cached_indicators.weekend_effect,
                    'update_time': self.cached_indicators.update_time.isoformat(),
                    'confidence_score': self.cached_indicators.confidence_score
                }
                if self.cached_indicators else None
            )
        }


# 全局实例
_cs2_macro_collector = None


def get_cs2_macro_collector() -> CS2MacroDataCollector:
    """获取CS2宏观数据采集器实例"""
    global _cs2_macro_collector
    if _cs2_macro_collector is None:
        _cs2_macro_collector = CS2MacroDataCollector()
    return _cs2_macro_collector


async def collect_cs2_macro_data():
    """收集CS2宏观数据的便捷函数"""
    collector = get_cs2_macro_collector()
    return await collector.collect_current_player_data()


async def get_cs2_macro_indicators():
    """获取CS2宏观指标的便捷函数"""
    collector = get_cs2_macro_collector()
    return await collector.get_macro_indicators()
