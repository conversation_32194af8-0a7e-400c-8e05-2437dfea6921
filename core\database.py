"""
Ares系统数据库模型和管理器
使用SQLAlchemy ORM定义数据模型，优化查询性能
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Union, Set
from contextlib import contextmanager
from sqlalchemy import (
    create_engine, Column, Integer, String, Float, DateTime, Boolean, Text, JSON,
    Index, ForeignKey, UniqueConstraint, CheckConstraint, event, text, DECIMAL, BigInteger, Enum
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.pool import StaticPool
from sqlalchemy.sql import func

from .exceptions import DatabaseConnectionError, DataValidationError, ErrorContext

# 创建基础模型类
Base = declarative_base()

# 配置日志
logger = logging.getLogger(__name__)


class Item(Base):
    """饰品基本信息表"""
    __tablename__ = 'items'

    # 主键和基本信息 - 使用market_hash_name作为主键
    market_hash_name = Column(String(300), primary_key=True, comment="市场哈希名称，作为主键")
    name = Column(String(200), nullable=False, comment="饰品中文名称")
    category = Column(String(50), comment="饰品类别")
    rarity = Column(String(20), comment="稀有度")
    float_value = Column(Float, comment="磨损值")

    # CS2饰品特有字段
    weapon_type = Column(String(50), comment="武器类型：步枪、狙击枪、手枪、刀具等")
    skin_name = Column(String(200), comment="皮肤名称")
    wear_rating = Column(Float, comment="磨损度评级 (0.0-1.0)")
    float_min = Column(Float, comment="最小磨损值")
    float_max = Column(Float, comment="最大磨损值")
    quality = Column(String(20), comment="品质：普通、StatTrak、纪念品等")
    collection = Column(String(100), comment="收藏品系列")
    case_name = Column(String(100), comment="来源箱子名称")

    # 投资相关字段
    investment_score = Column(Float, comment="投资评分 (0-100)")
    popularity_score = Column(Float, comment="流行度评分 (0-100)")
    liquidity_score = Column(Float, comment="流动性评分 (0-100)")

    # 基础数据相关字段 (新增)
    platform_data = Column(JSON, comment="各平台itemId映射，JSON格式存储")
    data_source = Column(String(20), default='steamdt', comment="数据来源")
    last_sync_time = Column(DateTime, comment="最后同步时间")

    # 市场信息
    icon_url = Column(String(500), comment="图标URL")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 状态字段
    is_active = Column(Boolean, default=True, comment="是否活跃")
    
    # 关系定义
    prices = relationship("Price", back_populates="item", cascade="all, delete-orphan")
    price_summaries = relationship("ItemPriceSummary", back_populates="item", cascade="all, delete-orphan")
    avg_prices = relationship("ItemAvgPrice", back_populates="item", cascade="all, delete-orphan")
    watchlist_items = relationship("WatchlistItem", back_populates="item", cascade="all, delete-orphan")
    holdings = relationship("Holding", back_populates="item", cascade="all, delete-orphan")
    
    # 索引定义
    __table_args__ = (
        Index('idx_item_category_rarity', 'category', 'rarity'),
        Index('idx_item_updated_at', 'updated_at'),
        Index('idx_item_name', 'name'),
        Index('idx_item_data_source', 'data_source'),
        Index('idx_item_last_sync_time', 'last_sync_time'),
        # CS2特有字段索引
        Index('idx_item_weapon_type', 'weapon_type'),
        Index('idx_item_skin_name', 'skin_name'),
        Index('idx_item_wear_rating', 'wear_rating'),
        Index('idx_item_investment_score', 'investment_score'),
        Index('idx_item_weapon_rarity', 'weapon_type', 'rarity'),
        Index('idx_item_quality_collection', 'quality', 'collection'),
        {'comment': '饰品基本信息表'}
    )
    
    def __repr__(self):
        return f"<Item(market_hash_name='{self.market_hash_name}', name='{self.name}')>"


class Price(Base):
    """价格数据表"""
    __tablename__ = 'prices'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 外键关联
    item_id = Column(String(300), ForeignKey('items.market_hash_name'), nullable=False, comment="饰品市场哈希名称")
    
    # 平台和价格信息
    platform = Column(String(20), nullable=False, comment="交易平台")
    platform_item_id = Column(String(100), comment="平台饰品ID")
    ask_price = Column(Float, comment="卖方报价")
    bid_price = Column(Float, comment="买方报价")
    last_price = Column(Float, comment="最新成交价")

    # SteamDT API 价格字段
    steamdt_sell_price = Column(DECIMAL(10, 2), comment="SteamDT在售价格")
    steamdt_sell_count = Column(Integer, comment="SteamDT在售数量")
    steamdt_bidding_price = Column(DECIMAL(10, 2), comment="SteamDT求购价格")
    steamdt_bidding_count = Column(Integer, comment="SteamDT求购数量")
    steamdt_update_time = Column(BigInteger, comment="SteamDT数据更新时间戳")

    # 交易量信息
    volume_24h = Column(Integer, default=0, comment="24小时交易量")
    volume_7d = Column(Integer, default=0, comment="7天交易量")

    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, comment="数据时间戳")
    query_time = Column(DateTime, default=datetime.utcnow, comment="查询时间")

    # 数据分层和质量标记
    data_tier = Column(Enum('hot', 'warm', 'cold', name='data_tier_enum'), default='warm', comment="数据层级")
    cache_status = Column(Enum('cached', 'expired', 'missing', name='cache_status_enum'), default='missing', comment="缓存状态")
    data_quality = Column(String(10), default='good', comment="数据质量标记")
    
    # 关系定义
    item = relationship("Item", back_populates="prices")
    
    # 索引定义 - 关键性能优化
    __table_args__ = (
        Index('idx_price_item_timestamp', 'item_id', 'timestamp'),
        Index('idx_price_platform_timestamp', 'platform', 'timestamp'),
        Index('idx_price_timestamp', 'timestamp'),  # 用于数据清理
        Index('idx_price_item_platform', 'item_id', 'platform'),  # 用于最新价格查询
        Index('idx_price_steamdt_update_time', 'steamdt_update_time'),  # SteamDT数据时间索引
        Index('idx_price_data_tier', 'data_tier'),  # 数据分层索引
        Index('idx_price_cache_status', 'cache_status'),  # 缓存状态索引
        Index('idx_price_platform_item_id', 'platform', 'platform_item_id'),  # 平台饰品ID索引
        UniqueConstraint('item_id', 'platform', 'timestamp', name='uq_price_item_platform_time'),
        CheckConstraint('ask_price >= 0', name='ck_ask_price_positive'),
        CheckConstraint('bid_price >= 0', name='ck_bid_price_positive'),
        CheckConstraint('steamdt_sell_price >= 0', name='ck_steamdt_sell_price_positive'),
        CheckConstraint('steamdt_bidding_price >= 0', name='ck_steamdt_bidding_price_positive'),
        {'comment': '价格数据表 - 支持SteamDT API和分层存储'}
    )
    
    def __repr__(self):
        return f"<Price(item_id='{self.item_id}', platform='{self.platform}', ask_price={self.ask_price})>"


class ItemPriceSummary(Base):
    """价格历史汇总表"""
    __tablename__ = 'item_price_summary'

    # 复合主键
    item_id = Column(String(300), ForeignKey('items.market_hash_name'), primary_key=True, comment="饰品市场哈希名称")
    platform = Column(String(20), primary_key=True, comment="交易平台")
    date = Column(DateTime, primary_key=True, comment="汇总日期")

    # 价格统计
    open_price = Column(DECIMAL(10, 2), comment="开盘价")
    high_price = Column(DECIMAL(10, 2), comment="最高价")
    low_price = Column(DECIMAL(10, 2), comment="最低价")
    close_price = Column(DECIMAL(10, 2), comment="收盘价")
    avg_price = Column(DECIMAL(10, 2), comment="平均价格")

    # 交易量统计
    total_sell_count = Column(Integer, default=0, comment="总在售数量")
    total_bidding_count = Column(Integer, default=0, comment="总求购数量")

    # 价格分析
    price_volatility = Column(DECIMAL(8, 4), comment="价格波动率")
    price_trend = Column(Enum('rising', 'falling', 'stable', name='price_trend_enum'), comment="价格趋势")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系定义
    item = relationship("Item", back_populates="price_summaries")

    # 索引定义
    __table_args__ = (
        Index('idx_summary_item_date', 'item_id', 'date'),
        Index('idx_summary_platform_date', 'platform', 'date'),
        Index('idx_summary_date', 'date'),
        Index('idx_summary_price_trend', 'price_trend'),
        CheckConstraint('open_price >= 0', name='ck_summary_open_price_positive'),
        CheckConstraint('high_price >= 0', name='ck_summary_high_price_positive'),
        CheckConstraint('low_price >= 0', name='ck_summary_low_price_positive'),
        CheckConstraint('close_price >= 0', name='ck_summary_close_price_positive'),
        CheckConstraint('avg_price >= 0', name='ck_summary_avg_price_positive'),
        {'comment': '价格历史汇总表 - 用于快速查询和趋势分析'}
    )

    def __repr__(self):
        return f"<ItemPriceSummary(item_id='{self.item_id}', platform='{self.platform}', date='{self.date}')>"


class ItemAvgPrice(Base):
    """7天均价数据表"""
    __tablename__ = 'item_avg_prices'

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 外键关联
    item_id = Column(String(300), ForeignKey('items.market_hash_name'), nullable=False, comment="饰品市场哈希名称")

    # 平台信息
    platform = Column(String(20), nullable=False, comment="交易平台")

    # 均价数据
    avg_price_7d = Column(DECIMAL(10, 2), comment="7天平均价格")
    data_source = Column(String(50), default='steamdt', comment="数据来源")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系定义
    item = relationship("Item", back_populates="avg_prices")

    # 索引定义
    __table_args__ = (
        Index('idx_avg_price_item_platform', 'item_id', 'platform'),
        Index('idx_avg_price_created_at', 'created_at'),
        Index('idx_avg_price_data_source', 'data_source'),
        UniqueConstraint('item_id', 'platform', 'created_at', name='uq_avg_price_item_platform_time'),
        CheckConstraint('avg_price_7d >= 0', name='ck_avg_price_7d_positive'),
        {'comment': '7天均价数据表 - 存储SteamDT均价API数据'}
    )

    def __repr__(self):
        return f"<ItemAvgPrice(item_id='{self.item_id}', platform='{self.platform}', avg_price_7d={self.avg_price_7d})>"


class WatchlistItem(Base):
    """监控列表表"""
    __tablename__ = 'watchlist_items'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 外键关联
    item_id = Column(String(300), ForeignKey('items.market_hash_name'), nullable=False, comment="饰品市场哈希名称")
    
    # 监控配置
    pool_type = Column(String(20), nullable=False, comment="监控池类型: core/main")
    priority_score = Column(Float, default=0.0, comment="优先级评分")
    
    # 更新策略
    last_updated = Column(DateTime, comment="最后更新时间")
    update_frequency = Column(Integer, comment="更新频率(分钟)")
    next_update_time = Column(DateTime, comment="下次更新时间")
    
    # 用户配置
    user_added = Column(Boolean, default=False, comment="用户手动添加")
    user_priority = Column(Integer, default=0, comment="用户设置优先级")
    
    # 监控状态
    is_active = Column(Boolean, default=True, comment="是否活跃监控")
    failure_count = Column(Integer, default=0, comment="连续失败次数")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关系定义
    item = relationship("Item", back_populates="watchlist_items")
    
    # 索引定义 - 调度器性能优化
    __table_args__ = (
        Index('idx_watchlist_pool_priority', 'pool_type', 'priority_score'),
        Index('idx_watchlist_next_update', 'next_update_time'),
        Index('idx_watchlist_last_updated', 'last_updated'),
        Index('idx_watchlist_user_added', 'user_added'),
        UniqueConstraint('item_id', 'pool_type', name='uq_watchlist_item_pool'),
        CheckConstraint("pool_type IN ('core', 'main')", name='ck_pool_type_valid'),
        {'comment': '监控列表表'}
    )
    
    def __repr__(self):
        return f"<WatchlistItem(item_id='{self.item_id}', pool_type='{self.pool_type}', priority={self.priority_score})>"


class Holding(Base):
    """持仓记录表"""
    __tablename__ = 'holdings'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 外键关联
    item_id = Column(String(300), ForeignKey('items.market_hash_name'), nullable=False, comment="饰品市场哈希名称")
    
    # 持仓信息
    quantity = Column(Integer, nullable=False, comment="持仓数量")
    cost_basis = Column(Float, nullable=False, comment="成本基础")
    purchase_date = Column(DateTime, nullable=False, comment="购买日期")
    purchase_platform = Column(String(20), comment="购买平台")
    
    # 目标价格
    target_price = Column(Float, comment="目标价格")
    stop_loss_price = Column(Float, comment="止损价格")
    
    # 备注信息
    notes = Column(Text, comment="备注信息")
    purchase_reason = Column(String(100), comment="购买原因")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否活跃持仓")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关系定义
    item = relationship("Item", back_populates="holdings")
    
    # 索引定义
    __table_args__ = (
        Index('idx_holding_item_active', 'item_id', 'is_active'),
        Index('idx_holding_purchase_date', 'purchase_date'),
        Index('idx_holding_cost_basis', 'cost_basis'),
        CheckConstraint('quantity > 0', name='ck_quantity_positive'),
        CheckConstraint('cost_basis >= 0', name='ck_cost_basis_positive'),
        {'comment': '持仓记录表'}
    )
    
    def __repr__(self):
        return f"<Holding(item_id='{self.item_id}', quantity={self.quantity}, cost_basis={self.cost_basis})>"


class AlertRule(Base):
    """预警规则表"""
    __tablename__ = 'alert_rules'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 规则基本信息
    rule_name = Column(String(100), nullable=False, comment="规则名称")
    rule_type = Column(String(20), nullable=False, comment="规则类型")
    
    # 规则条件 (JSON格式存储)
    conditions = Column(Text, nullable=False, comment="触发条件(JSON)")
    actions = Column(Text, nullable=False, comment="执行动作(JSON)")
    
    # 规则状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    priority = Column(Integer, default=0, comment="优先级")
    
    # 执行统计
    trigger_count = Column(Integer, default=0, comment="触发次数")
    last_triggered = Column(DateTime, comment="最后触发时间")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 索引定义
    __table_args__ = (
        Index('idx_alert_rule_type_active', 'rule_type', 'is_active'),
        Index('idx_alert_priority', 'priority'),
        CheckConstraint("rule_type IN ('price_change', 'volume_spike', 'spread_opportunity', 'portfolio_risk')", 
                       name='ck_rule_type_valid'),
        {'comment': '预警规则表'}
    )


class MacroStat(Base):
    """宏观数据表"""
    __tablename__ = 'macro_stats'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 指标信息
    metric_name = Column(String(50), nullable=False, comment="指标名称")
    metric_value = Column(Float, nullable=False, comment="指标值")
    metric_unit = Column(String(20), comment="指标单位")
    
    # 数据来源
    data_source = Column(String(50), comment="数据来源")
    
    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, comment="数据时间戳")
    
    # 索引定义
    __table_args__ = (
        Index('idx_macro_metric_timestamp', 'metric_name', 'timestamp'),
        Index('idx_macro_timestamp', 'timestamp'),
        UniqueConstraint('metric_name', 'timestamp', name='uq_macro_metric_time'),
        {'comment': '宏观数据表'}
    )


# 数据库事件监听器
@event.listens_for(Price, 'before_insert')
def set_price_next_update_time(mapper, connection, target):
    """在插入价格数据前设置下次更新时间"""
    if target.timestamp:
        # 根据数据质量设置不同的更新间隔
        if target.data_quality == 'high':
            interval = timedelta(minutes=15)
        elif target.data_quality == 'medium':
            interval = timedelta(minutes=30)
        else:
            interval = timedelta(hours=1)
        
        # 这里可以添加更复杂的逻辑


@event.listens_for(WatchlistItem, 'before_insert')
@event.listens_for(WatchlistItem, 'before_update')
def calculate_next_update_time(mapper, connection, target):
    """计算下次更新时间"""
    if target.update_frequency and target.last_updated:
        target.next_update_time = target.last_updated + timedelta(minutes=target.update_frequency)
    elif target.pool_type == 'core':
        target.next_update_time = datetime.utcnow() + timedelta(minutes=30)
    elif target.pool_type == 'main':
        target.next_update_time = datetime.utcnow() + timedelta(minutes=240)


class DatabaseManager:
    """数据库管理器 - 提供连接池、会话管理和查询优化"""

    def __init__(self, database_url: str, **kwargs):
        """
        初始化数据库管理器

        Args:
            database_url: 数据库连接URL
            **kwargs: 额外的引擎配置参数
        """
        self.database_url = database_url

        # 默认引擎配置
        engine_config = {
            'pool_size': kwargs.get('pool_size', 20),
            'max_overflow': kwargs.get('max_overflow', 30),
            'pool_pre_ping': kwargs.get('pool_pre_ping', True),
            'pool_recycle': kwargs.get('pool_recycle', 3600),  # 1小时回收连接
            'echo': kwargs.get('echo', False),
            'future': True  # 使用SQLAlchemy 2.0风格
        }

        # 对SQLite进行特殊配置
        if database_url.startswith('sqlite'):
            engine_config = {
                'poolclass': StaticPool,
                'connect_args': {
                    'check_same_thread': False,
                    'timeout': 30,
                    'isolation_level': None  # 启用autocommit模式
                },
                'echo': kwargs.get('echo', False),
                'future': True
            }

        try:
            self.engine = create_engine(database_url, **engine_config)
            self.SessionLocal = sessionmaker(bind=self.engine, expire_on_commit=False)
            logger.info(f"数据库连接已建立: {database_url}")
        except Exception as e:
            raise DatabaseConnectionError(
                f"数据库连接失败: {str(e)}",
                context=ErrorContext(additional_data={'database_url': database_url})
            )

    def create_tables(self):
        """创建所有数据表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            raise DatabaseConnectionError(
                f"数据库表创建失败: {str(e)}",
                context=ErrorContext(operation="create_tables")
            )

    def drop_tables(self):
        """删除所有数据表 - 谨慎使用"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.warning("数据库表已删除")
        except Exception as e:
            raise DatabaseConnectionError(
                f"数据库表删除失败: {str(e)}",
                context=ErrorContext(operation="drop_tables")
            )

    @contextmanager
    def get_session(self):
        """获取数据库会话的上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库会话错误: {str(e)}")
            raise
        finally:
            session.close()

    def get_session_direct(self) -> Session:
        """直接获取数据库会话 - 需要手动管理"""
        return self.SessionLocal()

    def health_check(self) -> Dict[str, Any]:
        """数据库健康检查"""
        try:
            with self.get_session() as session:
                # 执行简单查询测试连接
                result = session.execute(text("SELECT 1")).scalar()

                # 获取表统计信息
                stats = {}
                for table_name in ['items', 'prices', 'watchlist_items', 'holdings']:
                    try:
                        count = session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
                        stats[table_name] = count
                    except:
                        stats[table_name] = 0

                # 获取引擎信息（对SQLite进行特殊处理）
                engine_info = {}
                try:
                    if hasattr(self.engine.pool, 'size'):
                        engine_info = {
                            'pool_size': self.engine.pool.size(),
                            'checked_in': self.engine.pool.checkedin(),
                            'checked_out': self.engine.pool.checkedout(),
                            'overflow': self.engine.pool.overflow(),
                        }
                    else:
                        engine_info = {
                            'pool_type': str(type(self.engine.pool).__name__),
                            'database_type': 'sqlite'
                        }
                except:
                    engine_info = {'error': 'Unable to get pool info'}

                return {
                    'status': 'healthy',
                    'connection_test': result == 1,
                    'table_stats': stats,
                    'engine_info': engine_info
                }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'connection_test': False
            }

    def cleanup_old_data(self, days_to_keep: int = 365):
        """清理旧数据"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)

        try:
            with self.get_session() as session:
                # 清理旧的价格数据
                deleted_prices = session.query(Price).filter(
                    Price.timestamp < cutoff_date
                ).delete()

                # 清理旧的宏观数据
                deleted_macro = session.query(MacroStat).filter(
                    MacroStat.timestamp < cutoff_date
                ).delete()

                logger.info(f"数据清理完成: 删除了 {deleted_prices} 条价格数据, {deleted_macro} 条宏观数据")

                return {
                    'deleted_prices': deleted_prices,
                    'deleted_macro': deleted_macro,
                    'cutoff_date': cutoff_date.isoformat()
                }
        except Exception as e:
            raise DatabaseConnectionError(
                f"数据清理失败: {str(e)}",
                context=ErrorContext(operation="cleanup_old_data")
            )

    def backup_database(self, backup_path: str):
        """备份数据库 (仅适用于SQLite)"""
        if not self.database_url.startswith('sqlite'):
            raise DataValidationError("备份功能仅支持SQLite数据库")

        try:
            import shutil
            db_path = self.database_url.replace('sqlite:///', '')
            shutil.copy2(db_path, backup_path)
            logger.info(f"数据库备份完成: {backup_path}")
        except Exception as e:
            raise DatabaseConnectionError(
                f"数据库备份失败: {str(e)}",
                context=ErrorContext(operation="backup_database")
            )

    def get_database_size(self) -> Dict[str, Any]:
        """获取数据库大小信息"""
        try:
            if self.database_url.startswith('sqlite'):
                db_path = self.database_url.replace('sqlite:///', '')
                if os.path.exists(db_path):
                    size_bytes = os.path.getsize(db_path)
                    size_mb = size_bytes / (1024 * 1024)
                    return {
                        'size_bytes': size_bytes,
                        'size_mb': round(size_mb, 2),
                        'path': db_path
                    }

            return {'size_bytes': 0, 'size_mb': 0, 'path': 'unknown'}
        except Exception as e:
            logger.error(f"获取数据库大小失败: {str(e)}")
            return {'error': str(e)}

    def close(self):
        """关闭数据库连接"""
        try:
            self.engine.dispose()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {str(e)}")


class OptimizedQueries:
    """优化的数据库查询类"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    def get_latest_prices(self, item_ids: List[str], platform: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取最新价格数据 - 优化版"""
        with self.db_manager.get_session() as session:
            # 使用窗口函数获取每个饰品的最新价格
            subquery = session.query(
                Price.item_id,
                Price.platform,
                Price.ask_price,
                Price.bid_price,
                Price.last_price,
                Price.volume_24h,
                Price.timestamp,
                func.row_number().over(
                    partition_by=[Price.item_id, Price.platform],
                    order_by=Price.timestamp.desc()
                ).label('rn')
            ).filter(
                Price.item_id.in_(item_ids)
            )

            if platform:
                subquery = subquery.filter(Price.platform == platform)

            subquery = subquery.subquery()

            # 只选择最新的记录
            query = session.query(subquery).filter(subquery.c.rn == 1)

            results = []
            for row in query.all():
                results.append({
                    'item_id': row.item_id,
                    'platform': row.platform,
                    'ask_price': row.ask_price,
                    'bid_price': row.bid_price,
                    'last_price': row.last_price,
                    'volume_24h': row.volume_24h,
                    'timestamp': row.timestamp.isoformat() if row.timestamp else None
                })

            return results

    def get_watchlist_with_prices(self, pool_type: Optional[str] = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取监控列表及最新价格 - 高性能版"""
        with self.db_manager.get_session() as session:
            # 构建复杂查询
            query = session.query(
                WatchlistItem.item_id,
                WatchlistItem.pool_type,
                WatchlistItem.priority_score,
                WatchlistItem.last_updated,
                WatchlistItem.next_update_time,
                Item.name,
                Item.category,
                Item.rarity
            ).join(Item, WatchlistItem.item_id == Item.item_id).filter(
                WatchlistItem.is_active == True
            )

            if pool_type:
                query = query.filter(WatchlistItem.pool_type == pool_type)

            query = query.order_by(WatchlistItem.priority_score.desc()).limit(limit)

            results = []
            for row in query.all():
                results.append({
                    'item_id': row.item_id,
                    'pool_type': row.pool_type,
                    'priority_score': row.priority_score,
                    'last_updated': row.last_updated.isoformat() if row.last_updated else None,
                    'next_update_time': row.next_update_time.isoformat() if row.next_update_time else None,
                    'name': row.name,
                    'category': row.category,
                    'rarity': row.rarity
                })

            return results

    def get_price_history(self, item_id: str, days: int = 30, platform: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取价格历史 - 采样优化版"""
        with self.db_manager.get_session() as session:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            query = session.query(
                func.date(Price.timestamp).label('date'),
                func.avg(Price.ask_price).label('avg_ask_price'),
                func.avg(Price.bid_price).label('avg_bid_price'),
                func.avg(Price.last_price).label('avg_last_price'),
                func.sum(Price.volume_24h).label('total_volume'),
                func.count().label('data_points')
            ).filter(
                Price.item_id == item_id,
                Price.timestamp >= cutoff_date
            )

            if platform:
                query = query.filter(Price.platform == platform)

            query = query.group_by(func.date(Price.timestamp)).order_by('date')

            results = []
            for row in query.all():
                results.append({
                    'date': row.date.isoformat() if row.date else None,
                    'avg_ask_price': float(row.avg_ask_price) if row.avg_ask_price else None,
                    'avg_bid_price': float(row.avg_bid_price) if row.avg_bid_price else None,
                    'avg_last_price': float(row.avg_last_price) if row.avg_last_price else None,
                    'total_volume': int(row.total_volume) if row.total_volume else 0,
                    'data_points': int(row.data_points)
                })

            return results

    def batch_update_priorities(self, updates: List[Dict[str, Any]]):
        """批量更新优先级 - 高性能版"""
        with self.db_manager.get_session() as session:
            # 使用bulk_update_mappings进行批量更新
            session.bulk_update_mappings(WatchlistItem, updates)
            logger.info(f"批量更新了 {len(updates)} 个监控项的优先级")

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        with self.db_manager.get_session() as session:
            # 计算持仓统计
            holdings_stats = session.query(
                func.count(Holding.id).label('total_holdings'),
                func.sum(Holding.quantity).label('total_quantity'),
                func.sum(Holding.cost_basis * Holding.quantity).label('total_cost'),
                func.avg(Holding.cost_basis).label('avg_cost_basis')
            ).filter(Holding.is_active == True).first()

            # 计算监控统计
            watchlist_stats = session.query(
                WatchlistItem.pool_type,
                func.count().label('count')
            ).filter(WatchlistItem.is_active == True).group_by(WatchlistItem.pool_type).all()

            return {
                'holdings': {
                    'total_holdings': holdings_stats.total_holdings or 0,
                    'total_quantity': holdings_stats.total_quantity or 0,
                    'total_cost': float(holdings_stats.total_cost or 0),
                    'avg_cost_basis': float(holdings_stats.avg_cost_basis or 0)
                },
                'watchlist': {row.pool_type: row.count for row in watchlist_stats}
            }

    # 调度器支持方法
    async def get_active_watchlist_items(self, session: Session) -> List[WatchlistItem]:
        """获取所有活跃的监控项"""
        try:
            result = session.query(WatchlistItem).filter(
                WatchlistItem.is_active == True
            ).all()
            return result
        except Exception as e:
            logger.error("Error getting active watchlist items: %s", str(e))
            raise DatabaseConnectionError(
                message=f"Failed to get active watchlist items: {str(e)}",
                context=ErrorContext(operation="get_active_watchlist_items")
            )

    async def get_watchlist_items_by_priority(self, session: Session) -> List[WatchlistItem]:
        """获取按优先级排序的监控项"""
        try:
            result = session.query(WatchlistItem).filter(
                WatchlistItem.is_active == True
            ).order_by(WatchlistItem.priority_score.desc()).all()
            return result
        except Exception as e:
            logger.error("Error getting watchlist items by priority: %s", str(e))
            raise DatabaseConnectionError(
                message=f"Failed to get watchlist items by priority: {str(e)}",
                context=ErrorContext(operation="get_watchlist_items_by_priority")
            )

    async def get_watchlist_item(self, session: Session, item_id: str) -> Optional[WatchlistItem]:
        """获取指定的监控项"""
        try:
            result = session.query(WatchlistItem).filter(
                WatchlistItem.item_id == item_id
            ).first()
            return result
        except Exception as e:
            logger.error("Error getting watchlist item %s: %s", item_id, str(e))
            return None

    async def get_latest_price(self, session: Session, item_id: str) -> Optional[Price]:
        """获取饰品的最新价格"""
        try:
            result = session.query(Price).filter(
                Price.item_id == item_id
            ).order_by(Price.timestamp.desc()).first()
            return result
        except Exception as e:
            logger.error("Error getting latest price for %s: %s", item_id, str(e))
            return None

    async def get_price_history(self, session: Session, item_id: str, days: int = 7) -> List[Price]:
        """获取饰品的价格历史"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            result = session.query(Price).filter(
                Price.item_id == item_id,
                Price.timestamp >= cutoff_date
            ).order_by(Price.timestamp.desc()).all()
            return result
        except Exception as e:
            logger.error("Error getting price history for %s: %s", item_id, str(e))
            return []

    async def update_watchlist_priority(self, session: Session, item_id: str, priority_score: float):
        """更新监控项优先级"""
        try:
            session.query(WatchlistItem).filter(
                WatchlistItem.item_id == item_id
            ).update({
                'priority_score': priority_score,
                'updated_at': datetime.utcnow()
            })
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error("Error updating watchlist priority for %s: %s", item_id, str(e))
            raise

    async def update_pool_type(self, session: Session, item_id: str, pool_type: str):
        """更新池类型"""
        try:
            session.query(WatchlistItem).filter(
                WatchlistItem.item_id == item_id
            ).update({
                'pool_type': pool_type,
                'updated_at': datetime.utcnow()
            })
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error("Error updating pool type for %s: %s", item_id, str(e))
            raise

    async def update_update_frequency(self, session: Session, item_id: str, frequency: int):
        """更新更新频率"""
        try:
            session.query(WatchlistItem).filter(
                WatchlistItem.item_id == item_id
            ).update({
                'update_frequency': frequency,
                'updated_at': datetime.utcnow()
            })
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error("Error updating update frequency for %s: %s", item_id, str(e))
            raise

    async def get_active_item_ids(self, session: Session) -> Set[str]:
        """获取所有活跃的饰品ID"""
        try:
            result = session.query(WatchlistItem.item_id).filter(
                WatchlistItem.is_active == True
            ).all()
            return {row.item_id for row in result}
        except Exception as e:
            logger.error("Error getting active item IDs: %s", str(e))
            return set()

    async def update_watchlist_last_updated(self, session: Session, item_id: str, last_updated: datetime):
        """更新最后更新时间"""
        try:
            session.query(WatchlistItem).filter(
                WatchlistItem.item_id == item_id
            ).update({
                'last_updated': last_updated,
                'updated_at': datetime.utcnow()
            })
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error("Error updating last updated for %s: %s", item_id, str(e))
            raise

    async def update_watchlist_failure_count(self, session: Session, item_id: str, failure_count: int):
        """更新失败计数"""
        try:
            session.query(WatchlistItem).filter(
                WatchlistItem.item_id == item_id
            ).update({
                'failure_count': failure_count,
                'updated_at': datetime.utcnow()
            })
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error("Error updating failure count for %s: %s", item_id, str(e))
            raise

    async def update_user_priority(self, session: Session, item_id: str, user_priority: int):
        """更新用户优先级"""
        try:
            session.query(WatchlistItem).filter(
                WatchlistItem.item_id == item_id
            ).update({
                'user_priority': user_priority,
                'updated_at': datetime.utcnow()
            })
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error("Error updating user priority for %s: %s", item_id, str(e))
            raise

    async def get_pool_items(self, session: Session, pool_type: str) -> List[WatchlistItem]:
        """获取指定池的所有项目"""
        try:
            result = session.query(WatchlistItem).filter(
                WatchlistItem.pool_type == pool_type,
                WatchlistItem.is_active == True
            ).all()
            return result
        except Exception as e:
            logger.error("Error getting pool items for %s: %s", pool_type, str(e))
            return []


# 全局数据库管理器实例
_database_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global _database_manager
    if _database_manager is None:
        from .config import get_config_manager
        config_manager = get_config_manager()

        database_url = config_manager.get('database.url', 'sqlite:///data/ares.db')
        _database_manager = DatabaseManager(database_url)

    return _database_manager
