# 交互设计文档 (IDD)："Ares" 系统 v3.0

**版本：** v3.0 (智能筛选优化版)  
**更新说明：**
- 新增智能筛选功能设计，支持从1000个饰品中快速发现机会
- 优化分层监控的用户界面展示
- 完善API资源使用状态的可视化
- 调整数据更新频率的用户期望管理

## 1.0 核心设计哲学

### 信息密度优先
目标用户是专业的技术人员，追求的是数据的全面性和获取效率，而非UI的华丽性。界面应紧凑、信息密度高，支持快速决策。

### 数据驱动决策
每个界面元素都应服务于一个决策目标。用户看到的每一个数字，都应该能帮助他回答一个问题：买入、卖出、持有还是观望。

### 暗色主题 (Dark Mode)
考虑到长时间的盯盘需求，建议采用暗色主题以减少视觉疲劳。

### 分层信息架构
根据API限制和监控策略，将信息分为"核心关注"、"主要监控"、"发现池"三个层级，用户界面需要清晰体现这种层级关系。

## 2.0 核心用户用例与交互设计

### 用例 0：系统初始化与配置 (System Initialization & Configuration)

**用户故事：** 作为首次使用系统的用户，我需要一个简单直接的方式来完成系统的基础配置，以便它能正常连接数据源并开始工作。

#### 界面与交互 (系统设置页面)

**API配置区域：**
- steamdt.com API密钥输入框（加密显示）
- "测试连接"按钮，显示API调用状态和剩余配额
- API使用情况仪表盘：今日已用/总配额，实时更新

**数据库配置：**
- 本地SQLite数据库文件路径选择器
- "测试连接"按钮，验证数据库读写权限
- 数据库状态显示：文件大小、记录数量、最后更新时间

**饰品列表管理：**
- "主监控池"配置区域：
  - 文本区域，支持批量导入饰品URL/ID
  - 当前列表统计：总数量、有效数量、无效数量
  - "验证列表"按钮，检查饰品ID的有效性
- "核心关注池"配置区域：
  - 从主监控池中选择的重点饰品列表
  - 支持拖拽排序和快速添加/移除

**通知服务配置：**
- Discord/Telegram Webhook URL输入框
- "发送测试消息"按钮
- 通知规则快速配置开关

### 用例 1：每日概览 - 智能投资组合仪表盘 (Intelligent Dashboard)

**用户故事：** 我每天打开应用，希望在第一时间就能掌握我所有资产的宏观状态，并通过智能筛选快速发现最紧急的机会或风险。

#### 界面布局 (响应式三栏布局)

**顶部状态栏：**
- API使用状态：今日已用/剩余调用次数，实时更新
- 系统运行状态：发现器、追踪器运行状态指示灯
- 最后更新时间：核心池/主池的最后更新时间戳

**左侧面板 (30%) - "战略储备 (长期持仓)"**
- **概览卡片：**
  - 总成本、当前总市值、今日浮动盈亏、累计浮动盈亏
  - 持仓数量、平均持仓时间
- **资产列表 (表格)：**
  - 列：饰品名称、持仓成本、当前市价、浮动盈亏(%)、更新时间
  - 支持按盈亏排序，快速识别表现最好/最差的资产
  - 行操作：编辑、删除、查看详情

**中间面板 (40%) - "核心关注池 (重点监控)"**
- **概览卡片：**
  - 关注数量 (30/30)、发现机会数量、平均价差
  - 更新频率显示：每30分钟
- **智能筛选工具栏：**
  - 价格区间滑块、交易量筛选、波动性筛选
  - 快速筛选按钮：高价差、高成交量、新发现
  - 筛选条件保存/加载功能
- **关注列表 (表格)：**
  - 列：饰品名称、各平台最优价差(%)、24h成交量、实时价格、更新时间
  - 智能排序：默认按机会评分排序
  - 行操作：移除关注、查看详情、添加到持仓

**右侧面板 (30%) - "发现与监控"**
- **主监控池概览：**
  - 总监控数量 (970/1000)、更新进度条
  - 更新频率显示：每4小时轮换
- **智能推荐区域：**
  - 基于排行榜发现的新机会（最多5个）
  - 从主监控池中筛选出的潜在机会
  - 一键添加到核心关注池
- **宏观指标卡片：**
  - CS2在线玩家数趋势图（迷你图表）
  - 市场活跃度指标

#### 交互流程
1. **打开应用**：默认进入此页，系统自动刷新最新数据
2. **快速筛选**：通过中间面板的筛选工具快速发现机会
3. **深度分析**：点击任一饰品行，进入详情分析页
4. **投资决策**：基于分析结果，执行买入/卖出操作

### 用例 2：深度分析 - 饰品详情分析页 (Item Detail Page)

**用户故事：** 当我发现一个机会或风险后，我需要深入挖掘这个饰品的所有相关数据，以做出最终决策。

#### 界面布局

**顶部信息栏：**
- 饰品大图、名称、磨损度、稀有度
- 当前所属池：核心关注/主监控/持仓
- 快速操作按钮：添加到关注、添加到持仓、设置预警

**左侧 (40%) - 实时数据面板：**
- **跨平台价格聚合表：**
  - 列：平台Logo、最低售价、在售数量、最高求购价、求购数量、价差
  - 实时更新状态指示
  - 最优交易平台高亮显示
- **我的持仓信息：**
  - 持仓数量、平均成本、当前盈亏
  - 持仓历史记录（买入/卖出记录）

**右侧 (60%) - 分析面板：**
- **历史价格图表：**
  - 可切换时间范围：7天、30天、90天、全部
  - 支持叠加移动平均线（MA5、MA20、MA60）
  - 成交量柱状图叠加
  - 重要事件标记（官方更新、市场事件）
- **数据分析摘要：**
  - 价格统计：最高/最低/平均价格、波动率
  - 交易统计：平均日成交量、流动性评分
  - 趋势分析：短期/长期趋势判断

#### 交互流程
1. **从仪表盘跳转**：点击饰品行进入详情页
2. **数据验证**：通过左侧表格确认最优交易平台和价格
3. **趋势分析**：通过右侧图表判断历史价格位置和趋势
4. **决策支持**：结合实时数据和历史趋势形成投资决策

### 用例 3：配置策略 - 预警配置页 (Alert Configuration Page)

**用户故事：** 我不希望一直盯盘，我需要设置好我的交易规则和风险底线，让系统在我需要的时候主动通知我。

#### 界面布局

**页面分为三个主要区域：**

**交易机会预警区域：**
- 预警类型：价差机会、套利机会、成交量异常
- 条件设置：价差阈值、成交量倍数、持续时间
- 适用范围：核心关注池、特定饰品、特定价格区间

**持仓风险预警区域：**
- 预警类型：价格跌幅、止损线、宏观风险
- 条件设置：跌幅百分比、绝对金额、时间窗口
- 适用范围：全部持仓、特定饰品、特定成本区间

**预警规则管理：**
- 现有规则列表，支持启用/禁用开关
- 规则执行历史和效果统计
- 批量操作：导入/导出规则配置

#### 交互流程 (新建预警)
1. **选择预警类型**：交易机会 vs 持仓风险
2. **定义触发条件**：通过表单设置具体参数
3. **选择执行动作**：通知方式、消息模板
4. **测试和保存**：发送测试通知，确认规则有效性

### 用例 4：持仓管理 (Portfolio Management)

**用户故事：** 当我买入或卖出一个新的"战略储备"饰品后，我需要一种方式来更新我的持仓列表，特别是准确记录成本。

#### 界面与交互 (在仪表盘左侧面板)

**持仓列表操作：**
- 表头"+ 添加持仓"按钮，支持快速添加
- 每行末尾的操作图标：编辑、删除、查看详情
- 批量操作：批量编辑、批量导出

**添加/编辑持仓弹窗：**
- **基本信息：** 饰品URL/ID（支持自动识别）、饰品名称（自动填充）
- **交易信息：** 买入日期、买入数量、买入总成本、交易平台
- **备注信息：** 买入原因、目标价位、止损价位
- **验证机制：** 实时验证饰品ID有效性，显示当前市价作为参考

### 用例 5：系统状态监控 (System Status Monitoring)

**用户故事：** 作为系统的所有者，我需要知道我的自动化模块是否在正常工作，特别是在API限制下的运行效率。

#### 界面与交互 (系统设置页面底部)

**API使用监控：**
- 实时API配额使用图表（小时级别）
- 调用成功率统计
- 错误类型分布（超限、网络错误、数据错误）
- 预计配额耗尽时间

**发现器状态：**
- 上次运行时间、运行耗时
- 运行结果：发现新目标数量、更新成功率
- "立即手动运行"按钮（显示预计耗时）
- 排行榜数据更新状态

**追踪器状态：**
- 当前正在追踪的目标数量（核心池30个，主池970个）
- 各池的最近更新时间和下次更新时间
- 更新进度条：主池轮换进度
- 数据质量指标：成功率、平均延迟

**系统健康度：**
- 数据库状态：大小、性能、备份状态
- 日志文件快速访问链接
- 系统资源使用情况（CPU、内存、磁盘）

## 3.0 响应式设计考虑

### 桌面端 (主要使用场景)
- 三栏布局，充分利用宽屏空间
- 支持多窗口操作，可同时查看仪表盘和详情页
- 快捷键支持：F5刷新、Ctrl+F搜索、ESC关闭弹窗

### 移动端 (辅助查看)
- 折叠式导航，优先显示核心关注池
- 卡片式布局，支持左右滑动切换
- 关键指标的大字体显示

## 4.0 用户体验优化

### 加载状态管理
- 数据加载时显示骨架屏，而非空白页面
- 实时更新进度条，让用户了解系统工作状态
- 网络错误时的友好提示和重试机制

### 数据可视化
- 使用颜色编码：绿色(盈利/机会)、红色(亏损/风险)、灰色(中性)
- 趋势箭头：上涨↗、下跌↘、持平→
- 迷你图表：在列表中嵌入小型趋势图

### 交互反馈
- 操作确认：删除持仓、清空筛选条件等重要操作需要确认
- 成功提示：数据更新、设置保存等操作的即时反馈
- 错误处理：清晰的错误信息和解决建议

---

**设计状态：** 已完成核心交互设计，待UI设计师进行视觉设计和原型制作
