交互设计文档 (IDD)："Ares" 系统 v1.1
v1.1 更新说明:
新增 用例 0，覆盖系统的首次初始化和核心配置。
新增 用例 4，明确了如何对“战略储备”持仓进行增删改查（CRUD）操作。
新增 用例 5，定义了如何管理和监控“扫描器”模块。
这些新增用例确保了MVP在工程上的可操作性和完整性。
1.0 核心设计哲学
信息密度优先： 目标用户是专业的技术人员，追求的是数据的全面性和获取效率，而非UI的华丽性。界面应紧凑、信息密度高。
数据驱动决策： 每个界面元素都应服务于一个决策目标。用户看到的每一个数字，都应该能帮助他回答一个问题。
暗色主题 (Dark Mode)： 考虑到长时间的盯盘需求，建议采用暗色主题以减少视觉疲劳。
2.0 核心用户用例与交互设计
用例 0 (新增)：系统初始化与配置 (System Initialization & Configuration)
用户故事： 作为首次使用系统的用户，我需要一个简单直接的方式来完成系统的基础配置，以便它能正常连接数据源并开始工作。
界面与交互 (系统设置 页面):
数据库配置:
提供一个输入框，让用户指定本地SQLite数据库文件的存放路径。
提供一个“测试连接”按钮，点击后系统尝试连接数据库并反馈成功或失败。
“战略储备”列表配置 (holdings.json):
提供一个文本区域（Text Area），允许用户直接粘贴或编辑一个JSON格式的列表，内容为需要长期追踪的饰品URL或ID。
提供一个“保存并加载”按钮。
“扫描器”大列表配置:
同样提供一个文本区域，允许用户编辑扫描器需要遍历的数千个饰品的大列表。
通知服务配置:
提供输入框，用于填写Discord/Telegram Webhook的URL，并提供“发送测试消息”按钮。
用例 1：每日概览 - 投资组合仪表盘 (Dashboard)
用户故事： 我每天打开应用，希望在第一时间就能掌握我所有资产的宏观状态，并发现最紧急的机会或风险。
界面布局 (单页仪表盘):
左侧面板 (35%) - “战略储备 (长期持仓)”
概览卡片： 显示“总成本”、“当前总市值”、“今日浮动盈亏”、“累计浮动盈亏”。
资产列表 (表格)： 饰品名称, 持仓成本, 当前市价, 浮动盈亏(%), 宏观指标。
右侧面板 (65%) - “主战部队 (短线交易)”
概览卡片： 显示“可用资金”、“今日已实现利润”、“候选池机会数量”。
候选池列表 (表格)： 饰品名称, 关注平台, 最优价差(%), 24h成交量, 实时价格 (买/卖)。
顶部导航栏： [仪表盘] [宏观雷达] [预警配置] [系统设置]
交互流程：
打开应用，默认进入此页。
通过右侧“最优价差”发现机会，通过左侧“浮动盈亏”检查持仓。
点击任一饰品行，进入“详情分析页”。
用例 2：深度分析 - 饰品详情分析页 (Item Detail Page)
用户故事： 当我发现一个机会或风险后，我需要深入挖掘这个饰品的所有相关数据，以做出最终决策。
界面布局：
顶部: 饰品大图、名称、磨损度。
左侧: 跨平台价格聚合表 (平台Logo, 最低售价, 在售数量, 最高求购价, 求购数量) 和我的持仓信息。
右侧: 历史价格图表 (可切换时间范围、叠加移动平均线) 和数据分析摘要。
交互流程：
从仪表盘跳转至此。
通过左侧表格确认最优交易平台和价格。
通过右侧图表判断历史价格位置。
形成决策，切换至外部平台操作。
用例 3：配置策略 - 预警配置页 (Alert Configuration Page)
用户故事： 我不希望一直盯盘，我需要设置好我的交易规则和风险底线，让系统在我需要的时候主动通知我。
界面布局：
页面分为“交易机会预警”和“持仓风险预警”两部分。
提供“新建预警规则”按钮和现有规则列表（带启用/禁用开关）。
交互流程 (新建预警):
点击“新建”按钮。
通过表单定义 IF 条件 (规则类型, 适用对象, 平台, 条件, 阈值)。
定义 THEN 动作 (执行动作，如发送通知)。
保存规则。
用例 4 (新增)：持仓管理 (Portfolio Management)
用户故事： 当我买入或卖出一个新的“战略储备”饰品后，我需要一种方式来更新我的持仓列表，特别是准确记录成本。
界面与交互 (在仪表盘左侧面板):
在“战略储备”资产列表的表头，提供一个“+ 添加持仓”按钮。
点击后，弹出一个表单，包含字段：饰品URL/ID, 买入日期, 买入数量, 买入总成本。
在每一行持仓资产的末尾，提供“编辑”和“删除”图标。
点击“编辑”，可修改“持仓成本”等信息。点击“删除”，则将该资产移出持仓列表。
用例 5 (新增)：系统状态监控 (System Status Monitoring)
用户故事： 作为系统的所有者，我需要知道我的自动化模块（特别是扫描器）是否在正常工作。
界面与交互 (在系统设置页或页脚):
扫描器状态:
显示“上次运行时间”。
显示“上次运行结果”（例如，“成功完成，发现XX个新目标”或“运行失败，错误信息：...”）。
提供一个“立即手动运行”按钮，用于测试和调试。
追踪器状态:
显示“当前正在追踪的目标数量”（持仓X个，交易Y个）。
显示“最近一次数据更新时间”。
日志查看:
提供一个链接或按钮，可以方便地打开系统的日志文件。
