# CS2饰品投资系统交互设计文档 (IDD)

**版本**: v1.0  
**创建日期**: 2025年7月17日  
**设计原则**: 信息密度优先，支持长时间盯盘  
**主题风格**: CS2游戏风格暗色主题  

## 🎨 设计原则

### 1.1 核心设计理念
- **信息密度优先**: 在有限空间内展示最多有用信息
- **暗色主题**: 减少眼部疲劳，支持长时间使用
- **游戏化风格**: 采用CS2游戏元素，增强用户代入感
- **数据驱动**: 所有设计决策基于数据展示需求

### 1.2 用户体验目标
- 支持2-8小时连续使用不疲劳
- 3秒内找到任何关键信息
- 单手操作完成90%常用功能
- 零学习成本的直观界面

## 🖥️ 整体布局架构

### 2.1 主导航结构
```
顶部导航栏
├── 🏠 仪表盘 (Dashboard)
├── 🔍 发现器 (Discoverer) 
├── 📊 追踪器 (Tracker)
├── 💼 持仓管理 (Holdings)
├── ⚠️ 预警中心 (Alerts)
├── 🌍 宏观雷达 (Macro)
├── ⚙️ 系统设置 (Settings)
└── 📈 系统状态 (Status)
```

### 2.2 页面布局模式
**三栏布局** (适用于大部分页面):
- **左侧栏** (20%): 筛选器、快速导航
- **主内容区** (60%): 数据表格、图表
- **右侧栏** (20%): 详情面板、操作面板

**全屏布局** (适用于仪表盘):
- 网格化卡片布局
- 响应式自适应

## 📊 核心页面设计

### 3.1 仪表盘页面 (用例1)
**用户故事**: "作为投资者，我需要一个总览页面快速了解当前投资状况"

**页面结构**:
```
┌─────────────────────────────────────────────────┐
│ 📈 投资组合概览                                    │
│ 总价值: ¥45,230 (+2.3%) | 今日盈亏: +¥1,024      │
├─────────────────┬───────────────────────────────┤
│ 🔥 热门机会 (6)  │ ⚠️ 风险预警 (2)                │
│ AK-47红线 +15%  │ M4A4龙王 流动性不足             │
│ AWP闪电 +12%    │ 手套价格异常波动                │
├─────────────────┼───────────────────────────────┤
│ 📊 市场大盘      │ 🎯 核心关注池                   │
│ 指数: 1397 (-4%) │ 30/30 正常更新                 │
│ 成交额: +37%     │ 最后更新: 2分钟前               │
└─────────────────┴───────────────────────────────┘
```

**交互特性**:
- 实时数据更新 (30秒刷新)
- 卡片点击进入详情页
- 拖拽调整卡片顺序
- 快捷键支持 (F5刷新, Ctrl+1-8切换页面)

### 3.2 发现器页面 (用例2)
**用户故事**: "我需要快速发现新的投资机会"

**页面布局**:
```
┌─筛选器─────┬─────────机会列表─────────┬─详情面板─┐
│ 📊 排行榜   │ 饰品名称    涨幅   热度   │ AK-47红线 │
│ ☑️ 涨幅榜   │ AK-47红线   +15%   🔥🔥🔥 │ ┌─价格─┐ │
│ ☑️ 热度榜   │ AWP闪电     +12%   🔥🔥   │ │Steam │ │
│ ☐ 成交榜   │ M4A4龙王    +8%    🔥     │ │¥1,230│ │
│            │                          │ │BUFF  │ │
│ 🎯 筛选条件 │                          │ │¥1,050│ │
│ 价格: 100- │                          │ └─────┘ │
│ 1000       │                          │ 📈 趋势图 │
│ 热度: 🔥🔥+ │                          │ ┌─────┐ │
└───────────┴─────────────────────────┴─────────┘
```

**交互特性**:
- 实时筛选 (输入即筛选)
- 多选排行榜组合
- 表格排序和分页
- 详情面板悬浮显示

### 3.3 追踪器页面 (用例3)
**用户故事**: "我需要监控我关注的饰品价格变化"

**分层视图设计**:
```
┌─────────────────────────────────────────────────┐
│ 🎯 核心关注池 (30/30)    ⏰ 最后更新: 2分钟前    │
├─────────────────────────────────────────────────┤
│ 饰品名称        当前价格  24h变化  状态  操作    │
│ AK-47红线       ¥1,230   +2.3%   🟢   [详情]   │
│ AWP龙狙         ¥8,500   -1.2%   🟡   [详情]   │
├─────────────────────────────────────────────────┤
│ 🌊 主监控池 (970/1000)  ⏰ 最后更新: 1小时前    │
├─────────────────────────────────────────────────┤
│ [分页显示，每页50个]                             │
│ 饰品名称        当前价格  24h变化  热度  操作    │
│ M4A4龙王        ¥2,100   +0.8%   🔥🔥  [关注]   │
└─────────────────────────────────────────────────┘
```

**状态指示系统**:
- 🟢 正常更新
- 🟡 更新延迟
- 🔴 更新失败
- ⏸️ 暂停监控

### 3.4 持仓管理页面 (用例4)
**用户故事**: "我需要管理我的饰品持仓和计算盈亏"

**持仓视图**:
```
┌─────────────────────────────────────────────────┐
│ 💼 我的持仓总览                                  │
│ 总成本: ¥42,500 | 当前价值: ¥45,230 | 盈亏: +6.4% │
├─────────────────────────────────────────────────┤
│ 🏆 战略储备 (长期持有)                           │
│ 饰品名称     成本    当前价值  盈亏    持有天数   │
│ AK-47红线    ¥1,100  ¥1,230   +11.8%  45天     │
│ AWP龙狙      ¥8,200  ¥8,500   +3.7%   30天     │
├─────────────────────────────────────────────────┤
│ ⚡ 主战部队 (活跃交易)                           │
│ 饰品名称     成本    当前价值  盈亏    操作      │
│ M4A4龙王     ¥2,000  ¥2,100   +5.0%   [卖出]   │
└─────────────────────────────────────────────────┘
```

**添加持仓流程**:
1. 点击 [+ 添加持仓] 按钮
2. 输入饰品URL或手动搜索
3. 自动识别饰品信息
4. 输入购买价格和日期
5. 选择分类 (战略储备/主战部队)
6. 确认添加

### 3.5 预警中心页面 (用例5)
**用户故事**: "我需要及时收到重要的价格和机会提醒"

**预警管理界面**:
```
┌─────────────────────────────────────────────────┐
│ ⚠️ 活跃预警 (3)                                  │
│ 🔴 AK-47红线价格突破¥1,200 (5分钟前)             │
│ 🟡 AWP闪电热度上升50% (10分钟前)                 │
│ 🟢 M4A4龙王套利机会 Steam-BUFF价差>10% (1小时前) │
├─────────────────────────────────────────────────┤
│ ⚙️ 预警规则配置                                  │
│ [+ 新建规则]                                    │
│ 📊 价格预警: AK-47红线 > ¥1,300 [编辑] [删除]    │
│ 🔥 热度预警: 任意饰品热度上升>30% [编辑] [删除]   │
│ 💰 套利预警: 价差>8% [编辑] [删除]               │
└─────────────────────────────────────────────────┘
```

**预警规则配置**:
- 价格阈值预警
- 涨跌幅预警
- 热度变化预警
- 套利机会预警
- 持仓风险预警

### 3.6 宏观雷达页面 (用例6)
**用户故事**: "我需要了解整个CS2市场的宏观趋势"

**宏观数据仪表盘**:
```
┌─────────────────────────────────────────────────┐
│ 🌍 CS2市场宏观雷达                               │
├─────────────┬─────────────┬─────────────────────┤
│ 🎮 在线玩家  │ 📊 大盘指数  │ 💰 市场成交          │
│ 895,591     │ 1397.55     │ 成交额: 8360万      │
│ ↓ -8.08%    │ ↓ -4.53%    │ ↑ +37.12%          │
├─────────────┼─────────────┼─────────────────────┤
│ 📈 趋势图表  │ 🔥 热度指数  │ ⚠️ 市场信号          │
│ [7天趋势]   │ 65.2/100    │ 🟡 价量背离         │
│ [30天趋势]  │ ↑ +5.2      │ 🟢 流动性充足       │
└─────────────┴─────────────┴─────────────────────┘
```

### 3.7 系统设置页面 (用例7)
**用户故事**: "我需要配置系统参数和API设置"

**设置分类**:
```
┌─设置导航─┬─────────设置内容─────────┐
│ 🔑 API   │ SteamDT API配置           │
│ 📊 监控   │ API Key: [**********]     │
│ ⚠️ 预警   │ 状态: ✅ 连接正常          │
│ 🎨 界面   │ 剩余调用: 8,234/14,400    │
│ 💾 数据   │ [测试连接] [重置密钥]      │
│ 🔧 高级   │                          │
└─────────┴─────────────────────────┘
```

## 🎨 视觉设计规范

### 4.1 色彩系统
**主色调** (CS2风格):
- 背景色: #1a1a1a (深灰)
- 卡片背景: #2d2d2d (中灰)
- 文字主色: #ffffff (白色)
- 文字次色: #b3b3b3 (浅灰)

**功能色彩**:
- 成功/上涨: #4CAF50 (绿色)
- 警告/中性: #FF9800 (橙色)
- 危险/下跌: #F44336 (红色)
- 信息/链接: #2196F3 (蓝色)

**热度指示**:
- 🔥🔥🔥 高热度: #FF4444
- 🔥🔥 中热度: #FF8800
- 🔥 低热度: #FFAA00

### 4.2 字体系统
- **主字体**: "Roboto", "Microsoft YaHei", sans-serif
- **数字字体**: "Roboto Mono", monospace (确保数字对齐)
- **标题字体**: "Roboto", bold

### 4.3 图标系统
- 使用Material Design图标
- 统一16px/24px尺寸
- 保持视觉一致性

## 📱 响应式设计

### 5.1 断点定义
- **桌面端**: ≥1200px (主要目标)
- **平板端**: 768px-1199px (简化布局)
- **手机端**: <768px (最小化功能)

### 5.2 适配策略
- 桌面端: 三栏布局，信息密度最高
- 平板端: 两栏布局，隐藏次要信息
- 手机端: 单栏布局，仅核心功能

## ⌨️ 交互规范

### 6.1 快捷键系统
- **Ctrl+1-8**: 快速切换页面
- **F5**: 刷新当前页面数据
- **Ctrl+F**: 快速搜索
- **Esc**: 关闭弹窗/取消操作
- **Space**: 暂停/继续自动刷新

### 6.2 状态反馈
- **加载状态**: 骨架屏 + 进度指示
- **成功状态**: 绿色提示 + 自动消失
- **错误状态**: 红色提示 + 手动关闭
- **空状态**: 友好的空状态插画

### 6.3 数据更新策略
- **实时数据**: WebSocket连接
- **定时刷新**: 30秒/5分钟/30分钟
- **手动刷新**: 用户主动触发
- **智能刷新**: 基于用户活跃度

## 🔧 技术实现要求

### 7.1 前端技术栈
- **框架**: Streamlit
- **样式**: CSS + Streamlit组件
- **图表**: Plotly/Altair
- **状态管理**: Streamlit Session State

### 7.2 性能要求
- **首屏加载**: <3秒
- **页面切换**: <1秒
- **数据刷新**: <2秒
- **内存占用**: <500MB

### 7.3 兼容性要求
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **分辨率**: 1920×1080 (主要), 1366×768 (兼容)
- **网络**: 支持弱网环境 (2G/3G)

## 📊 用户体验指标

### 8.1 可用性指标
- 任务完成率: >95%
- 任务完成时间: <30秒
- 用户错误率: <5%
- 用户满意度: >4.5/5

### 8.2 性能指标
- 页面加载时间: <3秒
- 交互响应时间: <200ms
- 数据准确性: >99%
- 系统可用性: >99%

---

**设计迭代**: 本文档将根据用户反馈和使用数据持续优化，确保最佳用户体验。
