"""
价格比较和套利检测测试
验证跨平台价格比较和套利机会识别功能
"""

import pytest
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.price_comparator import (
    PlatformPriceComparator, PriceComparison, Platform, PlatformFees
)
from services.arbitrage_detector import (
    ArbitrageDetector, ArbitrageOpportunity, ArbitrageRiskLevel
)
from services.steamdt_api import ItemPriceData, PlatformPrice


class TestPlatformFees:
    """平台手续费测试"""
    
    def test_calculate_sell_cost(self):
        """测试卖出成本计算"""
        fees = PlatformFees(
            platform=Platform.STEAM,
            sell_fee_rate=0.13,
            buy_fee_rate=0.0,
            withdraw_fee_rate=0.0,
            min_withdraw_amount=0.0,
            withdraw_time_days=0
        )
        
        # Steam收取13%手续费
        price = 100.0
        expected_revenue = price * (1 - 0.13 - 0.0)  # 87.0
        assert fees.calculate_sell_cost(price) == expected_revenue
    
    def test_calculate_buy_cost(self):
        """测试买入成本计算"""
        fees = PlatformFees(
            platform=Platform.BUFF,
            sell_fee_rate=0.025,
            buy_fee_rate=0.0,
            withdraw_fee_rate=0.01,
            min_withdraw_amount=100.0,
            withdraw_time_days=1
        )
        
        # BUFF买入无额外费用
        price = 100.0
        expected_cost = price * (1 + 0.0)  # 100.0
        assert fees.calculate_buy_cost(price) == expected_cost


class TestPlatformPriceComparator:
    """平台价格比较器测试"""
    
    @pytest.fixture
    def comparator(self):
        """创建价格比较器实例"""
        with patch('services.price_comparator.get_config_manager') as mock_config:
            mock_config.return_value.get.return_value = None
            return PlatformPriceComparator()
    
    @pytest.fixture
    def mock_platform_prices(self):
        """创建模拟平台价格数据"""
        steam_price = PlatformPrice(
            platform='steam',
            platform_item_id='123456',
            sell_price=100.0,
            sell_count=50,
            bidding_price=95.0,
            bidding_count=20,
            update_time=1625097600
        )
        
        buff_price = PlatformPrice(
            platform='buff',
            platform_item_id='789012',
            sell_price=85.0,
            sell_count=100,
            bidding_price=80.0,
            bidding_count=30,
            update_time=1625097600
        )
        
        c5game_price = PlatformPrice(
            platform='c5game',
            platform_item_id='345678',
            sell_price=90.0,
            sell_count=30,
            bidding_price=85.0,
            bidding_count=10,
            update_time=1625097600
        )
        
        return [steam_price, buff_price, c5game_price]
    
    def test_identify_platform(self, comparator):
        """测试平台识别"""
        assert comparator._identify_platform('steam') == Platform.STEAM
        assert comparator._identify_platform('Steam Market') == Platform.STEAM
        assert comparator._identify_platform('buff') == Platform.BUFF
        assert comparator._identify_platform('BUFF163') == Platform.BUFF
        assert comparator._identify_platform('c5game') == Platform.C5GAME
        assert comparator._identify_platform('C5') == Platform.C5GAME
        assert comparator._identify_platform('unknown') is None
    
    def test_parse_platform_prices(self, comparator, mock_platform_prices):
        """测试平台价格解析"""
        parsed_prices = comparator._parse_platform_prices(mock_platform_prices)
        
        assert len(parsed_prices) == 3
        assert Platform.STEAM in parsed_prices
        assert Platform.BUFF in parsed_prices
        assert Platform.C5GAME in parsed_prices
        
        assert parsed_prices[Platform.STEAM].sell_price == 100.0
        assert parsed_prices[Platform.BUFF].sell_price == 85.0
        assert parsed_prices[Platform.C5GAME].sell_price == 90.0
    
    def test_calculate_price_spread(self, comparator, mock_platform_prices):
        """测试价差计算"""
        parsed_prices = comparator._parse_platform_prices(mock_platform_prices)
        price_spread = comparator._calculate_price_spread(parsed_prices)
        
        assert price_spread['min_price'] == 85.0  # BUFF最低
        assert price_spread['max_price'] == 100.0  # Steam最高
        assert price_spread['absolute_spread'] == 15.0  # 最大差价
        assert price_spread['relative_spread_percent'] == pytest.approx(15.0 / 85.0 * 100)  # 相对差价
    
    def test_find_best_arbitrage_opportunity(self, comparator, mock_platform_prices):
        """测试最佳套利机会查找"""
        parsed_prices = comparator._parse_platform_prices(mock_platform_prices)
        best_buy, best_sell, max_profit = comparator._find_best_arbitrage_opportunity(parsed_prices)
        
        # 应该是BUFF买入，Steam卖出
        assert best_buy == Platform.BUFF
        assert best_sell == Platform.STEAM
        
        # 验证利润率计算
        buy_price = parsed_prices[Platform.BUFF].sell_price
        sell_price = parsed_prices[Platform.STEAM].sell_price
        
        # 考虑手续费的利润率
        buy_fees = comparator.platform_fees[Platform.BUFF]
        sell_fees = comparator.platform_fees[Platform.STEAM]
        
        actual_buy_cost = buy_fees.calculate_buy_cost(buy_price)
        actual_sell_revenue = sell_fees.calculate_sell_cost(sell_price)
        
        expected_profit_margin = ((actual_sell_revenue - actual_buy_cost) / actual_buy_cost) * 100
        assert max_profit == pytest.approx(expected_profit_margin)
    
    def test_calculate_profit_margin(self, comparator):
        """测试利润率计算"""
        buy_platform = Platform.BUFF
        sell_platform = Platform.STEAM
        buy_price = 85.0
        sell_price = 100.0
        
        profit_margin = comparator._calculate_profit_margin(
            buy_platform, sell_platform, buy_price, sell_price
        )
        
        # 手动计算预期利润率
        buy_fees = comparator.platform_fees[Platform.BUFF]
        sell_fees = comparator.platform_fees[Platform.STEAM]
        
        actual_buy_cost = buy_fees.calculate_buy_cost(buy_price)
        actual_sell_revenue = sell_fees.calculate_sell_cost(sell_price)
        
        expected_profit_margin = ((actual_sell_revenue - actual_buy_cost) / actual_buy_cost) * 100
        assert profit_margin == pytest.approx(expected_profit_margin)


class TestArbitrageDetector:
    """套利检测器测试"""
    
    @pytest.fixture
    def detector(self):
        """创建套利检测器实例"""
        with patch('services.arbitrage_detector.get_config_manager') as mock_config:
            mock_config.return_value.get.return_value = 5.0  # 默认配置值
            return ArbitrageDetector()
    
    @pytest.fixture
    def mock_price_comparison(self):
        """创建模拟价格比较结果"""
        platform_prices = {
            Platform.STEAM: PlatformPrice(
                platform='steam',
                platform_item_id='123456',
                sell_price=100.0,
                sell_count=50,
                bidding_price=95.0,
                bidding_count=20,
                update_time=1625097600
            ),
            Platform.BUFF: PlatformPrice(
                platform='buff',
                platform_item_id='789012',
                sell_price=85.0,
                sell_count=100,
                bidding_price=80.0,
                bidding_count=30,
                update_time=1625097600
            )
        }
        
        price_spread = {
            'min_price': 85.0,
            'max_price': 100.0,
            'avg_price': 92.5,
            'absolute_spread': 15.0,
            'relative_spread_percent': 17.65,
            'price_variance': 56.25
        }
        
        return PriceComparison(
            item_name="AK-47 | Redline",
            market_hash_name="AK-47 | Redline (Field-Tested)",
            platform_prices=platform_prices,
            price_spread=price_spread,
            best_buy_platform=Platform.BUFF,
            best_sell_platform=Platform.STEAM,
            max_profit_margin=10.0,
            comparison_time=datetime.now()
        )
    
    def test_calculate_net_profit(self, detector):
        """测试净利润计算"""
        buy_platform = Platform.BUFF
        sell_platform = Platform.STEAM
        buy_price = 85.0
        sell_price = 100.0
        
        net_profit = detector._calculate_net_profit(
            buy_platform, sell_platform, buy_price, sell_price
        )
        
        # 手动计算预期净利润
        buy_fees = detector.price_comparator.platform_fees[Platform.BUFF]
        sell_fees = detector.price_comparator.platform_fees[Platform.STEAM]
        
        actual_buy_cost = buy_fees.calculate_buy_cost(buy_price)
        actual_sell_revenue = sell_fees.calculate_sell_cost(sell_price)
        
        expected_net_profit = actual_sell_revenue - actual_buy_cost
        assert net_profit == pytest.approx(expected_net_profit)
    
    def test_assess_risk_level(self, detector, mock_price_comparison):
        """测试风险等级评估"""
        buy_price_data = mock_price_comparison.platform_prices[Platform.BUFF]
        sell_price_data = mock_price_comparison.platform_prices[Platform.STEAM]
        
        risk_level = detector._assess_risk_level(
            mock_price_comparison, buy_price_data, sell_price_data
        )
        
        # 验证风险等级是枚举类型
        assert isinstance(risk_level, ArbitrageRiskLevel)
    
    def test_calculate_liquidity_score(self, detector):
        """测试流动性评分计算"""
        buy_price_data = PlatformPrice(
            platform='buff',
            platform_item_id='789012',
            sell_price=85.0,
            sell_count=100,
            bidding_price=80.0,
            bidding_count=30,
            update_time=1625097600
        )
        
        sell_price_data = PlatformPrice(
            platform='steam',
            platform_item_id='123456',
            sell_price=100.0,
            sell_count=50,
            bidding_price=95.0,
            bidding_count=20,
            update_time=1625097600
        )
        
        liquidity_score = detector._calculate_liquidity_score(
            buy_price_data, sell_price_data
        )
        
        # 总交易量 = 100 + 30 + 50 + 20 = 200，应该得到90分
        assert liquidity_score == 90.0
    
    def test_is_valid_opportunity(self, detector):
        """测试套利机会验证"""
        # 有效的套利机会
        valid_opportunity = ArbitrageOpportunity(
            item_name="AK-47 | Redline",
            market_hash_name="AK-47 | Redline (Field-Tested)",
            buy_platform=Platform.BUFF,
            sell_platform=Platform.STEAM,
            buy_price=85.0,
            sell_price=100.0,
            gross_profit=15.0,
            net_profit=10.0,
            profit_margin_percent=10.0,
            risk_level=ArbitrageRiskLevel.MEDIUM,
            liquidity_score=80.0,
            time_to_profit_days=2,
            confidence_score=75.0,
            reasons=["测试原因"],
            detected_at=datetime.now()
        )
        
        # 无效的套利机会（利润率太低）
        invalid_opportunity = ArbitrageOpportunity(
            item_name="AK-47 | Redline",
            market_hash_name="AK-47 | Redline (Field-Tested)",
            buy_platform=Platform.BUFF,
            sell_platform=Platform.STEAM,
            buy_price=85.0,
            sell_price=87.0,
            gross_profit=2.0,
            net_profit=1.0,
            profit_margin_percent=1.0,  # 低于最小利润率
            risk_level=ArbitrageRiskLevel.MEDIUM,
            liquidity_score=80.0,
            time_to_profit_days=2,
            confidence_score=75.0,
            reasons=["测试原因"],
            detected_at=datetime.now()
        )
        
        assert detector._is_valid_opportunity(valid_opportunity) == True
        assert detector._is_valid_opportunity(invalid_opportunity) == False


if __name__ == "__main__":
    # 运行基本测试
    print("运行价格比较和套利检测基本测试...")
    
    # 测试平台枚举
    print("测试平台枚举...")
    assert Platform.STEAM.value == "steam"
    assert Platform.BUFF.value == "buff"
    assert Platform.C5GAME.value == "c5game"
    print("✓ 平台枚举测试通过")
    
    # 测试风险等级枚举
    print("测试风险等级枚举...")
    assert ArbitrageRiskLevel.LOW.value == "low"
    assert ArbitrageRiskLevel.MEDIUM.value == "medium"
    assert ArbitrageRiskLevel.HIGH.value == "high"
    print("✓ 风险等级枚举测试通过")
    
    # 测试平台手续费
    print("测试平台手续费...")
    fees = PlatformFees(
        platform=Platform.STEAM,
        sell_fee_rate=0.13,
        buy_fee_rate=0.0,
        withdraw_fee_rate=0.0,
        min_withdraw_amount=0.0,
        withdraw_time_days=0
    )
    assert fees.calculate_sell_cost(100.0) == 87.0
    print("✓ 平台手续费测试通过")
    
    print("所有价格比较和套利检测基本测试通过！")
    
    # 运行pytest
    pytest.main(["-xvs", __file__])
