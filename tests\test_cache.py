"""
缓存系统测试
验证多级缓存、装饰器和性能优化功能
"""

import pytest
import asyncio
import time
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.cache import (
    CacheConfig, MemoryCacheBackend, MultiLevelCache, CacheManager,
    CacheItem, setup_cache, get_cache_manager
)
from core.decorators import (
    cache_result, cache_hot_data, cache_query_result, 
    _make_cache_key, CacheWarmer, get_cache_warmer
)


class TestCacheItem:
    """缓存项测试"""
    
    def test_cache_item_creation(self):
        """测试缓存项创建"""
        item = CacheItem(key="test_key", value="test_value", ttl=300)
        
        assert item.key == "test_key"
        assert item.value == "test_value"
        assert item.ttl == 300
        assert item.access_count == 0
        assert not item.is_expired
    
    def test_cache_item_expiration(self):
        """测试缓存项过期"""
        item = CacheItem(key="test_key", value="test_value", ttl=1)
        
        # 立即检查，应该未过期
        assert not item.is_expired
        
        # 等待过期
        time.sleep(1.1)
        assert item.is_expired
    
    def test_cache_item_touch(self):
        """测试缓存项访问"""
        item = CacheItem(key="test_key", value="test_value", ttl=300)
        
        initial_access_time = item.accessed_at
        initial_count = item.access_count
        
        time.sleep(0.01)  # 确保时间差异
        item.touch()
        
        assert item.accessed_at > initial_access_time
        assert item.access_count == initial_count + 1


class TestMemoryCacheBackend:
    """内存缓存后端测试"""
    
    @pytest.fixture
    def memory_cache(self):
        """创建内存缓存实例"""
        config = CacheConfig(max_memory_items=10, default_ttl=300)
        return MemoryCacheBackend(config)
    
    @pytest.mark.asyncio
    async def test_basic_operations(self, memory_cache):
        """测试基本操作"""
        # 设置值
        success = await memory_cache.set("key1", "value1", 300)
        assert success
        
        # 获取值
        value = await memory_cache.get("key1")
        assert value == "value1"
        
        # 检查存在
        exists = await memory_cache.exists("key1")
        assert exists
        
        # 删除值
        deleted = await memory_cache.delete("key1")
        assert deleted
        
        # 验证删除
        value = await memory_cache.get("key1")
        assert value is None
    
    @pytest.mark.asyncio
    async def test_ttl_expiration(self, memory_cache):
        """测试TTL过期"""
        # 设置短TTL
        await memory_cache.set("expire_key", "expire_value", 1)
        
        # 立即获取应该成功
        value = await memory_cache.get("expire_key")
        assert value == "expire_value"
        
        # 等待过期
        time.sleep(1.1)
        
        # 过期后获取应该返回None
        value = await memory_cache.get("expire_key")
        assert value is None
    
    @pytest.mark.asyncio
    async def test_lru_eviction(self, memory_cache):
        """测试LRU驱逐"""
        # 填满缓存
        for i in range(10):
            await memory_cache.set(f"key{i}", f"value{i}", 300)
        
        # 访问第一个键，使其成为最近使用
        await memory_cache.get("key0")
        
        # 添加新键，应该驱逐最少使用的键
        await memory_cache.set("new_key", "new_value", 300)
        
        # key0应该仍然存在（最近访问过）
        value = await memory_cache.get("key0")
        assert value == "value0"
        
        # 新键应该存在
        value = await memory_cache.get("new_key")
        assert value == "new_value"
    
    @pytest.mark.asyncio
    async def test_cache_stats(self, memory_cache):
        """测试缓存统计"""
        # 执行一些操作
        await memory_cache.set("key1", "value1")
        await memory_cache.get("key1")  # 命中
        await memory_cache.get("nonexistent")  # 未命中
        
        stats = await memory_cache.get_stats()
        
        assert stats['hits'] == 1
        assert stats['misses'] == 1
        assert stats['sets'] == 1
        assert stats['total_items'] == 1
        assert stats['hit_rate'] == 50.0


class TestMultiLevelCache:
    """多级缓存测试"""
    
    @pytest.fixture
    def multi_cache(self):
        """创建多级缓存实例"""
        config = CacheConfig(max_memory_items=5, default_ttl=300)
        # 不使用Redis，只测试内存缓存
        return MultiLevelCache(config, redis_url=None)
    
    @pytest.mark.asyncio
    async def test_cache_levels(self, multi_cache):
        """测试缓存级别"""
        # 设置值到L1缓存
        success = await multi_cache.set("test_key", "test_value", use_l1=True, use_l2=False)
        assert success
        
        # 从L1缓存获取
        value = await multi_cache.get("test_key", use_l1=True, use_l2=False)
        assert value == "test_value"
        
        # 从L2缓存获取应该失败（没有设置）
        value = await multi_cache.get("test_key", use_l1=False, use_l2=True)
        assert value is None
    
    @pytest.mark.asyncio
    async def test_cache_operations(self, multi_cache):
        """测试缓存操作"""
        # 设置多个值
        await multi_cache.set("key1", "value1")
        await multi_cache.set("key2", "value2")
        
        # 检查存在
        assert await multi_cache.exists("key1")
        assert await multi_cache.exists("key2")
        assert not await multi_cache.exists("nonexistent")
        
        # 删除一个值
        await multi_cache.delete("key1")
        assert not await multi_cache.exists("key1")
        assert await multi_cache.exists("key2")
        
        # 清空缓存
        await multi_cache.clear()
        assert not await multi_cache.exists("key2")


class TestCacheManager:
    """缓存管理器测试"""
    
    @pytest.fixture
    def cache_manager(self):
        """创建缓存管理器实例"""
        config = CacheConfig(
            default_ttl=300,
            hot_data_ttl=600,
            query_result_ttl=180,
            session_ttl=3600
        )
        return CacheManager(config, redis_url=None)
    
    @pytest.mark.asyncio
    async def test_hot_data_cache(self, cache_manager):
        """测试热点数据缓存"""
        # 设置热点数据
        success = await cache_manager.set_hot_data("hot_item", {"price": 100, "volume": 50})
        assert success
        
        # 获取热点数据
        data = await cache_manager.get_hot_data("hot_item")
        assert data == {"price": 100, "volume": 50}
    
    @pytest.mark.asyncio
    async def test_item_analysis_cache(self, cache_manager):
        """测试饰品分析缓存"""
        analysis_data = {
            "trend": "up",
            "volatility": 0.15,
            "recommendation": "buy"
        }
        
        # 设置分析数据
        success = await cache_manager.set_item_analysis("item123", analysis_data)
        assert success
        
        # 获取分析数据
        data = await cache_manager.get_item_analysis("item123")
        assert data == analysis_data
    
    @pytest.mark.asyncio
    async def test_price_data_cache(self, cache_manager):
        """测试价格数据缓存"""
        price_data = {
            "ask_price": 150.0,
            "bid_price": 145.0,
            "last_price": 148.0,
            "timestamp": "2025-07-17T10:00:00Z"
        }
        
        # 设置价格数据
        success = await cache_manager.set_price_data("item123", price_data, "steam")
        assert success
        
        # 获取价格数据
        data = await cache_manager.get_price_data("item123", "steam")
        assert data == price_data
    
    @pytest.mark.asyncio
    async def test_query_result_cache(self, cache_manager):
        """测试查询结果缓存"""
        query_data = {"filter": "rarity=legendary", "sort": "price_desc"}
        result_data = [{"id": "item1", "price": 200}, {"id": "item2", "price": 180}]
        
        # 缓存查询结果
        query_hash = await cache_manager.cache_query_result(query_data, result_data)
        assert query_hash is not None
        
        # 获取查询结果
        cached_result = await cache_manager.get_query_result(query_hash)
        assert cached_result == result_data
    
    @pytest.mark.asyncio
    async def test_session_management(self, cache_manager):
        """测试会话管理"""
        session_data = {
            "user_id": "user123",
            "login_time": "2025-07-17T10:00:00Z",
            "preferences": {"theme": "dark"}
        }
        
        # 设置会话
        success = await cache_manager.set_session("session123", session_data)
        assert success
        
        # 获取会话
        data = await cache_manager.get_session("session123")
        assert data == session_data
        
        # 删除会话
        deleted = await cache_manager.delete_session("session123")
        assert deleted
        
        # 验证删除
        data = await cache_manager.get_session("session123")
        assert data is None
    
    @pytest.mark.asyncio
    async def test_batch_operations(self, cache_manager):
        """测试批量操作"""
        # 批量设置
        data = {
            "key1": "value1",
            "key2": "value2",
            "key3": "value3"
        }
        results = await cache_manager.set_multiple(data, prefix="test")
        assert all(results.values())
        
        # 批量获取
        keys = ["key1", "key2", "key3"]
        retrieved = await cache_manager.get_multiple(keys, prefix="test")
        assert len(retrieved) == 3
        assert retrieved["key1"] == "value1"
        
        # 批量删除
        delete_results = await cache_manager.delete_multiple(keys, prefix="test")
        assert all(delete_results.values())


class TestCacheDecorators:
    """缓存装饰器测试"""
    
    def test_make_cache_key(self):
        """测试缓存键生成"""
        def test_func(a, b, c=None):
            return a + b
        
        key1 = _make_cache_key(test_func, (1, 2), {"c": 3}, "test_prefix")
        key2 = _make_cache_key(test_func, (1, 2), {"c": 3}, "test_prefix")
        key3 = _make_cache_key(test_func, (1, 3), {"c": 3}, "test_prefix")
        
        # 相同参数应该生成相同的键
        assert key1 == key2
        
        # 不同参数应该生成不同的键
        assert key1 != key3
        
        # 键应该包含前缀
        assert "test_prefix" in key1
    
    @pytest.mark.asyncio
    async def test_cache_result_decorator(self):
        """测试缓存结果装饰器"""
        call_count = 0
        
        @cache_result(ttl=300, key_prefix="test_func")
        async def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.01)  # 模拟耗时操作
            return x + y
        
        # 第一次调用
        result1 = await expensive_function(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # 第二次调用应该从缓存获取
        result2 = await expensive_function(1, 2)
        assert result2 == 3
        assert call_count == 1  # 没有增加
        
        # 不同参数应该重新计算
        result3 = await expensive_function(2, 3)
        assert result3 == 5
        assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_cache_hot_data_decorator(self):
        """测试热点数据装饰器"""
        @cache_hot_data(ttl=600)
        async def get_hot_item_data(item_id):
            return {"item_id": item_id, "price": 100, "volume": 50}
        
        # 调用函数
        data = await get_hot_item_data("hot_item_123")
        assert data["item_id"] == "hot_item_123"
        assert data["price"] == 100


class TestCacheWarmer:
    """缓存预热器测试"""
    
    @pytest.mark.asyncio
    async def test_cache_warmer(self):
        """测试缓存预热器"""
        warmer = get_cache_warmer()
        
        # 测试预热饰品数据
        item_ids = ["item1", "item2", "item3"]
        await warmer.warm_up_item_data(item_ids)
        
        # 测试预热热点数据
        data_keys = ["hot_key1", "hot_key2"]
        await warmer.warm_up_hot_data(data_keys)
        
        # 验证不抛异常
        assert True


if __name__ == "__main__":
    # 运行基本测试
    print("运行缓存系统基本测试...")
    
    # 测试缓存项
    print("测试缓存项...")
    item = CacheItem(key="test", value="value", ttl=300)
    assert item.key == "test"
    assert item.value == "value"
    assert not item.is_expired
    print("✓ 缓存项测试通过")
    
    # 测试内存缓存
    print("测试内存缓存...")
    async def test_memory_cache():
        config = CacheConfig(max_memory_items=5)
        cache = MemoryCacheBackend(config)
        
        # 基本操作
        await cache.set("key1", "value1")
        value = await cache.get("key1")
        assert value == "value1"
        
        exists = await cache.exists("key1")
        assert exists
        
        deleted = await cache.delete("key1")
        assert deleted
        
        value = await cache.get("key1")
        assert value is None
    
    asyncio.run(test_memory_cache())
    print("✓ 内存缓存测试通过")
    
    # 测试多级缓存
    print("测试多级缓存...")
    async def test_multi_cache():
        config = CacheConfig()
        cache = MultiLevelCache(config, redis_url=None)
        
        await cache.set("test_key", "test_value")
        value = await cache.get("test_key")
        assert value == "test_value"
        
        stats = await cache.get_stats()
        assert 'l1_cache' in stats
    
    asyncio.run(test_multi_cache())
    print("✓ 多级缓存测试通过")
    
    # 测试缓存管理器
    print("测试缓存管理器...")
    async def test_cache_manager():
        # 设置测试环境变量
        import os
        from cryptography.fernet import Fernet

        os.environ['STEAMDT_API_KEY'] = 'test_key'
        os.environ['ENCRYPTION_KEY'] = Fernet.generate_key().decode()
        os.environ['SECRET_KEY'] = 'test_secret_key'

        try:
            config = CacheConfig()
            manager = CacheManager(config, redis_url=None)

            # 热点数据
            await manager.set_hot_data("hot1", {"price": 100})
            data = await manager.get_hot_data("hot1")
            assert data["price"] == 100

            # 会话管理
            await manager.set_session("sess1", {"user": "test"})
            session = await manager.get_session("sess1")
            assert session["user"] == "test"
        finally:
            # 清理环境变量
            for key in ['STEAMDT_API_KEY', 'ENCRYPTION_KEY', 'SECRET_KEY']:
                os.environ.pop(key, None)

    asyncio.run(test_cache_manager())
    print("✓ 缓存管理器测试通过")
    
    # 测试装饰器
    print("测试缓存装饰器...")
    def test_func(a, b):
        return a + b
    
    key = _make_cache_key(test_func, (1, 2), {}, "test")
    assert "test" in key
    print("✓ 缓存装饰器测试通过")
    
    print("所有缓存系统测试通过！")
