"""
监控体系测试
验证错误处理、指标收集、日志记录和通知功能
"""

import pytest
import asyncio
import tempfile
import time
import logging
from pathlib import Path
from unittest.mock import Mock, patch
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.exceptions import AresException, ErrorSeverity, ErrorCategory, ErrorContext
from core.error_handler import <PERSON>rrorHandler, RecoveryStrategy, RecoveryAction
from core.metrics import MetricsCollector, BusinessMetricsCollector, timer
from core.logging_config import LoggingConfig, get_logger, JSONFormatter
from core.notifications import NotificationService, NotificationType, NotificationMessage


class TestErrorHandler:
    """错误处理器测试"""
    
    @pytest.fixture
    def error_handler(self):
        """创建错误处理器实例"""
        config = {
            'notification_enabled': True,
            'recovery_strategies': {}
        }
        return ErrorHandler(config)
    
    def test_handle_ares_exception(self, error_handler):
        """测试处理Ares异常"""
        exception = AresException(
            message="Test error",
            category=ErrorCategory.API_ERROR,
            severity=ErrorSeverity.MEDIUM,
            context=ErrorContext(operation="test_operation")
        )
        
        result = error_handler.handle_exception(exception)
        
        # 验证统计信息更新
        stats = error_handler.get_error_stats()
        assert stats['total_errors'] == 1
        assert stats['errors_by_category']['api_error'] == 1
        assert stats['errors_by_severity']['medium'] == 1
    
    def test_handle_regular_exception(self, error_handler):
        """测试处理普通异常"""
        exception = ValueError("Test value error")
        
        result = error_handler.handle_exception(exception)
        
        # 验证异常被包装为Ares异常
        stats = error_handler.get_error_stats()
        assert stats['total_errors'] == 1
        assert stats['errors_by_category']['system_error'] == 1
    
    def test_recovery_strategy_retry(self, error_handler):
        """测试重试恢复策略"""
        # 设置重试策略
        error_handler.set_recovery_strategy(
            ErrorCategory.API_ERROR,
            RecoveryAction(strategy=RecoveryStrategy.RETRY, max_retries=3)
        )
        
        exception = AresException(
            message="API error",
            category=ErrorCategory.API_ERROR,
            severity=ErrorSeverity.MEDIUM
        )
        
        result = error_handler.handle_exception(exception)
        
        # 验证策略被应用
        assert result is None  # 默认返回None
    
    def test_circuit_breaker(self, error_handler):
        """测试断路器功能"""
        # 设置断路器策略
        error_handler.set_recovery_strategy(
            ErrorCategory.DATABASE_ERROR,
            RecoveryAction(
                strategy=RecoveryStrategy.CIRCUIT_BREAK,
                escalation_threshold=2
            )
        )
        
        # 触发多次错误
        for i in range(3):
            exception = AresException(
                message=f"DB error {i}",
                category=ErrorCategory.DATABASE_ERROR,
                severity=ErrorSeverity.HIGH
            )
            error_handler.handle_exception(exception, operation="db_query")
        
        # 验证断路器状态
        stats = error_handler.get_error_stats()
        assert 'database_error_db_query' in stats['circuit_breakers']
    
    def test_notification_handler(self, error_handler):
        """测试通知处理器"""
        notifications = []
        
        def mock_notification_handler(data):
            notifications.append(data)
        
        error_handler.add_notification_handler(mock_notification_handler)
        
        # 触发高严重性错误
        exception = AresException(
            message="Critical error",
            category=ErrorCategory.SYSTEM_ERROR,
            severity=ErrorSeverity.CRITICAL
        )
        
        error_handler.handle_exception(exception)
        
        # 验证通知被发送
        assert len(notifications) == 1
        assert notifications[0]['severity'] == 'critical'


class TestMetricsCollector:
    """指标收集器测试"""
    
    @pytest.fixture
    def metrics_collector(self):
        """创建指标收集器实例"""
        return MetricsCollector(retention_seconds=3600, max_points_per_metric=100)
    
    def test_counter_operations(self, metrics_collector):
        """测试计数器操作"""
        # 增加计数器
        metrics_collector.increment_counter('test_counter', 5.0)
        metrics_collector.increment_counter('test_counter', 3.0)
        
        # 验证计数器值
        assert metrics_collector.get_counter('test_counter') == 8.0
    
    def test_gauge_operations(self, metrics_collector):
        """测试仪表操作"""
        # 设置仪表值
        metrics_collector.set_gauge('test_gauge', 42.0)
        metrics_collector.set_gauge('test_gauge', 35.0)
        
        # 验证仪表值
        assert metrics_collector.get_gauge('test_gauge') == 35.0
    
    def test_histogram_operations(self, metrics_collector):
        """测试直方图操作"""
        # 记录直方图数据
        values = [1.0, 2.0, 3.0, 4.0, 5.0]
        for value in values:
            metrics_collector.record_histogram('test_histogram', value)
        
        # 验证统计信息
        stats = metrics_collector.get_histogram_stats('test_histogram')
        assert stats['count'] == 5
        assert stats['min'] == 1.0
        assert stats['max'] == 5.0
        assert stats['avg'] == 3.0
    
    def test_timer_operations(self, metrics_collector):
        """测试计时器操作"""
        # 记录计时器数据
        durations = [0.1, 0.2, 0.15, 0.3, 0.25]
        for duration in durations:
            metrics_collector.record_timer('test_timer', duration)
        
        # 验证统计信息
        stats = metrics_collector.get_timer_stats('test_timer')
        assert stats['count'] == 5
        assert stats['min'] == 0.1
        assert stats['max'] == 0.3
    
    def test_timer_context_manager(self, metrics_collector):
        """测试计时器上下文管理器"""
        with timer('context_timer'):
            time.sleep(0.01)  # 模拟操作
        
        # 验证计时器记录
        stats = metrics_collector.get_timer_stats('context_timer')
        assert stats['count'] == 1
        assert stats['min'] > 0
    
    def test_tags_support(self, metrics_collector):
        """测试标签支持"""
        tags1 = {'service': 'api', 'endpoint': '/users'}
        tags2 = {'service': 'api', 'endpoint': '/items'}
        
        metrics_collector.increment_counter('requests', 1.0, tags1)
        metrics_collector.increment_counter('requests', 2.0, tags2)
        
        # 验证不同标签的指标独立
        assert metrics_collector.get_counter('requests', tags1) == 1.0
        assert metrics_collector.get_counter('requests', tags2) == 2.0
    
    def test_metrics_export(self, metrics_collector):
        """测试指标导出"""
        # 添加一些测试数据
        metrics_collector.increment_counter('test_counter', 10.0)
        metrics_collector.set_gauge('test_gauge', 20.0)
        metrics_collector.record_histogram('test_histogram', 30.0)
        
        # 导出JSON格式
        json_export = metrics_collector.export_metrics('json')
        assert 'counters' in json_export
        assert 'gauges' in json_export
        assert 'histograms' in json_export
        
        # 导出Prometheus格式
        prometheus_export = metrics_collector.export_metrics('prometheus')
        assert 'test_counter' in prometheus_export
        assert 'test_gauge' in prometheus_export


class TestBusinessMetrics:
    """业务指标测试"""
    
    @pytest.fixture
    def business_metrics(self):
        """创建业务指标收集器实例"""
        metrics_collector = MetricsCollector()
        return BusinessMetricsCollector(metrics_collector)
    
    def test_api_call_metrics(self, business_metrics):
        """测试API调用指标"""
        business_metrics.record_api_call('/api/items', True, 0.5)
        business_metrics.record_api_call('/api/items', False, 1.2)
        
        # 验证指标记录
        metrics = business_metrics.metrics
        assert metrics.get_counter('api_calls_total', {'endpoint': '/api/items', 'success': 'True'}) == 1.0
        assert metrics.get_counter('api_calls_total', {'endpoint': '/api/items', 'success': 'False'}) == 1.0
    
    def test_database_operation_metrics(self, business_metrics):
        """测试数据库操作指标"""
        business_metrics.record_database_operation('SELECT', 0.1, 5)
        business_metrics.record_database_operation('INSERT', 0.2, 1)
        
        # 验证指标记录
        metrics = business_metrics.metrics
        assert metrics.get_counter('db_operations_total', {'operation': 'SELECT'}) == 1.0
        assert metrics.get_counter('db_operations_total', {'operation': 'INSERT'}) == 1.0
    
    def test_portfolio_metrics(self, business_metrics):
        """测试投资组合指标"""
        business_metrics.update_portfolio_metrics(10000.0, 50, 1500.0)
        
        # 验证指标记录
        metrics = business_metrics.metrics
        assert metrics.get_gauge('portfolio_total_value') == 10000.0
        assert metrics.get_gauge('portfolio_item_count') == 50
        assert metrics.get_gauge('portfolio_profit_loss') == 1500.0


class TestLoggingConfig:
    """日志配置测试"""
    
    def test_json_formatter(self):
        """测试JSON格式化器"""
        formatter = JSONFormatter()
        
        # 创建测试日志记录
        record = logging.LogRecord(
            name='test_logger',
            level=logging.INFO,
            pathname='test.py',
            lineno=10,
            msg='Test message',
            args=(),
            exc_info=None
        )
        
        # 格式化日志
        formatted = formatter.format(record)
        
        # 验证JSON格式
        import json
        log_data = json.loads(formatted)
        assert log_data['level'] == 'INFO'
        assert log_data['message'] == 'Test message'
        assert 'timestamp' in log_data
    
    def test_logging_config_setup(self):
        """测试日志配置设置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / 'test.log'
            
            config = {
                'level': 'DEBUG',
                'format': 'json',
                'file_path': str(log_file),
                'max_size': '1MB',
                'backup_count': 3
            }
            
            logging_config = LoggingConfig(config)
            
            # 测试日志记录
            logger = logging_config.get_logger('test')
            logger.info('Test log message')
            
            # 验证日志文件创建
            assert log_file.exists()
    
    def test_context_logger(self):
        """测试上下文日志器"""
        logger = get_logger('test_context', {'user_id': '123', 'session': 'abc'})
        
        # 添加更多上下文
        logger.add_context(operation='test_operation')
        
        # 记录日志（这里只验证不抛异常）
        logger.info('Test message with context')
        
        # 验证上下文
        assert logger.context['user_id'] == '123'
        assert logger.context['operation'] == 'test_operation'


@pytest.mark.asyncio
class TestNotificationService:
    """通知服务测试"""
    
    @pytest.fixture
    def notification_service(self):
        """创建通知服务实例"""
        config = {
            'discord': {
                'enabled': False,  # 测试时禁用实际发送
                'webhook_url': 'https://discord.com/api/webhooks/test'
            },
            'telegram': {
                'enabled': False,  # 测试时禁用实际发送
                'bot_token': 'test_token',
                'chat_id': 'test_chat'
            }
        }
        return NotificationService(config)
    
    async def test_send_notification(self, notification_service):
        """测试发送通知"""
        # 由于渠道被禁用，这应该返回空结果
        results = await notification_service.send_notification(
            title="Test Notification",
            content="This is a test",
            type=NotificationType.INFO
        )
        
        assert isinstance(results, dict)
    
    async def test_error_notification(self, notification_service):
        """测试错误通知"""
        error_data = {
            'severity': 'high',
            'category': 'api_error',
            'message': 'API call failed',
            'context': {'endpoint': '/api/test'}
        }
        
        await notification_service.send_error_notification(error_data)
        
        # 验证不抛异常
        assert True
    
    def test_notification_filters(self, notification_service):
        """测试通知过滤器"""
        filtered_messages = []
        
        def test_filter(message: NotificationMessage) -> bool:
            if message.type == NotificationType.INFO:
                filtered_messages.append(message)
                return False  # 过滤掉INFO类型
            return True
        
        notification_service.add_filter(test_filter)
        
        # 这个测试需要实际发送来验证过滤器
        # 这里只验证过滤器被正确添加
        assert len(notification_service.filters) == 1


@pytest.mark.asyncio
class TestHealthCheck:
    """健康检查测试"""

    @pytest.fixture
    def health_monitor(self):
        """创建健康监控器实例"""
        from core.health_check import HealthMonitor, SystemResourcesHealthChecker, ApplicationHealthChecker

        monitor = HealthMonitor(check_interval=1)
        monitor.add_checker(SystemResourcesHealthChecker())
        monitor.add_checker(ApplicationHealthChecker())
        return monitor

    async def test_health_check_execution(self, health_monitor):
        """测试健康检查执行"""
        results = await health_monitor.check_all_health()

        assert len(results) >= 2  # 至少有系统资源和应用程序检查器
        assert 'system_resources' in results
        assert 'application' in results

        # 验证结果格式
        for component, result in results.items():
            assert hasattr(result, 'component')
            assert hasattr(result, 'status')
            assert hasattr(result, 'message')
            assert hasattr(result, 'timestamp')

    async def test_overall_status(self, health_monitor):
        """测试整体状态计算"""
        await health_monitor.check_all_health()
        overall_status = health_monitor.get_overall_status()

        # 应该返回有效的状态
        from core.health_check import HealthStatus
        assert overall_status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL, HealthStatus.UNKNOWN]

    async def test_health_summary(self, health_monitor):
        """测试健康状态摘要"""
        await health_monitor.check_all_health()
        summary = health_monitor.get_health_summary()

        assert 'overall_status' in summary
        assert 'components' in summary
        assert 'total_components' in summary
        assert 'healthy_components' in summary


if __name__ == "__main__":
    # 运行基本测试
    print("运行监控体系基本测试...")

    # 测试错误处理器
    print("测试错误处理器...")
    error_handler = ErrorHandler({})

    exception = AresException(
        message="Test error",
        category=ErrorCategory.API_ERROR,
        severity=ErrorSeverity.MEDIUM
    )

    error_handler.handle_exception(exception)
    stats = error_handler.get_error_stats()
    assert stats['total_errors'] == 1
    print("✓ 错误处理器测试通过")

    # 测试指标收集器
    print("测试指标收集器...")
    metrics = MetricsCollector()

    metrics.increment_counter('test_counter', 5.0)
    metrics.set_gauge('test_gauge', 42.0)
    metrics.record_histogram('test_histogram', 10.0)

    assert metrics.get_counter('test_counter') == 5.0
    assert metrics.get_gauge('test_gauge') == 42.0

    histogram_stats = metrics.get_histogram_stats('test_histogram')
    assert histogram_stats['count'] == 1
    print("✓ 指标收集器测试通过")

    # 测试业务指标
    print("测试业务指标...")
    business_metrics = BusinessMetricsCollector(metrics)

    business_metrics.record_api_call('/test', True, 0.5)
    business_metrics.update_portfolio_metrics(1000.0, 10, 100.0)

    assert metrics.get_gauge('portfolio_total_value') == 1000.0
    print("✓ 业务指标测试通过")

    # 测试日志配置
    print("测试日志配置...")
    formatter = JSONFormatter()

    record = logging.LogRecord(
        name='test',
        level=logging.INFO,
        pathname='test.py',
        lineno=1,
        msg='Test',
        args=(),
        exc_info=None
    )

    formatted = formatter.format(record)
    import json
    log_data = json.loads(formatted)
    assert log_data['level'] == 'INFO'
    print("✓ 日志配置测试通过")

    # 测试健康检查
    print("测试健康检查...")
    import asyncio
    from core.health_check import HealthMonitor, SystemResourcesHealthChecker

    async def test_health_check():
        monitor = HealthMonitor(check_interval=1)
        monitor.add_checker(SystemResourcesHealthChecker())

        results = await monitor.check_all_health()
        assert len(results) >= 1
        assert 'system_resources' in results

        overall_status = monitor.get_overall_status()
        assert overall_status is not None

        summary = monitor.get_health_summary()
        assert 'overall_status' in summary
        assert 'components' in summary

    asyncio.run(test_health_check())
    print("✓ 健康检查测试通过")

    print("所有监控体系测试通过！")
