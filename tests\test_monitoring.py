"""
系统监控和健康检查测试
验证监控系统的各项功能
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from monitoring.health_check import (
    HealthChecker, HealthCheckResult, HealthStatus, get_health_checker
)
from monitoring.system_monitor import (
    SystemMonitor, MetricPoint, AlertRule, Alert, get_system_monitor
)


class TestHealthChecker:
    """健康检查器测试"""

    @pytest.fixture
    def health_checker(self):
        """创建健康检查器实例"""
        return HealthChecker()

    def test_health_checker_initialization(self, health_checker):
        """测试健康检查器初始化"""
        assert len(health_checker.checks) > 0
        assert "database" in health_checker.checks
        assert "redis" in health_checker.checks
        assert "api_endpoints" in health_checker.checks
        assert "disk_space" in health_checker.checks
        assert "memory_usage" in health_checker.checks
        assert "cpu_usage" in health_checker.checks

    @pytest.mark.asyncio
    async def test_database_health_check(self, health_checker):
        """测试数据库健康检查"""
        result = await health_checker._check_database()

        assert isinstance(result, HealthCheckResult)
        assert result.name == "database"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.CRITICAL]
        assert result.response_time >= 0

    @pytest.mark.asyncio
    async def test_redis_health_check(self, health_checker):
        """测试Redis健康检查"""
        result = await health_checker._check_redis()

        assert isinstance(result, HealthCheckResult)
        assert result.name == "redis"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.CRITICAL]
        assert result.response_time >= 0

    @pytest.mark.asyncio
    async def test_disk_space_check(self, health_checker):
        """测试磁盘空间检查"""
        result = await health_checker._check_disk_space()

        assert isinstance(result, HealthCheckResult)
        assert result.name == "disk_space"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert "total_gb" in result.details
        assert "used_percent" in result.details

    @pytest.mark.asyncio
    async def test_memory_usage_check(self, health_checker):
        """测试内存使用检查"""
        result = await health_checker._check_memory_usage()

        assert isinstance(result, HealthCheckResult)
        assert result.name == "memory_usage"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert "total_gb" in result.details
        assert "used_percent" in result.details

    @pytest.mark.asyncio
    async def test_cpu_usage_check(self, health_checker):
        """测试CPU使用率检查"""
        result = await health_checker._check_cpu_usage()

        assert isinstance(result, HealthCheckResult)
        assert result.name == "cpu_usage"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
        assert "cpu_percent" in result.details

    @pytest.mark.asyncio
    async def test_run_all_health_checks(self, health_checker):
        """测试运行所有健康检查"""
        results = await health_checker.run_health_checks(force=True)

        assert len(results) > 0
        assert all(isinstance(result, HealthCheckResult) for result in results.values())

        # 检查所有默认检查都有结果
        expected_checks = ["database", "redis", "disk_space", "memory_usage", "cpu_usage"]
        for check_name in expected_checks:
            assert check_name in results

    def test_overall_status_calculation(self, health_checker):
        """测试整体状态计算"""
        # 测试全部健康
        healthy_results = {
            "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            "check2": HealthCheckResult("check2", HealthStatus.HEALTHY, "OK")
        }
        assert health_checker.get_overall_status(healthy_results) == HealthStatus.HEALTHY

        # 测试有警告
        warning_results = {
            "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            "check2": HealthCheckResult("check2", HealthStatus.WARNING, "Warning")
        }
        assert health_checker.get_overall_status(warning_results) == HealthStatus.WARNING

        # 测试有严重问题
        critical_results = {
            "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            "check2": HealthCheckResult("check2", HealthStatus.CRITICAL, "Critical")
        }
        assert health_checker.get_overall_status(critical_results) == HealthStatus.CRITICAL

    def test_health_summary_generation(self, health_checker):
        """测试健康摘要生成"""
        results = {
            "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            "check2": HealthCheckResult("check2", HealthStatus.WARNING, "Warning"),
            "check3": HealthCheckResult("check3", HealthStatus.CRITICAL, "Critical")
        }

        summary = health_checker.get_health_summary(results)

        assert summary["overall_status"] == HealthStatus.CRITICAL.value
        assert summary["total_checks"] == 3
        assert summary["status_counts"]["healthy"] == 1
        assert summary["status_counts"]["warning"] == 1
        assert summary["status_counts"]["critical"] == 1
        assert "checks" in summary
        assert "timestamp" in summary


class TestSystemMonitor:
    """系统监控器测试"""

    @pytest.fixture
    def system_monitor(self):
        """创建系统监控器实例"""
        return SystemMonitor()

    def test_system_monitor_initialization(self, system_monitor):
        """测试系统监控器初始化"""
        assert len(system_monitor.alert_rules) > 0
        assert not system_monitor.monitoring_active
        assert system_monitor.monitor_thread is None

    def test_add_metric(self, system_monitor):
        """测试添加指标"""
        timestamp = datetime.utcnow()
        system_monitor.add_metric("test_metric", 42.0, timestamp)

        latest = system_monitor.get_latest_metric("test_metric")
        assert latest is not None
        assert latest.value == 42.0
        assert latest.timestamp == timestamp

    def test_metric_history(self, system_monitor):
        """测试指标历史"""
        # 添加多个数据点
        base_time = datetime.utcnow()
        for i in range(5):
            timestamp = base_time + timedelta(minutes=i)
            system_monitor.add_metric("history_test", float(i), timestamp)

        # 获取历史数据
        history = system_monitor.get_metric_history("history_test", duration_minutes=10)
        assert len(history) == 5

        # 检查数据顺序
        values = [point.value for point in history]
        assert values == [0.0, 1.0, 2.0, 3.0, 4.0]

    def test_api_request_recording(self, system_monitor):
        """测试API请求记录"""
        endpoint = "/api/test"

        # 记录成功请求
        system_monitor.record_api_request(endpoint, True, 0.1)
        system_monitor.record_api_request(endpoint, True, 0.2)

        # 记录失败请求
        system_monitor.record_api_request(endpoint, False, 0.5)

        # 获取统计
        stats = system_monitor.get_api_stats(endpoint)

        assert stats['total_requests'] == 3
        assert stats['success_requests'] == 2
        assert stats['error_requests'] == 1
        assert stats['avg_response_time'] == 0.15  # (0.1 + 0.2) / 2

    def test_alert_rule_management(self, system_monitor):
        """测试告警规则管理"""
        # 添加告警规则
        rule = AlertRule("test_rule", "test_metric", ">", 100.0, 60, "high")
        system_monitor.add_alert_rule(rule)

        # 检查规则是否添加
        rule_names = [r.name for r in system_monitor.alert_rules]
        assert "test_rule" in rule_names

        # 删除告警规则
        success = system_monitor.remove_alert_rule("test_rule")
        assert success

        # 检查规则是否删除
        rule_names = [r.name for r in system_monitor.alert_rules]
        assert "test_rule" not in rule_names

    def test_alert_evaluation(self, system_monitor):
        """测试告警评估"""
        # 添加测试规则
        rule = AlertRule("test_alert", "test_metric", ">", 50.0, 60, "medium")
        system_monitor.add_alert_rule(rule)

        # 添加触发告警的指标
        system_monitor.add_metric("test_metric", 75.0)

        # 手动检查告警
        system_monitor._check_alerts()

        # 检查是否触发告警
        active_alerts = system_monitor.get_active_alerts()
        assert len(active_alerts) > 0

        # 检查告警内容
        alert = active_alerts[0]
        assert alert.rule_name == "test_alert"
        assert alert.value == 75.0
        assert alert.threshold == 50.0


    def test_alert_resolution(self, system_monitor):
        """测试告警解决"""
        # 添加测试规则
        rule = AlertRule("resolve_test", "resolve_metric", ">", 50.0, 60, "medium")
        system_monitor.add_alert_rule(rule)

        # 触发告警
        system_monitor.add_metric("resolve_metric", 75.0)
        system_monitor._check_alerts()

        # 确认告警被触发
        assert len(system_monitor.get_active_alerts()) > 0

        # 添加正常指标
        system_monitor.add_metric("resolve_metric", 25.0)
        system_monitor._check_alerts()

        # 确认告警被解决
        active_alerts = system_monitor.get_active_alerts()
        resolve_alert = next((a for a in active_alerts if a.rule_name == "resolve_test"), None)
        assert resolve_alert is None

    def test_system_overview(self, system_monitor):
        """测试系统概览"""
        # 添加一些测试数据
        system_monitor.add_metric("cpu_usage", 45.0)
        system_monitor.add_metric("memory_usage", 60.0)
        system_monitor.add_metric("disk_usage", 30.0)

        system_monitor.record_api_request("/api/test", True, 0.1)
        system_monitor.record_api_request("/api/test", False, 0.2)

        overview = system_monitor.get_system_overview()

        assert "system_metrics" in overview
        assert "api_metrics" in overview
        assert "alerts" in overview
        assert "monitoring_status" in overview
        assert "timestamp" in overview

        # 检查系统指标
        assert overview["system_metrics"]["cpu_usage"] == 45.0
        assert overview["system_metrics"]["memory_usage"] == 60.0
        assert overview["system_metrics"]["disk_usage"] == 30.0

        # 检查API指标
        assert overview["api_metrics"]["total_requests"] == 2
        assert overview["api_metrics"]["total_errors"] == 1

    def test_monitoring_lifecycle(self, system_monitor):
        """测试监控生命周期"""
        # 启动监控
        system_monitor.start_monitoring(interval=1)  # 1秒间隔用于测试
        assert system_monitor.monitoring_active
        assert system_monitor.monitor_thread is not None

        # 等待一段时间让监控收集数据
        time.sleep(2)

        # 检查是否收集了数据
        cpu_metric = system_monitor.get_latest_metric("cpu_usage")
        assert cpu_metric is not None

        # 停止监控
        system_monitor.stop_monitoring()
        assert not system_monitor.monitoring_active


class TestLoggingConfig:
    """日志配置测试"""
    
    def test_json_formatter(self):
        """测试JSON格式化器"""
        formatter = JSONFormatter()
        
        # 创建测试日志记录
        record = logging.LogRecord(
            name='test_logger',
            level=logging.INFO,
            pathname='test.py',
            lineno=10,
            msg='Test message',
            args=(),
            exc_info=None
        )
        
        # 格式化日志
        formatted = formatter.format(record)
        
        # 验证JSON格式
        import json
        log_data = json.loads(formatted)
        assert log_data['level'] == 'INFO'
        assert log_data['message'] == 'Test message'
        assert 'timestamp' in log_data
    
    def test_logging_config_setup(self):
        """测试日志配置设置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / 'test.log'
            
            config = {
                'level': 'DEBUG',
                'format': 'json',
                'file_path': str(log_file),
                'max_size': '1MB',
                'backup_count': 3
            }
            
            logging_config = LoggingConfig(config)
            
            # 测试日志记录
            logger = logging_config.get_logger('test')
            logger.info('Test log message')
            
            # 验证日志文件创建
            assert log_file.exists()
    
    def test_context_logger(self):
        """测试上下文日志器"""
        logger = get_logger('test_context', {'user_id': '123', 'session': 'abc'})
        
        # 添加更多上下文
        logger.add_context(operation='test_operation')
        
        # 记录日志（这里只验证不抛异常）
        logger.info('Test message with context')
        
        # 验证上下文
        assert logger.context['user_id'] == '123'
        assert logger.context['operation'] == 'test_operation'


@pytest.mark.asyncio
class TestNotificationService:
    """通知服务测试"""
    
    @pytest.fixture
    def notification_service(self):
        """创建通知服务实例"""
        config = {
            'discord': {
                'enabled': False,  # 测试时禁用实际发送
                'webhook_url': 'https://discord.com/api/webhooks/test'
            },
            'telegram': {
                'enabled': False,  # 测试时禁用实际发送
                'bot_token': 'test_token',
                'chat_id': 'test_chat'
            }
        }
        return NotificationService(config)
    
    async def test_send_notification(self, notification_service):
        """测试发送通知"""
        # 由于渠道被禁用，这应该返回空结果
        results = await notification_service.send_notification(
            title="Test Notification",
            content="This is a test",
            type=NotificationType.INFO
        )
        
        assert isinstance(results, dict)
    
    async def test_error_notification(self, notification_service):
        """测试错误通知"""
        error_data = {
            'severity': 'high',
            'category': 'api_error',
            'message': 'API call failed',
            'context': {'endpoint': '/api/test'}
        }
        
        await notification_service.send_error_notification(error_data)
        
        # 验证不抛异常
        assert True
    
    def test_notification_filters(self, notification_service):
        """测试通知过滤器"""
        filtered_messages = []
        
        def test_filter(message: NotificationMessage) -> bool:
            if message.type == NotificationType.INFO:
                filtered_messages.append(message)
                return False  # 过滤掉INFO类型
            return True
        
        notification_service.add_filter(test_filter)
        
        # 这个测试需要实际发送来验证过滤器
        # 这里只验证过滤器被正确添加
        assert len(notification_service.filters) == 1


@pytest.mark.asyncio
class TestHealthCheck:
    """健康检查测试"""

    @pytest.fixture
    def health_monitor(self):
        """创建健康监控器实例"""
        from core.health_check import HealthMonitor, SystemResourcesHealthChecker, ApplicationHealthChecker

        monitor = HealthMonitor(check_interval=1)
        monitor.add_checker(SystemResourcesHealthChecker())
        monitor.add_checker(ApplicationHealthChecker())
        return monitor

    async def test_health_check_execution(self, health_monitor):
        """测试健康检查执行"""
        results = await health_monitor.check_all_health()

        assert len(results) >= 2  # 至少有系统资源和应用程序检查器
        assert 'system_resources' in results
        assert 'application' in results

        # 验证结果格式
        for component, result in results.items():
            assert hasattr(result, 'component')
            assert hasattr(result, 'status')
            assert hasattr(result, 'message')
            assert hasattr(result, 'timestamp')

    async def test_overall_status(self, health_monitor):
        """测试整体状态计算"""
        await health_monitor.check_all_health()
        overall_status = health_monitor.get_overall_status()

        # 应该返回有效的状态
        from core.health_check import HealthStatus
        assert overall_status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL, HealthStatus.UNKNOWN]

    async def test_health_summary(self, health_monitor):
        """测试健康状态摘要"""
        await health_monitor.check_all_health()
        summary = health_monitor.get_health_summary()

        assert 'overall_status' in summary
        assert 'components' in summary
        assert 'total_components' in summary
        assert 'healthy_components' in summary


if __name__ == "__main__":
    # 运行基本测试
    print("运行监控体系基本测试...")

    # 测试错误处理器
    print("测试错误处理器...")
    error_handler = ErrorHandler({})

    exception = AresException(
        message="Test error",
        category=ErrorCategory.API_ERROR,
        severity=ErrorSeverity.MEDIUM
    )

    error_handler.handle_exception(exception)
    stats = error_handler.get_error_stats()
    assert stats['total_errors'] == 1
    print("✓ 错误处理器测试通过")

    # 测试指标收集器
    print("测试指标收集器...")
    metrics = MetricsCollector()

    metrics.increment_counter('test_counter', 5.0)
    metrics.set_gauge('test_gauge', 42.0)
    metrics.record_histogram('test_histogram', 10.0)

    assert metrics.get_counter('test_counter') == 5.0
    assert metrics.get_gauge('test_gauge') == 42.0

    histogram_stats = metrics.get_histogram_stats('test_histogram')
    assert histogram_stats['count'] == 1
    print("✓ 指标收集器测试通过")

    # 测试业务指标
    print("测试业务指标...")
    business_metrics = BusinessMetricsCollector(metrics)

    business_metrics.record_api_call('/test', True, 0.5)
    business_metrics.update_portfolio_metrics(1000.0, 10, 100.0)

    assert metrics.get_gauge('portfolio_total_value') == 1000.0
    print("✓ 业务指标测试通过")

    # 测试日志配置
    print("测试日志配置...")
    formatter = JSONFormatter()

    record = logging.LogRecord(
        name='test',
        level=logging.INFO,
        pathname='test.py',
        lineno=1,
        msg='Test',
        args=(),
        exc_info=None
    )

    formatted = formatter.format(record)
    import json
    log_data = json.loads(formatted)
    assert log_data['level'] == 'INFO'
    print("✓ 日志配置测试通过")

    # 测试健康检查
    print("测试健康检查...")
    import asyncio
    from core.health_check import HealthMonitor, SystemResourcesHealthChecker

    async def test_health_check():
        monitor = HealthMonitor(check_interval=1)
        monitor.add_checker(SystemResourcesHealthChecker())

        results = await monitor.check_all_health()
        assert len(results) >= 1
        assert 'system_resources' in results

        overall_status = monitor.get_overall_status()
        assert overall_status is not None

        summary = monitor.get_health_summary()
        assert 'overall_status' in summary
        assert 'components' in summary

    asyncio.run(test_health_check())
    print("✓ 健康检查测试通过")

    print("所有监控体系测试通过！")

    # 新增的监控系统测试
    print("\n运行新监控系统测试...")

    # 测试健康检查器
    print("测试新健康检查器...")
    health_checker = HealthChecker()
    assert len(health_checker.checks) > 0
    print("✓ 新健康检查器初始化测试通过")

    # 测试系统监控器
    print("测试新系统监控器...")
    system_monitor = SystemMonitor()

    # 测试指标添加
    system_monitor.add_metric("test_metric", 42.0)
    latest = system_monitor.get_latest_metric("test_metric")
    assert latest.value == 42.0
    print("✓ 新指标添加测试通过")

    # 测试API请求记录
    system_monitor.record_api_request("/api/test", True, 0.1)
    stats = system_monitor.get_api_stats("/api/test")
    assert stats['total_requests'] == 1
    print("✓ 新API请求记录测试通过")

    # 测试告警规则
    rule = AlertRule("test_rule", "test_metric", ">", 50.0, 60, "high")
    assert rule.evaluate(75.0) == True
    assert rule.evaluate(25.0) == False
    print("✓ 新告警规则测试通过")

    # 测试系统概览
    overview = system_monitor.get_system_overview()
    assert "system_metrics" in overview
    assert "api_metrics" in overview
    print("✓ 新系统概览测试通过")

    print("新监控系统测试通过！")
