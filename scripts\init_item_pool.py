"""
初始化1000个有价值饰品的脚本
一次性建立主监控池的基础数据
"""

import asyncio
import logging
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import DatabaseManager, Item, get_database_manager
from services.item_discovery import get_item_discovery_service, ItemEvaluation, ItemValueTier
from services.cs2_item import CS2Item, CS2Rarity, CS2WeaponType, CS2Quality
from services.web_scraper import get_web_scraper, close_web_scraper
from services.steamdt_api import get_steamdt_api_manager, close_steamdt_api_manager


class ItemPoolInitializer:
    """饰品池初始化器"""
    
    def __init__(self):
        """初始化"""
        self.logger = logging.getLogger(__name__)
        self.discovery_service = get_item_discovery_service()
        self.target_count = 1000
        
        # 初始化结果
        self.initialization_results = {
            'start_time': None,
            'end_time': None,
            'total_discovered': 0,
            'total_saved': 0,
            'tier_distribution': {},
            'errors': [],
            'success': False
        }
    
    async def initialize_item_pool(self) -> Dict[str, Any]:
        """
        初始化饰品池
        
        Returns:
            Dict[str, Any]: 初始化结果
        """
        self.logger.info(f"Starting initialization of {self.target_count} valuable items...")
        self.initialization_results['start_time'] = datetime.now()
        
        try:
            # 步骤1：发现有价值的饰品
            valuable_items = await self._discover_valuable_items()
            
            # 步骤2：保存到数据库
            saved_count = await self._save_items_to_database(valuable_items)
            
            # 步骤3：生成报告
            self._generate_initialization_report(valuable_items, saved_count)
            
            self.initialization_results['success'] = True
            self.logger.info("Item pool initialization completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error during item pool initialization: {e}")
            self.initialization_results['errors'].append(str(e))
            self.initialization_results['success'] = False
        
        finally:
            self.initialization_results['end_time'] = datetime.now()
            await self._cleanup_resources()
        
        return self.initialization_results
    
    async def _discover_valuable_items(self) -> List[ItemEvaluation]:
        """发现有价值的饰品"""
        self.logger.info("Discovering valuable items...")
        
        try:
            # 使用发现服务获取有价值的饰品
            valuable_items = await self.discovery_service.discover_valuable_items(self.target_count)
            
            self.initialization_results['total_discovered'] = len(valuable_items)
            
            # 统计价值等级分布
            tier_counts = {}
            for tier in ItemValueTier:
                count = len([item for item in valuable_items if item.value_tier == tier])
                tier_counts[tier.value] = count
            
            self.initialization_results['tier_distribution'] = tier_counts
            
            self.logger.info(f"Discovered {len(valuable_items)} valuable items")
            self.logger.info(f"Tier distribution: {tier_counts}")
            
            return valuable_items
            
        except Exception as e:
            self.logger.error(f"Error discovering valuable items: {e}")
            raise
    
    async def _save_items_to_database(self, items: List[ItemEvaluation]) -> int:
        """保存饰品到数据库"""
        self.logger.info(f"Saving {len(items)} items to database...")
        
        saved_count = 0
        db_manager = get_database_manager()
        
        try:
            async with db_manager.get_session() as session:
                for item_eval in items:
                    try:
                        # 检查饰品是否已存在
                        existing_item = await db_manager.get_item_by_market_hash_name(
                            session, item_eval.market_hash_name
                        )
                        
                        if existing_item:
                            # 更新现有饰品
                            await self._update_existing_item(session, existing_item, item_eval)
                        else:
                            # 创建新饰品
                            await self._create_new_item(session, item_eval)
                        
                        saved_count += 1
                        
                        # 每100个提交一次
                        if saved_count % 100 == 0:
                            await session.commit()
                            self.logger.info(f"Saved {saved_count}/{len(items)} items")
                    
                    except Exception as e:
                        self.logger.error(f"Error saving item {item_eval.item_name}: {e}")
                        self.initialization_results['errors'].append(
                            f"Failed to save {item_eval.item_name}: {str(e)}"
                        )
                        continue
                
                # 最终提交
                await session.commit()
            
            self.initialization_results['total_saved'] = saved_count
            self.logger.info(f"Successfully saved {saved_count} items to database")
            
        except Exception as e:
            self.logger.error(f"Error saving items to database: {e}")
            raise
        
        return saved_count
    
    async def _update_existing_item(self, session, existing_item: Item, item_eval: ItemEvaluation):
        """更新现有饰品"""
        # 更新投资相关字段
        existing_item.investment_score = item_eval.final_score
        existing_item.popularity_score = item_eval.investment_score
        existing_item.liquidity_score = item_eval.liquidity_score
        existing_item.updated_at = datetime.now()
        
        # 如果有CS2特有信息，尝试解析和更新
        self._update_cs2_fields(existing_item, item_eval)
    
    async def _create_new_item(self, session, item_eval: ItemEvaluation):
        """创建新饰品"""
        # 生成唯一ID
        item_id = self._generate_item_id(item_eval.market_hash_name)
        
        # 创建新的Item实例
        new_item = Item(
            item_id=item_id,
            name=item_eval.item_name,
            market_hash_name=item_eval.market_hash_name,
            category="weapon",  # 默认分类
            investment_score=item_eval.final_score,
            popularity_score=item_eval.investment_score,
            liquidity_score=item_eval.liquidity_score,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            is_active=True
        )
        
        # 解析CS2特有字段
        self._update_cs2_fields(new_item, item_eval)
        
        # 添加到会话
        session.add(new_item)
    
    def _update_cs2_fields(self, item: Item, item_eval: ItemEvaluation):
        """更新CS2特有字段"""
        try:
            # 解析饰品名称，提取武器类型和皮肤名称
            weapon_info = self._parse_weapon_info(item_eval.item_name)
            
            if weapon_info:
                item.weapon_type = weapon_info.get('weapon_type')
                item.skin_name = weapon_info.get('skin_name')
                item.quality = weapon_info.get('quality', '普通')
                
                # 根据价格推测稀有度
                item.rarity = self._estimate_rarity(item_eval.current_price, weapon_info.get('weapon_type'))
        
        except Exception as e:
            self.logger.warning(f"Error updating CS2 fields for {item_eval.item_name}: {e}")
    
    def _parse_weapon_info(self, item_name: str) -> Dict[str, str]:
        """解析武器信息"""
        weapon_info = {}
        
        # 武器类型映射
        weapon_patterns = {
            'AK-47': '步枪',
            'M4A4': '步枪',
            'M4A1-S': '步枪',
            'AWP': '狙击枪',
            'Glock-18': '手枪',
            'USP-S': '手枪',
            'Desert Eagle': '手枪',
            'Karambit': '刀具',
            'Bayonet': '刀具',
            'Butterfly Knife': '刀具',
            'Driver Gloves': '手套',
            'Sport Gloves': '手套'
        }
        
        # 查找武器类型
        for weapon, weapon_type in weapon_patterns.items():
            if weapon in item_name:
                weapon_info['weapon_type'] = weapon_type
                break
        
        # 提取皮肤名称（通常在 | 后面）
        if ' | ' in item_name:
            parts = item_name.split(' | ')
            if len(parts) >= 2:
                skin_part = parts[1]
                # 移除磨损度信息
                skin_name = skin_part.split(' (')[0]
                weapon_info['skin_name'] = skin_name
        
        # 检查品质
        if 'StatTrak™' in item_name:
            weapon_info['quality'] = 'StatTrak™'
        elif 'Souvenir' in item_name:
            weapon_info['quality'] = '纪念品'
        else:
            weapon_info['quality'] = '普通'
        
        return weapon_info
    
    def _estimate_rarity(self, price: float, weapon_type: str) -> str:
        """根据价格估算稀有度"""
        # 刀具和手套通常稀有度更高
        if weapon_type in ['刀具', '手套']:
            if price >= 1000:
                return '隐秘'
            elif price >= 500:
                return '保密'
            elif price >= 200:
                return '受限'
            else:
                return '军规级'
        else:
            # 普通武器
            if price >= 500:
                return '隐秘'
            elif price >= 100:
                return '保密'
            elif price >= 50:
                return '受限'
            elif price >= 20:
                return '军规级'
            elif price >= 5:
                return '工业级'
            else:
                return '消费级'
    
    def _generate_item_id(self, market_hash_name: str) -> str:
        """生成饰品ID"""
        # 简化市场哈希名称作为ID
        item_id = market_hash_name.lower()
        item_id = ''.join(c if c.isalnum() else '_' for c in item_id)
        item_id = item_id[:50]  # 限制长度
        return item_id
    
    def _generate_initialization_report(self, items: List[ItemEvaluation], saved_count: int):
        """生成初始化报告"""
        report = {
            'initialization_summary': {
                'target_count': self.target_count,
                'discovered_count': len(items),
                'saved_count': saved_count,
                'success_rate': (saved_count / len(items) * 100) if items else 0,
                'duration': (self.initialization_results['end_time'] - self.initialization_results['start_time']).total_seconds() if self.initialization_results['end_time'] else 0
            },
            'tier_distribution': self.initialization_results['tier_distribution'],
            'top_items': [
                {
                    'name': item.item_name,
                    'price': item.current_price,
                    'score': item.final_score,
                    'tier': item.value_tier.value
                }
                for item in sorted(items, key=lambda x: x.final_score, reverse=True)[:20]
            ],
            'errors': self.initialization_results['errors']
        }
        
        # 保存报告
        report_file = Path("data/initialization_report.json")
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Initialization report saved to {report_file}")
    
    async def _cleanup_resources(self):
        """清理资源"""
        try:
            await close_web_scraper()
            await close_steamdt_api_manager()
        except Exception as e:
            self.logger.error(f"Error cleaning up resources: {e}")


async def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('data/init_item_pool.log', encoding='utf-8')
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("Starting item pool initialization...")
    
    try:
        # 创建初始化器
        initializer = ItemPoolInitializer()
        
        # 执行初始化
        results = await initializer.initialize_item_pool()
        
        # 输出结果
        if results['success']:
            logger.info("✅ Item pool initialization completed successfully!")
            logger.info(f"📊 Discovered: {results['total_discovered']} items")
            logger.info(f"💾 Saved: {results['total_saved']} items")
            logger.info(f"📈 Tier distribution: {results['tier_distribution']}")
        else:
            logger.error("❌ Item pool initialization failed!")
            logger.error(f"🚨 Errors: {results['errors']}")
        
        return results
        
    except Exception as e:
        logger.error(f"Fatal error in item pool initialization: {e}")
        return {'success': False, 'error': str(e)}


if __name__ == "__main__":
    # 运行初始化
    results = asyncio.run(main())
    
    # 退出码
    exit_code = 0 if results.get('success', False) else 1
    sys.exit(exit_code)
