“Ares”系统 - 编码实现任务列表
开发方法论： 我们将采用敏捷开发的Sprint模式，聚焦于快速交付MVP（最小可行产品）的核心价值。每个Sprint都是一个为期1-2周的开发周期。
Sprint 0: 项目初始化与环境配置 (Project Initialization & Environment Setup)
目标： 搭建稳固的开发地基，确保所有后续工作能顺利开展。
任务 0.1: 创建项目结构
目标: 建立一个清晰、可扩展的文件夹和文件结构。
步骤:
创建主项目文件夹 ares_system。
在内部创建子文件夹: config (存放配置文件), core (存放核心业务逻辑), db (存放数据库文件), scripts (存放独立的脚本，如扫描器), app (存放UI相关代码), logs (存放日志)。
PRD关联: 全局基础。
完成标准: Git仓库初始化并提交初始项目结构。
任务 0.2: 环境配置
目标: 隔离项目依赖，确保环境一致性。
步骤:
使用 venv 或 conda 创建Python虚拟环境。
创建 requirements.txt 文件，并添加基础依赖库: requests, beautifulsoup4, pandas, sqlalchemy。
PRD关联: 全局基础。
完成标准: 可以在虚拟环境中成功 pip install -r requirements.txt。
任务 0.3: 配置文件管理
目标: 将所有可变配置项与代码分离。
步骤:
在 config 文件夹下创建 settings.yaml 或 config.py。
定义配置项：数据库文件路径、日志文件路径、扫描器大列表文件路径、静态持仓列表文件路径。
PRD关联: 1.1, 1.2。
完成标准: 项目代码可以成功读取配置项。
Sprint 1: 核心数据管道 (Core Data Pipeline - MVP核心)
目标： 打通从数据源到本地数据库的完整链路，验证“扫描器+追踪器”架构的可行性。
任务 1.1: 数据库模型设计与实现
目标: 设计并创建用于存储饰品数据的数据库表。
步骤:
使用 SQLAlchemy Core 或 ORM 定义数据表结构（例如 prices 表，包含字段：item_id, platform, ask_price, bid_price, volume, timestamp）。
编写一个初始化脚本，用于创建数据库文件和表。
PRD关联: 2.3。
完成标准: 数据库和表结构创建成功，可以进行增删改查。
任务 1.2: 开发扫描器模块 (scanner.py)
目标: 实现低频、大规模的饰品筛选功能。
步骤:
编写一个函数，读取配置文件中指定的“扫描器大列表”。
编写核心抓取与解析函数，能针对单个 steamdt 页面，提取所需数据。
实现筛选逻辑（价差、成交量）。
将筛选结果写入 watchlist.json 文件。
注意： 必须加入合理的请求延迟（time.sleep()）和异常处理。
PRD关联: 2.1。
完成标准: 手动运行脚本后，能生成一个包含符合条件饰品信息的 watchlist.json 文件。
任务 1.3: 开发追踪器模块 (tracker.py)
目标: 实现分层、高频的数据追踪与入库。
步骤:
编写函数，分别读取静态的 holdings.json 和动态的 watchlist.json。
实现调度逻辑，为两组列表中的目标分配不同的更新频率。
复用扫描器的核心抓取函数，获取实时数据。
编写数据库操作函数，将获取到的数据存入SQLite数据库。
PRD关联: 2.2。
完成标准: 启动脚本后，能看到数据库中持续有新数据写入。
任务 1.4: 开发宏观数据采集器 (macro_data_collector.py)
目标: 获取CS2玩家在线人数数据。
步骤:
研究并对接 SteamCharts 或类似网站的公开数据。
编写脚本，每日获取一次数据并存入数据库的另一张表（例如 macro_stats）。
PRD关联: 3.1。
完成标准: 数据库中成功记录了玩家人数的历史数据。
Sprint 2: MVP用户界面与数据展示 (MVP UI & Data Display)
目标： 让数据变得可见、可用，完成MVP的核心用户体验闭环。
任务 2.1: 选择并搭建UI框架
目标: 建立一个能快速开发的可视化前端。
建议: 考虑到开发效率，强烈推荐使用 Streamlit 或 Dash。
步骤: 创建 app.py，搭建基础页面布局（例如，侧边栏导航、主内容区）。
PRD关联: 全局UI基础。
完成标准: 运行 streamlit run app.py 后能看到一个基本的网页应用。
任务 2.2: 开发系统配置与状态页面
目标: 实现PRD中定义的系统管理功能。
步骤:
在UI上创建表单，允许用户编辑配置文件中的路径。
读取并展示扫描器和追踪器的日志或状态文件。
PRD关联: Epic 1。
完成标准: 用户可以通过UI界面管理系统的基本配置。
任务 2.3: 开发投资组合仪表盘
目标: 展示核心的“持仓”和“交易”看板。
步骤:
编写数据查询逻辑，从SQLite数据库中读取最新的数据。
使用 pandas 进行数据处理和计算（如计算浮动盈亏）。
在UI上用表格（st.dataframe）组件将左右两个面板的数据清晰地展示出来。
展示宏观玩家人数的图表（st.line_chart）。
PRD关联: 4.1, 4.3, 4.4, 3.1。
完成标准: 仪表盘能正确、实时地反映数据库中的数据。
任务 2.4: 实现持仓管理功能
目标: 让用户可以方便地管理自己的长期持仓。
步骤:
在“战略储备”面板上添加入库按钮。
使用Streamlit的表单（st.form）功能，实现对持仓的增、删、改操作，并将结果更新到 holdings.json 和数据库中。
PRD关联: 4.2。
完成标准: 用户可以完整地执行一次添加新持仓并记录成本的操作。
Sprint 3: 核心功能完善 (Core Feature Polish - "Should-Haves")
目标： 完成MVP定义中的“可以包含”项，让系统变得更智能、更实用。
任务 3.1: 开发详情分析页
目标: 提供单个饰品的深度数据视图。
步骤:
实现页面路由或会话状态管理，使得点击仪表盘的饰品能跳转到详情页。
在详情页中，查询并展示该饰品的所有历史数据，并用图表库绘制价格曲线。
实现移动平均线等技术指标的计算与叠加显示。
PRD关联: 4.5。
完成标准: 详情页功能完整，图表交互流畅。
任务 3.2: 开发核心预警系统
目标: 将被动查看变为主动通知。
步骤:
在“追踪器”模块中，加入预警规则的判断逻辑。
开发一个通知发送模块（例如 notifier.py），集成Discord/Telegram的Webhook。
在UI上创建预警规则的配置页面。
PRD关联: 5.1, 5.2, 5.3, 5.4。
完成标准: 当满足预设条件时，能成功收到通知。
后续迭代 (Future Sprints)
任务 4.1: 实现高级分析功能（资产健康度诊断、投资组合基准对比）。
任务 4.2 (新增): 开发社区与官方动态追踪模块
目标: 聚合外部定性信息，为长期投资决策提供宏观背景。
步骤:
开发一个爬虫模块，用于抓取 r/csgomarketforum 的帖子标题和热度。
开发一个RSS或爬虫模块，用于聚合CS2官方博客的更新。
在UI上创建一个“宏观雷达”页面来展示这些信息流。
PRD关联: 3.2, 3.3。
任务 4.3: 对接交易API，实现（半）自动化的交易执行模块。
任务 4.4: 引入机器学习模型，进行价格趋势预测。
