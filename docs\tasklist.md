# "Ares"系统 - 编码实现任务列表 v3.0

**版本：** v3.0 (API约束优化版)  
**开发方法论：** 我们将采用敏捷开发的Sprint模式，聚焦于快速交付MVP（最小可行产品）的核心价值。每个Sprint都是一个为期1-2周的开发周期。

## Sprint 0: 项目初始化与环境配置 (Project Initialization & Environment Setup)

**目标：** 搭建稳固的开发地基，确保所有后续工作能顺利开展。

### 任务 0.1: 创建项目结构
**目标：** 建立一个清晰、可扩展的文件夹和文件结构。

**步骤：**
- 创建主项目文件夹 `ares_system`
- 在内部创建子文件夹：
  - `config/` (存放配置文件)
  - `core/` (存放核心业务逻辑)
  - `db/` (存放数据库文件)
  - `scripts/` (存放独立的脚本，如发现器、追踪器)
  - `app/` (存放UI相关代码)
  - `logs/` (存放日志)
  - `data/` (存放饰品列表、筛选规则等数据文件)

**PRD关联：** 全局基础  
**完成标准：** Git仓库初始化并提交初始项目结构

### 任务 0.2: 环境配置
**目标：** 隔离项目依赖，确保环境一致性。

**步骤：**
- 使用 `venv` 或 `conda` 创建Python虚拟环境
- 创建 `requirements.txt` 文件，添加基础依赖库：
  - `requests` (API调用)
  - `beautifulsoup4` (数据解析，如需要)
  - `pandas` (数据处理)
  - `sqlalchemy` (数据库ORM)
  - `streamlit` (UI框架)
  - `plotly` (图表库)
  - `pyyaml` (配置文件解析)

**PRD关联：** 全局基础  
**完成标准：** 可以在虚拟环境中成功 `pip install -r requirements.txt`

### 任务 0.3: 配置文件管理
**目标：** 将所有可变配置项与代码分离。

**步骤：**
- 在 `config/` 文件夹下创建 `settings.yaml`
- 定义配置项：
  - 数据库文件路径
  - 日志文件路径和级别
  - steamdt.com API配置（密钥、基础URL、限制参数）
  - 主监控池文件路径 (`data/main_watchlist.json`)
  - 核心关注池文件路径 (`data/core_watchlist.json`)
  - 持仓列表文件路径 (`data/holdings.json`)
  - 通知服务配置（Discord/Telegram Webhook）

**PRD关联：** 1.1, 1.2  
**完成标准：** 项目代码可以成功读取配置项，支持环境变量覆盖

### 任务 0.4: API管理模块设计
**目标：** 建立统一的API调用管理机制，确保不超出频率限制。

**步骤：**
- 创建 `core/api_manager.py`
- 实现API调用队列和限流器：
  - 每分钟最多10次调用的限制
  - 调用失败的自动重试机制（最多3次）
  - API配额监控和预警
- 实现API密钥的安全存储和加载
- 添加API调用日志记录

**PRD关联：** 2.3, 4.4  
**完成标准：** API管理器能够稳定控制调用频率，记录使用情况

## Sprint 1: 核心数据管道 (Core Data Pipeline - MVP核心)

**目标：** 打通从数据源到本地数据库的完整链路，验证"发现器+追踪器"架构的可行性。

### 任务 1.1: 数据库模型设计与实现
**目标：** 设计并创建用于存储饰品数据的数据库表。

**步骤：**
- 使用 SQLAlchemy ORM 定义数据表结构：
  - `items` 表：饰品基本信息（item_id, name, category, rarity等）
  - `prices` 表：价格数据（item_id, platform, ask_price, bid_price, volume, timestamp）
  - `holdings` 表：持仓记录（item_id, quantity, cost_basis, purchase_date, notes）
  - `alerts` 表：预警规则（rule_id, rule_type, conditions, actions, is_active）
  - `macro_stats` 表：宏观数据（metric_name, value, timestamp）
- 编写数据库初始化脚本 `scripts/init_db.py`
- 实现数据库连接池和事务管理

**PRD关联：** 2.3  
**完成标准：** 数据库和表结构创建成功，可以进行增删改查操作

### 任务 1.2: 开发发现器模块 (discoverer.py)
**目标：** 实现基于排行榜API的机会发现功能。

**步骤：**
- 创建 `scripts/discoverer.py`
- 实现排行榜API调用：
  - 最热饰品排行榜
  - 最新饰品排行榜  
  - 价格上涨排行榜
  - 价格下跌排行榜
- 实现智能筛选逻辑：
  - 价格区间筛选
  - 交易量阈值筛选
  - 去重和优先级排序
- 将发现结果更新到主监控池
- 添加合理的请求延迟和异常处理

**PRD关联：** 2.1  
**完成标准：** 手动运行脚本后，能够从排行榜发现新机会并更新主监控池

### 任务 1.3: 开发追踪器模块 (tracker.py)
**目标：** 实现分层、差异化频率的数据追踪与入库。

**步骤：**
- 创建 `scripts/tracker.py`
- 实现分层监控逻辑：
  - 读取核心关注池 (`core_watchlist.json`) - 30个饰品
  - 读取主监控池 (`main_watchlist.json`) - 970个饰品
  - 读取持仓列表 (`holdings.json`)
- 实现差异化更新策略：
  - 核心关注池：每30分钟更新一轮
  - 主监控池：每4小时轮换更新（每次更新约240个）
  - 持仓列表：每小时更新一次
- 复用API管理器，获取实时价格数据
- 将数据写入SQLite数据库，附加时间戳

**PRD关联：** 2.2  
**完成标准：** 启动脚本后，能看到数据库中按不同频率有新数据写入

### 任务 1.4: 开发智能筛选模块 (filter.py)
**目标：** 实现从1000个饰品中智能筛选出有价值目标的功能。

**步骤：**
- 创建 `core/filter.py`
- 实现多维度筛选器：
  - 价格区间筛选（最低价、最高价）
  - 交易量筛选（24h成交量阈值）
  - 波动性筛选（价格波动率）
  - 价差筛选（跨平台价差百分比）
- 实现筛选条件的保存和加载
- 实现智能推荐算法：
  - 基于历史表现的评分
  - 基于用户行为的个性化推荐
- 提供从主监控池向核心关注池的推荐接口

**PRD关联：** 2.4  
**完成标准：** 筛选器能够根据设定条件从大量饰品中筛选出符合要求的目标

### 任务 1.5: 开发宏观数据采集器 (macro_collector.py)
**目标：** 获取CS2玩家在线人数等宏观数据。

**步骤：**
- 创建 `scripts/macro_collector.py`
- 研究并对接 SteamCharts 或类似网站的公开数据
- 实现数据采集：
  - CS2当前在线玩家数
  - 24小时峰值玩家数
  - 30天平均在线数趋势
- 每日定时获取数据并存入 `macro_stats` 表
- 实现数据验证和异常处理

**PRD关联：** 3.1  
**完成标准：** 数据库中成功记录了玩家人数的历史数据

## Sprint 2: MVP用户界面与数据展示 (MVP UI & Data Display)

**目标：** 让数据变得可见、可用，完成MVP的核心用户体验闭环。

### 任务 2.1: 搭建UI框架
**目标：** 建立一个能快速开发的可视化前端。

**步骤：**
- 创建 `app/main.py` 作为Streamlit应用入口
- 搭建基础页面布局：
  - 侧边栏导航：仪表盘、系统设置、预警配置
  - 主内容区：响应式三栏布局
  - 顶部状态栏：API使用情况、系统状态
- 实现暗色主题配置
- 添加页面路由和状态管理

**PRD关联：** 全局UI基础  
**完成标准：** 运行 `streamlit run app/main.py` 后能看到基本的网页应用

### 任务 2.2: 开发系统配置与状态页面
**目标：** 实现PRD中定义的系统管理功能。

**步骤：**
- 创建 `app/pages/settings.py`
- 实现配置管理界面：
  - API密钥配置和测试
  - 数据库路径配置和连接测试
  - 饰品列表管理（导入/导出/验证）
  - 通知服务配置和测试
- 实现系统状态监控：
  - API使用情况实时图表
  - 发现器和追踪器运行状态
  - 数据库状态和性能指标
  - 日志查看器
- 添加手动触发功能：立即运行发现器/追踪器

**PRD关联：** Epic 1  
**完成标准：** 用户可以通过UI界面完成所有系统配置和状态监控

### 任务 2.3: 开发智能投资组合仪表盘
**目标：** 展示核心的分层监控数据和智能筛选功能。

**步骤：**
- 创建 `app/pages/dashboard.py`
- 实现三栏布局：
  - **左栏 - 战略储备：** 持仓概览、资产列表、盈亏统计
  - **中栏 - 核心关注池：** 智能筛选工具、关注列表、机会排序
  - **右栏 - 发现与监控：** 主监控池状态、智能推荐、宏观指标
- 实现智能筛选界面：
  - 多维度筛选器（价格、交易量、波动性）
  - 快速筛选按钮和条件保存
  - 实时筛选结果更新
- 编写数据查询逻辑，从SQLite读取最新数据
- 使用 pandas 进行数据处理和计算
- 实现数据表格的交互功能：排序、搜索、分页

**PRD关联：** 4.1, 4.3, 4.4, 4.6, 3.1  
**完成标准：** 仪表盘能正确、实时地反映数据库中的数据，筛选功能正常工作

### 任务 2.4: 实现持仓管理功能
**目标：** 让用户可以方便地管理自己的长期持仓。

**步骤：**
- 在战略储备面板添加持仓管理功能
- 实现添加持仓弹窗：
  - 饰品URL/ID输入和验证
  - 买入信息录入（日期、数量、成本、平台）
  - 备注信息（买入原因、目标价位等）
- 实现持仓编辑和删除功能
- 实现批量操作：导入/导出持仓数据
- 添加持仓统计和分析：
  - 总成本、当前市值、浮动盈亏
  - 持仓分布、平均持仓时间
  - 表现最好/最差的持仓

**PRD关联：** 4.2  
**完成标准：** 用户可以完整地执行持仓的增删改查操作，数据同步到数据库

## Sprint 3: 核心功能完善 (Core Feature Polish)

**目标：** 完成MVP定义中的"可以包含"项，让系统变得更智能、更实用。

### 任务 3.1: 开发详情分析页
**目标：** 提供单个饰品的深度数据视图。

**步骤：**
- 创建 `app/pages/item_detail.py`
- 实现页面路由：从仪表盘点击饰品跳转到详情页
- 实现左右分栏布局：
  - **左侧 - 实时数据：** 跨平台价格表、持仓信息
  - **右侧 - 历史分析：** 价格图表、技术指标、统计摘要
- 实现历史价格图表：
  - 使用 Plotly 绘制交互式图表
  - 支持时间范围切换（7天、30天、90天）
  - 叠加移动平均线（MA5、MA20、MA60）
  - 成交量柱状图叠加
- 实现数据分析功能：
  - 价格统计（最高/最低/平均、波动率）
  - 交易统计（平均成交量、流动性评分）
  - 趋势分析（短期/长期趋势判断）

**PRD关联：** 4.5  
**完成标准：** 详情页功能完整，图表交互流畅，数据分析准确

### 任务 3.2: 开发核心预警系统
**目标：** 将被动查看变为主动通知。

**步骤：**
- 创建 `app/pages/alerts.py` 预警配置页面
- 实现预警规则管理：
  - 规则创建表单（条件设置、动作配置）
  - 规则列表管理（启用/禁用、编辑/删除）
  - 规则模板和快速配置
- 在追踪器中集成预警判断逻辑：
  - 价格变动预警（涨跌幅、绝对价格）
  - 交易机会预警（价差、套利机会）
  - 持仓风险预警（止损、止盈）
- 开发通知发送模块 `core/notifier.py`：
  - Discord Webhook集成
  - Telegram Bot集成
  - 消息模板和格式化
- 实现预警历史记录和效果统计

**PRD关联：** 5.1, 5.2, 5.3, 5.4  
**完成标准：** 当满足预设条件时，能成功收到通知，预警规则管理界面完整

### 任务 3.3: 性能优化与错误处理
**目标：** 提升系统稳定性和用户体验。

**步骤：**
- 实现数据缓存机制：
  - Redis或内存缓存热点数据
  - 减少重复的数据库查询
- 优化API调用策略：
  - 智能批量处理
  - 失败重试和降级策略
- 完善错误处理：
  - 友好的错误提示界面
  - 详细的错误日志记录
  - 自动恢复机制
- 添加数据验证：
  - 输入数据格式验证
  - 业务逻辑一致性检查
  - 异常数据自动标记

**PRD关联：** 4.1, 4.2  
**完成标准：** 系统运行稳定，错误处理完善，用户体验流畅

## 后续迭代 (Future Sprints)

### 任务 4.1: 高级分析功能
- 资产健康度诊断算法
- 投资组合基准对比
- 风险评估模型

### 任务 4.2: 社区与官方动态追踪
- Reddit社区数据爬取
- CS2官方博客RSS聚合
- 宏观雷达页面开发

### 任务 4.3: 交易执行模块
- 第三方平台API对接
- 一键下单功能
- 自动化交易策略

### 任务 4.4: 机器学习增强
- 价格趋势预测模型
- 智能推荐算法优化
- 异常检测系统

---

**开发状态：** 已完成任务规划，可开始Sprint 0的实施工作
