"""
Ares系统健康检查模块
提供系统组件健康状态监控和诊断功能
"""

import asyncio
import time
import psutil
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod

from .metrics import get_metrics_collector
from .error_handler import get_error_handler
from .exceptions import AresException, ErrorCategory, ErrorSeverity

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    response_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'component': self.component,
            'status': self.status.value,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'response_time': self.response_time
        }


class HealthChecker(ABC):
    """健康检查器抽象基类"""
    
    def __init__(self, name: str, timeout: float = 5.0):
        self.name = name
        self.timeout = timeout
    
    @abstractmethod
    async def check_health(self) -> HealthCheckResult:
        """执行健康检查"""
        pass


class DatabaseHealthChecker(HealthChecker):
    """数据库健康检查器"""
    
    def __init__(self, database_manager, timeout: float = 5.0):
        super().__init__("database", timeout)
        self.database_manager = database_manager
    
    async def check_health(self) -> HealthCheckResult:
        """检查数据库健康状态"""
        start_time = time.time()
        
        try:
            # 执行健康检查
            health_info = self.database_manager.health_check()
            response_time = time.time() - start_time
            
            if health_info['status'] == 'healthy':
                return HealthCheckResult(
                    component=self.name,
                    status=HealthStatus.HEALTHY,
                    message="Database connection is healthy",
                    details=health_info,
                    response_time=response_time
                )
            else:
                return HealthCheckResult(
                    component=self.name,
                    status=HealthStatus.CRITICAL,
                    message=f"Database health check failed: {health_info.get('error', 'Unknown error')}",
                    details=health_info,
                    response_time=response_time
                )
        
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.CRITICAL,
                message=f"Database health check failed: {str(e)}",
                details={'error': str(e)},
                response_time=response_time
            )


class APIHealthChecker(HealthChecker):
    """API健康检查器"""
    
    def __init__(self, api_client, timeout: float = 10.0):
        super().__init__("api", timeout)
        self.api_client = api_client
    
    async def check_health(self) -> HealthCheckResult:
        """检查API健康状态"""
        start_time = time.time()
        
        try:
            # 这里可以调用API的健康检查端点
            # 暂时模拟检查
            await asyncio.sleep(0.1)  # 模拟API调用
            response_time = time.time() - start_time
            
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.HEALTHY,
                message="API service is responding",
                details={'endpoint': 'health_check'},
                response_time=response_time
            )
        
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.CRITICAL,
                message=f"API health check failed: {str(e)}",
                details={'error': str(e)},
                response_time=response_time
            )


class RedisHealthChecker(HealthChecker):
    """Redis健康检查器"""
    
    def __init__(self, redis_client, timeout: float = 5.0):
        super().__init__("redis", timeout)
        self.redis_client = redis_client
    
    async def check_health(self) -> HealthCheckResult:
        """检查Redis健康状态"""
        start_time = time.time()
        
        try:
            # 执行Redis ping命令
            if hasattr(self.redis_client, 'ping'):
                await self.redis_client.ping()
            response_time = time.time() - start_time
            
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.HEALTHY,
                message="Redis connection is healthy",
                details={'ping': 'success'},
                response_time=response_time
            )
        
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.CRITICAL,
                message=f"Redis health check failed: {str(e)}",
                details={'error': str(e)},
                response_time=response_time
            )


class SystemResourcesHealthChecker(HealthChecker):
    """系统资源健康检查器"""
    
    def __init__(self, timeout: float = 2.0):
        super().__init__("system_resources", timeout)
        self.cpu_threshold = 80.0  # CPU使用率阈值
        self.memory_threshold = 85.0  # 内存使用率阈值
        self.disk_threshold = 90.0  # 磁盘使用率阈值
    
    async def check_health(self) -> HealthCheckResult:
        """检查系统资源健康状态"""
        start_time = time.time()
        
        try:
            # 获取系统资源信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            response_time = time.time() - start_time
            
            details = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_percent': disk.percent,
                'disk_free_gb': disk.free / (1024**3)
            }
            
            # 判断健康状态
            status = HealthStatus.HEALTHY
            messages = []
            
            if cpu_percent > self.cpu_threshold:
                status = HealthStatus.WARNING
                messages.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory.percent > self.memory_threshold:
                status = HealthStatus.WARNING
                messages.append(f"High memory usage: {memory.percent:.1f}%")
            
            if disk.percent > self.disk_threshold:
                status = HealthStatus.CRITICAL
                messages.append(f"High disk usage: {disk.percent:.1f}%")
            
            if not messages:
                message = "System resources are healthy"
            else:
                message = "; ".join(messages)
            
            return HealthCheckResult(
                component=self.name,
                status=status,
                message=message,
                details=details,
                response_time=response_time
            )
        
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.CRITICAL,
                message=f"System resources check failed: {str(e)}",
                details={'error': str(e)},
                response_time=response_time
            )


class ApplicationHealthChecker(HealthChecker):
    """应用程序健康检查器"""
    
    def __init__(self, timeout: float = 3.0):
        super().__init__("application", timeout)
    
    async def check_health(self) -> HealthCheckResult:
        """检查应用程序健康状态"""
        start_time = time.time()
        
        try:
            # 检查错误处理器状态
            error_handler = get_error_handler()
            error_stats = error_handler.get_error_stats()
            
            # 检查指标收集器状态
            metrics_collector = get_metrics_collector()
            metrics_summary = metrics_collector.get_metrics_summary()
            
            response_time = time.time() - start_time
            
            details = {
                'error_stats': error_stats,
                'metrics_summary': metrics_summary,
                'uptime_seconds': time.time() - start_time  # 简化的运行时间
            }
            
            # 判断应用健康状态
            recent_critical_errors = error_stats.get('errors_by_severity', {}).get('critical', 0)
            
            if recent_critical_errors > 5:
                status = HealthStatus.CRITICAL
                message = f"Too many critical errors: {recent_critical_errors}"
            elif error_stats.get('total_errors', 0) > 100:
                status = HealthStatus.WARNING
                message = f"High error count: {error_stats['total_errors']}"
            else:
                status = HealthStatus.HEALTHY
                message = "Application is running normally"
            
            return HealthCheckResult(
                component=self.name,
                status=status,
                message=message,
                details=details,
                response_time=response_time
            )
        
        except Exception as e:
            response_time = time.time() - start_time
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.CRITICAL,
                message=f"Application health check failed: {str(e)}",
                details={'error': str(e)},
                response_time=response_time
            )


class HealthMonitor:
    """健康监控管理器"""
    
    def __init__(self, check_interval: int = 60):
        """
        初始化健康监控器
        
        Args:
            check_interval: 检查间隔（秒）
        """
        self.check_interval = check_interval
        self.checkers: List[HealthChecker] = []
        self.last_results: Dict[str, HealthCheckResult] = {}
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # 健康状态变更回调
        self.status_change_callbacks: List[Callable] = []
    
    def add_checker(self, checker: HealthChecker):
        """添加健康检查器"""
        self.checkers.append(checker)
        logger.info(f"Added health checker: {checker.name}")
    
    def remove_checker(self, checker_name: str):
        """移除健康检查器"""
        self.checkers = [c for c in self.checkers if c.name != checker_name]
        self.last_results.pop(checker_name, None)
        logger.info(f"Removed health checker: {checker_name}")
    
    def add_status_change_callback(self, callback: Callable):
        """添加状态变更回调"""
        self.status_change_callbacks.append(callback)
    
    async def check_all_health(self) -> Dict[str, HealthCheckResult]:
        """执行所有健康检查"""
        results = {}
        
        # 并发执行所有检查
        tasks = []
        for checker in self.checkers:
            task = asyncio.create_task(self._run_single_check(checker))
            tasks.append((checker.name, task))
        
        # 等待所有检查完成
        for checker_name, task in tasks:
            try:
                result = await task
                results[checker_name] = result
                
                # 检查状态变更
                self._check_status_change(checker_name, result)
                
            except Exception as e:
                logger.error(f"Health check failed for {checker_name}: {str(e)}")
                results[checker_name] = HealthCheckResult(
                    component=checker_name,
                    status=HealthStatus.CRITICAL,
                    message=f"Health check exception: {str(e)}",
                    details={'error': str(e)}
                )
        
        self.last_results = results
        return results
    
    async def _run_single_check(self, checker: HealthChecker) -> HealthCheckResult:
        """运行单个健康检查"""
        try:
            return await asyncio.wait_for(checker.check_health(), timeout=checker.timeout)
        except asyncio.TimeoutError:
            return HealthCheckResult(
                component=checker.name,
                status=HealthStatus.CRITICAL,
                message=f"Health check timeout after {checker.timeout}s",
                details={'timeout': checker.timeout}
            )
    
    def _check_status_change(self, checker_name: str, new_result: HealthCheckResult):
        """检查状态变更并触发回调"""
        old_result = self.last_results.get(checker_name)
        
        if old_result and old_result.status != new_result.status:
            # 状态发生变更
            change_info = {
                'component': checker_name,
                'old_status': old_result.status.value,
                'new_status': new_result.status.value,
                'timestamp': new_result.timestamp,
                'message': new_result.message
            }
            
            # 触发回调
            for callback in self.status_change_callbacks:
                try:
                    callback(change_info)
                except Exception as e:
                    logger.error(f"Status change callback failed: {str(e)}")
    
    def get_overall_status(self) -> HealthStatus:
        """获取整体健康状态"""
        if not self.last_results:
            return HealthStatus.UNKNOWN
        
        statuses = [result.status for result in self.last_results.values()]
        
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        overall_status = self.get_overall_status()
        
        component_statuses = {}
        for name, result in self.last_results.items():
            component_statuses[name] = {
                'status': result.status.value,
                'message': result.message,
                'response_time': result.response_time,
                'last_check': result.timestamp.isoformat()
            }
        
        return {
            'overall_status': overall_status.value,
            'components': component_statuses,
            'total_components': len(self.checkers),
            'healthy_components': len([r for r in self.last_results.values() if r.status == HealthStatus.HEALTHY]),
            'warning_components': len([r for r in self.last_results.values() if r.status == HealthStatus.WARNING]),
            'critical_components': len([r for r in self.last_results.values() if r.status == HealthStatus.CRITICAL]),
            'last_check_time': max([r.timestamp for r in self.last_results.values()]).isoformat() if self.last_results else None
        }
    
    async def start_monitoring(self):
        """开始健康监控"""
        if self.monitoring:
            logger.warning("Health monitoring is already running")
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info(f"Health monitoring started with {self.check_interval}s interval")
    
    async def stop_monitoring(self):
        """停止健康监控"""
        if not self.monitoring:
            return
        
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Health monitoring stopped")
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                await self.check_all_health()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitoring loop error: {str(e)}")
                await asyncio.sleep(self.check_interval)


# 全局健康监控器实例
_health_monitor: Optional[HealthMonitor] = None


def get_health_monitor() -> HealthMonitor:
    """获取全局健康监控器实例"""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = HealthMonitor()
    return _health_monitor


def setup_health_monitoring(database_manager=None, api_client=None, redis_client=None):
    """设置健康监控"""
    monitor = get_health_monitor()
    
    # 添加系统资源检查器
    monitor.add_checker(SystemResourcesHealthChecker())
    
    # 添加应用程序检查器
    monitor.add_checker(ApplicationHealthChecker())
    
    # 添加数据库检查器
    if database_manager:
        monitor.add_checker(DatabaseHealthChecker(database_manager))
    
    # 添加API检查器
    if api_client:
        monitor.add_checker(APIHealthChecker(api_client))
    
    # 添加Redis检查器
    if redis_client:
        monitor.add_checker(RedisHealthChecker(redis_client))
    
    logger.info("Health monitoring setup completed")
