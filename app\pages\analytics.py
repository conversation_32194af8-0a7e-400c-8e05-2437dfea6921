"""
Ares数据分析页面
提供深度数据分析、报告生成和趋势预测
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import random
import numpy as np

from app.utils.state_manager import StateManager


def show_analytics_page():
    """显示数据分析页面"""
    st.markdown('<h1 class="section-title">📊 数据分析</h1>', unsafe_allow_html=True)
    
    # 获取状态管理器
    state_manager = StateManager()
    
    # 页面选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["📈 趋势分析", "🔍 深度洞察", "📋 报告生成", "🤖 AI预测"])
    
    with tab1:
        show_trend_analysis(state_manager)
    
    with tab2:
        show_deep_insights(state_manager)
    
    with tab3:
        show_report_generation(state_manager)
    
    with tab4:
        show_ai_predictions(state_manager)


def show_trend_analysis(state_manager: StateManager):
    """显示趋势分析"""
    st.markdown("### 📈 趋势分析")
    
    # 分析选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        analysis_type = st.selectbox(
            "分析类型",
            ["价格趋势", "交易量趋势", "市场情绪", "波动性分析"]
        )
    
    with col2:
        time_period = st.selectbox(
            "时间周期",
            ["7天", "30天", "90天", "1年"]
        )
    
    with col3:
        comparison_mode = st.selectbox(
            "对比模式",
            ["单品分析", "类别对比", "市场对比"]
        )
    
    # 生成趋势数据
    if analysis_type == "价格趋势":
        show_price_trend_analysis(time_period, comparison_mode)
    elif analysis_type == "交易量趋势":
        show_volume_trend_analysis(time_period, comparison_mode)
    elif analysis_type == "市场情绪":
        show_market_sentiment_analysis(time_period)
    elif analysis_type == "波动性分析":
        show_volatility_analysis(time_period)


def show_price_trend_analysis(time_period, comparison_mode):
    """显示价格趋势分析"""
    st.markdown("#### 💰 价格趋势分析")
    
    # 生成模拟数据
    days_map = {"7天": 7, "30天": 30, "90天": 90, "1年": 365}
    days = days_map.get(time_period, 30)
    
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), end=datetime.now(), freq='D')
    
    if comparison_mode == "单品分析":
        selected_item = st.selectbox(
            "选择商品",
            ["AK-47 | Redline", "AWP | Dragon Lore", "Karambit | Fade"]
        )
        
        # 生成单品价格数据
        base_price = random.uniform(100, 1000)
        prices = []
        
        for i in range(len(dates)):
            change = random.uniform(-3, 3)
            base_price *= (1 + change / 100)
            prices.append(base_price)
        
        df = pd.DataFrame({
            'date': dates,
            'price': prices,
            'item': selected_item
        })
        
        fig = px.line(df, x='date', y='price', title=f'{selected_item} 价格趋势')
        
    elif comparison_mode == "类别对比":
        categories = ['步枪', '狙击枪', '刀具', '手枪']
        
        data = []
        for category in categories:
            base_price = random.uniform(50, 500)
            for date in dates:
                change = random.uniform(-2, 2)
                base_price *= (1 + change / 100)
                data.append({
                    'date': date,
                    'price': base_price,
                    'category': category
                })
        
        df = pd.DataFrame(data)
        fig = px.line(df, x='date', y='price', color='category', title='各类别价格趋势对比')
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 趋势统计
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("平均涨幅", f"{random.uniform(-5, 15):+.2f}%")
    
    with col2:
        st.metric("最大涨幅", f"{random.uniform(10, 30):+.2f}%")
    
    with col3:
        st.metric("最大跌幅", f"{random.uniform(-20, -5):+.2f}%")
    
    with col4:
        st.metric("波动率", f"{random.uniform(5, 25):.2f}%")


def show_volume_trend_analysis(time_period, comparison_mode):
    """显示交易量趋势分析"""
    st.markdown("#### 📊 交易量趋势分析")
    
    days_map = {"7天": 7, "30天": 30, "90天": 90, "1年": 365}
    days = days_map.get(time_period, 30)
    
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), end=datetime.now(), freq='D')
    
    # 生成交易量数据
    volumes = [random.randint(50, 500) for _ in dates]
    
    df = pd.DataFrame({
        'date': dates,
        'volume': volumes
    })
    
    fig = px.bar(df, x='date', y='volume', title='交易量趋势')
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_market_sentiment_analysis(time_period):
    """显示市场情绪分析"""
    st.markdown("#### 😊 市场情绪分析")
    
    # 情绪指标
    col1, col2, col3 = st.columns(3)
    
    with col1:
        sentiment_score = random.uniform(0.3, 0.8)
        st.metric(
            "市场情绪指数",
            f"{sentiment_score:.2f}",
            delta=f"{random.uniform(-0.1, 0.1):+.2f}"
        )
    
    with col2:
        fear_greed = random.randint(20, 80)
        st.metric(
            "恐惧贪婪指数",
            f"{fear_greed}",
            delta=f"{random.randint(-10, 10):+d}"
        )
    
    with col3:
        volatility_index = random.uniform(10, 40)
        st.metric(
            "波动性指数",
            f"{volatility_index:.1f}",
            delta=f"{random.uniform(-5, 5):+.1f}"
        )
    
    # 情绪分布图
    sentiment_data = {
        '极度恐惧': random.randint(5, 15),
        '恐惧': random.randint(15, 25),
        '中性': random.randint(30, 50),
        '贪婪': random.randint(15, 25),
        '极度贪婪': random.randint(5, 15)
    }
    
    fig = px.pie(
        values=list(sentiment_data.values()),
        names=list(sentiment_data.keys()),
        title="市场情绪分布"
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_volatility_analysis(time_period):
    """显示波动性分析"""
    st.markdown("#### 📈 波动性分析")
    
    # 生成波动性数据
    items = ['AK-47 | Redline', 'AWP | Dragon Lore', 'Karambit | Fade', 'M4A4 | Howl']
    volatility_data = []
    
    for item in items:
        volatility_data.append({
            'item': item,
            'volatility': random.uniform(5, 30),
            'risk_level': random.choice(['低', '中', '高'])
        })
    
    df = pd.DataFrame(volatility_data)
    
    fig = px.bar(
        df,
        x='item',
        y='volatility',
        color='risk_level',
        title='各商品波动性对比'
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        xaxis_tickangle=-45
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_deep_insights(state_manager: StateManager):
    """显示深度洞察"""
    st.markdown("### 🔍 深度洞察")
    
    # 洞察类型选择
    insight_type = st.selectbox(
        "洞察类型",
        ["投资组合分析", "风险评估", "机会发现", "相关性分析"]
    )
    
    if insight_type == "投资组合分析":
        show_portfolio_insights(state_manager)
    elif insight_type == "风险评估":
        show_risk_assessment(state_manager)
    elif insight_type == "机会发现":
        show_opportunity_discovery(state_manager)
    elif insight_type == "相关性分析":
        show_correlation_analysis(state_manager)


def show_portfolio_insights(state_manager: StateManager):
    """显示投资组合洞察"""
    st.markdown("#### 💼 投资组合深度分析")
    
    portfolio_data = state_manager.get_portfolio_data()
    
    # 风险收益散点图
    holdings_df = pd.DataFrame(portfolio_data['holdings'])
    
    # 添加模拟的风险数据
    holdings_df['risk_score'] = [random.uniform(1, 10) for _ in range(len(holdings_df))]
    
    fig = px.scatter(
        holdings_df,
        x='risk_score',
        y='profit_percentage',
        size='total_value',
        hover_name='name',
        title='风险-收益分析',
        labels={'risk_score': '风险评分', 'profit_percentage': '收益率(%)'}
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 投资组合建议
    st.markdown("#### 💡 投资建议")
    
    suggestions = [
        "🔴 高风险持仓：M4A4 | Howl 风险评分较高，建议适当减仓",
        "🟢 优质资产：AK-47 | Redline 表现稳定，可考虑增持",
        "🟡 关注机会：Karambit | Fade 近期波动较大，注意时机",
        "🔵 分散建议：当前持仓集中度较高，建议增加品类多样性"
    ]
    
    for suggestion in suggestions:
        st.markdown(f"- {suggestion}")


def show_risk_assessment(state_manager: StateManager):
    """显示风险评估"""
    st.markdown("#### ⚠️ 风险评估")
    
    # 风险指标
    col1, col2, col3 = st.columns(3)
    
    with col1:
        portfolio_risk = random.uniform(3, 8)
        risk_color = "🔴" if portfolio_risk > 7 else "🟡" if portfolio_risk > 5 else "🟢"
        st.metric(f"{risk_color} 投资组合风险", f"{portfolio_risk:.1f}/10")
    
    with col2:
        concentration_risk = random.uniform(2, 9)
        conc_color = "🔴" if concentration_risk > 7 else "🟡" if concentration_risk > 5 else "🟢"
        st.metric(f"{conc_color} 集中度风险", f"{concentration_risk:.1f}/10")
    
    with col3:
        liquidity_risk = random.uniform(1, 6)
        liq_color = "🔴" if liquidity_risk > 5 else "🟡" if liquidity_risk > 3 else "🟢"
        st.metric(f"{liq_color} 流动性风险", f"{liquidity_risk:.1f}/10")
    
    # 风险分解
    risk_breakdown = {
        '市场风险': 35,
        '流动性风险': 20,
        '集中度风险': 25,
        '操作风险': 15,
        '其他风险': 5
    }
    
    fig = px.pie(
        values=list(risk_breakdown.values()),
        names=list(risk_breakdown.keys()),
        title="风险构成分析"
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_opportunity_discovery(state_manager: StateManager):
    """显示机会发现"""
    st.markdown("#### 🎯 投资机会发现")
    
    # 机会列表
    opportunities = [
        {
            'item': 'Glock-18 | Fade (Factory New)',
            'opportunity_type': '价格低估',
            'confidence': 85,
            'potential_return': 15.2,
            'risk_level': '中',
            'reason': '历史价格分析显示当前价格低于合理估值'
        },
        {
            'item': 'M4A1-S | Knight (Factory New)',
            'opportunity_type': '趋势突破',
            'confidence': 72,
            'potential_return': 22.8,
            'risk_level': '高',
            'reason': '技术指标显示即将突破关键阻力位'
        },
        {
            'item': 'USP-S | Kill Confirmed (Minimal Wear)',
            'opportunity_type': '套利机会',
            'confidence': 91,
            'potential_return': 8.5,
            'risk_level': '低',
            'reason': '不同平台间存在价格差异'
        }
    ]
    
    for opp in opportunities:
        confidence_color = "🟢" if opp['confidence'] > 80 else "🟡" if opp['confidence'] > 60 else "🔴"
        risk_color = {"低": "🟢", "中": "🟡", "高": "🔴"}[opp['risk_level']]
        
        st.markdown(f"""
        <div style="
            padding: 1rem;
            margin-bottom: 0.5rem;
            background-color: rgba(38, 39, 48, 0.8);
            border-radius: 8px;
            border-left: 4px solid #1f77b4;
        ">
            <h4 style="margin: 0; color: #1f77b4;">{opp['item']}</h4>
            <p style="margin: 0.25rem 0; color: #fafafa;"><strong>机会类型:</strong> {opp['opportunity_type']}</p>
            <div style="display: flex; justify-content: space-between; margin: 0.5rem 0;">
                <span>{confidence_color} 置信度: {opp['confidence']}%</span>
                <span>🎯 预期收益: +{opp['potential_return']:.1f}%</span>
                <span>{risk_color} 风险等级: {opp['risk_level']}</span>
            </div>
            <p style="margin: 0.25rem 0; color: #a6a6a6; font-style: italic;">{opp['reason']}</p>
        </div>
        """, unsafe_allow_html=True)


def show_correlation_analysis(state_manager: StateManager):
    """显示相关性分析"""
    st.markdown("#### 🔗 相关性分析")
    
    # 生成相关性矩阵
    items = ['AK-47 | Redline', 'AWP | Dragon Lore', 'Karambit | Fade', 'M4A4 | Howl', 'Glock-18 | Fade']
    
    # 生成模拟相关性数据
    correlation_matrix = np.random.rand(len(items), len(items))
    correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2  # 使矩阵对称
    np.fill_diagonal(correlation_matrix, 1)  # 对角线为1
    
    fig = px.imshow(
        correlation_matrix,
        x=items,
        y=items,
        color_continuous_scale='RdBu',
        title="商品价格相关性矩阵"
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_report_generation(state_manager: StateManager):
    """显示报告生成"""
    st.markdown("### 📋 报告生成")
    
    # 报告类型选择
    col1, col2 = st.columns(2)
    
    with col1:
        report_type = st.selectbox(
            "报告类型",
            ["投资组合报告", "市场分析报告", "风险评估报告", "绩效分析报告"]
        )
    
    with col2:
        report_period = st.selectbox(
            "报告周期",
            ["日报", "周报", "月报", "季报", "年报"]
        )
    
    # 报告配置
    st.markdown("#### 报告配置")
    
    include_charts = st.checkbox("包含图表", value=True)
    include_recommendations = st.checkbox("包含投资建议", value=True)
    include_risk_analysis = st.checkbox("包含风险分析", value=True)
    
    # 生成报告按钮
    if st.button("生成报告", use_container_width=True):
        with st.spinner("正在生成报告..."):
            # 模拟报告生成过程
            import time
            time.sleep(2)
            
            st.success("报告生成完成！")
            
            # 显示报告预览
            show_report_preview(report_type, report_period, state_manager)


def show_report_preview(report_type, report_period, state_manager):
    """显示报告预览"""
    st.markdown("#### 📄 报告预览")
    
    # 报告标题
    st.markdown(f"# {report_type} - {report_period}")
    st.markdown(f"**生成时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行摘要
    st.markdown("## 📊 执行摘要")
    
    if report_type == "投资组合报告":
        portfolio_data = state_manager.get_portfolio_data()
        
        st.markdown(f"""
        - **投资组合总价值:** ${portfolio_data['total_value']:,.2f}
        - **总收益率:** {portfolio_data['profit_percentage']:+.2f}%
        - **持仓数量:** {len(portfolio_data['holdings'])} 个
        - **最佳表现:** AK-47 | Redline (+15.2%)
        - **风险评级:** 中等风险
        """)
    
    elif report_type == "市场分析报告":
        market_data = state_manager.get_market_data()
        
        st.markdown(f"""
        - **市场总商品数:** {market_data['total_items']:,}
        - **活跃挂单数:** {market_data['active_listings']:,}
        - **平均价格变化:** {market_data['avg_price_change']:+.1f}%
        - **24小时交易量:** {market_data['volume_24h']:,}
        - **市场情绪:** 谨慎乐观
        """)
    
    # 下载按钮
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("下载PDF报告"):
            st.info("PDF下载功能开发中...")
    
    with col2:
        if st.button("下载Excel报告"):
            st.info("Excel下载功能开发中...")


def show_ai_predictions(state_manager: StateManager):
    """显示AI预测"""
    st.markdown("### 🤖 AI预测")
    
    st.info("🚧 AI预测功能正在开发中，敬请期待！")
    
    # 预测类型选择
    prediction_type = st.selectbox(
        "预测类型",
        ["价格预测", "趋势预测", "风险预测", "机会预测"]
    )
    
    # 预测时间范围
    prediction_horizon = st.selectbox(
        "预测时间范围",
        ["1天", "7天", "30天", "90天"]
    )
    
    # 模拟预测结果
    if st.button("开始预测"):
        with st.spinner("AI模型正在分析..."):
            import time
            time.sleep(3)
            
            st.success("预测完成！")
            
            # 显示模拟预测结果
            st.markdown("#### 🔮 预测结果")
            
            if prediction_type == "价格预测":
                st.markdown("""
                **AK-47 | Redline 价格预测:**
                - 7天内预测涨幅: +8.5% (置信度: 72%)
                - 关键支撑位: $48.50
                - 关键阻力位: $58.20
                """)
            
            elif prediction_type == "趋势预测":
                st.markdown("""
                **市场趋势预测:**
                - 整体趋势: 上涨 📈
                - 预测强度: 中等
                - 关键转折点: 2024年8月15日
                """)


if __name__ == "__main__":
    show_analytics_page()
