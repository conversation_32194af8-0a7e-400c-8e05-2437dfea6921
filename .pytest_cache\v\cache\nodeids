["tests/test_dashboard.py::TestDataTable::test_create_discovery_table", "tests/test_dashboard.py::TestDataTable::test_create_portfolio_table", "tests/test_dashboard.py::TestDataTable::test_create_watchlist_table", "tests/test_dashboard.py::TestDataTable::test_data_table_initialization", "tests/test_dashboard.py::TestFilterPanel::test_add_filter", "tests/test_dashboard.py::TestFilterPanel::test_apply_filters_to_dataframe", "tests/test_dashboard.py::TestFilterPanel::test_create_portfolio_filter_panel", "tests/test_dashboard.py::TestFilterPanel::test_create_watchlist_filter_panel", "tests/test_dashboard.py::TestFilterPanel::test_filter_panel_initialization", "tests/test_deployment.py::TestDockerConfiguration::test_docker_compose_exists", "tests/test_deployment.py::TestDockerConfiguration::test_docker_compose_syntax", "tests/test_deployment.py::TestDockerConfiguration::test_dockerfile_exists", "tests/test_deployment.py::TestDockerConfiguration::test_dockerfile_syntax", "tests/test_deployment.py::TestDockerConfiguration::test_production_compose_exists", "tests/test_deployment.py::TestDockerConfiguration::test_production_compose_syntax", "tests/test_discoverer.py::TestDiscoveryOpportunity::test_discovery_opportunity_creation", "tests/test_discoverer.py::TestDiscoveryOpportunity::test_discovery_opportunity_to_dict", "tests/test_discoverer.py::TestDiscoveryStats::test_discovery_stats_initialization", "tests/test_discoverer.py::TestDiscoveryStats::test_discovery_stats_to_dict", "tests/test_holdings.py::TestDataExporter::test_data_exporter_initialization", "tests/test_holdings.py::TestDataExporter::test_export_to_csv", "tests/test_holdings.py::TestDataExporter::test_export_to_json", "tests/test_holdings.py::TestDataExporter::test_prepare_holdings_data", "tests/test_holdings.py::TestHolding::test_holding_initialization", "tests/test_holdings.py::TestHolding::test_holding_post_init_calculation", "tests/test_holdings.py::TestHolding::test_holding_to_dict", "tests/test_scheduler.py::TestIntelligentScheduler::test_add_remove_item_from_schedule", "tests/test_scheduler.py::TestIntelligentScheduler::test_api_limit_check", "tests/test_scheduler.py::TestIntelligentScheduler::test_get_schedule_status", "tests/test_scheduler.py::TestIntelligentScheduler::test_scheduler_initialization", "tests/test_scheduler.py::TestIntelligentScheduler::test_state_persistence", "tests/test_scheduler.py::TestPriorityCalculator::test_batch_priority_calculation", "tests/test_scheduler.py::TestPriorityCalculator::test_calculate_priority_score", "tests/test_scheduler.py::TestPriorityCalculator::test_pool_priority_thresholds", "tests/test_scheduler.py::TestPriorityCalculator::test_pool_promotion_demotion", "tests/test_scheduler.py::TestPriorityCalculator::test_priority_calculator_initialization", "tests/test_scheduler.py::TestPriorityCalculator::test_spread_score_calculation", "tests/test_scheduler.py::TestPriorityCalculator::test_user_interest_score", "tests/test_scheduler.py::TestPriorityCalculator::test_volume_score_calculation", "tests/test_scheduler.py::TestPriorityCalculator::test_weights_update", "tests/test_scheduler.py::TestScheduleTask::test_schedule_task_comparison", "tests/test_scheduler.py::TestScheduleTask::test_schedule_task_creation", "tests/test_scheduler.py::TestSchedulerState::test_scheduler_state_initialization", "tests/test_scheduler.py::test_calculate_priority_score_function", "tests/test_scheduler.py::test_global_priority_calculator", "tests/test_steamdt_api.py::TestItemPriceData::test_get_best_sell_price", "tests/test_steamdt_api.py::TestItemPriceData::test_get_steam_price", "tests/test_steamdt_api.py::TestPlatformPrice::test_from_dict", "tests/test_steamdt_api.py::TestSteamdtAPIManager::test_get_batch_prices", "tests/test_steamdt_api.py::TestSteamdtAPIManager::test_get_item_price", "tests/test_steamdt_api.py::TestSteamdtAPIManager::test_get_item_wear_data", "tests/test_tracker.py::TestTrackingBatch::test_tracking_batch_creation", "tests/test_tracker.py::TestTrackingStats::test_tracking_stats_initialization", "tests/test_tracker.py::TestTrackingStats::test_tracking_stats_to_dict"]