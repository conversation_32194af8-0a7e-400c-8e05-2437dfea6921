# CS2饰品投资系统部署指南

## 📋 目录

- [系统概述](#系统概述)
- [环境要求](#环境要求)
- [安装部署](#安装部署)
- [配置说明](#配置说明)
- [启动运行](#启动运行)
- [监控运维](#监控运维)
- [性能优化](#性能优化)
- [故障排除](#故障排除)
- [API限制管理](#api限制管理)

## 🎯 系统概述

CS2饰品投资系统是一个专业的Counter-Strike 2饰品投资决策支持平台，提供以下核心功能：

- **实时价格监控**: 集成steamdt.com API，监控Steam、BUFF、C5Game等主流平台价格
- **跨平台套利分析**: 智能识别跨平台价差，计算套利机会和风险评估
- **宏观市场分析**: 基于CS2玩家在线数据分析市场整体趋势
- **分层监控系统**: 战略储备、核心关注池、发现监控的三层架构
- **专业投资界面**: CS2游戏风格的用户界面，支持多种数据可视化

## 🔧 环境要求

### 硬件要求

**最低配置:**
- CPU: 2核心 2.0GHz
- 内存: 4GB RAM
- 存储: 20GB 可用空间
- 网络: 稳定的互联网连接

**推荐配置:**
- CPU: 4核心 3.0GHz
- 内存: 8GB RAM
- 存储: 50GB SSD
- 网络: 100Mbps+ 带宽

### 软件要求

- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.8+ (推荐 3.9+)
- **数据库**: SQLite (默认) 或 PostgreSQL (生产环境)
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+

## 📦 安装部署

### 1. 获取源代码

```bash
# 克隆项目仓库
git clone https://github.com/your-org/csgoares.git
cd csgoares
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 如果使用PostgreSQL
pip install psycopg2-binary
```

### 4. 数据库初始化

```bash
# 创建数据库表
python scripts/init_database.py

# 导入初始数据（可选）
python scripts/import_initial_data.py
```

## ⚙️ 配置说明

### 1. 环境变量配置

创建 `.env` 文件：

```bash
# API配置
STEAMDT_API_KEY=your_steamdt_api_key_here
STEAM_API_KEY=your_steam_api_key_here

# 数据库配置
DATABASE_URL=sqlite:///data/csgoares.db
# 或使用PostgreSQL
# DATABASE_URL=postgresql://user:password@localhost:5432/csgoares

# 缓存配置
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=300

# API限制配置
STEAMDT_RATE_LIMIT=60  # 每分钟请求数
STEAM_RATE_LIMIT=100   # 每分钟请求数

# 监控配置
MONITORING_INTERVAL=30  # 监控间隔（秒）
ALERT_EMAIL=<EMAIL>

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/csgoares.log
```

### 2. API密钥获取

**SteamDT API密钥:**
1. 访问 [steamdt.com](https://steamdt.com)
2. 注册账户并申请API访问权限
3. 获取API密钥并配置到环境变量

**Steam API密钥:**
1. 访问 [Steam Web API](https://steamcommunity.com/dev/apikey)
2. 使用Steam账户登录
3. 申请API密钥并配置到环境变量

### 3. 数据库配置

**SQLite (开发/测试环境):**
```python
DATABASE_URL=sqlite:///data/csgoares.db
```

**PostgreSQL (生产环境):**
```python
DATABASE_URL=postgresql://username:password@localhost:5432/csgoares
```

## 🚀 启动运行

### 1. 开发环境启动

```bash
# 启动Web界面
streamlit run app/main.py --server.port 8501

# 启动后台监控服务
python scripts/start_monitoring.py

# 启动性能监控
python scripts/performance_monitor.py --duration 60
```

### 2. 生产环境部署

**使用Docker (推荐):**

```bash
# 构建镜像
docker build -t csgoares:latest .

# 运行容器
docker run -d \
  --name csgoares \
  -p 8501:8501 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  csgoares:latest
```

**使用systemd服务:**

创建 `/etc/systemd/system/csgoares.service`:

```ini
[Unit]
Description=CS2 Skin Investment System
After=network.target

[Service]
Type=simple
User=csgoares
WorkingDirectory=/opt/csgoares
Environment=PATH=/opt/csgoares/venv/bin
ExecStart=/opt/csgoares/venv/bin/streamlit run app/main.py --server.port 8501
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务:
```bash
sudo systemctl enable csgoares
sudo systemctl start csgoares
```

## 📊 监控运维

### 1. 系统监控

**启动系统监控:**
```bash
python scripts/start_monitoring.py --interval 30
```

**查看监控状态:**
```bash
# 查看系统状态
python scripts/check_system_status.py

# 查看API调用统计
python scripts/api_stats.py

# 查看性能报告
python scripts/performance_monitor.py --duration 10 --verbose
```

### 2. 日志管理

**日志文件位置:**
- 应用日志: `logs/csgoares.log`
- 错误日志: `logs/error.log`
- API调用日志: `logs/api.log`
- 性能日志: `logs/performance.log`

**日志轮转配置:**
```python
# logging.conf
[handler_fileHandler]
class=logging.handlers.RotatingFileHandler
args=('logs/csgoares.log', 'a', 10485760, 5)
```

### 3. 数据备份

**自动备份脚本:**
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/csgoares"

# 备份数据库
sqlite3 data/csgoares.db ".backup $BACKUP_DIR/db_$DATE.db"

# 备份配置文件
cp .env $BACKUP_DIR/env_$DATE.bak

# 清理7天前的备份
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
```

## ⚡ 性能优化

### 1. API调用优化

**批量请求策略:**
```python
# 配置批量大小
BATCH_SIZE = 50  # 每批处理50个饰品

# 配置请求间隔
REQUEST_INTERVAL = 1.0  # 请求间隔1秒
```

**缓存策略:**
```python
# 价格数据缓存
PRICE_CACHE_TTL = 300  # 5分钟

# 宏观数据缓存
MACRO_CACHE_TTL = 600  # 10分钟
```

### 2. 数据库优化

**索引优化:**
```sql
-- 为常用查询字段创建索引
CREATE INDEX idx_item_name ON items(market_hash_name);
CREATE INDEX idx_timestamp ON price_history(timestamp);
CREATE INDEX idx_platform ON price_data(platform);
```

**查询优化:**
```python
# 使用分页查询大量数据
QUERY_PAGE_SIZE = 100

# 使用连接池
DATABASE_POOL_SIZE = 10
```

### 3. 内存优化

**数据结构优化:**
```python
# 使用生成器处理大量数据
def process_items_generator(items):
    for item in items:
        yield process_item(item)

# 及时清理不需要的数据
import gc
gc.collect()
```

## 🔧 故障排除

### 1. 常见问题

**问题: API调用失败**
```
错误: HTTPError: 429 Too Many Requests
解决: 检查API限制配置，增加请求间隔
```

**问题: 数据库连接失败**
```
错误: OperationalError: database is locked
解决: 检查数据库文件权限，确保没有其他进程占用
```

**问题: 内存使用过高**
```
错误: MemoryError
解决: 减少批量处理大小，增加垃圾回收频率
```

### 2. 诊断工具

**系统诊断:**
```bash
# 检查系统状态
python scripts/diagnose_system.py

# 检查API连通性
python scripts/test_api_connectivity.py

# 检查数据库状态
python scripts/check_database.py
```

**性能诊断:**
```bash
# 生成性能报告
python scripts/performance_monitor.py --duration 5 --verbose

# 检查内存使用
python scripts/memory_profiler.py

# 检查API响应时间
python scripts/api_benchmark.py
```

## 🚦 API限制管理

### 1. 限制策略

**SteamDT API限制:**
- 免费版: 60次/分钟
- 付费版: 300次/分钟
- 建议间隔: 1-2秒

**Steam API限制:**
- 标准限制: 100次/分钟
- 建议间隔: 0.6秒

### 2. 限制监控

**实时监控:**
```python
# 监控API调用频率
api_monitor = APIRateMonitor()
api_monitor.track_request('steamdt')

# 检查是否超限
if api_monitor.is_rate_limited('steamdt'):
    await asyncio.sleep(api_monitor.get_wait_time('steamdt'))
```

**告警配置:**
```python
# 配置告警阈值
RATE_LIMIT_WARNING = 0.8  # 80%时告警
RATE_LIMIT_CRITICAL = 0.95  # 95%时停止请求
```

### 3. 优化策略

**智能调度:**
```python
# 优先级队列
HIGH_PRIORITY = ['战略储备']
MEDIUM_PRIORITY = ['核心关注池']
LOW_PRIORITY = ['发现监控']

# 动态调整间隔
def calculate_request_interval(current_usage):
    if current_usage > 0.9:
        return 2.0  # 高使用率时增加间隔
    elif current_usage > 0.7:
        return 1.5
    else:
        return 1.0  # 正常间隔
```

## 📞 技术支持

如遇到部署或运维问题，请：

1. 查看日志文件获取详细错误信息
2. 运行诊断脚本检查系统状态
3. 参考故障排除章节的解决方案
4. 联系技术支持团队

**联系方式:**
- 邮箱: <EMAIL>
- 文档: https://docs.csgoares.com
- 社区: https://community.csgoares.com

---

*最后更新: 2024年1月*
