"""
Ares系统缓存管理器
提供Redis缓存、本地缓存和多级缓存策略，支持热点数据缓存、查询结果缓存和会话状态管理
"""

import json
import pickle
import hashlib
import time
import asyncio
import logging
from typing import Any, Optional, Dict, List, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod

try:
    import redis.asyncio as redis
    import redis.exceptions
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

from .config import get_config_manager
from .metrics import get_metrics_collector
from .exceptions import AresException, ErrorCategory, ErrorSeverity

logger = logging.getLogger(__name__)


class CacheLevel(Enum):
    """缓存级别"""
    L1_MEMORY = "l1_memory"      # 内存缓存（最快）
    L2_REDIS = "l2_redis"        # Redis缓存（快）
    L3_DATABASE = "l3_database"  # 数据库（慢）


class CacheStrategy(Enum):
    """缓存策略"""
    WRITE_THROUGH = "write_through"    # 写穿透
    WRITE_BACK = "write_back"          # 写回
    WRITE_AROUND = "write_around"      # 写绕过


@dataclass
class CacheConfig:
    """缓存配置"""
    default_ttl: int = 1800  # 默认TTL 30分钟
    hot_data_ttl: int = 600   # 热点数据TTL 10分钟
    query_result_ttl: int = 300  # 查询结果TTL 5分钟
    session_ttl: int = 3600   # 会话TTL 1小时
    max_memory_items: int = 1000  # 内存缓存最大条目数
    compression_enabled: bool = True  # 启用压缩
    serialization_format: str = "json"  # 序列化格式: json, pickle


@dataclass
class CacheItem:
    """缓存项"""
    key: str
    value: Any
    ttl: int
    created_at: float = field(default_factory=time.time)
    accessed_at: float = field(default_factory=time.time)
    access_count: int = 0
    
    @property
    def is_expired(self) -> bool:
        """检查是否过期"""
        return time.time() - self.created_at > self.ttl
    
    @property
    def age(self) -> float:
        """获取年龄（秒）"""
        return time.time() - self.created_at
    
    def touch(self):
        """更新访问时间"""
        self.accessed_at = time.time()
        self.access_count += 1


class CacheBackend(ABC):
    """缓存后端抽象基类"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """清空缓存"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        pass


class MemoryCacheBackend(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache: Dict[str, CacheItem] = {}
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0
        }
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            item = self.cache[key]
            if item.is_expired:
                await self.delete(key)
                self.stats['misses'] += 1
                return None
            
            item.touch()
            self.stats['hits'] += 1
            return item.value
        
        self.stats['misses'] += 1
        return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存值"""
        if ttl is None:
            ttl = self.config.default_ttl
        
        # 检查是否需要清理过期项
        await self._cleanup_expired()
        
        # 检查是否需要驱逐项
        if len(self.cache) >= self.config.max_memory_items:
            await self._evict_lru()
        
        self.cache[key] = CacheItem(key=key, value=value, ttl=ttl)
        self.stats['sets'] += 1
        return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        if key in self.cache:
            del self.cache[key]
            self.stats['deletes'] += 1
            return True
        return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if key in self.cache:
            item = self.cache[key]
            if item.is_expired:
                await self.delete(key)
                return False
            return True
        return False
    
    async def clear(self) -> bool:
        """清空缓存"""
        self.cache.clear()
        return True
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            'total_items': len(self.cache),
            'hit_rate': hit_rate,
            'memory_usage': sum(len(str(item.value)) for item in self.cache.values())
        }
    
    async def _cleanup_expired(self):
        """清理过期项"""
        expired_keys = [key for key, item in self.cache.items() if item.is_expired]
        for key in expired_keys:
            del self.cache[key]
    
    async def _evict_lru(self):
        """驱逐最近最少使用的项"""
        if not self.cache:
            return
        
        # 找到最近最少访问的项
        lru_key = min(self.cache.keys(), key=lambda k: self.cache[k].accessed_at)
        del self.cache[lru_key]
        self.stats['evictions'] += 1


class RedisCacheBackend(CacheBackend):
    """Redis缓存后端"""
    
    def __init__(self, config: CacheConfig, redis_url: str):
        self.config = config
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
        
        if not REDIS_AVAILABLE:
            raise AresException(
                "Redis not available. Install with: pip install redis",
                category=ErrorCategory.CONFIGURATION_ERROR,
                severity=ErrorSeverity.HIGH
            )
    
    async def _get_client(self) -> redis.Redis:
        """获取Redis客户端"""
        if self.redis_client is None:
            try:
                self.redis_client = redis.from_url(
                    self.redis_url,
                    decode_responses=False,  # 保持二进制数据
                    socket_timeout=5,
                    socket_connect_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                # 测试连接
                await self.redis_client.ping()
                logger.info("Redis connection established")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {str(e)}")
                raise AresException(
                    f"Redis connection failed: {str(e)}",
                    category=ErrorCategory.SYSTEM_ERROR,
                    severity=ErrorSeverity.HIGH
                )
        
        return self.redis_client
    
    def _serialize(self, value: Any) -> bytes:
        """序列化值"""
        try:
            if self.config.serialization_format == "json":
                return json.dumps(value, default=str).encode('utf-8')
            else:  # pickle
                return pickle.dumps(value)
        except Exception as e:
            logger.error(f"Serialization failed: {str(e)}")
            raise
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化值"""
        try:
            if self.config.serialization_format == "json":
                return json.loads(data.decode('utf-8'))
            else:  # pickle
                return pickle.loads(data)
        except Exception as e:
            logger.error(f"Deserialization failed: {str(e)}")
            raise
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            client = await self._get_client()
            data = await client.get(key)
            
            if data is not None:
                self.stats['hits'] += 1
                return self._deserialize(data)
            else:
                self.stats['misses'] += 1
                return None
        
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Redis get error: {str(e)}")
            return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存值"""
        try:
            if ttl is None:
                ttl = self.config.default_ttl
            
            client = await self._get_client()
            data = self._serialize(value)
            
            result = await client.setex(key, ttl, data)
            if result:
                self.stats['sets'] += 1
                return True
            return False
        
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Redis set error: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            client = await self._get_client()
            result = await client.delete(key)
            
            if result > 0:
                self.stats['deletes'] += 1
                return True
            return False
        
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Redis delete error: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            client = await self._get_client()
            result = await client.exists(key)
            return result > 0
        
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Redis exists error: {str(e)}")
            return False
    
    async def clear(self) -> bool:
        """清空缓存"""
        try:
            client = await self._get_client()
            await client.flushdb()
            return True
        
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Redis clear error: {str(e)}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        try:
            client = await self._get_client()
            info = await client.info()
            
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'redis_memory_used': info.get('used_memory', 0),
                'redis_memory_human': info.get('used_memory_human', '0B'),
                'redis_connected_clients': info.get('connected_clients', 0),
                'redis_total_commands_processed': info.get('total_commands_processed', 0),
                'redis_keyspace_hits': info.get('keyspace_hits', 0),
                'redis_keyspace_misses': info.get('keyspace_misses', 0)
            }
        
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Redis stats error: {str(e)}")
            return self.stats
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None


class MultiLevelCache:
    """多级缓存管理器"""
    
    def __init__(self, config: CacheConfig, redis_url: Optional[str] = None):
        """
        初始化多级缓存
        
        Args:
            config: 缓存配置
            redis_url: Redis连接URL
        """
        self.config = config
        self.metrics = get_metrics_collector()
        
        # 初始化缓存后端
        self.l1_cache = MemoryCacheBackend(config)
        self.l2_cache = None
        
        if redis_url and REDIS_AVAILABLE:
            try:
                self.l2_cache = RedisCacheBackend(config, redis_url)
                logger.info("Multi-level cache initialized with Redis")
            except Exception as e:
                logger.warning(f"Redis cache initialization failed, using memory only: {str(e)}")
        else:
            logger.info("Multi-level cache initialized with memory only")
    
    async def get(self, key: str, use_l1: bool = True, use_l2: bool = True) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            use_l1: 是否使用L1缓存
            use_l2: 是否使用L2缓存
            
        Returns:
            缓存值或None
        """
        start_time = time.time()
        
        try:
            # 尝试从L1缓存获取
            if use_l1:
                value = await self.l1_cache.get(key)
                if value is not None:
                    self.metrics.increment_counter('cache_hits', tags={'level': 'l1'})
                    self.metrics.record_timer('cache_get_duration', time.time() - start_time, tags={'level': 'l1'})
                    return value
            
            # 尝试从L2缓存获取
            if use_l2 and self.l2_cache:
                value = await self.l2_cache.get(key)
                if value is not None:
                    # 回填到L1缓存
                    if use_l1:
                        await self.l1_cache.set(key, value, self.config.hot_data_ttl)
                    
                    self.metrics.increment_counter('cache_hits', tags={'level': 'l2'})
                    self.metrics.record_timer('cache_get_duration', time.time() - start_time, tags={'level': 'l2'})
                    return value
            
            # 缓存未命中
            self.metrics.increment_counter('cache_misses')
            self.metrics.record_timer('cache_get_duration', time.time() - start_time, tags={'level': 'miss'})
            return None
        
        except Exception as e:
            logger.error(f"Cache get error: {str(e)}")
            self.metrics.increment_counter('cache_errors', tags={'operation': 'get'})
            return None

    async def set(self, key: str, value: Any, ttl: int = None, use_l1: bool = True, use_l2: bool = True) -> bool:
        """
        设置缓存值

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            use_l1: 是否使用L1缓存
            use_l2: 是否使用L2缓存

        Returns:
            是否设置成功
        """
        start_time = time.time()
        success = True

        try:
            # 设置到L1缓存
            if use_l1:
                l1_success = await self.l1_cache.set(key, value, ttl)
                if not l1_success:
                    success = False
                    logger.warning(f"L1 cache set failed for key: {key}")

            # 设置到L2缓存
            if use_l2 and self.l2_cache:
                l2_success = await self.l2_cache.set(key, value, ttl)
                if not l2_success:
                    success = False
                    logger.warning(f"L2 cache set failed for key: {key}")

            if success:
                self.metrics.increment_counter('cache_sets')
            else:
                self.metrics.increment_counter('cache_errors', tags={'operation': 'set'})

            self.metrics.record_timer('cache_set_duration', time.time() - start_time)
            return success

        except Exception as e:
            logger.error(f"Cache set error: {str(e)}")
            self.metrics.increment_counter('cache_errors', tags={'operation': 'set'})
            return False

    async def delete(self, key: str, use_l1: bool = True, use_l2: bool = True) -> bool:
        """
        删除缓存值

        Args:
            key: 缓存键
            use_l1: 是否从L1缓存删除
            use_l2: 是否从L2缓存删除

        Returns:
            是否删除成功
        """
        start_time = time.time()
        success = True

        try:
            # 从L1缓存删除
            if use_l1:
                await self.l1_cache.delete(key)

            # 从L2缓存删除
            if use_l2 and self.l2_cache:
                await self.l2_cache.delete(key)

            self.metrics.increment_counter('cache_deletes')
            self.metrics.record_timer('cache_delete_duration', time.time() - start_time)
            return success

        except Exception as e:
            logger.error(f"Cache delete error: {str(e)}")
            self.metrics.increment_counter('cache_errors', tags={'operation': 'delete'})
            return False

    async def exists(self, key: str, use_l1: bool = True, use_l2: bool = True) -> bool:
        """
        检查缓存键是否存在

        Args:
            key: 缓存键
            use_l1: 是否检查L1缓存
            use_l2: 是否检查L2缓存

        Returns:
            是否存在
        """
        try:
            # 检查L1缓存
            if use_l1 and await self.l1_cache.exists(key):
                return True

            # 检查L2缓存
            if use_l2 and self.l2_cache and await self.l2_cache.exists(key):
                return True

            return False

        except Exception as e:
            logger.error(f"Cache exists error: {str(e)}")
            return False

    async def clear(self, use_l1: bool = True, use_l2: bool = True) -> bool:
        """
        清空缓存

        Args:
            use_l1: 是否清空L1缓存
            use_l2: 是否清空L2缓存

        Returns:
            是否清空成功
        """
        try:
            success = True

            if use_l1:
                l1_success = await self.l1_cache.clear()
                if not l1_success:
                    success = False

            if use_l2 and self.l2_cache:
                l2_success = await self.l2_cache.clear()
                if not l2_success:
                    success = False

            if success:
                logger.info("Cache cleared successfully")

            return success

        except Exception as e:
            logger.error(f"Cache clear error: {str(e)}")
            return False

    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            stats = {
                'l1_cache': await self.l1_cache.get_stats(),
                'l2_cache': None,
                'total_operations': 0,
                'overall_hit_rate': 0.0
            }

            if self.l2_cache:
                stats['l2_cache'] = await self.l2_cache.get_stats()

            # 计算总体统计
            l1_stats = stats['l1_cache']
            l2_stats = stats['l2_cache'] or {}

            total_hits = l1_stats.get('hits', 0) + l2_stats.get('hits', 0)
            total_misses = l1_stats.get('misses', 0) + l2_stats.get('misses', 0)
            total_operations = total_hits + total_misses

            stats['total_operations'] = total_operations
            stats['overall_hit_rate'] = (total_hits / total_operations * 100) if total_operations > 0 else 0

            return stats

        except Exception as e:
            logger.error(f"Cache stats error: {str(e)}")
            return {}

    async def close(self):
        """关闭缓存连接"""
        try:
            if self.l2_cache:
                await self.l2_cache.close()
            logger.info("Cache connections closed")
        except Exception as e:
            logger.error(f"Cache close error: {str(e)}")


class CacheManager:
    """缓存管理器 - 提供高级缓存功能"""

    def __init__(self, config: Optional[CacheConfig] = None, redis_url: Optional[str] = None):
        """
        初始化缓存管理器

        Args:
            config: 缓存配置
            redis_url: Redis连接URL
        """
        self.config = config or CacheConfig()

        # 从配置管理器获取Redis URL
        if redis_url is None:
            try:
                config_manager = get_config_manager()
                redis_url = config_manager.get('redis_url')
            except Exception:
                # 配置管理器不可用时使用默认值
                redis_url = None

        self.cache = MultiLevelCache(self.config, redis_url)
        self.metrics = get_metrics_collector()

        # 缓存键前缀
        self.key_prefixes = {
            'item_analysis': 'item_analysis:',
            'price_data': 'price_data:',
            'query_result': 'query_result:',
            'session': 'session:',
            'user_data': 'user_data:',
            'hot_data': 'hot_data:'
        }

    def _make_key(self, prefix: str, key: str) -> str:
        """生成缓存键"""
        return f"{self.key_prefixes.get(prefix, prefix)}{key}"

    def _hash_key(self, data: Union[str, Dict, List]) -> str:
        """生成数据哈希键"""
        if isinstance(data, str):
            content = data
        else:
            content = json.dumps(data, sort_keys=True, default=str)

        return hashlib.md5(content.encode('utf-8')).hexdigest()

    # 热点数据缓存
    async def get_hot_data(self, key: str) -> Optional[Any]:
        """获取热点数据"""
        cache_key = self._make_key('hot_data', key)
        return await self.cache.get(cache_key)

    async def set_hot_data(self, key: str, value: Any) -> bool:
        """设置热点数据"""
        cache_key = self._make_key('hot_data', key)
        return await self.cache.set(cache_key, value, self.config.hot_data_ttl)

    # 饰品分析缓存
    async def get_item_analysis(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取饰品分析数据"""
        cache_key = self._make_key('item_analysis', item_id)
        return await self.cache.get(cache_key)

    async def set_item_analysis(self, item_id: str, analysis_data: Dict[str, Any]) -> bool:
        """设置饰品分析数据"""
        cache_key = self._make_key('item_analysis', item_id)
        return await self.cache.set(cache_key, analysis_data, self.config.default_ttl)

    # 价格数据缓存
    async def get_price_data(self, item_id: str, platform: str = None) -> Optional[Dict[str, Any]]:
        """获取价格数据"""
        key = f"{item_id}:{platform}" if platform else item_id
        cache_key = self._make_key('price_data', key)
        return await self.cache.get(cache_key)

    async def set_price_data(self, item_id: str, price_data: Dict[str, Any], platform: str = None) -> bool:
        """设置价格数据"""
        key = f"{item_id}:{platform}" if platform else item_id
        cache_key = self._make_key('price_data', key)
        return await self.cache.set(cache_key, price_data, self.config.hot_data_ttl)

    # 查询结果缓存
    async def get_query_result(self, query_hash: str) -> Optional[Any]:
        """获取查询结果"""
        cache_key = self._make_key('query_result', query_hash)
        return await self.cache.get(cache_key)

    async def set_query_result(self, query_hash: str, result: Any) -> bool:
        """设置查询结果"""
        cache_key = self._make_key('query_result', query_hash)
        return await self.cache.set(cache_key, result, self.config.query_result_ttl)

    async def cache_query_result(self, query_data: Union[str, Dict, List], result: Any) -> str:
        """缓存查询结果并返回哈希键"""
        query_hash = self._hash_key(query_data)
        await self.set_query_result(query_hash, result)
        return query_hash

    # 会话管理
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话数据"""
        cache_key = self._make_key('session', session_id)
        return await self.cache.get(cache_key)

    async def set_session(self, session_id: str, session_data: Dict[str, Any]) -> bool:
        """设置会话数据"""
        cache_key = self._make_key('session', session_id)
        return await self.cache.set(cache_key, session_data, self.config.session_ttl)

    async def delete_session(self, session_id: str) -> bool:
        """删除会话数据"""
        cache_key = self._make_key('session', session_id)
        return await self.cache.delete(cache_key)

    # 用户数据缓存
    async def get_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户数据"""
        cache_key = self._make_key('user_data', user_id)
        return await self.cache.get(cache_key)

    async def set_user_data(self, user_id: str, user_data: Dict[str, Any]) -> bool:
        """设置用户数据"""
        cache_key = self._make_key('user_data', user_id)
        return await self.cache.set(cache_key, user_data, self.config.default_ttl)

    # 批量操作
    async def get_multiple(self, keys: List[str], prefix: str = '') -> Dict[str, Any]:
        """批量获取缓存值"""
        results = {}
        for key in keys:
            cache_key = self._make_key(prefix, key) if prefix else key
            value = await self.cache.get(cache_key)
            if value is not None:
                results[key] = value
        return results

    async def set_multiple(self, data: Dict[str, Any], prefix: str = '', ttl: int = None) -> Dict[str, bool]:
        """批量设置缓存值"""
        results = {}
        for key, value in data.items():
            cache_key = self._make_key(prefix, key) if prefix else key
            success = await self.cache.set(cache_key, value, ttl)
            results[key] = success
        return results

    async def delete_multiple(self, keys: List[str], prefix: str = '') -> Dict[str, bool]:
        """批量删除缓存值"""
        results = {}
        for key in keys:
            cache_key = self._make_key(prefix, key) if prefix else key
            success = await self.cache.delete(cache_key)
            results[key] = success
        return results

    # 缓存管理
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return await self.cache.get_stats()

    async def clear_cache(self, prefix: str = None) -> bool:
        """清空缓存"""
        if prefix is None:
            return await self.cache.clear()
        else:
            # TODO: 实现按前缀清空缓存
            logger.warning("Prefix-based cache clearing not implemented yet")
            return False

    async def close(self):
        """关闭缓存管理器"""
        await self.cache.close()


# 全局缓存管理器实例
_cache_manager: Optional[CacheManager] = None


def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager


def setup_cache(config: Optional[CacheConfig] = None, redis_url: Optional[str] = None):
    """设置全局缓存管理器"""
    global _cache_manager
    _cache_manager = CacheManager(config, redis_url)
