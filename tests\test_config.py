"""
配置管理模块测试
验证分层配置、环境变量覆盖、加密存储和热更新功能
"""

import os
import tempfile
import pytest
from pathlib import Path
from cryptography.fernet import Fernet
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import ConfigManager, AresSettings, get_config_manager, ConfigChangeEvent
from core.exceptions import ConfigurationError


class TestAresSettings:
    """Pydantic设置模型测试"""
    
    def test_settings_validation_success(self):
        """测试设置验证成功"""
        # 设置必需的环境变量
        os.environ['STEAMDT_API_KEY'] = 'test_api_key'
        os.environ['ENCRYPTION_KEY'] = Fernet.generate_key().decode()
        os.environ['SECRET_KEY'] = 'test_secret_key'
        
        try:
            settings = AresSettings()
            assert settings.steamdt_api_key == 'test_api_key'
            assert settings.app_name == "Ares Investment System"
            assert settings.core_pool_size == 30
        finally:
            # 清理环境变量
            for key in ['STEAMDT_API_KEY', 'ENCRYPTION_KEY', 'SECRET_KEY']:
                os.environ.pop(key, None)
    
    def test_settings_validation_failure(self):
        """测试设置验证失败"""
        # 不设置必需的环境变量
        with pytest.raises(Exception):  # Pydantic会抛出验证错误
            AresSettings()
    
    def test_environment_validation(self):
        """测试环境变量验证"""
        os.environ['STEAMDT_API_KEY'] = 'test_api_key'
        os.environ['ENCRYPTION_KEY'] = Fernet.generate_key().decode()
        os.environ['SECRET_KEY'] = 'test_secret_key'
        os.environ['APP_ENV'] = 'invalid_env'
        
        try:
            with pytest.raises(Exception):  # 应该验证失败
                AresSettings()
        finally:
            for key in ['STEAMDT_API_KEY', 'ENCRYPTION_KEY', 'SECRET_KEY', 'APP_ENV']:
                os.environ.pop(key, None)


class TestConfigManager:
    """配置管理器测试"""
    
    @pytest.fixture
    def temp_config_files(self):
        """创建临时配置文件"""
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        config_file = Path(temp_dir) / "settings.yaml"
        env_file = Path(temp_dir) / ".env"
        
        # 创建测试配置文件
        config_content = """
app:
  name: "Test Ares System"
  version: "1.0.0"
  debug: true

database:
  path: "test.db"
  pool_size: 10

api:
  steamdt:
    base_url: "https://test.api.com"
    rate_limit:
      calls_per_minute: 5
"""
        
        env_content = """
STEAMDT_API_KEY=test_api_key_from_env
ENCRYPTION_KEY={}
SECRET_KEY=test_secret_from_env
DATABASE_URL=sqlite:///test_from_env.db
""".format(Fernet.generate_key().decode())
        
        config_file.write_text(config_content, encoding='utf-8')
        env_file.write_text(env_content, encoding='utf-8')
        
        yield str(config_file), str(env_file)
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
    
    def test_config_manager_initialization(self, temp_config_files):
        """测试配置管理器初始化"""
        config_file, env_file = temp_config_files
        
        config_manager = ConfigManager(config_file=config_file, env_file=env_file)
        
        assert config_manager.config_file == Path(config_file)
        assert config_manager.env_file == Path(env_file)
        assert config_manager.settings is not None
        assert config_manager.cipher is not None
    
    def test_config_layered_access(self, temp_config_files):
        """测试分层配置访问"""
        config_file, env_file = temp_config_files
        config_manager = ConfigManager(config_file=config_file, env_file=env_file)
        
        # 测试YAML配置
        assert config_manager.get('app.name', source='yaml') == "Test Ares System"
        assert config_manager.get('database.pool_size', source='yaml') == 10
        
        # 测试环境变量覆盖
        assert config_manager.get('steamdt_api_key', source='env') == 'test_api_key_from_env'
        
        # 测试自动优先级（环境变量 > YAML）
        assert config_manager.get('steamdt_api_key') == 'test_api_key_from_env'
    
    def test_runtime_config(self, temp_config_files):
        """测试运行时配置"""
        config_file, env_file = temp_config_files
        config_manager = ConfigManager(config_file=config_file, env_file=env_file)
        
        # 设置运行时配置
        config_manager.set('runtime.test_key', 'test_value', source='runtime')
        
        # 验证运行时配置优先级最高
        assert config_manager.get('runtime.test_key') == 'test_value'
        assert config_manager.get('runtime.test_key', source='runtime') == 'test_value'
    
    def test_config_encryption(self, temp_config_files):
        """测试配置加密功能"""
        config_file, env_file = temp_config_files
        config_manager = ConfigManager(config_file=config_file, env_file=env_file)
        
        # 测试加密和解密
        original_value = "sensitive_data_123"
        encrypted_value = config_manager.encrypt_value(original_value)
        decrypted_value = config_manager.decrypt_value(encrypted_value)
        
        assert encrypted_value != original_value
        assert decrypted_value == original_value
    
    def test_config_validation(self, temp_config_files):
        """测试配置验证"""
        config_file, env_file = temp_config_files
        config_manager = ConfigManager(config_file=config_file, env_file=env_file)
        
        validation_result = config_manager.validate_config()
        
        assert 'valid' in validation_result
        assert 'errors' in validation_result
        assert 'warnings' in validation_result
        assert isinstance(validation_result['valid'], bool)
    
    def test_change_listeners(self, temp_config_files):
        """测试配置变更监听器"""
        config_file, env_file = temp_config_files
        config_manager = ConfigManager(config_file=config_file, env_file=env_file)
        
        # 记录变更事件
        change_events = []
        
        def change_listener(event: ConfigChangeEvent):
            change_events.append(event)
        
        config_manager.add_change_listener(change_listener)
        
        # 触发配置变更
        config_manager.set('test.key', 'test_value')
        
        # 验证监听器被调用
        assert len(change_events) == 1
        assert change_events[0].key == 'test.key'
        assert change_events[0].new_value == 'test_value'
    
    def test_config_export(self, temp_config_files):
        """测试配置导出"""
        config_file, env_file = temp_config_files
        config_manager = ConfigManager(config_file=config_file, env_file=env_file)
        
        # 导出配置
        temp_export_file = tempfile.NamedTemporaryFile(delete=False, suffix='.yaml')
        temp_export_file.close()
        
        try:
            config_manager.export_config(temp_export_file.name, format='yaml')
            
            # 验证文件存在
            assert Path(temp_export_file.name).exists()
            
            # 验证内容
            with open(temp_export_file.name, 'r', encoding='utf-8') as f:
                content = f.read()
                assert 'app_name' in content
                assert '***HIDDEN***' in content  # 敏感信息应该被隐藏
        
        finally:
            os.unlink(temp_export_file.name)


def test_global_config_manager():
    """测试全局配置管理器"""
    # 设置必需的环境变量
    os.environ['STEAMDT_API_KEY'] = 'test_global_key'
    os.environ['ENCRYPTION_KEY'] = Fernet.generate_key().decode()
    os.environ['SECRET_KEY'] = 'test_global_secret'
    
    try:
        from core.config import get_config, set_config
        
        # 测试全局配置获取
        config_manager = get_config_manager()
        assert config_manager is not None
        
        # 测试快捷方式
        set_config('global.test', 'global_value')
        assert get_config('global.test') == 'global_value'
        
    finally:
        # 清理环境变量
        for key in ['STEAMDT_API_KEY', 'ENCRYPTION_KEY', 'SECRET_KEY']:
            os.environ.pop(key, None)


if __name__ == "__main__":
    # 运行基本测试
    print("运行配置管理基本测试...")
    
    # 设置测试环境变量
    os.environ['STEAMDT_API_KEY'] = 'test_key'
    os.environ['ENCRYPTION_KEY'] = Fernet.generate_key().decode()
    os.environ['SECRET_KEY'] = 'test_secret'
    
    try:
        # 测试Pydantic设置
        print("测试Pydantic设置...")
        settings = AresSettings()
        assert settings.steamdt_api_key == 'test_key'
        print("✓ Pydantic设置测试通过")
        
        # 测试配置管理器
        print("测试配置管理器...")
        
        # 创建临时配置文件
        temp_dir = tempfile.mkdtemp()
        config_file = Path(temp_dir) / "test_settings.yaml"
        env_file = Path(temp_dir) / "test.env"
        
        config_content = """
app:
  name: "Test System"
  debug: true
test:
  value: 123
"""
        config_file.write_text(config_content, encoding='utf-8')
        
        config_manager = ConfigManager(config_file=str(config_file), env_file=str(env_file))
        
        # 测试配置获取
        print(f"Debug: app.name = {config_manager.get('app.name')}")
        print(f"Debug: config_data = {config_manager.config_data}")
        assert config_manager.get('app.name') == "Test System"
        assert config_manager.get('test.value') == 123
        
        # 测试配置设置
        config_manager.set('runtime.test', 'runtime_value')
        assert config_manager.get('runtime.test') == 'runtime_value'
        
        # 测试加密
        encrypted = config_manager.encrypt_value("secret")
        decrypted = config_manager.decrypt_value(encrypted)
        assert decrypted == "secret"
        
        # 测试配置验证
        validation = config_manager.validate_config()
        assert 'valid' in validation
        
        print("✓ 配置管理器测试通过")
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
        
        print("所有配置管理测试通过！")
        
    finally:
        # 清理环境变量
        for key in ['STEAMDT_API_KEY', 'ENCRYPTION_KEY', 'SECRET_KEY']:
            os.environ.pop(key, None)
