"""
CS2市场分析器
基于CS2玩家在线数据分析市场趋势，为投资决策提供支持
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np
from statistics import mean, stdev

from services.cs2_macro_collector import get_cs2_macro_collector, CS2MacroIndicators, CS2PlayerData


class MarketTrend(Enum):
    """市场趋势"""
    BULLISH = "bullish"          # 看涨
    BEARISH = "bearish"          # 看跌
    SIDEWAYS = "sideways"        # 横盘
    VOLATILE = "volatile"        # 波动


class InvestmentTiming(Enum):
    """投资时机"""
    BUY = "buy"                  # 买入时机
    SELL = "sell"                # 卖出时机
    HOLD = "hold"                # 持有
    WAIT = "wait"                # 等待


@dataclass
class MarketAnalysis:
    """市场分析结果"""
    trend: MarketTrend
    timing: InvestmentTiming
    confidence: float            # 置信度 (0-100)
    heat_level: str             # 市场热度等级
    volatility_score: float     # 波动性评分 (0-100)
    momentum_score: float       # 动量评分 (-100 to 100)
    support_level: float        # 支撑位（玩家数）
    resistance_level: float     # 阻力位（玩家数）
    analysis_time: datetime
    key_insights: List[str]     # 关键洞察
    risk_factors: List[str]     # 风险因素
    recommendations: List[str]   # 投资建议


@dataclass
class MarketSignal:
    """市场信号"""
    signal_type: str            # 信号类型
    strength: float             # 信号强度 (0-100)
    description: str            # 信号描述
    timestamp: datetime
    duration_hours: int         # 信号持续时间（小时）


class CS2MarketAnalyzer:
    """CS2市场分析器"""
    
    def __init__(self):
        """初始化CS2市场分析器"""
        self.logger = logging.getLogger(__name__)
        self.cs2_collector = get_cs2_macro_collector()
        
        # 分析参数
        self.volatility_threshold = 15.0    # 波动性阈值
        self.momentum_threshold = 10.0      # 动量阈值
        self.heat_thresholds = {            # 热度阈值
            'cold': 25.0,
            'cool': 40.0,
            'warm': 60.0,
            'hot': 80.0
        }
    
    async def analyze_market(self) -> Optional[MarketAnalysis]:
        """分析市场状况"""
        try:
            # 获取宏观指标
            indicators = await self.cs2_collector.get_macro_indicators()
            if not indicators:
                self.logger.warning("无法获取CS2宏观指标")
                return None
            
            # 获取历史数据
            historical_data = self.cs2_collector.get_historical_data(days=7)
            if len(historical_data) < 24:  # 至少需要24小时数据
                self.logger.warning("历史数据不足，无法进行完整分析")
                return None
            
            # 分析市场趋势
            trend = self._analyze_trend(indicators, historical_data)
            
            # 分析投资时机
            timing = self._analyze_timing(indicators, historical_data)
            
            # 计算置信度
            confidence = self._calculate_confidence(indicators, historical_data)
            
            # 分析市场热度
            heat_level = self._analyze_heat_level(indicators.market_heat_index)
            
            # 计算波动性评分
            volatility_score = self._calculate_volatility(historical_data)
            
            # 计算动量评分
            momentum_score = self._calculate_momentum(indicators, historical_data)
            
            # 计算支撑位和阻力位
            support_level, resistance_level = self._calculate_support_resistance(historical_data)
            
            # 生成关键洞察
            key_insights = self._generate_insights(indicators, historical_data, trend, timing)
            
            # 识别风险因素
            risk_factors = self._identify_risks(indicators, historical_data, volatility_score)
            
            # 生成投资建议
            recommendations = self._generate_recommendations(trend, timing, heat_level, volatility_score)
            
            return MarketAnalysis(
                trend=trend,
                timing=timing,
                confidence=confidence,
                heat_level=heat_level,
                volatility_score=volatility_score,
                momentum_score=momentum_score,
                support_level=support_level,
                resistance_level=resistance_level,
                analysis_time=datetime.now(),
                key_insights=key_insights,
                risk_factors=risk_factors,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"市场分析时出错: {e}")
            return None
    
    def _analyze_trend(self, indicators: CS2MacroIndicators, historical_data: List[CS2PlayerData]) -> MarketTrend:
        """分析市场趋势"""
        try:
            # 基于多个指标判断趋势
            trend_signals = []
            
            # 24小时趋势
            if indicators.player_trend_24h > 5:
                trend_signals.append(1)  # 看涨
            elif indicators.player_trend_24h < -5:
                trend_signals.append(-1)  # 看跌
            else:
                trend_signals.append(0)  # 横盘
            
            # 7天趋势
            if indicators.player_trend_7d > 10:
                trend_signals.append(1)
            elif indicators.player_trend_7d < -10:
                trend_signals.append(-1)
            else:
                trend_signals.append(0)
            
            # 市场热度趋势
            if indicators.market_heat_index > 70:
                trend_signals.append(1)
            elif indicators.market_heat_index < 30:
                trend_signals.append(-1)
            else:
                trend_signals.append(0)
            
            # 计算波动性
            recent_counts = [data.player_count for data in historical_data[-24:]]
            if len(recent_counts) >= 2:
                volatility = (stdev(recent_counts) / mean(recent_counts)) * 100
                if volatility > self.volatility_threshold:
                    return MarketTrend.VOLATILE
            
            # 综合判断
            avg_signal = mean(trend_signals)
            if avg_signal > 0.3:
                return MarketTrend.BULLISH
            elif avg_signal < -0.3:
                return MarketTrend.BEARISH
            else:
                return MarketTrend.SIDEWAYS
                
        except Exception as e:
            self.logger.error(f"分析趋势时出错: {e}")
            return MarketTrend.SIDEWAYS
    
    def _analyze_timing(self, indicators: CS2MacroIndicators, historical_data: List[CS2PlayerData]) -> InvestmentTiming:
        """分析投资时机"""
        try:
            timing_score = 0
            
            # 基于市场热度
            if indicators.market_heat_index < 30:
                timing_score += 2  # 低热度，买入时机
            elif indicators.market_heat_index > 80:
                timing_score -= 2  # 高热度，卖出时机
            
            # 基于趋势
            if indicators.player_trend_24h > 10:
                timing_score -= 1  # 上涨趋势，可能过热
            elif indicators.player_trend_24h < -10:
                timing_score += 1  # 下跌趋势，可能超卖
            
            # 基于周末效应
            current_weekday = datetime.now().weekday()
            if current_weekday >= 5:  # 周末
                if indicators.weekend_effect > 5:
                    timing_score += 1  # 周末效应积极
            
            # 基于置信度
            if indicators.confidence_score < 50:
                return InvestmentTiming.WAIT  # 数据不可靠，等待
            
            # 判断时机
            if timing_score >= 2:
                return InvestmentTiming.BUY
            elif timing_score <= -2:
                return InvestmentTiming.SELL
            elif abs(timing_score) <= 1:
                return InvestmentTiming.HOLD
            else:
                return InvestmentTiming.WAIT
                
        except Exception as e:
            self.logger.error(f"分析投资时机时出错: {e}")
            return InvestmentTiming.WAIT
    
    def _calculate_confidence(self, indicators: CS2MacroIndicators, historical_data: List[CS2PlayerData]) -> float:
        """计算分析置信度"""
        try:
            confidence = indicators.confidence_score
            
            # 基于数据量调整
            data_hours = len(historical_data)
            if data_hours >= 168:  # 7天数据
                confidence *= 1.0
            elif data_hours >= 72:  # 3天数据
                confidence *= 0.9
            elif data_hours >= 24:  # 1天数据
                confidence *= 0.7
            else:
                confidence *= 0.5
            
            # 基于数据一致性调整
            recent_trends = []
            for i in range(min(6, len(historical_data) - 1)):
                current = historical_data[-(i+1)].player_count
                previous = historical_data[-(i+2)].player_count
                if previous > 0:
                    trend = ((current - previous) / previous) * 100
                    recent_trends.append(trend)
            
            if recent_trends:
                trend_consistency = 1 - (stdev(recent_trends) / 100)
                confidence *= max(0.5, trend_consistency)
            
            return max(0.0, min(100.0, confidence))
            
        except Exception as e:
            self.logger.error(f"计算置信度时出错: {e}")
            return 50.0
    
    def _analyze_heat_level(self, heat_index: float) -> str:
        """分析市场热度等级"""
        if heat_index >= self.heat_thresholds['hot']:
            return "极热"
        elif heat_index >= self.heat_thresholds['warm']:
            return "热"
        elif heat_index >= self.heat_thresholds['cool']:
            return "温"
        elif heat_index >= self.heat_thresholds['cold']:
            return "冷"
        else:
            return "极冷"
    
    def _calculate_volatility(self, historical_data: List[CS2PlayerData]) -> float:
        """计算波动性评分"""
        try:
            if len(historical_data) < 2:
                return 50.0
            
            # 计算价格变化的标准差
            player_counts = [data.player_count for data in historical_data]
            if not player_counts:
                return 50.0
            
            avg_count = mean(player_counts)
            if avg_count == 0:
                return 50.0
            
            volatility = (stdev(player_counts) / avg_count) * 100
            
            # 标准化到0-100范围
            normalized_volatility = min(100.0, volatility * 2)
            
            return normalized_volatility
            
        except Exception as e:
            self.logger.error(f"计算波动性时出错: {e}")
            return 50.0
    
    def _calculate_momentum(self, indicators: CS2MacroIndicators, historical_data: List[CS2PlayerData]) -> float:
        """计算动量评分"""
        try:
            momentum = 0.0
            
            # 24小时动量
            momentum += indicators.player_trend_24h * 0.6
            
            # 7天动量
            momentum += indicators.player_trend_7d * 0.4
            
            # 限制在-100到100范围内
            return max(-100.0, min(100.0, momentum))
            
        except Exception as e:
            self.logger.error(f"计算动量时出错: {e}")
            return 0.0
    
    def _calculate_support_resistance(self, historical_data: List[CS2PlayerData]) -> Tuple[float, float]:
        """计算支撑位和阻力位"""
        try:
            if len(historical_data) < 24:
                return 0.0, 0.0
            
            player_counts = [data.player_count for data in historical_data]
            
            # 使用分位数计算支撑位和阻力位
            support_level = np.percentile(player_counts, 25)  # 25%分位数作为支撑位
            resistance_level = np.percentile(player_counts, 75)  # 75%分位数作为阻力位
            
            return float(support_level), float(resistance_level)
            
        except Exception as e:
            self.logger.error(f"计算支撑阻力位时出错: {e}")
            return 0.0, 0.0
    
    def _generate_insights(self, indicators: CS2MacroIndicators, historical_data: List[CS2PlayerData], 
                          trend: MarketTrend, timing: InvestmentTiming) -> List[str]:
        """生成关键洞察"""
        insights = []
        
        try:
            # 玩家数量洞察
            current_players = indicators.current_players
            insights.append(f"当前CS2在线玩家数: {current_players:,}")
            
            # 趋势洞察
            if indicators.player_trend_24h > 5:
                insights.append(f"24小时玩家数上涨 {indicators.player_trend_24h:.1f}%，市场活跃度提升")
            elif indicators.player_trend_24h < -5:
                insights.append(f"24小时玩家数下降 {abs(indicators.player_trend_24h):.1f}%，市场活跃度降低")
            
            # 热度洞察
            if indicators.market_heat_index > 80:
                insights.append("市场热度极高，可能存在过热风险")
            elif indicators.market_heat_index < 20:
                insights.append("市场热度极低，可能存在投资机会")
            
            # 周末效应洞察
            if abs(indicators.weekend_effect) > 10:
                if indicators.weekend_effect > 0:
                    insights.append(f"周末效应显著，周末玩家数增加 {indicators.weekend_effect:.1f}%")
                else:
                    insights.append(f"周末效应负面，周末玩家数减少 {abs(indicators.weekend_effect):.1f}%")
            
            # 高峰时段洞察
            if indicators.peak_hours_ratio > 0.6:
                insights.append("高峰时段比例较高，市场活跃时间集中")
            elif indicators.peak_hours_ratio < 0.4:
                insights.append("高峰时段比例较低，市场活跃度分散")
            
        except Exception as e:
            self.logger.error(f"生成洞察时出错: {e}")
            insights.append("数据分析过程中出现异常")
        
        return insights
    
    def _identify_risks(self, indicators: CS2MacroIndicators, historical_data: List[CS2PlayerData], 
                       volatility_score: float) -> List[str]:
        """识别风险因素"""
        risks = []
        
        try:
            # 数据质量风险
            if indicators.confidence_score < 50:
                risks.append("数据置信度较低，分析结果可能不准确")
            
            # 波动性风险
            if volatility_score > 70:
                risks.append("市场波动性极高，投资风险较大")
            elif volatility_score > 50:
                risks.append("市场波动性较高，需要谨慎投资")
            
            # 趋势风险
            if abs(indicators.player_trend_24h) > 20:
                risks.append("短期趋势变化剧烈，可能出现反转")
            
            # 热度风险
            if indicators.market_heat_index > 90:
                risks.append("市场过热，存在泡沫风险")
            elif indicators.market_heat_index < 10:
                risks.append("市场过冷，可能缺乏流动性")
            
            # 数据时效性风险
            if len(historical_data) < 48:
                risks.append("历史数据不足，分析可能不够全面")
            
        except Exception as e:
            self.logger.error(f"识别风险时出错: {e}")
            risks.append("风险评估过程中出现异常")
        
        return risks
    
    def _generate_recommendations(self, trend: MarketTrend, timing: InvestmentTiming, 
                                heat_level: str, volatility_score: float) -> List[str]:
        """生成投资建议"""
        recommendations = []
        
        try:
            # 基于趋势的建议
            if trend == MarketTrend.BULLISH:
                recommendations.append("市场趋势向好，可考虑适度增加投资")
            elif trend == MarketTrend.BEARISH:
                recommendations.append("市场趋势下行，建议谨慎投资或减仓")
            elif trend == MarketTrend.VOLATILE:
                recommendations.append("市场波动剧烈，建议短线操作或观望")
            else:
                recommendations.append("市场横盘整理，可等待明确信号")
            
            # 基于时机的建议
            if timing == InvestmentTiming.BUY:
                recommendations.append("当前可能是较好的买入时机")
            elif timing == InvestmentTiming.SELL:
                recommendations.append("当前可能是较好的卖出时机")
            elif timing == InvestmentTiming.HOLD:
                recommendations.append("建议持有现有仓位，等待更好时机")
            else:
                recommendations.append("建议等待更明确的市场信号")
            
            # 基于热度的建议
            if heat_level in ["极热", "热"]:
                recommendations.append("市场热度较高，注意控制仓位和风险")
            elif heat_level in ["极冷", "冷"]:
                recommendations.append("市场热度较低，可关注价值投资机会")
            
            # 基于波动性的建议
            if volatility_score > 70:
                recommendations.append("高波动环境下，建议分批建仓，控制单次投资规模")
            elif volatility_score < 30:
                recommendations.append("低波动环境下，可考虑长期持有策略")
            
        except Exception as e:
            self.logger.error(f"生成建议时出错: {e}")
            recommendations.append("建议生成过程中出现异常，请谨慎决策")
        
        return recommendations
    
    async def detect_market_signals(self) -> List[MarketSignal]:
        """检测市场信号"""
        signals = []
        
        try:
            indicators = await self.cs2_collector.get_macro_indicators()
            if not indicators:
                return signals
            
            historical_data = self.cs2_collector.get_historical_data(days=3)
            
            # 检测突破信号
            if len(historical_data) >= 24:
                support, resistance = self._calculate_support_resistance(historical_data)
                current_players = indicators.current_players
                
                if current_players > resistance * 1.05:  # 突破阻力位
                    signals.append(MarketSignal(
                        signal_type="突破阻力位",
                        strength=75.0,
                        description=f"玩家数突破阻力位 {resistance:,.0f}，当前 {current_players:,}",
                        timestamp=datetime.now(),
                        duration_hours=6
                    ))
                
                elif current_players < support * 0.95:  # 跌破支撑位
                    signals.append(MarketSignal(
                        signal_type="跌破支撑位",
                        strength=75.0,
                        description=f"玩家数跌破支撑位 {support:,.0f}，当前 {current_players:,}",
                        timestamp=datetime.now(),
                        duration_hours=6
                    ))
            
            # 检测热度异常信号
            if indicators.market_heat_index > 90:
                signals.append(MarketSignal(
                    signal_type="市场过热",
                    strength=85.0,
                    description=f"市场热度指数达到 {indicators.market_heat_index:.1f}，存在过热风险",
                    timestamp=datetime.now(),
                    duration_hours=12
                ))
            
            elif indicators.market_heat_index < 10:
                signals.append(MarketSignal(
                    signal_type="市场过冷",
                    strength=80.0,
                    description=f"市场热度指数仅为 {indicators.market_heat_index:.1f}，可能存在机会",
                    timestamp=datetime.now(),
                    duration_hours=12
                ))
            
            # 检测趋势反转信号
            if abs(indicators.player_trend_24h) > 15:
                if indicators.player_trend_24h > 0:
                    signal_type = "强势上涨"
                    description = f"24小时玩家数上涨 {indicators.player_trend_24h:.1f}%"
                else:
                    signal_type = "急剧下跌"
                    description = f"24小时玩家数下跌 {abs(indicators.player_trend_24h):.1f}%"
                
                signals.append(MarketSignal(
                    signal_type=signal_type,
                    strength=70.0,
                    description=description,
                    timestamp=datetime.now(),
                    duration_hours=8
                ))
            
        except Exception as e:
            self.logger.error(f"检测市场信号时出错: {e}")
        
        return signals


# 全局实例
_cs2_market_analyzer = None


def get_cs2_market_analyzer() -> CS2MarketAnalyzer:
    """获取CS2市场分析器实例"""
    global _cs2_market_analyzer
    if _cs2_market_analyzer is None:
        _cs2_market_analyzer = CS2MarketAnalyzer()
    return _cs2_market_analyzer
