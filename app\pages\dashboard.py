"""
Ares投资组合仪表盘页面
集成智能筛选功能，实现战略储备、核心关注池、发现监控的分层展示和交互操作
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import random
import numpy as np

from app.utils.state_manager import StateManager
from app.components.data_table import DataTable, create_portfolio_table, create_watchlist_table, create_discovery_table
from app.components.filter_panel import FilterPanel, create_portfolio_filter_panel, create_watchlist_filter_panel, FilterType


def show_dashboard_page():
    """显示投资组合仪表盘页面"""
    st.markdown('<h1 class="section-title">💼 投资组合仪表盘</h1>', unsafe_allow_html=True)

    # 获取状态管理器
    state_manager = StateManager()

    # 渲染关键指标概览
    render_key_metrics_overview(state_manager)

    # 渲染三栏分层展示
    render_three_column_layout(state_manager)


def render_key_metrics(state_manager: StateManager):
    """渲染关键指标"""
    st.markdown('<h2 class="section-title">📊 关键指标</h2>', unsafe_allow_html=True)
    
    # 获取数据
    portfolio_data = state_manager.get_portfolio_data()
    market_data = state_manager.get_market_data()
    
    # 创建指标卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="💼 投资组合总值",
            value=f"${portfolio_data['total_value']:,.2f}",
            delta=f"{portfolio_data['profit_percentage']:+.2f}%"
        )
    
    with col2:
        st.metric(
            label="💰 总盈亏",
            value=f"${portfolio_data['total_profit']:+,.2f}",
            delta=f"${portfolio_data['total_profit'] - portfolio_data['total_cost'] * 0.05:+.2f}"
        )
    
    with col3:
        st.metric(
            label="📈 市场活跃度",
            value=f"{market_data['active_listings']:,}",
            delta=f"{market_data['avg_price_change']:+.1f}%"
        )
    
    with col4:
        st.metric(
            label="🔄 24h交易量",
            value=f"{market_data['volume_24h']:,}",
            delta=f"+{random.randint(50, 200)}"
        )


def render_charts_section(state_manager: StateManager):
    """渲染图表区域"""
    st.markdown('<h2 class="section-title">📈 数据可视化</h2>', unsafe_allow_html=True)
    
    # 创建两列布局
    col1, col2 = st.columns(2)
    
    with col1:
        render_portfolio_chart(state_manager)
    
    with col2:
        render_market_trend_chart(state_manager)
    
    # 第二行图表
    col3, col4 = st.columns(2)
    
    with col3:
        render_holdings_distribution(state_manager)
    
    with col4:
        render_performance_chart(state_manager)


def render_portfolio_chart(state_manager: StateManager):
    """渲染投资组合图表"""
    st.markdown("### 💼 投资组合分布")
    
    portfolio_data = state_manager.get_portfolio_data()
    holdings = portfolio_data['holdings']
    
    # 创建饼图数据
    df = pd.DataFrame(holdings)
    
    fig = px.pie(
        df,
        values='total_value',
        names='name',
        title="投资组合资产分布",
        color_discrete_sequence=px.colors.qualitative.Set3
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        title_font_size=16,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.01
        )
    )
    
    st.plotly_chart(fig, use_container_width=True)


def render_market_trend_chart(state_manager: StateManager):
    """渲染市场趋势图表"""
    st.markdown("### 📈 市场趋势")
    
    # 生成模拟的市场趋势数据
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
    
    # 模拟价格数据
    base_price = 100
    prices = []
    for i in range(len(dates)):
        change = random.uniform(-5, 5)
        base_price *= (1 + change / 100)
        prices.append(base_price)
    
    df = pd.DataFrame({
        'date': dates,
        'price_index': prices
    })
    
    fig = px.line(
        df,
        x='date',
        y='price_index',
        title='市场价格指数趋势',
        line_shape='spline'
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        title_font_size=16,
        xaxis_title="日期",
        yaxis_title="价格指数"
    )
    
    fig.update_traces(line_color='#1f77b4', line_width=3)
    
    st.plotly_chart(fig, use_container_width=True)


def render_holdings_distribution(state_manager: StateManager):
    """渲染持仓分布图"""
    st.markdown("### 📋 持仓盈亏分布")
    
    portfolio_data = state_manager.get_portfolio_data()
    holdings = portfolio_data['holdings']
    
    df = pd.DataFrame(holdings)
    
    # 创建条形图
    fig = px.bar(
        df,
        x='name',
        y='profit_loss',
        color='profit_percentage',
        title="各持仓盈亏情况",
        color_continuous_scale='RdYlGn'
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        title_font_size=16,
        xaxis_title="持仓",
        yaxis_title="盈亏 ($)",
        xaxis_tickangle=-45
    )
    
    st.plotly_chart(fig, use_container_width=True)


def render_performance_chart(state_manager: StateManager):
    """渲染绩效图表"""
    st.markdown("### 📊 绩效表现")
    
    # 生成模拟绩效数据
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
    
    portfolio_value = []
    benchmark_value = []
    
    portfolio_base = 10000
    benchmark_base = 10000
    
    for i in range(len(dates)):
        # 投资组合表现（稍微好于基准）
        portfolio_change = random.uniform(-3, 4)
        portfolio_base *= (1 + portfolio_change / 100)
        portfolio_value.append(portfolio_base)
        
        # 基准表现
        benchmark_change = random.uniform(-2, 3)
        benchmark_base *= (1 + benchmark_change / 100)
        benchmark_value.append(benchmark_base)
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=dates,
        y=portfolio_value,
        mode='lines',
        name='投资组合',
        line=dict(color='#1f77b4', width=3)
    ))
    
    fig.add_trace(go.Scatter(
        x=dates,
        y=benchmark_value,
        mode='lines',
        name='市场基准',
        line=dict(color='#ff7f0e', width=2, dash='dash')
    ))
    
    fig.update_layout(
        title='投资组合 vs 市场基准',
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white',
        title_font_size=16,
        xaxis_title="日期",
        yaxis_title="价值 ($)",
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        )
    )
    
    st.plotly_chart(fig, use_container_width=True)


def render_quick_actions():
    """渲染快速操作"""
    st.markdown('<h2 class="section-title">⚡ 快速操作</h2>', unsafe_allow_html=True)
    
    # 创建操作按钮
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("📊 查看投资组合", use_container_width=True):
            st.session_state.current_page = 'portfolio'
            st.rerun()
        
        if st.button("📈 市场监控", use_container_width=True):
            st.session_state.current_page = 'market'
            st.rerun()
        
        if st.button("🚨 预警中心", use_container_width=True):
            st.session_state.current_page = 'alerts'
            st.rerun()
    
    with col2:
        if st.button("📋 持仓管理", use_container_width=True):
            st.session_state.current_page = 'holdings'
            st.rerun()
        
        if st.button("📊 数据分析", use_container_width=True):
            st.session_state.current_page = 'analytics'
            st.rerun()
        
        if st.button("⚙️ 系统设置", use_container_width=True):
            st.session_state.current_page = 'settings'
            st.rerun()


def render_recent_activities(state_manager: StateManager):
    """渲染最新动态"""
    st.markdown('<h2 class="section-title">📰 最新动态</h2>', unsafe_allow_html=True)
    
    # 模拟最新动态数据
    activities = [
        {
            'time': '2分钟前',
            'type': 'trade',
            'icon': '💰',
            'message': 'AK-47 | Redline 价格上涨 15.2%',
            'status': 'positive'
        },
        {
            'time': '5分钟前',
            'type': 'alert',
            'icon': '🚨',
            'message': '触发价格预警：AWP | Dragon Lore',
            'status': 'warning'
        },
        {
            'time': '10分钟前',
            'type': 'system',
            'icon': '🔧',
            'message': '系统自动备份完成',
            'status': 'neutral'
        },
        {
            'time': '15分钟前',
            'type': 'trade',
            'icon': '📈',
            'message': 'Karambit | Fade 交易量激增',
            'status': 'positive'
        },
        {
            'time': '20分钟前',
            'type': 'portfolio',
            'icon': '💼',
            'message': '投资组合价值突破 $15,000',
            'status': 'positive'
        }
    ]
    
    # 渲染活动列表
    for activity in activities:
        status_color = {
            'positive': '#2ca02c',
            'warning': '#ff7f0e',
            'negative': '#d62728',
            'neutral': '#a6a6a6'
        }.get(activity['status'], '#a6a6a6')
        
        st.markdown(f"""
        <div style="
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-left: 3px solid {status_color};
            background-color: rgba(38, 39, 48, 0.5);
            border-radius: 0 8px 8px 0;
        ">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                    <span style="font-size: 1.2em; margin-right: 0.5rem;">{activity['icon']}</span>
                    <span style="color: #fafafa;">{activity['message']}</span>
                </div>
                <span style="color: #a6a6a6; font-size: 0.8em;">{activity['time']}</span>
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    # 查看更多按钮
    if st.button("查看所有动态", use_container_width=True):
        st.info("功能开发中...")


def render_system_overview():
    """渲染系统概览"""
    st.markdown('<h2 class="section-title">🔧 系统概览</h2>', unsafe_allow_html=True)
    
    # 系统状态指标
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            label="🖥️ 系统负载",
            value="23%",
            delta="-5%"
        )
    
    with col2:
        st.metric(
            label="💾 内存使用",
            value="1.2GB",
            delta="+0.1GB"
        )
    
    with col3:
        st.metric(
            label="🌐 API调用",
            value="1,234",
            delta="+156"
        )


if __name__ == "__main__":
    show_dashboard_page()
