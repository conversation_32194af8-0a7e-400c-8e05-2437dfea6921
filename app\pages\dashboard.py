"""
Ares投资组合仪表盘页面
集成智能筛选功能，实现战略储备、核心关注池、发现监控的分层展示和交互操作
"""

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import random
import numpy as np

from app.utils.state_manager import StateManager
from app.components.data_table import DataTable, create_portfolio_table, create_watchlist_table, create_discovery_table
from app.components.filter_panel import FilterPanel, create_portfolio_filter_panel, create_watchlist_filter_panel, FilterType


def show_dashboard_page():
    """显示投资组合仪表盘页面"""
    st.markdown('<h1 class="section-title">💼 投资组合仪表盘</h1>', unsafe_allow_html=True)

    # 获取状态管理器
    state_manager = StateManager()

    # 渲染关键指标概览
    render_key_metrics_overview(state_manager)

    # 渲染三栏分层展示
    render_three_column_layout(state_manager)


def render_key_metrics_overview(state_manager: StateManager):
    """渲染关键指标概览"""
    st.markdown('<h2 class="section-title">📊 投资概览</h2>', unsafe_allow_html=True)

    # 获取数据
    portfolio_data = state_manager.get_portfolio_data()
    market_data = state_manager.get_market_data()

    # 创建指标卡片
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.metric(
            label="💼 投资组合总值",
            value=f"${portfolio_data['total_value']:,.2f}",
            delta=f"{portfolio_data['profit_percentage']:+.2f}%"
        )

    with col2:
        st.metric(
            label="💰 总盈亏",
            value=f"${portfolio_data['total_profit']:+,.2f}",
            delta=f"${portfolio_data['total_profit'] - portfolio_data['total_cost'] * 0.05:+.2f}"
        )

    with col3:
        st.metric(
            label="⭐ 核心关注池",
            value=f"{random.randint(15, 25)}",
            delta=f"+{random.randint(1, 3)}"
        )

    with col4:
        st.metric(
            label="�️ 主监控池",
            value=f"{random.randint(180, 220)}",
            delta=f"+{random.randint(5, 15)}"
        )

    with col5:
        st.metric(
            label="🔍 新发现机会",
            value=f"{random.randint(8, 15)}",
            delta=f"+{random.randint(2, 5)}"
        )


def render_three_column_layout(state_manager: StateManager):
    """渲染三栏分层展示布局"""
    # 创建三栏布局
    col1, col2, col3 = st.columns([1, 1.2, 1])

    with col1:
        render_strategic_holdings(state_manager)

    with col2:
        render_core_watchlist(state_manager)

    with col3:
        render_discovery_panel(state_manager)


def render_strategic_holdings(state_manager: StateManager):
    """渲染战略储备展示（左栏）"""
    st.markdown('<h3 class="section-title">🏆 战略储备</h3>', unsafe_allow_html=True)

    # 获取投资组合数据
    portfolio_data = state_manager.get_portfolio_data()
    holdings = portfolio_data.get('holdings', [])

    # 转换为DataFrame
    if holdings:
        df = pd.DataFrame(holdings)

        # 添加一些额外的列用于展示
        df['profit_percentage'] = ((df['current_price'] - df['avg_cost']) / df['avg_cost'] * 100).round(2)
        df['total_value'] = (df['current_price'] * df['quantity']).round(2)
        df['profit_loss'] = (df['total_value'] - df['avg_cost'] * df['quantity']).round(2)

        # 只显示高价值项目（战略储备）
        strategic_df = df[df['total_value'] >= 200].copy()

        if len(strategic_df) > 0:
            # 创建投资组合表格
            portfolio_table = create_portfolio_table(strategic_df, "strategic_holdings")
            portfolio_table.render()
        else:
            st.info("暂无战略储备项目")
    else:
        st.info("暂无投资组合数据")

    # 战略储备快速统计
    with st.container():
        st.markdown("**📈 战略储备统计**")
        if holdings:
            strategic_value = sum(h.get('total_value', 0) for h in holdings if h.get('total_value', 0) >= 200)
            strategic_count = len([h for h in holdings if h.get('total_value', 0) >= 200])

            col1, col2 = st.columns(2)
            with col1:
                st.metric("总价值", f"${strategic_value:,.2f}")
            with col2:
                st.metric("项目数", strategic_count)


def render_core_watchlist(state_manager: StateManager):
    """渲染核心关注池（中栏）"""
    st.markdown('<h3 class="section-title">⭐ 核心关注池</h3>', unsafe_allow_html=True)

    # 创建筛选面板
    filter_panel = create_watchlist_filter_panel("core_watchlist_filter")
    active_filters = filter_panel.render()

    # 生成模拟的核心关注池数据
    core_watchlist_data = generate_core_watchlist_data()

    # 应用筛选器
    filtered_data = filter_panel.apply_filters_to_dataframe(core_watchlist_data)

    # 创建监控列表表格
    if len(filtered_data) > 0:
        watchlist_table = create_watchlist_table(filtered_data, "core_watchlist")
        watchlist_table.render()
    else:
        st.info("没有符合筛选条件的项目")

    # 核心关注池统计
    with st.container():
        st.markdown("**📊 关注池统计**")
        col1, col2, col3 = st.columns(3)

        with col1:
            avg_score = filtered_data['priority_score'].mean() if len(filtered_data) > 0 else 0
            st.metric("平均评分", f"{avg_score:.1f}")

        with col2:
            rising_count = len(filtered_data[filtered_data['change_24h'] > 0]) if len(filtered_data) > 0 else 0
            st.metric("上涨项目", rising_count)

        with col3:
            high_volume = len(filtered_data[filtered_data['volume_24h'] > 100]) if len(filtered_data) > 0 else 0
            st.metric("高交易量", high_volume)


def render_discovery_panel(state_manager: StateManager):
    """渲染发现监控面板（右栏）"""
    st.markdown('<h3 class="section-title">🔍 发现监控</h3>', unsafe_allow_html=True)

    # 创建发现筛选面板
    discovery_filter = create_discovery_filter_panel("discovery_filter")
    discovery_filter.render()

    # 生成模拟的发现机会数据
    discovery_data = generate_discovery_data()

    # 应用筛选器
    filtered_discovery = discovery_filter.apply_filters_to_dataframe(discovery_data)

    # 创建发现机会表格
    if len(filtered_discovery) > 0:
        discovery_table = create_discovery_table(filtered_discovery, "discovery_opportunities")
        discovery_table.render()
    else:
        st.info("暂无发现机会")

    # 发现统计
    with st.container():
        st.markdown("**🎯 发现统计**")
        col1, col2 = st.columns(2)

        with col1:
            today_discoveries = len(filtered_discovery[filtered_discovery['discovery_date'] == datetime.now().date()]) if len(filtered_discovery) > 0 else 0
            st.metric("今日发现", today_discoveries)

        with col2:
            high_score = len(filtered_discovery[filtered_discovery['opportunity_score'] >= 7.0]) if len(filtered_discovery) > 0 else 0
            st.metric("高分机会", high_score)


def generate_core_watchlist_data() -> pd.DataFrame:
    """生成模拟的核心关注池数据"""
    np.random.seed(42)  # 固定随机种子以保持一致性

    items = [
        "AK-47 | Redline (Field-Tested)",
        "AWP | Dragon Lore (Factory New)",
        "Karambit | Fade (Minimal Wear)",
        "M4A4 | Howl (Field-Tested)",
        "Butterfly Knife | Doppler",
        "Glock-18 | Fade (Factory New)",
        "USP-S | Kill Confirmed",
        "AK-47 | Fire Serpent",
        "AWP | Medusa (Well-Worn)",
        "Karambit | Crimson Web",
        "M4A1-S | Hot Rod",
        "Desert Eagle | Blaze",
        "AK-47 | Case Hardened",
        "AWP | Lightning Strike",
        "Butterfly Knife | Fade",
        "StatTrak™ AK-47 | Vulcan",
        "Karambit | Tiger Tooth",
        "M4A4 | Asiimov",
        "AWP | Asiimov (Field-Tested)",
        "Glock-18 | Water Elemental"
    ]

    data = []
    for i, item in enumerate(items):
        data.append({
            'item_name': item,
            'pool_type': 'core' if i < 10 else 'main',
            'current_price': round(np.random.uniform(50, 2000), 2),
            'change_24h': round(np.random.uniform(-15, 20), 2),
            'volume_24h': np.random.randint(10, 500),
            'priority_score': round(np.random.uniform(3.0, 9.5), 1),
            'last_updated': datetime.now() - timedelta(minutes=np.random.randint(1, 60)),
            'category': np.random.choice(['武器', '刀具', '手套', '贴纸']),
            'rarity': np.random.choice(['普通', '稀有', '极其稀有', '隐秘', '保密'])
        })

    return pd.DataFrame(data)


def generate_discovery_data() -> pd.DataFrame:
    """生成模拟的发现机会数据"""
    np.random.seed(123)  # 固定随机种子

    discovery_items = [
        "StatTrak™ M4A1-S | Hyper Beast",
        "AK-47 | Neon Rider",
        "AWP | Neo-Noir",
        "Karambit | Autotronic",
        "M4A4 | The Emperor",
        "Glock-18 | Moonrise",
        "USP-S | Neo-Noir",
        "AK-47 | Phantom Disruptor",
        "AWP | Containment Breach",
        "Butterfly Knife | Lore",
        "M4A1-S | Printstream",
        "Desert Eagle | Printstream"
    ]

    data = []
    for i, item in enumerate(discovery_items):
        discovery_date = datetime.now().date() - timedelta(days=np.random.randint(0, 7))
        data.append({
            'item_name': item,
            'ranking_type': np.random.choice(['hot', 'rising', 'new', 'falling']),
            'ranking_position': np.random.randint(1, 50),
            'opportunity_score': round(np.random.uniform(4.0, 9.0), 1),
            'current_price': round(np.random.uniform(30, 800), 2),
            'price_change_7d': round(np.random.uniform(-10, 25), 2),
            'discovery_date': discovery_date,
            'source': 'steamdt_rankings',
            'status': np.random.choice(['new', 'monitoring', 'added_to_watchlist'])
        })

    return pd.DataFrame(data)


def create_discovery_filter_panel(key: str = "discovery_filter") -> FilterPanel:
    """创建发现机会筛选面板"""
    panel = FilterPanel(key, "发现机会筛选")

    panel.add_filter(
        "item_name", FilterType.TEXT, "项目名称"
    )

    panel.add_filter(
        "ranking_type", FilterType.SELECT, "排行榜类型",
        options=["全部", "hot", "rising", "new", "falling"],
        default_value="全部"
    )

    panel.add_filter(
        "opportunity_score", FilterType.SLIDER, "最低机会评分",
        min_value=0.0, max_value=10.0, default_value=5.0
    )

    panel.add_filter(
        "price_change_7d", FilterType.NUMBER_RANGE, "7天价格变化 (%)",
        min_value=-50.0, max_value=50.0, default_value=(-50.0, 50.0)
    )

    panel.add_filter(
        "status", FilterType.MULTI_SELECT, "状态",
        options=["new", "monitoring", "added_to_watchlist"],
        default_value=[]
    )

    return panel


# 移除旧的图表函数，因为新的仪表盘使用分层展示


# 旧函数已移除，新的仪表盘使用分层展示架构


if __name__ == "__main__":
    show_dashboard_page()


if __name__ == "__main__":
    show_dashboard_page()
