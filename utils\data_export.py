"""
Ares数据导入导出工具
支持多种格式的数据导入导出功能
"""

import pandas as pd
import json
import csv
import io
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, date
from pathlib import Path
import openpyxl
from dataclasses import asdict

from services.holdings import Holding, HoldingsSummary

logger = logging.getLogger(__name__)


class DataExporter:
    """数据导出器"""
    
    def __init__(self):
        """初始化数据导出器"""
        self.supported_formats = ['csv', 'excel', 'json', 'txt']
    
    def export_holdings(self, 
                       holdings: List[Holding], 
                       format_type: str = 'csv',
                       include_fields: Optional[List[str]] = None,
                       filename: Optional[str] = None) -> Union[str, bytes]:
        """
        导出持仓数据
        
        Args:
            holdings: 持仓列表
            format_type: 导出格式 ('csv', 'excel', 'json', 'txt')
            include_fields: 包含的字段列表
            filename: 文件名（可选）
            
        Returns:
            Union[str, bytes]: 导出的数据
        """
        try:
            if format_type.lower() not in self.supported_formats:
                raise ValueError(f"Unsupported format: {format_type}")
            
            # 准备数据
            data = self._prepare_holdings_data(holdings, include_fields)
            
            # 根据格式导出
            if format_type.lower() == 'csv':
                return self._export_to_csv(data)
            elif format_type.lower() == 'excel':
                return self._export_to_excel(data, filename)
            elif format_type.lower() == 'json':
                return self._export_to_json(data)
            elif format_type.lower() == 'txt':
                return self._export_to_txt(data)
            
        except Exception as e:
            logger.error("Error exporting holdings: %s", str(e))
            raise
    
    def export_holdings_summary(self, 
                               summary: HoldingsSummary,
                               format_type: str = 'json') -> str:
        """
        导出持仓汇总数据
        
        Args:
            summary: 持仓汇总
            format_type: 导出格式
            
        Returns:
            str: 导出的数据
        """
        try:
            data = summary.to_dict()
            
            if format_type.lower() == 'json':
                return json.dumps(data, indent=2, ensure_ascii=False)
            elif format_type.lower() == 'csv':
                df = pd.DataFrame([data])
                return df.to_csv(index=False, encoding='utf-8-sig')
            else:
                raise ValueError(f"Unsupported format for summary: {format_type}")
                
        except Exception as e:
            logger.error("Error exporting holdings summary: %s", str(e))
            raise
    
    def _prepare_holdings_data(self, 
                              holdings: List[Holding], 
                              include_fields: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """准备持仓数据"""
        # 默认字段映射
        field_mapping = {
            'item_name': '饰品名称',
            'quantity': '数量',
            'avg_cost': '平均成本',
            'current_price': '当前价格',
            'total_cost': '总成本',
            'total_value': '总价值',
            'profit_loss': '盈亏金额',
            'profit_percentage': '盈亏率',
            'purchase_date': '购买日期',
            'platform': '购买平台',
            'condition': '磨损程度',
            'notes': '备注',
            'created_at': '创建时间',
            'updated_at': '更新时间'
        }
        
        # 如果没有指定字段，使用默认字段
        if include_fields is None:
            include_fields = ['item_name', 'quantity', 'avg_cost', 'current_price', 
                            'total_value', 'profit_loss', 'profit_percentage']
        
        data = []
        for holding in holdings:
            row = {}
            holding_dict = holding.to_dict()
            
            for field in include_fields:
                if field in holding_dict:
                    display_name = field_mapping.get(field, field)
                    value = holding_dict[field]
                    
                    # 格式化特殊字段
                    if field in ['avg_cost', 'current_price', 'total_cost', 'total_value', 'profit_loss']:
                        row[display_name] = f"${value:.2f}" if value is not None else "$0.00"
                    elif field == 'profit_percentage':
                        row[display_name] = f"{value:.2f}%" if value is not None else "0.00%"
                    elif field in ['purchase_date', 'created_at', 'updated_at']:
                        if value:
                            if isinstance(value, str):
                                row[display_name] = value
                            else:
                                row[display_name] = value.isoformat() if hasattr(value, 'isoformat') else str(value)
                        else:
                            row[display_name] = ""
                    else:
                        row[display_name] = value if value is not None else ""
            
            data.append(row)
        
        return data
    
    def _export_to_csv(self, data: List[Dict[str, Any]]) -> str:
        """导出为CSV格式"""
        if not data:
            return ""
        
        df = pd.DataFrame(data)
        return df.to_csv(index=False, encoding='utf-8-sig')
    
    def _export_to_excel(self, data: List[Dict[str, Any]], filename: Optional[str] = None) -> bytes:
        """导出为Excel格式"""
        if not data:
            # 返回空的Excel文件
            df = pd.DataFrame()
        else:
            df = pd.DataFrame(data)
        
        output = io.BytesIO()
        
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='持仓数据', index=False)
            
            # 设置列宽
            worksheet = writer.sheets['持仓数据']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        output.seek(0)
        return output.getvalue()
    
    def _export_to_json(self, data: List[Dict[str, Any]]) -> str:
        """导出为JSON格式"""
        export_data = {
            'export_time': datetime.now().isoformat(),
            'total_records': len(data),
            'data': data
        }
        
        return json.dumps(export_data, indent=2, ensure_ascii=False)
    
    def _export_to_txt(self, data: List[Dict[str, Any]]) -> str:
        """导出为文本格式"""
        if not data:
            return "没有数据可导出"
        
        lines = []
        lines.append("=" * 80)
        lines.append("Ares持仓数据导出")
        lines.append(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"记录数量: {len(data)}")
        lines.append("=" * 80)
        lines.append("")
        
        for i, record in enumerate(data, 1):
            lines.append(f"记录 {i}:")
            lines.append("-" * 40)
            
            for key, value in record.items():
                lines.append(f"{key}: {value}")
            
            lines.append("")
        
        return "\n".join(lines)


class DataImporter:
    """数据导入器"""
    
    def __init__(self):
        """初始化数据导入器"""
        self.supported_formats = ['csv', 'excel', 'json']
        self.required_fields = ['item_name', 'quantity', 'avg_cost']
    
    def import_holdings(self, 
                       file_content: Union[str, bytes], 
                       format_type: str,
                       field_mapping: Optional[Dict[str, str]] = None) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        导入持仓数据
        
        Args:
            file_content: 文件内容
            format_type: 文件格式
            field_mapping: 字段映射 (文件字段 -> 系统字段)
            
        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (导入的数据, 错误信息列表)
        """
        try:
            if format_type.lower() not in self.supported_formats:
                raise ValueError(f"Unsupported format: {format_type}")
            
            # 解析文件内容
            if format_type.lower() == 'csv':
                raw_data = self._parse_csv(file_content)
            elif format_type.lower() == 'excel':
                raw_data = self._parse_excel(file_content)
            elif format_type.lower() == 'json':
                raw_data = self._parse_json(file_content)
            
            # 应用字段映射
            if field_mapping:
                raw_data = self._apply_field_mapping(raw_data, field_mapping)
            
            # 验证和清理数据
            clean_data, errors = self._validate_and_clean_data(raw_data)
            
            return clean_data, errors
            
        except Exception as e:
            logger.error("Error importing holdings: %s", str(e))
            return [], [f"导入失败: {str(e)}"]
    
    def _parse_csv(self, content: Union[str, bytes]) -> List[Dict[str, Any]]:
        """解析CSV文件"""
        if isinstance(content, bytes):
            content = content.decode('utf-8-sig')
        
        reader = csv.DictReader(io.StringIO(content))
        return list(reader)
    
    def _parse_excel(self, content: bytes) -> List[Dict[str, Any]]:
        """解析Excel文件"""
        df = pd.read_excel(io.BytesIO(content), engine='openpyxl')
        return df.to_dict('records')
    
    def _parse_json(self, content: Union[str, bytes]) -> List[Dict[str, Any]]:
        """解析JSON文件"""
        if isinstance(content, bytes):
            content = content.decode('utf-8')
        
        data = json.loads(content)
        
        # 如果是导出的格式，提取data字段
        if isinstance(data, dict) and 'data' in data:
            return data['data']
        elif isinstance(data, list):
            return data
        else:
            raise ValueError("Invalid JSON format")
    
    def _apply_field_mapping(self, 
                           data: List[Dict[str, Any]], 
                           field_mapping: Dict[str, str]) -> List[Dict[str, Any]]:
        """应用字段映射"""
        mapped_data = []
        
        for record in data:
            mapped_record = {}
            
            for file_field, system_field in field_mapping.items():
                if file_field in record:
                    mapped_record[system_field] = record[file_field]
            
            # 保留未映射的字段
            for key, value in record.items():
                if key not in field_mapping and key not in mapped_record:
                    mapped_record[key] = value
            
            mapped_data.append(mapped_record)
        
        return mapped_data
    
    def _validate_and_clean_data(self, data: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
        """验证和清理数据"""
        clean_data = []
        errors = []
        
        for i, record in enumerate(data, 1):
            record_errors = []
            clean_record = {}
            
            # 检查必需字段
            for field in self.required_fields:
                if field not in record or not record[field]:
                    record_errors.append(f"缺少必需字段: {field}")
                    continue
                
                # 清理和验证字段值
                if field == 'item_name':
                    clean_record[field] = str(record[field]).strip()
                    if len(clean_record[field]) < 3:
                        record_errors.append("饰品名称至少需要3个字符")
                
                elif field == 'quantity':
                    try:
                        clean_record[field] = int(float(record[field]))
                        if clean_record[field] <= 0:
                            record_errors.append("数量必须大于0")
                    except (ValueError, TypeError):
                        record_errors.append("数量必须是有效的数字")
                
                elif field == 'avg_cost':
                    try:
                        clean_record[field] = float(record[field])
                        if clean_record[field] <= 0:
                            record_errors.append("平均成本必须大于0")
                    except (ValueError, TypeError):
                        record_errors.append("平均成本必须是有效的数字")
            
            # 处理可选字段
            optional_fields = {
                'current_price': float,
                'purchase_date': str,
                'platform': str,
                'condition': str,
                'notes': str
            }
            
            for field, field_type in optional_fields.items():
                if field in record and record[field]:
                    try:
                        if field_type == float:
                            clean_record[field] = float(record[field])
                        else:
                            clean_record[field] = str(record[field]).strip()
                    except (ValueError, TypeError):
                        record_errors.append(f"{field} 格式无效")
            
            # 如果有错误，记录错误信息
            if record_errors:
                errors.extend([f"第{i}行: {error}" for error in record_errors])
            else:
                clean_data.append(clean_record)
        
        return clean_data, errors


# 全局导出器和导入器实例
_data_exporter: Optional[DataExporter] = None
_data_importer: Optional[DataImporter] = None


def get_data_exporter() -> DataExporter:
    """获取全局数据导出器实例"""
    global _data_exporter
    if _data_exporter is None:
        _data_exporter = DataExporter()
    return _data_exporter


def get_data_importer() -> DataImporter:
    """获取全局数据导入器实例"""
    global _data_importer
    if _data_importer is None:
        _data_importer = DataImporter()
    return _data_importer


# 便捷函数
def export_holdings_to_csv(holdings: List[Holding], include_fields: Optional[List[str]] = None) -> str:
    """导出持仓为CSV格式"""
    exporter = get_data_exporter()
    return exporter.export_holdings(holdings, 'csv', include_fields)


def export_holdings_to_excel(holdings: List[Holding], include_fields: Optional[List[str]] = None) -> bytes:
    """导出持仓为Excel格式"""
    exporter = get_data_exporter()
    return exporter.export_holdings(holdings, 'excel', include_fields)


def import_holdings_from_csv(csv_content: str) -> Tuple[List[Dict[str, Any]], List[str]]:
    """从CSV导入持仓数据"""
    importer = get_data_importer()
    return importer.import_holdings(csv_content, 'csv')


def import_holdings_from_excel(excel_content: bytes) -> Tuple[List[Dict[str, Any]], List[str]]:
    """从Excel导入持仓数据"""
    importer = get_data_importer()
    return importer.import_holdings(excel_content, 'excel')
