"""
基础数据收集器集成测试
测试整个基础数据收集系统的集成功能
"""

import pytest
import asyncio
import tempfile
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
import os
import base64
dummy_key_bytes = b'dummy_key_for_import_only_32byte'
dummy_key = base64.urlsafe_b64encode(dummy_key_bytes).decode()
os.environ.setdefault('STEAMDT_API_KEY', 'dummy_key_for_import')
os.environ.setdefault('ENCRYPTION_KEY', dummy_key)
os.environ.setdefault('SECRET_KEY', 'dummy_secret_key_for_import')
os.environ['DATABASE_URL'] = 'sqlite:///data/ares.db'

from services.base_data_collector import (
    BaseDataCollector, 
    get_base_data_collector,
    start_base_data_collector,
    stop_base_data_collector
)
from core.database import get_database_manager, Item


class TestSystemIntegration:
    """系统集成测试"""
    
    @pytest.fixture
    def temp_db_file(self):
        """创建临时数据库文件"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_path = Path(f.name)
        yield temp_path
        # 清理
        if temp_path.exists():
            temp_path.unlink()
    
    @pytest.fixture
    def integration_collector(self, temp_db_file):
        """创建集成测试用的收集器"""
        # 使用临时数据库
        os.environ['DATABASE_URL'] = f'sqlite:///{temp_db_file}'
        
        # 重置全局实例
        import services.base_data_collector
        services.base_data_collector._base_data_collector = None
        
        collector = get_base_data_collector()
        yield collector
        
        # 清理
        if collector.running:
            asyncio.run(collector.stop())
    
    @pytest.mark.asyncio
    async def test_full_data_collection_workflow(self, integration_collector):
        """测试完整的数据收集工作流"""
        collector = integration_collector
        
        # 模拟API响应
        mock_api_response = {
            'success': True,
            'data': [
                {
                    'name': 'AK-47 | 红线 (久经沙场)',
                    'marketHashName': 'AK-47 | Redline (Field-Tested)',
                    'platformList': [
                        {'name': 'steam', 'itemId': 'steam_123'},
                        {'name': 'buff163', 'itemId': 'buff_456'}
                    ]
                },
                {
                    'name': 'M4A4 | 龙王 (崭新出厂)',
                    'marketHashName': 'M4A4 | Dragon King (Factory New)',
                    'platformList': [
                        {'name': 'steam', 'itemId': 'steam_789'},
                        {'name': 'buff163', 'itemId': 'buff_101'}
                    ]
                }
            ]
        }
        
        # 模拟API调用
        with patch.object(collector, '_should_call_api', return_value=True), \
             patch.object(collector, '_call_api_with_retry', return_value=mock_api_response):
            
            # 执行数据收集
            result = await collector.collect_base_data()
            
            # 验证结果
            assert result.success
            assert result.total_items == 2
            assert result.new_items == 2
            assert result.updated_items == 0
            assert result.data_quality_score > 0
        
        # 验证数据库中的数据
        db_manager = get_database_manager()
        with db_manager.get_session() as session:
            items = session.query(Item).all()
            assert len(items) == 2
            
            # 验证第一个饰品
            ak47 = session.get(Item, 'AK-47 | Redline (Field-Tested)')
            assert ak47 is not None
            assert ak47.name == 'AK-47 | 红线 (久经沙场)'
            assert ak47.weapon_type == 'AK-47'
            assert ak47.skin_name == 'Redline'
            assert ak47.data_source == 'steamdt'
            assert ak47.is_active
            
            # 验证平台数据
            platform_data = json.loads(ak47.platform_data)
            assert 'steam' in platform_data
            assert 'buff163' in platform_data
            assert platform_data['steam'] == 'steam_123'
            assert platform_data['buff163'] == 'buff_456'
    
    @pytest.mark.asyncio
    async def test_service_lifecycle(self, integration_collector):
        """测试服务生命周期管理"""
        collector = integration_collector
        
        # 初始状态
        assert not collector.running
        
        # 启动服务
        await collector.start()
        assert collector.running
        assert collector.scheduler.running
        
        # 获取状态
        status = collector.get_scheduler_status()
        assert status['running']
        assert status['scheduler_running']
        
        # 停止服务
        await collector.stop()
        assert not collector.running
    
    @pytest.mark.asyncio
    async def test_manual_sync_integration(self, integration_collector):
        """测试手动同步集成"""
        collector = integration_collector
        
        # 模拟API响应
        mock_api_response = {
            'success': True,
            'data': [
                {
                    'name': '测试饰品',
                    'marketHashName': 'Test Item (Field-Tested)',
                    'platformList': [
                        {'name': 'steam', 'itemId': 'test_123'}
                    ]
                }
            ]
        }
        
        with patch.object(collector, '_call_api_with_retry', return_value=mock_api_response):
            
            # 执行手动同步
            result = await collector.trigger_manual_sync(force=True, reason="集成测试")
            
            # 验证结果
            assert result.success
            assert result.total_items == 1
            assert result.new_items == 1
        
        # 验证数据库
        db_manager = get_database_manager()
        with db_manager.get_session() as session:
            item = session.get(Item, 'Test Item (Field-Tested)')
            assert item is not None
            assert item.name == '测试饰品'
    
    def test_statistics_integration(self, integration_collector):
        """测试统计功能集成"""
        collector = integration_collector
        
        # 设置一些统计数据
        collector.api_call_count = 5
        collector.api_success_count = 4
        collector.api_failure_count = 1
        collector.total_items_collected = 100
        
        # 获取API统计
        api_stats = collector.get_api_statistics()
        assert api_stats['total_calls'] == 5
        assert api_stats['successful_calls'] == 4
        assert api_stats['failed_calls'] == 1
        assert api_stats['success_rate'] == 80.0
        assert api_stats['total_items_collected'] == 100
        
        # 获取数据库统计
        db_stats = collector.get_database_statistics()
        assert isinstance(db_stats, dict)
        assert 'total_items' in db_stats
        
        # 获取监控摘要
        summary = collector.get_monitoring_summary()
        assert 'service_status' in summary
        assert 'api_performance' in summary
        assert 'data_metrics' in summary
    
    def test_quality_validation_integration(self, integration_collector):
        """测试质量验证集成"""
        collector = integration_collector
        
        # 创建测试数据 - 包含高质量和低质量数据
        from services.base_data_collector import ItemBaseInfo, PlatformInfo
        
        high_quality_item = ItemBaseInfo(
            name='AK-47 | 红线 (久经沙场)',
            market_hash_name='AK-47 | Redline (Field-Tested)',
            platform_list=[
                PlatformInfo(name='steam', item_id='steam_123'),
                PlatformInfo(name='buff163', item_id='buff_456')
            ]
        )
        
        low_quality_item = ItemBaseInfo(
            name='',  # 空名称
            market_hash_name='Invalid Name',  # 无效格式
            platform_list=[]  # 空平台列表
        )
        
        items = [high_quality_item, low_quality_item]
        
        # 验证数据质量
        quality_score = collector._validate_data_quality(items)
        assert 0 <= quality_score <= 100
        
        # 过滤低质量数据
        valid_items = collector.validate_items_before_storage(items, min_quality_score=70.0)
        assert len(valid_items) == 1  # 只有高质量数据通过
        assert valid_items[0].name == 'AK-47 | 红线 (久经沙场)'
    
    def test_state_persistence_integration(self, integration_collector):
        """测试状态持久化集成"""
        collector = integration_collector
        
        # 设置状态
        collector.api_call_count = 10
        collector.api_success_count = 8
        collector.last_update = datetime.now()
        
        # 保存状态
        collector._save_state()
        
        # 创建新的收集器实例
        new_collector = BaseDataCollector()
        new_collector.state_file = collector.state_file
        
        # 加载状态
        new_collector._load_state()
        
        # 验证状态恢复
        assert new_collector.api_call_count == 10
        assert new_collector.api_success_count == 8
        assert new_collector.last_update is not None
    
    @pytest.mark.asyncio
    async def test_error_recovery_integration(self, integration_collector):
        """测试错误恢复集成"""
        collector = integration_collector
        
        # 模拟API调用失败
        with patch.object(collector, '_call_api_with_retry', side_effect=Exception("API Error")):
            
            result = await collector.collect_base_data()
            
            # 应该优雅地处理错误
            assert not result.success
            assert 'API Error' in result.error_message or 'API调用失败' in result.error_message
        
        # 验证错误统计
        assert collector.api_failure_count > 0
    
    def test_monitoring_data_export_integration(self, integration_collector):
        """测试监控数据导出集成"""
        collector = integration_collector
        
        # 设置一些监控数据
        collector.api_call_count = 20
        collector.api_success_count = 18
        collector.total_items_collected = 500
        
        # 导出JSON格式
        json_data = collector.export_monitoring_data(format_type='json')
        assert isinstance(json_data, str)
        
        parsed_data = json.loads(json_data)
        assert 'service_status' in parsed_data
        
        # 导出Prometheus格式
        prometheus_data = collector.export_monitoring_data(format_type='prometheus')
        assert isinstance(prometheus_data, str)
        assert 'base_data_service_running' in prometheus_data


class TestGlobalFunctionIntegration:
    """全局函数集成测试"""
    
    @pytest.mark.asyncio
    async def test_global_collector_management(self):
        """测试全局收集器管理"""
        # 重置全局实例
        import services.base_data_collector
        services.base_data_collector._base_data_collector = None
        
        # 获取收集器实例
        collector1 = get_base_data_collector()
        collector2 = get_base_data_collector()
        
        # 应该是同一个实例（单例模式）
        assert collector1 is collector2
        
        # 测试启动
        result = await start_base_data_collector()
        assert result is collector1
        assert collector1.running
        
        # 测试停止
        await stop_base_data_collector()
        assert not collector1.running
    
    def test_collector_running_status(self):
        """测试收集器运行状态检查"""
        from services.base_data_collector import is_base_data_collector_running
        
        # 重置全局实例
        import services.base_data_collector
        services.base_data_collector._base_data_collector = None
        
        # 初始状态
        assert not is_base_data_collector_running()
        
        # 获取收集器并设置运行状态
        collector = get_base_data_collector()
        collector.running = True
        
        assert is_base_data_collector_running()
        
        # 停止
        collector.running = False
        assert not is_base_data_collector_running()


class TestDatabaseIntegration:
    """数据库集成测试"""
    
    @pytest.fixture
    def temp_db_integration(self):
        """创建临时数据库用于集成测试"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_path = Path(f.name)
        
        # 设置临时数据库
        original_url = os.environ.get('DATABASE_URL')
        os.environ['DATABASE_URL'] = f'sqlite:///{temp_path}'
        
        # 初始化数据库
        from core.database import get_database_manager, Base
        db_manager = get_database_manager()
        Base.metadata.create_all(bind=db_manager.engine)
        
        yield db_manager
        
        # 清理
        if original_url:
            os.environ['DATABASE_URL'] = original_url
        if temp_path.exists():
            temp_path.unlink()
    
    def test_database_operations_integration(self, temp_db_integration):
        """测试数据库操作集成"""
        db_manager = temp_db_integration
        
        # 创建测试数据
        from services.base_data_collector import ItemBaseInfo, PlatformInfo
        
        test_item = ItemBaseInfo(
            name='测试饰品',
            market_hash_name='Test Item (Field-Tested)',
            platform_list=[
                PlatformInfo(name='steam', item_id='test_123'),
                PlatformInfo(name='buff163', item_id='test_456')
            ]
        )
        
        # 创建收集器并测试数据库操作
        collector = BaseDataCollector()
        
        # 测试插入
        result = collector._update_database([test_item])
        assert result.new_count == 1
        assert result.updated_count == 0
        
        # 验证数据
        with db_manager.get_session() as session:
            item = session.get(Item, 'Test Item (Field-Tested)')
            assert item is not None
            assert item.name == '测试饰品'
            assert item.weapon_type == 'Test Item'
            assert item.data_source == 'steamdt'
            assert item.is_active
        
        # 测试更新
        test_item.name = '更新后的饰品'
        result = collector._update_database([test_item])
        assert result.new_count == 0
        assert result.updated_count == 1
        
        # 验证更新
        with db_manager.get_session() as session:
            item = session.get(Item, 'Test Item (Field-Tested)')
            assert item.name == '更新后的饰品'
    
    def test_database_statistics_integration(self, temp_db_integration):
        """测试数据库统计集成"""
        db_manager = temp_db_integration
        collector = BaseDataCollector()
        
        # 插入一些测试数据
        with db_manager.get_session() as session:
            for i in range(5):
                item = Item(
                    market_hash_name=f'Test Item {i} (Field-Tested)',
                    name=f'测试饰品 {i}',
                    weapon_type='AK-47',
                    data_source='steamdt',
                    is_active=True
                )
                session.add(item)
            session.commit()
        
        # 获取统计信息
        stats = collector.get_database_statistics()
        
        assert stats['total_items'] == 5
        assert stats['steamdt_items'] == 5
        assert stats['active_items'] == 5
        assert 'weapon_type_distribution' in stats
        assert stats['weapon_type_distribution']['AK-47'] == 5
