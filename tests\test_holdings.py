"""
持仓管理功能测试
验证持仓管理服务和组件的核心功能
"""

import pytest
import asyncio
import pandas as pd
from datetime import datetime, date, timedelta
from pathlib import Path
import sys
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.holdings import HoldingsManager, Holding, HoldingsSummary, get_holdings_manager
from app.components.holdings_form import HoldingsForm, create_holdings_add_form
from utils.data_export import DataExporter, DataImporter, get_data_exporter, get_data_importer


class TestHolding:
    """持仓记录测试"""
    
    def test_holding_initialization(self):
        """测试持仓记录初始化"""
        holding = Holding(
            item_id="test_item_001",
            item_name="AK-47 | Redline (Field-Tested)",
            quantity=3,
            avg_cost=52.30,
            current_price=58.75,
            purchase_date=date(2024, 1, 15),
            platform="Steam市场",
            condition="久经沙场",
            notes="测试持仓"
        )
        
        assert holding.item_id == "test_item_001"
        assert holding.item_name == "AK-47 | Redline (Field-Tested)"
        assert holding.quantity == 3
        assert holding.avg_cost == 52.30
        assert holding.current_price == 58.75
        assert abs(holding.total_cost - 156.90) < 0.01  # 52.30 * 3
        assert abs(holding.total_value - 176.25) < 0.01  # 58.75 * 3
        assert abs(holding.profit_loss - 19.35) < 0.01  # 176.25 - 156.90
        assert abs(holding.profit_percentage - 12.33) < 0.01  # (19.35 / 156.90) * 100
    
    def test_holding_to_dict(self):
        """测试持仓记录转换为字典"""
        holding = Holding(
            item_id="test_item_002",
            item_name="AWP | Dragon Lore",
            quantity=1,
            avg_cost=2500.00,
            current_price=2750.00,
            purchase_date=date(2024, 2, 1)
        )
        
        holding_dict = holding.to_dict()
        
        assert holding_dict['item_id'] == "test_item_002"
        assert holding_dict['item_name'] == "AWP | Dragon Lore"
        assert holding_dict['quantity'] == 1
        assert holding_dict['avg_cost'] == 2500.00
        assert holding_dict['current_price'] == 2750.00
        assert holding_dict['total_cost'] == 2500.00
        assert holding_dict['total_value'] == 2750.00
        assert holding_dict['profit_loss'] == 250.00
        assert holding_dict['profit_percentage'] == 10.0
        assert holding_dict['purchase_date'] == "2024-02-01"
    
    def test_holding_post_init_calculation(self):
        """测试持仓记录后初始化计算"""
        holding = Holding(
            item_id="test_item_003",
            item_name="Karambit | Fade",
            quantity=2,
            avg_cost=800.00,
            current_price=750.00
        )
        
        # 验证自动计算的字段
        assert holding.total_cost == 1600.00  # 800.00 * 2
        assert holding.total_value == 1500.00  # 750.00 * 2
        assert holding.profit_loss == -100.00  # 1500.00 - 1600.00
        assert holding.profit_percentage == -6.25  # (-100.00 / 1600.00) * 100


class TestHoldingsSummary:
    """持仓汇总测试"""
    
    def test_holdings_summary_initialization(self):
        """测试持仓汇总初始化"""
        summary = HoldingsSummary(
            total_holdings=5,
            total_cost=5000.00,
            total_value=5500.00,
            total_profit_loss=500.00,
            avg_profit_percentage=10.0,
            profitable_holdings=3,
            losing_holdings=2
        )
        
        assert summary.total_holdings == 5
        assert summary.total_cost == 5000.00
        assert summary.total_value == 5500.00
        assert summary.total_profit_loss == 500.00
        assert summary.avg_profit_percentage == 10.0
        assert summary.profitable_holdings == 3
        assert summary.losing_holdings == 2
    
    def test_holdings_summary_to_dict(self):
        """测试持仓汇总转换为字典"""
        summary = HoldingsSummary(
            total_holdings=3,
            total_cost=1000.00,
            total_value=1200.00,
            total_profit_loss=200.00,
            avg_profit_percentage=20.0,
            profitable_holdings=2,
            losing_holdings=1
        )
        
        summary_dict = summary.to_dict()
        
        assert summary_dict['total_holdings'] == 3
        assert summary_dict['total_cost'] == 1000.00
        assert summary_dict['total_value'] == 1200.00
        assert summary_dict['total_profit_loss'] == 200.00
        assert summary_dict['avg_profit_percentage'] == 20.0
        assert summary_dict['profitable_holdings'] == 2
        assert summary_dict['losing_holdings'] == 1


class TestHoldingsManager:
    """持仓管理器测试"""
    
    def test_holdings_manager_initialization(self):
        """测试持仓管理器初始化"""
        manager = HoldingsManager()
        
        assert manager.config is not None
        assert manager.db_manager is not None
        assert manager.price_precision == 2
        assert manager.auto_update_prices == True
        assert manager.batch_size == 50
    
    @pytest.mark.asyncio
    async def test_calculate_pnl(self):
        """测试盈亏计算"""
        manager = HoldingsManager()
        
        holding = Holding(
            item_id="test_item_004",
            item_name="M4A4 | Howl",
            quantity=1,
            avg_cost=1000.00,
            current_price=1200.00
        )
        
        # 模拟价格获取
        manager._get_current_price = lambda item_id: 1200.00
        
        profit_loss, profit_percentage = await manager.calculate_pnl(holding)
        
        assert profit_loss == 200.00  # 1200.00 - 1000.00
        assert profit_percentage == 20.0  # (200.00 / 1000.00) * 100


class TestHoldingsForm:
    """持仓表单组件测试"""
    
    def test_holdings_form_initialization(self):
        """测试持仓表单初始化"""
        form = HoldingsForm("test_form", "测试表单")
        
        assert form.key == "test_form"
        assert form.title == "测试表单"
        assert form.holdings_manager is not None
    
    def test_validate_add_form(self):
        """测试添加表单验证"""
        form = HoldingsForm("test_form")
        
        # 测试有效数据
        errors = form._validate_add_form("AK-47 | Redline", 3, 52.30)
        assert len(errors) == 0
        
        # 测试无效数据
        errors = form._validate_add_form("", 0, -10.0)
        assert len(errors) == 3
        assert "饰品名称" in errors
        assert "数量" in errors
        assert "购买价格" in errors
        
        # 测试名称太短
        errors = form._validate_add_form("AK", 1, 100.0)
        assert "饰品名称" in errors
    
    def test_extract_item_id(self):
        """测试饰品ID提取"""
        form = HoldingsForm("test_form")
        
        item_id = form._extract_item_id("AK-47 | Redline (Field-Tested)")
        
        assert isinstance(item_id, str)
        assert len(item_id) == 16  # MD5哈希的前16位
        
        # 相同名称应该产生相同ID
        item_id2 = form._extract_item_id("AK-47 | Redline (Field-Tested)")
        assert item_id == item_id2
    
    def test_create_holdings_forms(self):
        """测试创建持仓表单工厂函数"""
        add_form = create_holdings_add_form("test_add")
        assert add_form.key == "test_add"
        assert add_form.title == "添加新持仓"


class TestDataExporter:
    """数据导出器测试"""
    
    def test_data_exporter_initialization(self):
        """测试数据导出器初始化"""
        exporter = DataExporter()
        
        assert 'csv' in exporter.supported_formats
        assert 'excel' in exporter.supported_formats
        assert 'json' in exporter.supported_formats
        assert 'txt' in exporter.supported_formats
    
    def test_prepare_holdings_data(self):
        """测试准备持仓数据"""
        exporter = DataExporter()
        
        holdings = [
            {
                'item_name': 'AK-47 | Redline',
                'quantity': 3,
                'avg_cost': 52.30,
                'current_price': 58.75,
                'total_value': 176.25,
                'profit_loss': 19.35,
                'profit_percentage': 12.33
            }
        ]
        
        data = exporter._prepare_holdings_data(holdings)
        
        assert len(data) == 1
        assert data[0]['饰品名称'] == 'AK-47 | Redline'
        assert data[0]['数量'] == 3
        assert data[0]['平均成本'] == '$52.30'
        assert data[0]['当前价格'] == '$58.75'
        assert data[0]['总价值'] == '$176.25'
        assert data[0]['盈亏金额'] == '$19.35'
        assert data[0]['盈亏率'] == '12.33%'
    
    def test_export_to_csv(self):
        """测试CSV导出"""
        exporter = DataExporter()
        
        data = [
            {'饰品名称': 'AK-47 | Redline', '数量': 3, '平均成本': '$52.30'},
            {'饰品名称': 'AWP | Dragon Lore', '数量': 1, '平均成本': '$2500.00'}
        ]
        
        csv_content = exporter._export_to_csv(data)
        
        assert isinstance(csv_content, str)
        assert 'AK-47 | Redline' in csv_content
        assert 'AWP | Dragon Lore' in csv_content
        assert '饰品名称,数量,平均成本' in csv_content
    
    def test_export_to_json(self):
        """测试JSON导出"""
        exporter = DataExporter()
        
        data = [
            {'饰品名称': 'AK-47 | Redline', '数量': 3}
        ]
        
        json_content = exporter._export_to_json(data)
        
        assert isinstance(json_content, str)
        parsed_data = json.loads(json_content)
        assert 'export_time' in parsed_data
        assert 'total_records' in parsed_data
        assert 'data' in parsed_data
        assert parsed_data['total_records'] == 1
        assert parsed_data['data'][0]['饰品名称'] == 'AK-47 | Redline'


class TestDataImporter:
    """数据导入器测试"""
    
    def test_data_importer_initialization(self):
        """测试数据导入器初始化"""
        importer = DataImporter()
        
        assert 'csv' in importer.supported_formats
        assert 'excel' in importer.supported_formats
        assert 'json' in importer.supported_formats
        assert 'item_name' in importer.required_fields
        assert 'quantity' in importer.required_fields
        assert 'avg_cost' in importer.required_fields
    
    def test_parse_csv(self):
        """测试CSV解析"""
        importer = DataImporter()
        
        csv_content = """item_name,quantity,avg_cost
AK-47 | Redline,3,52.30
AWP | Dragon Lore,1,2500.00"""
        
        data = importer._parse_csv(csv_content)
        
        assert len(data) == 2
        assert data[0]['item_name'] == 'AK-47 | Redline'
        assert data[0]['quantity'] == '3'
        assert data[0]['avg_cost'] == '52.30'
        assert data[1]['item_name'] == 'AWP | Dragon Lore'
    
    def test_validate_and_clean_data(self):
        """测试数据验证和清理"""
        importer = DataImporter()
        
        raw_data = [
            {'item_name': 'AK-47 | Redline', 'quantity': '3', 'avg_cost': '52.30'},
            {'item_name': 'AWP | Dragon Lore', 'quantity': '1', 'avg_cost': '2500.00'},
            {'item_name': '', 'quantity': '0', 'avg_cost': '-100'},  # 无效数据
            {'item_name': 'Valid Item', 'quantity': 'invalid', 'avg_cost': '100'}  # 无效数量
        ]
        
        clean_data, errors = importer._validate_and_clean_data(raw_data)
        
        # 应该有2条有效数据
        assert len(clean_data) == 2
        assert clean_data[0]['item_name'] == 'AK-47 | Redline'
        assert clean_data[0]['quantity'] == 3
        assert clean_data[0]['avg_cost'] == 52.30
        
        # 应该有错误信息
        assert len(errors) > 0
        assert any('第3行' in error for error in errors)
        assert any('第4行' in error for error in errors)


def test_global_instances():
    """测试全局实例"""
    manager1 = get_holdings_manager()
    manager2 = get_holdings_manager()
    assert manager1 is manager2
    
    exporter1 = get_data_exporter()
    exporter2 = get_data_exporter()
    assert exporter1 is exporter2
    
    importer1 = get_data_importer()
    importer2 = get_data_importer()
    assert importer1 is importer2


if __name__ == "__main__":
    # 运行基本测试
    print("运行持仓管理功能基本测试...")
    
    # 测试持仓记录
    print("测试持仓记录...")
    holding = Holding(
        item_id="test_001",
        item_name="AK-47 | Redline",
        quantity=3,
        avg_cost=52.30,
        current_price=58.75
    )
    assert abs(holding.total_cost - 156.90) < 0.01
    assert abs(holding.total_value - 176.25) < 0.01
    assert abs(holding.profit_loss - 19.35) < 0.01
    print("✓ 持仓记录测试通过")
    
    # 测试持仓汇总
    print("测试持仓汇总...")
    summary = HoldingsSummary(
        total_holdings=3,
        total_cost=1000.00,
        total_value=1200.00,
        total_profit_loss=200.00
    )
    assert summary.total_holdings == 3
    assert summary.total_profit_loss == 200.00
    print("✓ 持仓汇总测试通过")
    
    # 测试持仓管理器（跳过，因为需要配置）
    print("测试持仓管理器...")
    print("✓ 持仓管理器测试跳过（需要配置）")
    
    # 测试数据导出器
    print("测试数据导出器...")
    exporter = DataExporter()
    assert 'csv' in exporter.supported_formats
    print("✓ 数据导出器测试通过")
    
    # 测试数据导入器
    print("测试数据导入器...")
    importer = DataImporter()
    assert 'item_name' in importer.required_fields
    print("✓ 数据导入器测试通过")
    
    print("所有持仓管理功能测试通过！")
