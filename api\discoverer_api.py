"""
Ares发现器API接口
提供机会发现服务的管理和监控API
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, Body
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime

from services.discoverer import get_discoverer_service
from core.exceptions import AresException

# 创建路由器
router = APIRouter(prefix="/api/discoverer", tags=["发现器"])


# 请求模型
class DiscoveryConfigRequest(BaseModel):
    """发现配置请求"""
    discovery_limit: Optional[int] = Field(None, ge=10, le=200, description="每个排行榜的发现数量限制")
    min_opportunity_score: Optional[float] = Field(None, ge=0.0, le=10.0, description="最小机会评分")
    discovery_times: Optional[List[str]] = Field(None, description="发现时间列表")


class ManualDiscoveryRequest(BaseModel):
    """手动发现请求"""
    ranking_types: Optional[List[str]] = Field(None, description="指定排行榜类型")
    limit_override: Optional[int] = Field(None, ge=1, le=100, description="数量限制覆盖")


# 响应模型
class DiscovererStatusResponse(BaseModel):
    """发现器状态响应"""
    success: bool = Field(..., description="是否成功")
    status: Dict[str, Any] = Field(..., description="发现器状态")


class DiscoveryResponse(BaseModel):
    """发现响应"""
    success: bool = Field(..., description="是否成功")
    opportunities_count: int = Field(..., description="发现的机会数量")
    opportunities: List[Dict[str, Any]] = Field(..., description="发现的机会列表")
    timestamp: str = Field(..., description="时间戳")


# API端点
@router.get("/status", response_model=DiscovererStatusResponse)
async def get_discoverer_status():
    """获取发现器状态"""
    try:
        discoverer = get_discoverer_service()
        status = discoverer.get_status()
        
        return DiscovererStatusResponse(
            success=True,
            status=status
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start_discoverer(background_tasks: BackgroundTasks):
    """启动发现器服务"""
    try:
        discoverer = get_discoverer_service()
        
        if discoverer.running:
            return {
                "success": True,
                "message": "Discoverer is already running",
                "status": "running"
            }
        
        # 在后台启动发现器
        background_tasks.add_task(discoverer.start)
        
        return {
            "success": True,
            "message": "Discoverer start initiated",
            "status": "starting",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_discoverer():
    """停止发现器服务"""
    try:
        discoverer = get_discoverer_service()
        await discoverer.stop()
        
        return {
            "success": True,
            "message": "Discoverer stopped successfully",
            "status": "stopped",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/discover", response_model=DiscoveryResponse)
async def manual_discovery(request: Optional[ManualDiscoveryRequest] = None):
    """手动触发机会发现"""
    try:
        discoverer = get_discoverer_service()
        
        # 应用请求参数
        if request and request.limit_override:
            original_limit = discoverer.discovery_limit
            discoverer.discovery_limit = request.limit_override
        
        # 执行发现
        opportunities = await discoverer.manual_discovery()
        
        # 恢复原始配置
        if request and request.limit_override:
            discoverer.discovery_limit = original_limit
        
        return DiscoveryResponse(
            success=True,
            opportunities_count=len(opportunities),
            opportunities=[opp.to_dict() for opp in opportunities],
            timestamp=datetime.utcnow().isoformat()
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history")
async def get_discovery_history(
    limit: int = Query(10, ge=1, le=50, description="返回记录数量")
):
    """获取发现历史"""
    try:
        discoverer = get_discoverer_service()
        history = discoverer.get_discovery_history(limit)
        
        return {
            "success": True,
            "history": history,
            "total_sessions": len(history),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_discovery_statistics():
    """获取发现统计信息"""
    try:
        discoverer = get_discoverer_service()
        status = discoverer.get_status()
        stats = status['stats']
        
        # 计算额外统计信息
        avg_discoveries_per_session = (
            stats['total_discoveries'] / max(stats['discovery_sessions'], 1)
            if stats['discovery_sessions'] > 0 else 0
        )
        
        avg_additions_per_session = (
            stats['successful_additions'] / max(stats['discovery_sessions'], 1)
            if stats['discovery_sessions'] > 0 else 0
        )
        
        return {
            "success": True,
            "statistics": {
                **stats,
                "avg_discoveries_per_session": round(avg_discoveries_per_session, 2),
                "avg_additions_per_session": round(avg_additions_per_session, 2),
                "duplicate_rate": round(
                    (stats['duplicate_items'] / max(stats['total_discoveries'], 1)) * 100, 1
                )
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config")
async def update_discoverer_config(request: DiscoveryConfigRequest):
    """更新发现器配置"""
    try:
        discoverer = get_discoverer_service()
        
        updated_configs = {}
        
        if request.discovery_limit is not None:
            discoverer.discovery_limit = request.discovery_limit
            updated_configs['discovery_limit'] = request.discovery_limit
        
        if request.min_opportunity_score is not None:
            discoverer.min_opportunity_score = request.min_opportunity_score
            updated_configs['min_opportunity_score'] = request.min_opportunity_score
        
        if request.discovery_times is not None:
            # 验证时间格式
            for time_str in request.discovery_times:
                try:
                    hour, minute = map(int, time_str.split(':'))
                    if not (0 <= hour <= 23 and 0 <= minute <= 59):
                        raise ValueError("Invalid time format")
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid time format: {time_str}. Use HH:MM format."
                    )
            
            discoverer.discovery_times = request.discovery_times
            updated_configs['discovery_times'] = request.discovery_times
            
            # 重新设置定时任务
            if discoverer.running:
                discoverer.scheduler.remove_all_jobs()
                discoverer._setup_scheduled_tasks()
        
        return {
            "success": True,
            "message": "Configuration updated successfully",
            "updated_configs": updated_configs,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_discoverer_config():
    """获取发现器配置"""
    try:
        discoverer = get_discoverer_service()
        
        return {
            "success": True,
            "config": {
                "discovery_limit": discoverer.discovery_limit,
                "min_opportunity_score": discoverer.min_opportunity_score,
                "discovery_times": discoverer.discovery_times
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/rankings")
async def get_current_rankings(
    ranking_type: str = Query("hot", description="排行榜类型"),
    limit: int = Query(50, ge=1, le=200, description="返回数量")
):
    """获取当前排行榜数据"""
    try:
        discoverer = get_discoverer_service()
        
        if not discoverer.api_manager:
            await discoverer.initialize()
        
        # 获取排行榜数据
        from core.api_manager import get_rankings
        response = await get_rankings(discoverer.api_manager, ranking_type, limit)
        
        if response.status.value == "success":
            return {
                "success": True,
                "ranking_type": ranking_type,
                "data": response.data,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get rankings: {response.error_message}"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/opportunities/recent")
async def get_recent_opportunities(
    limit: int = Query(20, ge=1, le=100, description="返回数量")
):
    """获取最近发现的机会"""
    try:
        discoverer = get_discoverer_service()
        history = discoverer.get_discovery_history(limit=5)
        
        # 收集最近的机会
        recent_opportunities = []
        for session in reversed(history):
            for opp in session.get('opportunities', []):
                recent_opportunities.append(opp)
                if len(recent_opportunities) >= limit:
                    break
            if len(recent_opportunities) >= limit:
                break
        
        return {
            "success": True,
            "opportunities": recent_opportunities[:limit],
            "total_count": len(recent_opportunities),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset-stats")
async def reset_discovery_statistics():
    """重置发现统计信息"""
    try:
        discoverer = get_discoverer_service()
        
        # 重置统计信息
        discoverer.stats.total_discoveries = 0
        discoverer.stats.successful_additions = 0
        discoverer.stats.duplicate_items = 0
        discoverer.stats.api_calls_made = 0
        discoverer.stats.discovery_sessions = 0
        discoverer.stats.last_discovery_time = None
        
        # 保存历史记录
        discoverer._save_history()
        
        return {
            "success": True,
            "message": "Statistics reset successfully",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs")
async def get_scheduled_jobs():
    """获取计划任务信息"""
    try:
        discoverer = get_discoverer_service()
        
        jobs = []
        if discoverer.scheduler.running:
            for job in discoverer.scheduler.get_jobs():
                jobs.append({
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger)
                })
        
        return {
            "success": True,
            "jobs": jobs,
            "scheduler_running": discoverer.scheduler.running if hasattr(discoverer.scheduler, 'running') else False,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
