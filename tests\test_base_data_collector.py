"""
基础数据收集器测试
测试BaseDataCollector的各个功能模块
"""

import pytest
import asyncio
import json
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.base_data_collector import (
    BaseDataCollector,
    ItemBaseInfo,
    PlatformInfo,
    BaseDataResult,
    UpdateResult,
    get_base_data_collector
)
from core.database import Item


class TestBaseDataCollector:
    """基础数据收集器测试类"""
    
    @pytest.fixture
    def temp_state_file(self):
        """创建临时状态文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = Path(f.name)
        yield temp_path
        # 清理
        if temp_path.exists():
            temp_path.unlink()
    
    @pytest.fixture
    def mock_config_manager(self):
        """模拟配置管理器"""
        mock_config = Mock()
        mock_config.get.return_value = "02:00"
        return mock_config
    
    @pytest.fixture
    def mock_db_manager(self):
        """模拟数据库管理器"""
        mock_db = Mock()
        mock_session = Mock()
        mock_db.get_session.return_value.__enter__.return_value = mock_session
        mock_db.get_session.return_value.__exit__.return_value = None
        return mock_db
    
    @pytest.fixture
    def mock_system_monitor(self):
        """模拟系统监控器"""
        mock_monitor = Mock()
        mock_monitor.record_api_request = Mock()
        mock_monitor.record_metric = Mock()
        mock_monitor.get_api_stats.return_value = {}
        mock_monitor.get_latest_metric.return_value = None
        return mock_monitor
    
    @pytest.fixture
    def collector(self, temp_state_file, mock_config_manager, mock_db_manager, mock_system_monitor):
        """创建测试用的收集器实例"""
        with patch('services.base_data_collector.get_config_manager', return_value=mock_config_manager), \
             patch('services.base_data_collector.get_database_manager', return_value=mock_db_manager), \
             patch('services.base_data_collector.get_system_monitor', return_value=mock_system_monitor):
            
            collector = BaseDataCollector()
            collector.state_file = temp_state_file
            return collector
    
    @pytest.fixture
    def mock_api_response(self):
        """模拟API响应数据"""
        return {
            'success': True,
            'data': [
                {
                    'name': 'AK-47 | 红线 (久经沙场)',
                    'marketHashName': 'AK-47 | Redline (Field-Tested)',
                    'platformList': [
                        {'name': 'steam', 'itemId': 'steam_123'},
                        {'name': 'buff163', 'itemId': 'buff_456'}
                    ]
                },
                {
                    'name': 'M4A4 | 龙王 (崭新出厂)',
                    'marketHashName': 'M4A4 | Dragon King (Factory New)',
                    'platformList': [
                        {'name': 'steam', 'itemId': 'steam_789'},
                        {'name': 'buff163', 'itemId': 'buff_101'}
                    ]
                }
            ]
        }
    
    @pytest.fixture
    def sample_items(self):
        """示例饰品数据"""
        return [
            ItemBaseInfo(
                name='AK-47 | 红线 (久经沙场)',
                market_hash_name='AK-47 | Redline (Field-Tested)',
                platform_list=[
                    PlatformInfo(name='steam', item_id='steam_123'),
                    PlatformInfo(name='buff163', item_id='buff_456')
                ]
            ),
            ItemBaseInfo(
                name='M4A4 | 龙王 (崭新出厂)',
                market_hash_name='M4A4 | Dragon King (Factory New)',
                platform_list=[
                    PlatformInfo(name='steam', item_id='steam_789'),
                    PlatformInfo(name='buff163', item_id='buff_101')
                ]
            )
        ]


class TestDataParsing:
    """数据解析测试"""
    
    def test_parse_api_response_success(self, collector, mock_api_response):
        """测试成功解析API响应"""
        items = collector._parse_api_response(mock_api_response)
        
        assert len(items) == 2
        assert items[0].name == 'AK-47 | 红线 (久经沙场)'
        assert items[0].market_hash_name == 'AK-47 | Redline (Field-Tested)'
        assert len(items[0].platform_list) == 2
        assert items[0].platform_list[0].name == 'steam'
        assert items[0].platform_list[0].item_id == 'steam_123'
    
    def test_parse_api_response_empty_data(self, collector):
        """测试解析空数据"""
        empty_response = {'success': True, 'data': []}
        items = collector._parse_api_response(empty_response)
        assert len(items) == 0
    
    def test_parse_api_response_invalid_format(self, collector):
        """测试解析无效格式数据"""
        invalid_response = {'success': False, 'error': 'API Error'}
        items = collector._parse_api_response(invalid_response)
        assert len(items) == 0
    
    def test_parse_api_response_missing_fields(self, collector):
        """测试解析缺少字段的数据"""
        incomplete_response = {
            'success': True,
            'data': [
                {
                    'name': 'AK-47 | 红线',
                    # 缺少 marketHashName
                    'platformList': []
                }
            ]
        }
        items = collector._parse_api_response(incomplete_response)
        assert len(items) == 0  # 应该跳过不完整的数据
    
    def test_parse_weapon_info(self, collector):
        """测试武器信息解析"""
        weapon_type, skin_name = collector._parse_weapon_info('AK-47 | Redline (Field-Tested)')
        assert weapon_type == 'AK-47'
        assert skin_name == 'Redline'
        
        # 测试没有分隔符的情况
        weapon_type, skin_name = collector._parse_weapon_info('Karambit')
        assert weapon_type == 'Karambit'
        assert skin_name == ''


class TestDataValidation:
    """数据验证测试"""
    
    def test_validate_single_item_high_quality(self, collector, sample_items):
        """测试高质量数据验证"""
        item = sample_items[0]
        result = collector._validate_single_item(item, 0)
        
        assert result['is_valid']
        assert result['score'] >= 70
        assert len(result['issues']) == 0
    
    def test_validate_single_item_low_quality(self, collector):
        """测试低质量数据验证"""
        low_quality_item = ItemBaseInfo(
            name='',  # 空名称
            market_hash_name='Invalid Name',  # 无效格式
            platform_list=[]  # 空平台列表
        )
        
        result = collector._validate_single_item(low_quality_item, 0)
        
        assert not result['is_valid']
        assert result['score'] < 70
        assert len(result['issues']) > 0
    
    def test_validate_market_hash_name(self, collector):
        """测试market_hash_name格式验证"""
        assert collector._validate_market_hash_name('AK-47 | Redline (Field-Tested)')
        assert not collector._validate_market_hash_name('Invalid Name')
        assert not collector._validate_market_hash_name('')
    
    def test_validate_platform_ids(self, collector, sample_items):
        """测试平台ID验证"""
        item = sample_items[0]
        assert collector._validate_platform_ids(item.platform_list)
        
        # 测试空平台列表
        assert not collector._validate_platform_ids([])
    
    def test_validate_platform_item_id(self, collector):
        """测试平台特定ID验证"""
        assert collector._validate_platform_item_id('steam', '123456')
        assert collector._validate_platform_item_id('buff163', '789')
        assert not collector._validate_platform_item_id('steam', '')
        assert not collector._validate_platform_item_id('steam', 'invalid')
    
    def test_validate_data_quality(self, collector, sample_items):
        """测试整体数据质量验证"""
        quality_score = collector._validate_data_quality(sample_items)
        
        assert 0 <= quality_score <= 100
        assert quality_score > 70  # 示例数据应该是高质量的
    
    def test_validate_items_before_storage(self, collector, sample_items):
        """测试存储前数据过滤"""
        # 添加一个低质量的数据
        low_quality_item = ItemBaseInfo(
            name='',
            market_hash_name='Invalid',
            platform_list=[]
        )
        all_items = sample_items + [low_quality_item]
        
        valid_items = collector.validate_items_before_storage(all_items, min_quality_score=70.0)
        
        # 应该过滤掉低质量数据
        assert len(valid_items) == len(sample_items)
        assert all(item.name for item in valid_items)  # 所有有效数据都有名称


class TestAPICallLogic:
    """API调用逻辑测试"""
    
    @pytest.mark.asyncio
    async def test_collect_base_data_success(self, collector, mock_api_response):
        """测试成功的数据收集"""
        mock_api_manager = AsyncMock()
        mock_api_manager.get_base_item_info.return_value = mock_api_response
        
        with patch.object(collector, '_get_steamdt_api', return_value=mock_api_manager), \
             patch.object(collector, '_should_call_api', return_value=True), \
             patch.object(collector, '_update_database', return_value=UpdateResult(1, 1)):
            
            result = await collector.collect_base_data()
            
            assert result.success
            assert result.total_items == 2
            assert result.data_quality_score > 0
    
    @pytest.mark.asyncio
    async def test_collect_base_data_api_limit(self, collector):
        """测试API限制检查"""
        with patch.object(collector, '_should_call_api', return_value=False):
            result = await collector.collect_base_data()
            
            assert not result.success
            assert 'API调用限制' in result.error_message
    
    @pytest.mark.asyncio
    async def test_collect_base_data_api_failure(self, collector):
        """测试API调用失败"""
        mock_api_manager = AsyncMock()
        mock_api_manager.get_base_item_info.return_value = None
        
        with patch.object(collector, '_get_steamdt_api', return_value=mock_api_manager), \
             patch.object(collector, '_should_call_api', return_value=True):
            
            result = await collector.collect_base_data()
            
            assert not result.success
            assert 'API调用失败' in result.error_message
    
    def test_should_call_api_no_previous_call(self, collector):
        """测试首次API调用检查"""
        collector.last_update = None
        assert collector._should_call_api()
    
    def test_should_call_api_recent_call(self, collector):
        """测试最近调用过API的检查"""
        collector.last_update = datetime.now() - timedelta(hours=1)
        assert not collector._should_call_api()
    
    def test_should_call_api_old_call(self, collector):
        """测试很久前调用过API的检查"""
        collector.last_update = datetime.now() - timedelta(hours=25)
        assert collector._should_call_api()
    
    @pytest.mark.asyncio
    async def test_call_api_with_retry_success(self, collector):
        """测试API重试机制成功"""
        mock_api_manager = AsyncMock()
        mock_api_manager.get_base_item_info.return_value = {'success': True}
        
        result = await collector._call_api_with_retry(mock_api_manager)
        
        assert result is not None
        assert result['success']
    
    @pytest.mark.asyncio
    async def test_call_api_with_retry_failure(self, collector):
        """测试API重试机制失败"""
        mock_api_manager = AsyncMock()
        mock_api_manager.get_base_item_info.side_effect = Exception("API Error")
        
        result = await collector._call_api_with_retry(mock_api_manager, max_retries=1)
        
        assert result is None


class TestDatabaseOperations:
    """数据库操作测试"""

    def test_update_database_new_items(self, collector, sample_items):
        """测试新增饰品到数据库"""
        mock_session = collector.db_manager.get_session.return_value.__enter__.return_value
        mock_session.get.return_value = None  # 模拟饰品不存在

        result = collector._update_database(sample_items)

        assert result.new_count == len(sample_items)
        assert result.updated_count == 0
        assert mock_session.add.call_count == len(sample_items)

    def test_update_database_existing_items(self, collector, sample_items):
        """测试更新现有饰品"""
        mock_session = collector.db_manager.get_session.return_value.__enter__.return_value
        mock_existing_item = Mock(spec=Item)
        mock_existing_item.is_active = True
        mock_session.get.return_value = mock_existing_item

        result = collector._update_database(sample_items)

        assert result.new_count == 0
        assert result.updated_count == len(sample_items)
        assert mock_session.add.call_count == 0

    def test_update_database_mixed_items(self, collector, sample_items):
        """测试混合新增和更新饰品"""
        mock_session = collector.db_manager.get_session.return_value.__enter__.return_value

        # 第一个饰品存在，第二个不存在
        def mock_get(item_class, market_hash_name):
            if market_hash_name == sample_items[0].market_hash_name:
                mock_item = Mock(spec=Item)
                mock_item.is_active = True
                return mock_item
            return None

        mock_session.get.side_effect = mock_get

        result = collector._update_database(sample_items)

        assert result.new_count == 1
        assert result.updated_count == 1

    def test_update_database_empty_list(self, collector):
        """测试空列表的数据库更新"""
        result = collector._update_database([])

        assert result.new_count == 0
        assert result.updated_count == 0

    def test_create_new_item(self, collector, sample_items):
        """测试创建新饰品对象"""
        item_info = sample_items[0]
        new_item = collector._create_new_item(item_info)

        assert new_item.market_hash_name == item_info.market_hash_name
        assert new_item.name == item_info.name
        assert new_item.weapon_type == 'AK-47'
        assert new_item.skin_name == 'Redline'
        assert new_item.is_active

    def test_update_existing_item(self, collector, sample_items):
        """测试更新现有饰品"""
        item_info = sample_items[0]
        existing_item = Mock(spec=Item)
        existing_item.is_active = False

        collector._update_existing_item(existing_item, item_info)

        assert existing_item.name == item_info.name
        assert existing_item.data_source == item_info.data_source
        assert existing_item.is_active  # 应该被重新激活


class TestSchedulerIntegration:
    """调度器集成测试"""

    @pytest.mark.asyncio
    async def test_start_service(self, collector):
        """测试启动服务"""
        with patch.object(collector.scheduler, 'start') as mock_start:
            await collector.start()

            assert collector.running
            mock_start.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_service(self, collector):
        """测试停止服务"""
        collector.running = True
        collector.scheduler.running = True

        with patch.object(collector.scheduler, 'shutdown') as mock_shutdown:
            await collector.stop()

            assert not collector.running
            mock_shutdown.assert_called_once()

    def test_setup_scheduled_tasks(self, collector):
        """测试设置定时任务"""
        with patch.object(collector.scheduler, 'add_job') as mock_add_job:
            collector._setup_scheduled_tasks()

            mock_add_job.assert_called_once()
            call_args = mock_add_job.call_args
            assert call_args[1]['id'] == 'base_data_sync'

    @pytest.mark.asyncio
    async def test_collect_and_store_data_success(self, collector, mock_api_response):
        """测试定时任务执行成功"""
        mock_result = BaseDataResult(
            success=True,
            total_items=2,
            new_items=1,
            updated_items=1,
            data_quality_score=95.0
        )

        with patch.object(collector, 'collect_base_data', return_value=mock_result):
            await collector.collect_and_store_data()

            # 验证监控记录被调用
            assert collector.system_monitor.record_api_request.called

    @pytest.mark.asyncio
    async def test_trigger_manual_sync(self, collector, mock_api_response):
        """测试手动触发同步"""
        mock_result = BaseDataResult(
            success=True,
            total_items=2,
            new_items=1,
            updated_items=1
        )

        with patch.object(collector, 'collect_base_data', return_value=mock_result):
            result = await collector.trigger_manual_sync(force=True)

            assert result.success
            assert result.total_items == 2

    def test_update_sync_time(self, collector):
        """测试更新同步时间"""
        new_time = "03:30"
        old_time = collector.sync_time

        with patch.object(collector.scheduler, 'remove_job'), \
             patch.object(collector, '_setup_scheduled_tasks'):
            collector.update_sync_time(new_time)

            assert collector.sync_time == new_time
            assert collector.sync_time != old_time

    def test_get_scheduler_status(self, collector):
        """测试获取调度器状态"""
        collector.running = True
        collector.scheduler.running = True

        with patch.object(collector.scheduler, 'get_jobs', return_value=[]), \
             patch.object(collector.scheduler, 'get_job', return_value=None):
            status = collector.get_scheduler_status()

            assert status['running']
            assert status['scheduler_running']
            assert status['sync_time'] == collector.sync_time


class TestStatisticsAndMonitoring:
    """统计和监控测试"""

    def test_get_api_statistics(self, collector):
        """测试获取API统计"""
        collector.api_call_count = 10
        collector.api_success_count = 8
        collector.api_failure_count = 2
        collector.total_items_collected = 1000

        stats = collector.get_api_statistics()

        assert stats['total_calls'] == 10
        assert stats['successful_calls'] == 8
        assert stats['failed_calls'] == 2
        assert stats['success_rate'] == 80.0
        assert stats['total_items_collected'] == 1000

    def test_reset_statistics(self, collector):
        """测试重置统计"""
        collector.api_call_count = 10
        collector.api_success_count = 8

        with patch.object(collector, '_save_state'):
            collector.reset_statistics()

            assert collector.api_call_count == 0
            assert collector.api_success_count == 0

    def test_get_database_statistics(self, collector):
        """测试获取数据库统计"""
        mock_session = collector.db_manager.get_session.return_value.__enter__.return_value
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.count.return_value = 1000
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value.all.return_value = [('AK-47', 100), ('M4A4', 80)]

        stats = collector.get_database_statistics()

        assert 'total_items' in stats
        assert 'weapon_type_distribution' in stats

    def test_cleanup_old_items(self, collector):
        """测试清理旧数据"""
        mock_session = collector.db_manager.get_session.return_value.__enter__.return_value
        mock_old_items = [Mock(spec=Item) for _ in range(5)]
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = mock_old_items

        cleanup_count = collector.cleanup_old_items(days_threshold=30)

        assert cleanup_count == 5
        for item in mock_old_items:
            assert not item.is_active

    def test_get_monitoring_summary(self, collector):
        """测试获取监控摘要"""
        with patch.object(collector, 'get_api_statistics', return_value={'total_calls': 10}), \
             patch.object(collector, 'get_database_statistics', return_value={'total_items': 1000}), \
             patch.object(collector, 'get_quality_statistics', return_value={'status': 'available'}), \
             patch.object(collector, 'get_scheduler_status', return_value={'running': True}):

            summary = collector.get_monitoring_summary()

            assert 'service_status' in summary
            assert 'api_performance' in summary
            assert 'data_metrics' in summary
            assert 'health_indicators' in summary

    def test_export_monitoring_data_json(self, collector):
        """测试导出JSON格式监控数据"""
        with patch.object(collector, 'get_monitoring_summary', return_value={'test': 'data'}):
            data = collector.export_monitoring_data(format_type='json')

            assert isinstance(data, str)
            parsed_data = json.loads(data)
            assert parsed_data['test'] == 'data'

    def test_export_monitoring_data_prometheus(self, collector):
        """测试导出Prometheus格式监控数据"""
        mock_summary = {
            'service_status': {'running': True},
            'api_performance': {'internal_stats': {'total_calls': 10, 'success_rate': 90}},
            'data_metrics': {'quality': {'status': 'available', 'quality_score': 85}},
        }

        with patch.object(collector, 'get_monitoring_summary', return_value=mock_summary):
            data = collector.export_monitoring_data(format_type='prometheus')

            assert isinstance(data, str)
            assert 'base_data_service_running 1' in data
            assert 'base_data_api_success_rate 90' in data


class TestStateManagement:
    """状态管理测试"""

    def test_save_and_load_state(self, collector, temp_state_file):
        """测试状态保存和加载"""
        # 设置一些状态
        collector.last_update = datetime.now()
        collector.api_call_count = 5
        collector.api_success_count = 4

        # 保存状态
        collector._save_state()

        # 创建新的收集器实例并加载状态
        new_collector = BaseDataCollector()
        new_collector.state_file = temp_state_file
        new_collector._load_state()

        assert new_collector.api_call_count == 5
        assert new_collector.api_success_count == 4

    def test_load_state_file_not_exists(self, collector, temp_state_file):
        """测试加载不存在的状态文件"""
        temp_state_file.unlink()  # 删除文件

        # 应该不会抛出异常
        collector._load_state()

        # 默认值应该保持
        assert collector.api_call_count == 0

    def test_save_state_invalid_path(self, collector):
        """测试保存状态到无效路径"""
        collector.state_file = Path('/invalid/path/state.json')

        # 应该不会抛出异常，只是记录警告
        collector._save_state()


class TestGlobalFunctions:
    """全局函数测试"""

    def test_get_base_data_collector_singleton(self):
        """测试全局收集器单例模式"""
        collector1 = get_base_data_collector()
        collector2 = get_base_data_collector()

        assert collector1 is collector2

    @pytest.mark.asyncio
    async def test_start_base_data_collector(self):
        """测试启动全局收集器"""
        from services.base_data_collector import start_base_data_collector

        with patch('services.base_data_collector.get_base_data_collector') as mock_get:
            mock_collector = AsyncMock()
            mock_get.return_value = mock_collector

            result = await start_base_data_collector()

            mock_collector.start.assert_called_once()
            assert result is mock_collector

    @pytest.mark.asyncio
    async def test_stop_base_data_collector(self):
        """测试停止全局收集器"""
        from services.base_data_collector import stop_base_data_collector

        with patch('services.base_data_collector._base_data_collector') as mock_collector:
            mock_collector.running = True

            await stop_base_data_collector()

            mock_collector.stop.assert_called_once()

    def test_is_base_data_collector_running(self):
        """测试检查收集器运行状态"""
        from services.base_data_collector import is_base_data_collector_running

        with patch('services.base_data_collector._base_data_collector') as mock_collector:
            mock_collector.running = True
            assert is_base_data_collector_running()

            mock_collector.running = False
            assert not is_base_data_collector_running()

        # 测试收集器不存在的情况
        with patch('services.base_data_collector._base_data_collector', None):
            assert not is_base_data_collector_running()


class TestAPIIntegration:
    """API集成测试"""

    @pytest.mark.asyncio
    async def test_api_integration_with_real_collector(self):
        """测试与真实收集器的API集成"""
        from services.base_data_collector import get_base_data_collector

        collector = get_base_data_collector()

        # 测试基本属性
        assert hasattr(collector, 'running')
        assert hasattr(collector, 'sync_time')
        assert hasattr(collector, 'last_update')

        # 测试方法存在
        assert callable(getattr(collector, 'start', None))
        assert callable(getattr(collector, 'stop', None))
        assert callable(getattr(collector, 'get_api_statistics', None))
        assert callable(getattr(collector, 'get_database_statistics', None))

    @pytest.mark.asyncio
    async def test_api_endpoints_compatibility(self):
        """测试API端点兼容性"""
        # 这里可以添加对API端点的测试
        # 确保BaseDataCollector的方法与API端点期望的接口一致

        from services.base_data_collector import get_base_data_collector
        collector = get_base_data_collector()

        # 测试API统计方法
        stats = collector.get_api_statistics()
        required_keys = ['total_calls', 'successful_calls', 'failed_calls', 'success_rate']
        for key in required_keys:
            assert key in stats, f"API统计缺少必要字段: {key}"

        # 测试数据库统计方法
        db_stats = collector.get_database_statistics()
        assert isinstance(db_stats, dict)

        # 测试调度器状态方法
        scheduler_status = collector.get_scheduler_status()
        assert isinstance(scheduler_status, dict)
        assert 'running' in scheduler_status


class TestErrorHandling:
    """错误处理测试"""

    def test_handle_invalid_api_response(self, collector):
        """测试处理无效API响应"""
        invalid_responses = [
            None,
            {},
            {'success': False},
            {'success': True, 'data': None},
            {'success': True, 'data': 'invalid'},
            {'success': True, 'data': [{'invalid': 'data'}]}
        ]

        for invalid_response in invalid_responses:
            items = collector._parse_api_response(invalid_response)
            assert isinstance(items, list)
            assert len(items) == 0

    def test_handle_database_errors(self, collector):
        """测试数据库错误处理"""
        # 模拟数据库连接失败
        with patch.object(collector.db_manager, 'get_session') as mock_session:
            mock_session.side_effect = Exception("Database connection failed")

            # 应该不会抛出异常，而是返回错误结果
            try:
                result = collector._update_database([])
                # 如果没有异常处理，这里会抛出异常
            except Exception:
                # 如果抛出异常，说明错误处理不够完善
                pytest.fail("数据库错误没有被正确处理")

    def test_handle_configuration_errors(self, collector):
        """测试配置错误处理"""
        # 测试无效的同步时间
        invalid_times = ['25:00', 'invalid', '', None]

        for invalid_time in invalid_times:
            try:
                collector.update_sync_time(invalid_time)
                # 应该有验证逻辑
            except (ValueError, TypeError):
                # 预期的异常
                pass

    @pytest.mark.asyncio
    async def test_handle_api_timeout(self, collector):
        """测试API超时处理"""
        mock_api_manager = AsyncMock()
        mock_api_manager.get_base_item_info.side_effect = asyncio.TimeoutError("API timeout")

        with patch.object(collector, '_get_steamdt_api', return_value=mock_api_manager), \
             patch.object(collector, '_should_call_api', return_value=True):

            result = await collector.collect_base_data()

            assert not result.success
            assert 'timeout' in result.error_message.lower() or 'api' in result.error_message.lower()


class TestPerformanceAndScalability:
    """性能和可扩展性测试"""

    def test_large_dataset_processing(self, collector):
        """测试大数据集处理"""
        # 创建大量测试数据
        large_dataset = []
        for i in range(1000):
            item_data = {
                'name': f'测试饰品 {i}',
                'marketHashName': f'Test Item {i} (Field-Tested)',
                'platformList': [
                    {'name': 'steam', 'itemId': f'steam_{i}'},
                    {'name': 'buff163', 'itemId': f'buff_{i}'}
                ]
            }
            large_dataset.append(item_data)

        # 测试解析性能
        import time
        start_time = time.time()

        mock_response = {'success': True, 'data': large_dataset}
        items = collector._parse_api_response(mock_response)

        end_time = time.time()
        processing_time = end_time - start_time

        assert len(items) == 1000
        assert processing_time < 5.0  # 应该在5秒内完成
        print(f"处理1000个饰品耗时: {processing_time:.2f}秒")

    def test_memory_usage(self, collector):
        """测试内存使用"""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 处理大量数据
        large_dataset = []
        for i in range(5000):
            item_data = {
                'name': f'测试饰品 {i}',
                'marketHashName': f'Test Item {i} (Field-Tested)',
                'platformList': [
                    {'name': 'steam', 'itemId': f'steam_{i}'},
                    {'name': 'buff163', 'itemId': f'buff_{i}'}
                ]
            }
            large_dataset.append(item_data)

        mock_response = {'success': True, 'data': large_dataset}
        items = collector._parse_api_response(mock_response)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        print(f"内存使用增加: {memory_increase:.2f}MB")
        assert memory_increase < 100  # 内存增加应该小于100MB

    def test_concurrent_operations(self, collector):
        """测试并发操作"""
        import threading
        import time

        results = []
        errors = []

        def worker():
            try:
                # 模拟并发的统计查询
                stats = collector.get_api_statistics()
                results.append(stats)
            except Exception as e:
                errors.append(e)

        # 创建多个线程
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker)
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        assert len(errors) == 0, f"并发操作出现错误: {errors}"
        assert len(results) == 10

        # 所有结果应该是一致的（因为是读操作）
        first_result = results[0]
        for result in results[1:]:
            assert result == first_result


class TestDataIntegrity:
    """数据完整性测试"""

    def test_data_consistency_after_update(self, collector, sample_items):
        """测试更新后数据一致性"""
        # 模拟数据库操作
        mock_session = collector.db_manager.get_session.return_value.__enter__.return_value

        # 第一次插入
        mock_session.get.return_value = None
        result1 = collector._update_database(sample_items)

        # 第二次更新
        mock_existing_items = []
        for item_info in sample_items:
            mock_item = Mock(spec=Item)
            mock_item.market_hash_name = item_info.market_hash_name
            mock_item.is_active = True
            mock_existing_items.append(mock_item)

        mock_session.get.side_effect = lambda cls, key: next(
            (item for item in mock_existing_items if item.market_hash_name == key),
            None
        )

        result2 = collector._update_database(sample_items)

        # 验证一致性
        assert result1.new_count == len(sample_items)
        assert result1.updated_count == 0
        assert result2.new_count == 0
        assert result2.updated_count == len(sample_items)

    def test_platform_data_integrity(self, collector, sample_items):
        """测试平台数据完整性"""
        for item in sample_items:
            # 验证平台数据格式
            assert isinstance(item.platform_list, list)

            for platform in item.platform_list:
                assert hasattr(platform, 'name')
                assert hasattr(platform, 'item_id')
                assert platform.name
                assert platform.item_id

    def test_state_persistence(self, collector, temp_state_file):
        """测试状态持久化完整性"""
        # 设置初始状态
        original_state = {
            'api_call_count': 10,
            'api_success_count': 8,
            'api_failure_count': 2,
            'total_items_collected': 1000,
            'last_update': datetime.now()
        }

        collector.api_call_count = original_state['api_call_count']
        collector.api_success_count = original_state['api_success_count']
        collector.api_failure_count = original_state['api_failure_count']
        collector.total_items_collected = original_state['total_items_collected']
        collector.last_update = original_state['last_update']

        # 保存状态
        collector._save_state()

        # 重置状态
        collector.api_call_count = 0
        collector.api_success_count = 0
        collector.api_failure_count = 0
        collector.total_items_collected = 0
        collector.last_update = None

        # 加载状态
        collector._load_state()

        # 验证状态恢复
        assert collector.api_call_count == original_state['api_call_count']
        assert collector.api_success_count == original_state['api_success_count']
        assert collector.api_failure_count == original_state['api_failure_count']
        assert collector.total_items_collected == original_state['total_items_collected']
        # 时间比较允许小的误差
        if collector.last_update and original_state['last_update']:
            time_diff = abs((collector.last_update - original_state['last_update']).total_seconds())
            assert time_diff < 1.0  # 1秒误差范围内
