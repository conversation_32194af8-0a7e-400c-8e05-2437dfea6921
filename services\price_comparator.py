"""
跨平台价格比较服务
实现Steam、BUFF、C5Game等平台的价格比较和价差计算
"""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from services.steamdt_api import ItemPriceData, PlatformPrice, get_steamdt_api_manager
from core.database import Price, get_database_manager
from core.config import get_config_manager


class Platform(Enum):
    """交易平台枚举"""
    STEAM = "steam"
    BUFF = "buff"
    C5GAME = "c5game"
    IGXE = "igxe"
    UUYP = "uuyp"


@dataclass
class PlatformFees:
    """平台手续费配置"""
    platform: Platform
    sell_fee_rate: float      # 卖出手续费率
    buy_fee_rate: float       # 买入手续费率
    withdraw_fee_rate: float  # 提现手续费率
    min_withdraw_amount: float # 最小提现金额
    withdraw_time_days: int   # 提现到账时间（天）
    
    def calculate_sell_cost(self, price: float) -> float:
        """计算卖出总成本"""
        return price * (1 - self.sell_fee_rate - self.withdraw_fee_rate)
    
    def calculate_buy_cost(self, price: float) -> float:
        """计算买入总成本"""
        return price * (1 + self.buy_fee_rate)


@dataclass
class PriceComparison:
    """价格比较结果"""
    item_name: str
    market_hash_name: str
    platform_prices: Dict[Platform, PlatformPrice]
    price_spread: Dict[str, float]  # 价差信息
    best_buy_platform: Optional[Platform]
    best_sell_platform: Optional[Platform]
    max_profit_margin: float
    comparison_time: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_name': self.item_name,
            'market_hash_name': self.market_hash_name,
            'platform_prices': {
                platform.value: {
                    'sell_price': price.sell_price,
                    'bidding_price': price.bidding_price,
                    'sell_count': price.sell_count,
                    'bidding_count': price.bidding_count
                }
                for platform, price in self.platform_prices.items()
            },
            'price_spread': self.price_spread,
            'best_buy_platform': self.best_buy_platform.value if self.best_buy_platform else None,
            'best_sell_platform': self.best_sell_platform.value if self.best_sell_platform else None,
            'max_profit_margin': self.max_profit_margin,
            'comparison_time': self.comparison_time.isoformat()
        }


class PlatformPriceComparator:
    """跨平台价格比较器"""
    
    def __init__(self):
        """初始化价格比较器"""
        self.logger = logging.getLogger(__name__)
        self.config = get_config_manager()
        
        # 平台手续费配置
        self.platform_fees = {
            Platform.STEAM: PlatformFees(
                platform=Platform.STEAM,
                sell_fee_rate=0.13,      # Steam 13%手续费
                buy_fee_rate=0.0,        # 买入无额外费用
                withdraw_fee_rate=0.0,   # Steam钱包无提现
                min_withdraw_amount=0.0,
                withdraw_time_days=0
            ),
            Platform.BUFF: PlatformFees(
                platform=Platform.BUFF,
                sell_fee_rate=0.025,     # BUFF 2.5%手续费
                buy_fee_rate=0.0,
                withdraw_fee_rate=0.01,  # 1%提现费
                min_withdraw_amount=100.0,
                withdraw_time_days=1
            ),
            Platform.C5GAME: PlatformFees(
                platform=Platform.C5GAME,
                sell_fee_rate=0.05,      # C5 5%手续费
                buy_fee_rate=0.0,
                withdraw_fee_rate=0.02,  # 2%提现费
                min_withdraw_amount=50.0,
                withdraw_time_days=2
            ),
            Platform.IGXE: PlatformFees(
                platform=Platform.IGXE,
                sell_fee_rate=0.05,      # IGXE 5%手续费
                buy_fee_rate=0.0,
                withdraw_fee_rate=0.015, # 1.5%提现费
                min_withdraw_amount=100.0,
                withdraw_time_days=1
            )
        }
        
        self.logger.info("Platform Price Comparator initialized")
    
    async def compare_item_prices(self, market_hash_name: str) -> Optional[PriceComparison]:
        """
        比较单个饰品的跨平台价格
        
        Args:
            market_hash_name: 饰品市场哈希名称
            
        Returns:
            PriceComparison: 价格比较结果
        """
        try:
            # 获取多平台价格数据
            price_data = await self._get_multi_platform_prices(market_hash_name)
            
            if not price_data or not price_data.platform_prices:
                self.logger.warning(f"No price data found for {market_hash_name}")
                return None
            
            # 解析平台价格
            platform_prices = self._parse_platform_prices(price_data.platform_prices)
            
            if len(platform_prices) < 2:
                self.logger.warning(f"Insufficient platform data for comparison: {market_hash_name}")
                return None
            
            # 计算价差和套利机会
            price_spread = self._calculate_price_spread(platform_prices)
            best_buy, best_sell, max_profit = self._find_best_arbitrage_opportunity(platform_prices)
            
            return PriceComparison(
                item_name=market_hash_name.split(' | ')[0] if ' | ' in market_hash_name else market_hash_name,
                market_hash_name=market_hash_name,
                platform_prices=platform_prices,
                price_spread=price_spread,
                best_buy_platform=best_buy,
                best_sell_platform=best_sell,
                max_profit_margin=max_profit,
                comparison_time=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error comparing prices for {market_hash_name}: {e}")
            return None
    
    async def _get_multi_platform_prices(self, market_hash_name: str) -> Optional[ItemPriceData]:
        """获取多平台价格数据"""
        try:
            # 使用steamdt API获取多平台价格
            api_manager = await get_steamdt_api_manager()
            price_data = await api_manager.get_item_price(market_hash_name)
            
            return price_data
            
        except Exception as e:
            self.logger.error(f"Error fetching multi-platform prices: {e}")
            return None
    
    def _parse_platform_prices(self, platform_prices: List[PlatformPrice]) -> Dict[Platform, PlatformPrice]:
        """解析平台价格数据"""
        parsed_prices = {}
        
        for price in platform_prices:
            platform = self._identify_platform(price.platform)
            if platform and price.sell_price > 0:
                parsed_prices[platform] = price
        
        return parsed_prices
    
    def _identify_platform(self, platform_name: str) -> Optional[Platform]:
        """识别平台类型"""
        platform_name_lower = platform_name.lower()
        
        if 'steam' in platform_name_lower:
            return Platform.STEAM
        elif 'buff' in platform_name_lower:
            return Platform.BUFF
        elif 'c5' in platform_name_lower or 'c5game' in platform_name_lower:
            return Platform.C5GAME
        elif 'igxe' in platform_name_lower:
            return Platform.IGXE
        elif 'uuyp' in platform_name_lower:
            return Platform.UUYP
        
        return None
    
    def _calculate_price_spread(self, platform_prices: Dict[Platform, PlatformPrice]) -> Dict[str, float]:
        """计算价差信息"""
        if len(platform_prices) < 2:
            return {}
        
        prices = [price.sell_price for price in platform_prices.values() if price.sell_price > 0]
        
        if not prices:
            return {}
        
        min_price = min(prices)
        max_price = max(prices)
        avg_price = sum(prices) / len(prices)
        
        return {
            'min_price': min_price,
            'max_price': max_price,
            'avg_price': avg_price,
            'absolute_spread': max_price - min_price,
            'relative_spread_percent': ((max_price - min_price) / min_price) * 100 if min_price > 0 else 0,
            'price_variance': sum((p - avg_price) ** 2 for p in prices) / len(prices)
        }
    
    def _find_best_arbitrage_opportunity(self, platform_prices: Dict[Platform, PlatformPrice]) -> Tuple[Optional[Platform], Optional[Platform], float]:
        """寻找最佳套利机会"""
        best_buy_platform = None
        best_sell_platform = None
        max_profit_margin = 0.0
        
        platforms = list(platform_prices.keys())
        
        # 比较所有平台组合
        for buy_platform in platforms:
            for sell_platform in platforms:
                if buy_platform == sell_platform:
                    continue
                
                buy_price = platform_prices[buy_platform].sell_price
                sell_price = platform_prices[sell_platform].sell_price
                
                if buy_price <= 0 or sell_price <= 0:
                    continue
                
                # 计算考虑手续费后的实际利润
                profit_margin = self._calculate_profit_margin(
                    buy_platform, sell_platform, buy_price, sell_price
                )
                
                if profit_margin > max_profit_margin:
                    max_profit_margin = profit_margin
                    best_buy_platform = buy_platform
                    best_sell_platform = sell_platform
        
        return best_buy_platform, best_sell_platform, max_profit_margin
    
    def _calculate_profit_margin(self, buy_platform: Platform, sell_platform: Platform, 
                                buy_price: float, sell_price: float) -> float:
        """计算考虑手续费后的利润率"""
        try:
            # 获取平台手续费配置
            buy_fees = self.platform_fees.get(buy_platform)
            sell_fees = self.platform_fees.get(sell_platform)
            
            if not buy_fees or not sell_fees:
                # 如果没有手续费配置，使用简单计算
                return ((sell_price - buy_price) / buy_price) * 100 if buy_price > 0 else 0
            
            # 计算实际成本和收益
            actual_buy_cost = buy_fees.calculate_buy_cost(buy_price)
            actual_sell_revenue = sell_fees.calculate_sell_cost(sell_price)
            
            # 计算利润率
            if actual_buy_cost > 0:
                profit_margin = ((actual_sell_revenue - actual_buy_cost) / actual_buy_cost) * 100
                return max(0, profit_margin)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating profit margin: {e}")
            return 0.0
    
    async def compare_batch_prices(self, market_hash_names: List[str]) -> List[PriceComparison]:
        """批量比较价格"""
        comparisons = []
        
        for market_hash_name in market_hash_names:
            try:
                comparison = await self.compare_item_prices(market_hash_name)
                if comparison:
                    comparisons.append(comparison)
                
                # 避免请求过快
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Error in batch comparison for {market_hash_name}: {e}")
                continue
        
        return comparisons
    
    def get_platform_fees_info(self) -> Dict[str, Dict[str, Any]]:
        """获取平台手续费信息"""
        return {
            platform.value: {
                'sell_fee_rate': fees.sell_fee_rate,
                'buy_fee_rate': fees.buy_fee_rate,
                'withdraw_fee_rate': fees.withdraw_fee_rate,
                'min_withdraw_amount': fees.min_withdraw_amount,
                'withdraw_time_days': fees.withdraw_time_days
            }
            for platform, fees in self.platform_fees.items()
        }
    
    async def save_comparison_to_database(self, comparison: PriceComparison):
        """保存价格比较结果到数据库"""
        try:
            db_manager = get_database_manager()
            
            async with db_manager.get_session() as session:
                # 为每个平台保存价格记录
                for platform, price_data in comparison.platform_prices.items():
                    price_record = Price(
                        item_id=comparison.market_hash_name.lower().replace(' ', '_'),
                        platform=platform.value,
                        ask_price=price_data.sell_price,
                        bid_price=price_data.bidding_price,
                        last_price=price_data.sell_price,
                        volume_24h=price_data.sell_count + price_data.bidding_count,
                        timestamp=comparison.comparison_time,
                        data_quality='good'
                    )
                    session.add(price_record)
                
                await session.commit()
                self.logger.debug(f"Saved price comparison for {comparison.market_hash_name}")
                
        except Exception as e:
            self.logger.error(f"Error saving comparison to database: {e}")


# 全局价格比较器实例
_price_comparator: Optional[PlatformPriceComparator] = None


def get_price_comparator() -> PlatformPriceComparator:
    """获取全局价格比较器实例"""
    global _price_comparator
    
    if _price_comparator is None:
        _price_comparator = PlatformPriceComparator()
    
    return _price_comparator
