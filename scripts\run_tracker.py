#!/usr/bin/env python3
"""
Ares追踪器服务运行脚本
启动和管理分层追踪服务
"""

import asyncio
import logging
import sys
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.tracker import get_tracker_service, TrackerService
from core.logging_config import setup_logging
from core.config import get_config_manager
from core.exceptions import AresException


def setup_argument_parser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Ares追踪器服务运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_tracker.py                    # 启动追踪器服务
  python run_tracker.py --status           # 查看追踪器状态
  python run_tracker.py --force-update item123  # 强制更新指定饰品
  python run_tracker.py --config-check     # 检查配置
  python run_tracker.py --dry-run          # 干运行模式
        """
    )
    
    parser.add_argument(
        '--status', 
        action='store_true',
        help='查看追踪器状态并退出'
    )
    
    parser.add_argument(
        '--force-update',
        metavar='ITEM_ID',
        help='强制更新指定饰品'
    )
    
    parser.add_argument(
        '--config-check',
        action='store_true',
        help='检查配置并退出'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，不实际执行更新'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--batch-size',
        type=int,
        metavar='N',
        help='批处理大小覆盖'
    )
    
    parser.add_argument(
        '--cycle-interval',
        type=int,
        metavar='SECONDS',
        help='循环间隔覆盖(秒)'
    )
    
    return parser


async def check_status():
    """检查追踪器状态"""
    try:
        tracker = get_tracker_service()
        await tracker.initialize()
        
        status = tracker.get_status()
        
        print("=== Ares追踪器状态 ===")
        print(f"运行状态: {'运行中' if status['running'] else '已停止'}")
        print(f"运行时间: {status['uptime'] or '未知'}")
        print()
        
        stats = status['stats']
        print("=== 统计信息 ===")
        print(f"总更新次数: {stats['total_updates']}")
        print(f"成功更新: {stats['successful_updates']}")
        print(f"失败更新: {stats['failed_updates']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"今日API调用: {stats['api_calls_today']}")
        print(f"核心池更新: {stats['core_pool_updates']}")
        print(f"主监控池更新: {stats['main_pool_updates']}")
        print(f"最后更新时间: {stats['last_update_time'] or '无'}")
        print()
        
        if status['scheduler_status']:
            scheduler_status = status['scheduler_status']
            print("=== 调度器状态 ===")
            print(f"调度器运行: {'是' if scheduler_status['running'] else '否'}")
            print(f"队列大小: {scheduler_status['queue_size']}")
            print(f"下次任务时间: {scheduler_status['next_task_time'] or '无'}")
            print(f"今日API调用: {scheduler_status['api_calls_today']}")
        
        return True
        
    except Exception as e:
        print(f"获取状态失败: {str(e)}")
        return False


async def force_update_item(item_id: str):
    """强制更新指定饰品"""
    try:
        print(f"强制更新饰品: {item_id}")
        
        tracker = get_tracker_service()
        await tracker.initialize()
        
        success = await tracker.force_update_item(item_id)
        
        if success:
            print(f"✓ 饰品 {item_id} 更新成功")
        else:
            print(f"✗ 饰品 {item_id} 更新失败")
        
        return success
        
    except Exception as e:
        print(f"强制更新失败: {str(e)}")
        return False


def check_config():
    """检查配置"""
    try:
        print("=== 配置检查 ===")
        
        config = get_config_manager()
        
        # 检查关键配置
        required_configs = [
            'api.steamdt.base_url',
            'api.steamdt.api_key',
            'database.url',
            'tracker.batch_size',
            'tracker.cycle_interval'
        ]
        
        missing_configs = []
        for config_key in required_configs:
            value = config.get(config_key)
            if value is None:
                missing_configs.append(config_key)
            else:
                print(f"✓ {config_key}: {value}")
        
        if missing_configs:
            print("\n缺失的配置:")
            for config_key in missing_configs:
                print(f"✗ {config_key}")
            return False
        
        print("\n✓ 所有必需配置都已设置")
        return True
        
    except Exception as e:
        print(f"配置检查失败: {str(e)}")
        return False


async def run_tracker(args):
    """运行追踪器服务"""
    try:
        print("=== 启动Ares追踪器服务 ===")
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 创建追踪器实例
        tracker = get_tracker_service()
        
        # 应用命令行参数覆盖
        if args.batch_size:
            tracker.batch_size = args.batch_size
            print(f"批处理大小覆盖: {args.batch_size}")
        
        if args.cycle_interval:
            tracker.cycle_interval = args.cycle_interval
            print(f"循环间隔覆盖: {args.cycle_interval}秒")
        
        if args.dry_run:
            print("⚠️  干运行模式：不会实际执行更新")
            # 这里可以设置干运行标志
        
        print("正在初始化服务组件...")
        
        # 启动追踪器
        await tracker.start()
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止服务...")
        if 'tracker' in locals():
            await tracker.stop()
        print("服务已停止")
    except Exception as e:
        print(f"追踪器运行失败: {str(e)}")
        logging.error("Tracker service failed", exc_info=True)
        sys.exit(1)


async def main():
    """主函数"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(level=getattr(logging, args.log_level))
    
    try:
        # 根据参数执行不同操作
        if args.config_check:
            success = check_config()
            sys.exit(0 if success else 1)
        
        elif args.status:
            success = await check_status()
            sys.exit(0 if success else 1)
        
        elif args.force_update:
            success = await force_update_item(args.force_update)
            sys.exit(0 if success else 1)
        
        else:
            # 默认启动追踪器服务
            await run_tracker(args)
    
    except AresException as e:
        print(f"Ares系统错误: {e.message}")
        logging.error("Ares system error", exc_info=True)
        sys.exit(1)
    except Exception as e:
        print(f"未知错误: {str(e)}")
        logging.error("Unknown error", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # 在Windows上设置事件循环策略
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主函数
    asyncio.run(main())
