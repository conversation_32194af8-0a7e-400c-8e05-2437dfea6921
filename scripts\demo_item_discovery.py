"""
饰品发现功能演示脚本
展示饰品发现和评估算法的工作原理
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.item_discovery import ItemDiscoveryService, ItemValueTier
from services.web_scraper import RankingItem


def create_demo_ranking_items():
    """创建演示用的排行榜数据"""
    demo_items = [
        RankingItem(
            rank=1,
            item_name="AK-47 | Redline (Field-Tested)",
            market_hash_name="AK-47 | Redline (Field-Tested)",
            current_price=45.50,
            price_change=2.30,
            price_change_percent=5.3,
            volume_24h=180,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/ak47_redline.png",
            item_url="https://steamdt.com/item/ak47_redline"
        ),
        RankingItem(
            rank=2,
            item_name="AWP | Asiimov (Field-Tested)",
            market_hash_name="AWP | Asiimov (Field-Tested)",
            current_price=85.20,
            price_change=-1.80,
            price_change_percent=-2.1,
            volume_24h=95,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/awp_asiimov.png",
            item_url="https://steamdt.com/item/awp_asiimov"
        ),
        RankingItem(
            rank=3,
            item_name="Karambit | Fade (Factory New)",
            market_hash_name="Karambit | Fade (Factory New)",
            current_price=1250.00,
            price_change=45.00,
            price_change_percent=3.7,
            volume_24h=12,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/karambit_fade.png",
            item_url="https://steamdt.com/item/karambit_fade"
        ),
        RankingItem(
            rank=5,
            item_name="Glock-18 | Water Elemental (Minimal Wear)",
            market_hash_name="Glock-18 | Water Elemental (Minimal Wear)",
            current_price=18.75,
            price_change=0.85,
            price_change_percent=4.7,
            volume_24h=65,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/glock_water.png",
            item_url="https://steamdt.com/item/glock_water"
        ),
        RankingItem(
            rank=8,
            item_name="M4A4 | Howl (Field-Tested)",
            market_hash_name="M4A4 | Howl (Field-Tested)",
            current_price=3200.00,
            price_change=150.00,
            price_change_percent=4.9,
            volume_24h=3,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/m4a4_howl.png",
            item_url="https://steamdt.com/item/m4a4_howl"
        ),
        RankingItem(
            rank=12,
            item_name="StatTrak™ AK-47 | Vulcan (Minimal Wear)",
            market_hash_name="StatTrak™ AK-47 | Vulcan (Minimal Wear)",
            current_price=320.50,
            price_change=-8.20,
            price_change_percent=-2.5,
            volume_24h=25,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/ak47_vulcan_st.png",
            item_url="https://steamdt.com/item/ak47_vulcan_st"
        ),
        RankingItem(
            rank=15,
            item_name="Driver Gloves | Crimson Weave (Field-Tested)",
            market_hash_name="Driver Gloves | Crimson Weave (Field-Tested)",
            current_price=180.00,
            price_change=5.50,
            price_change_percent=3.2,
            volume_24h=18,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/gloves_crimson.png",
            item_url="https://steamdt.com/item/gloves_crimson"
        ),
        RankingItem(
            rank=20,
            item_name="Desert Eagle | Blaze (Factory New)",
            market_hash_name="Desert Eagle | Blaze (Factory New)",
            current_price=95.80,
            price_change=2.10,
            price_change_percent=2.2,
            volume_24h=42,
            icon_url="https://steamcommunity-a.akamaihd.net/economy/image/deagle_blaze.png",
            item_url="https://steamdt.com/item/deagle_blaze"
        )
    ]
    
    return demo_items


def demonstrate_item_evaluation():
    """演示饰品评估功能"""
    print("🔍 CS2饰品发现和评估系统演示")
    print("=" * 50)
    
    # 创建发现服务
    discovery_service = ItemDiscoveryService()
    
    # 创建演示数据
    demo_items = create_demo_ranking_items()
    
    print(f"📊 演示数据：{len(demo_items)} 个排行榜饰品")
    print()
    
    # 评估每个饰品
    evaluations = []
    
    for item in demo_items:
        print(f"🎯 评估饰品: {item.item_name}")
        print(f"   💰 当前价格: ¥{item.current_price:.2f}")
        print(f"   📈 价格变化: {item.price_change:+.2f} ({item.price_change_percent:+.1f}%)")
        print(f"   📊 24h交易量: {item.volume_24h}")
        
        # 基础筛选
        passes_filter = discovery_service._passes_basic_filter(item)
        print(f"   ✅ 基础筛选: {'通过' if passes_filter else '未通过'}")
        
        if not passes_filter:
            print("   ❌ 未通过基础筛选，跳过详细评估")
            print()
            continue
        
        # 详细评估
        value_tier = discovery_service._determine_value_tier(item.current_price)
        investment_score = discovery_service._calculate_investment_score(item)
        liquidity_score = discovery_service._calculate_liquidity_score(item)
        volatility_score = discovery_service._calculate_volatility_score(item)
        trend_score = discovery_service._calculate_trend_score(item)
        final_score = discovery_service._calculate_final_score(
            investment_score, liquidity_score, volatility_score, trend_score
        )
        
        print(f"   🏆 价值等级: {value_tier.value}")
        print(f"   📈 投资评分: {investment_score:.1f}")
        print(f"   💧 流动性评分: {liquidity_score:.1f}")
        print(f"   📊 波动性评分: {volatility_score:.1f}")
        print(f"   📉 趋势评分: {trend_score:.1f}")
        print(f"   🎯 最终评分: {final_score:.1f}")
        
        # 生成评估原因
        reasons = discovery_service._generate_evaluation_reasons(
            item, investment_score, liquidity_score, volatility_score, trend_score
        )
        print(f"   💡 评估原因:")
        for reason in reasons:
            print(f"      • {reason}")
        
        # 投资建议
        if final_score >= 80:
            recommendation = "🌟 强烈推荐"
        elif final_score >= 70:
            recommendation = "👍 推荐"
        elif final_score >= 60:
            recommendation = "🤔 谨慎考虑"
        else:
            recommendation = "❌ 不推荐"
        
        print(f"   🎯 投资建议: {recommendation}")
        print()
        
        # 保存评估结果
        if final_score >= 30:  # 最低评分要求
            from services.item_discovery import ItemEvaluation
            evaluation = ItemEvaluation(
                item_name=item.item_name,
                market_hash_name=item.market_hash_name,
                current_price=item.current_price,
                value_tier=value_tier,
                investment_score=investment_score,
                liquidity_score=liquidity_score,
                volatility_score=volatility_score,
                trend_score=trend_score,
                final_score=final_score,
                reasons=reasons,
                discovered_from="demo",
                discovered_at=datetime.now()
            )
            evaluations.append(evaluation)
    
    # 总结
    print("📋 评估总结")
    print("=" * 30)
    print(f"总饰品数: {len(demo_items)}")
    print(f"通过评估: {len(evaluations)}")
    print(f"通过率: {len(evaluations)/len(demo_items)*100:.1f}%")
    print()
    
    # 按价值等级分组
    tier_groups = {}
    for evaluation in evaluations:
        tier = evaluation.value_tier
        if tier not in tier_groups:
            tier_groups[tier] = []
        tier_groups[tier].append(evaluation)
    
    print("🏆 价值等级分布:")
    for tier in ItemValueTier:
        count = len(tier_groups.get(tier, []))
        if count > 0:
            print(f"   {tier.value}: {count} 个")
    print()
    
    # 排序并显示前5名
    evaluations.sort(key=lambda x: x.final_score, reverse=True)
    print("🥇 评分前5名:")
    for i, evaluation in enumerate(evaluations[:5], 1):
        print(f"   {i}. {evaluation.item_name}")
        print(f"      评分: {evaluation.final_score:.1f} | 价格: ¥{evaluation.current_price:.2f} | 等级: {evaluation.value_tier.value}")
    
    print()
    print("✅ 演示完成！")
    
    return evaluations


def demonstrate_diversity_balancing():
    """演示多样性平衡功能"""
    print("\n🎯 多样性平衡演示")
    print("=" * 30)
    
    discovery_service = ItemDiscoveryService()
    
    # 创建不平衡的评估结果（大部分是高价值饰品）
    from services.item_discovery import ItemEvaluation
    
    evaluations = []
    
    # 添加很多高价值饰品
    for i in range(20):
        evaluation = ItemEvaluation(
            item_name=f"Premium Item {i+1}",
            market_hash_name=f"Premium Item {i+1}",
            current_price=800.0 + i * 10,
            value_tier=ItemValueTier.PREMIUM,
            investment_score=85.0 + i,
            liquidity_score=80.0,
            volatility_score=75.0,
            trend_score=70.0,
            final_score=80.0 + i,
            reasons=["高价值饰品"],
            discovered_from="demo",
            discovered_at=datetime.now()
        )
        evaluations.append(evaluation)
    
    # 添加少量其他等级饰品
    for tier, count in [(ItemValueTier.HIGH, 5), (ItemValueTier.MEDIUM, 8), (ItemValueTier.LOW, 3)]:
        for i in range(count):
            evaluation = ItemEvaluation(
                item_name=f"{tier.value.title()} Item {i+1}",
                market_hash_name=f"{tier.value.title()} Item {i+1}",
                current_price=50.0 if tier == ItemValueTier.MEDIUM else 200.0,
                value_tier=tier,
                investment_score=70.0,
                liquidity_score=75.0,
                volatility_score=65.0,
                trend_score=60.0,
                final_score=70.0,
                reasons=[f"{tier.value}价值饰品"],
                discovered_from="demo",
                discovered_at=datetime.now()
            )
            evaluations.append(evaluation)
    
    print(f"原始评估结果: {len(evaluations)} 个饰品")
    
    # 统计原始分布
    original_distribution = {}
    for tier in ItemValueTier:
        count = len([e for e in evaluations if e.value_tier == tier])
        original_distribution[tier.value] = count
    
    print("原始分布:")
    for tier, count in original_distribution.items():
        print(f"   {tier}: {count} 个")
    
    # 应用多样性平衡
    target_count = 20
    balanced_items = discovery_service._ensure_diversity(evaluations, target_count)
    
    print(f"\n平衡后结果: {len(balanced_items)} 个饰品")
    
    # 统计平衡后分布
    balanced_distribution = {}
    for tier in ItemValueTier:
        count = len([e for e in balanced_items if e.value_tier == tier])
        balanced_distribution[tier.value] = count
    
    print("平衡后分布:")
    for tier, count in balanced_distribution.items():
        print(f"   {tier}: {count} 个")
    
    print("\n✅ 多样性平衡演示完成！")


if __name__ == "__main__":
    print("🚀 启动CS2饰品发现系统演示")
    print()
    
    try:
        # 演示饰品评估
        evaluations = demonstrate_item_evaluation()
        
        # 演示多样性平衡
        demonstrate_diversity_balancing()
        
        print("\n🎉 所有演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        sys.exit(1)
