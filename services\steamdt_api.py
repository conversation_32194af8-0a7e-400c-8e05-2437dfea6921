"""
SteamDT API专用服务类
基于steamdt.com官方API文档实现的CS2饰品数据获取服务
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone

from core.api_manager import APIManager, APIResponse, APICallResult
from core.exceptions import APIResponseError, ErrorContext


@dataclass
class PlatformPrice:
    """平台价格数据"""
    platform: str
    platform_item_id: str
    sell_price: float
    sell_count: int
    bidding_price: float
    bidding_count: int
    update_time: int
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PlatformPrice':
        """从API响应数据创建实例"""
        return cls(
            platform=data.get('platform', ''),
            platform_item_id=data.get('platformItemId', ''),
            sell_price=float(data.get('sellPrice', 0.0)),
            sell_count=int(data.get('sellCount', 0)),
            bidding_price=float(data.get('biddingPrice', 0.0)),
            bidding_count=int(data.get('biddingCount', 0)),
            update_time=int(data.get('updateTime', 0))
        )


@dataclass
class ItemPriceData:
    """饰品价格数据"""
    market_hash_name: str
    platform_prices: List[PlatformPrice]
    query_time: datetime
    
    def get_steam_price(self) -> Optional[PlatformPrice]:
        """获取Steam平台价格"""
        for price in self.platform_prices:
            if 'steam' in price.platform.lower():
                return price
        return None
    
    def get_best_sell_price(self) -> Optional[PlatformPrice]:
        """获取最低售价"""
        if not self.platform_prices:
            return None
        return min(self.platform_prices, key=lambda p: p.sell_price if p.sell_price > 0 else float('inf'))
    
    def get_best_buy_price(self) -> Optional[PlatformPrice]:
        """获取最高求购价"""
        if not self.platform_prices:
            return None
        return max(self.platform_prices, key=lambda p: p.bidding_price)


class SteamdtAPIManager:
    """SteamDT API管理器"""
    
    def __init__(self, api_manager: APIManager):
        """
        初始化SteamDT API管理器
        
        Args:
            api_manager: 基础API管理器实例
        """
        self.api_manager = api_manager
        self.logger = logging.getLogger(__name__)
        
        # API端点配置
        self.endpoints = {
            'price_single': '/open/cs2/v1/price/single',
            'price_batch': '/open/cs2/v1/price/batch',
            'price_avg': '/open/cs2/v1/price/avg',
            'item_base': '/open/cs2/v1/base',
            'wear_inspect': '/open/cs2/v1/wear',
            'wear_asmd': '/open/cs2/v2/wear'
        }
        
        # API限制配置（基于官方文档）
        self.rate_limits = {
            'price_single': 60,  # 每分钟60次
            'price_batch': 1,    # 每分钟1次
            'price_avg': 60,     # 每分钟60次（推测）
            'item_base': 1,      # 每日1次
            'wear_inspect': 600, # 每小时36000次 = 每分钟600次
            'wear_asmd': 600     # 每小时36000次 = 每分钟600次
        }
    
    async def get_item_price(self, market_hash_name: str) -> Optional[ItemPriceData]:
        """
        获取单个饰品的价格信息
        
        Args:
            market_hash_name: 饰品的市场哈希名称
            
        Returns:
            ItemPriceData: 饰品价格数据，失败时返回None
        """
        try:
            response = await self.api_manager.call_api(
                endpoint=self.endpoints['price_single'],
                method='GET',
                params={'marketHashName': market_hash_name}
            )
            
            if response.status != APICallResult.SUCCESS:
                self.logger.warning(
                    "Failed to get price for %s: %s", 
                    market_hash_name, response.error_message
                )
                return None
            
            return self._parse_price_response(market_hash_name, response.data)
            
        except Exception as e:
            self.logger.error(
                "Error getting price for %s: %s", 
                market_hash_name, str(e)
            )
            return None
    
    async def get_batch_prices(self, market_hash_names: List[str]) -> Dict[str, ItemPriceData]:
        """
        批量获取饰品价格信息
        
        Args:
            market_hash_names: 饰品市场哈希名称列表
            
        Returns:
            Dict[str, ItemPriceData]: 饰品名称到价格数据的映射
        """
        try:
            response = await self.api_manager.call_api(
                endpoint=self.endpoints['price_batch'],
                method='POST',
                data={'marketHashNames': market_hash_names}
            )
            
            if response.status != APICallResult.SUCCESS:
                self.logger.warning(
                    "Failed to get batch prices: %s", 
                    response.error_message
                )
                return {}
            
            return self._parse_batch_price_response(response.data)
            
        except Exception as e:
            self.logger.error(
                "Error getting batch prices: %s", str(e)
            )
            return {}
    
    async def get_average_prices(self, market_hash_name: str) -> Optional[Dict[str, Any]]:
        """
        获取饰品近7天各平台均价
        
        Args:
            market_hash_name: 饰品的市场哈希名称
            
        Returns:
            Dict[str, Any]: 平台均价数据，失败时返回None
        """
        try:
            response = await self.api_manager.call_api(
                endpoint=self.endpoints['price_avg'],
                method='GET',
                params={'marketHashName': market_hash_name}
            )
            
            if response.status != APICallResult.SUCCESS:
                self.logger.warning(
                    "Failed to get average prices for %s: %s", 
                    market_hash_name, response.error_message
                )
                return None
            
            return response.data
            
        except Exception as e:
            self.logger.error(
                "Error getting average prices for %s: %s", 
                market_hash_name, str(e)
            )
            return None

    def _parse_price_response(self, market_hash_name: str, data: Dict[str, Any]) -> Optional[ItemPriceData]:
        """
        解析单个饰品价格响应数据

        Args:
            market_hash_name: 饰品市场哈希名称
            data: API响应数据

        Returns:
            ItemPriceData: 解析后的价格数据
        """
        try:
            if not data or 'data' not in data:
                return None

            platform_prices = []
            for price_data in data['data']:
                platform_price = PlatformPrice.from_dict(price_data)
                platform_prices.append(platform_price)

            return ItemPriceData(
                market_hash_name=market_hash_name,
                platform_prices=platform_prices,
                query_time=datetime.now(timezone.utc)
            )

        except Exception as e:
            self.logger.error(
                "Error parsing price response for %s: %s",
                market_hash_name, str(e)
            )
            return None

    def _parse_batch_price_response(self, data: Dict[str, Any]) -> Dict[str, ItemPriceData]:
        """
        解析批量价格响应数据

        Args:
            data: API响应数据

        Returns:
            Dict[str, ItemPriceData]: 饰品名称到价格数据的映射
        """
        result = {}
        try:
            if not data or 'data' not in data:
                return result

            # 批量API的响应格式可能不同，需要根据实际API调整
            for item_data in data['data']:
                market_hash_name = item_data.get('marketHashName', '')
                if market_hash_name:
                    price_data = self._parse_price_response(market_hash_name, {'data': item_data.get('prices', [])})
                    if price_data:
                        result[market_hash_name] = price_data

            return result

        except Exception as e:
            self.logger.error("Error parsing batch price response: %s", str(e))
            return result

    async def get_item_wear_data(self, inspect_link: str) -> Optional[Dict[str, Any]]:
        """
        通过检视链接获取饰品磨损度数据

        Args:
            inspect_link: 饰品检视链接

        Returns:
            Dict[str, Any]: 磨损度数据，失败时返回None
        """
        try:
            response = await self.api_manager.call_api(
                endpoint=self.endpoints['wear_inspect'],
                method='POST',
                data={'inspectLink': inspect_link}
            )

            if response.status != APICallResult.SUCCESS:
                self.logger.warning(
                    "Failed to get wear data for inspect link: %s",
                    response.error_message
                )
                return None

            return response.data

        except Exception as e:
            self.logger.error(
                "Error getting wear data: %s", str(e)
            )
            return None

    async def get_base_item_info(self) -> Optional[Dict[str, Any]]:
        """
        获取Steam饰品基础信息（每日限制1次）

        Returns:
            Dict[str, Any]: 饰品基础信息，失败时返回None
        """
        try:
            response = await self.api_manager.call_api(
                endpoint=self.endpoints['item_base'],
                method='GET'
            )

            if response.status != APICallResult.SUCCESS:
                self.logger.warning(
                    "Failed to get base item info: %s",
                    response.error_message
                )
                return None

            return response.data

        except Exception as e:
            self.logger.error(
                "Error getting base item info: %s", str(e)
            )
            return None

    def get_api_usage_stats(self) -> Dict[str, Any]:
        """
        获取API使用统计信息

        Returns:
            Dict[str, Any]: API使用统计
        """
        return {
            'rate_limits': self.rate_limits,
            'endpoints': self.endpoints,
            'base_api_stats': self.api_manager.get_metrics()
        }


# 全局SteamDT API管理器实例
_steamdt_api_manager: Optional[SteamdtAPIManager] = None


async def get_steamdt_api_manager() -> SteamdtAPIManager:
    """获取全局SteamDT API管理器实例"""
    global _steamdt_api_manager

    if _steamdt_api_manager is None:
        from core.config import get_config_manager

        # 获取配置
        config_manager = get_config_manager()
        api_config = {
            'base_url': config_manager.get('api.steamdt.base_url', 'https://open.steamdt.com'),
            'api_key': config_manager.get('api.steamdt.api_key'),
            'rate_limit': config_manager.get('api.steamdt.rate_limit', {})
        }

        # 创建基础API管理器
        base_api_manager = APIManager(api_config)
        await base_api_manager.start_session()

        # 创建SteamDT API管理器
        _steamdt_api_manager = SteamdtAPIManager(base_api_manager)

    return _steamdt_api_manager


async def close_steamdt_api_manager():
    """关闭全局SteamDT API管理器"""
    global _steamdt_api_manager

    if _steamdt_api_manager:
        await _steamdt_api_manager.api_manager.close_session()
        _steamdt_api_manager = None
