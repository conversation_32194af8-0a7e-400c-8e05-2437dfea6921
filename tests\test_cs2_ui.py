"""
CS2用户界面组件测试
验证CS2饰品专用UI组件的功能
"""

import pytest
import pandas as pd
import sys
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.components.cs2_components import (
    CS2ItemCard, CS2PriceChart, CS2ArbitragePanel, CS2FilterPanel, 
    CS2MetricsOverview, create_cs2_portfolio_table, create_cs2_watchlist_table
)
from app.styles.cs2_theme import (
    get_rarity_color, get_weapon_type_color, get_quality_color,
    create_cs2_metric_html, create_cs2_alert_html, create_cs2_progress_html
)


class TestCS2Theme:
    """CS2主题测试"""
    
    def test_get_rarity_color(self):
        """测试稀有度颜色获取"""
        assert get_rarity_color('消费级') == '#95a5a6'
        assert get_rarity_color('工业级') == '#3498db'
        assert get_rarity_color('军规级') == '#2ecc71'
        assert get_rarity_color('受限') == '#9b59b6'
        assert get_rarity_color('保密') == '#e74c3c'
        assert get_rarity_color('隐秘') == '#f39c12'
        assert get_rarity_color('违禁品') == '#e67e22'
        assert get_rarity_color('未知') == '#95a5a6'  # 默认颜色
    
    def test_get_weapon_type_color(self):
        """测试武器类型颜色获取"""
        assert get_weapon_type_color('步枪') == '#34495e'
        assert get_weapon_type_color('狙击枪') == '#16a085'
        assert get_weapon_type_color('手枪') == '#8e44ad'
        assert get_weapon_type_color('刀具') == '#c0392b'
        assert get_weapon_type_color('手套') == '#d35400'
        assert get_weapon_type_color('未知') == '#7f8c8d'  # 默认颜色
    
    def test_get_quality_color(self):
        """测试品质颜色获取"""
        assert get_quality_color('普通') == '#7f8c8d'
        assert get_quality_color('StatTrak™') == '#e74c3c'
        assert get_quality_color('纪念品') == '#f39c12'
        assert get_quality_color('未知') == '#7f8c8d'  # 默认颜色
    
    def test_create_cs2_metric_html(self):
        """测试CS2指标HTML创建"""
        html = create_cs2_metric_html("测试指标", "100", "+5%", "positive")
        
        assert "cs2-metric-card" in html
        assert "cs2-metric-value" in html
        assert "cs2-metric-label" in html
        assert "cs2-metric-change positive" in html
        assert "测试指标" in html
        assert "100" in html
        assert "+5%" in html
    
    def test_create_cs2_alert_html(self):
        """测试CS2警告框HTML创建"""
        html = create_cs2_alert_html("测试消息", "warning")
        
        assert "cs2-alert warning" in html
        assert "测试消息" in html
    
    def test_create_cs2_progress_html(self):
        """测试CS2进度条HTML创建"""
        html = create_cs2_progress_html(75, 100, "测试进度")
        
        assert "cs2-progress" in html
        assert "cs2-progress-bar" in html
        assert "width: 75.0%" in html
        assert "测试进度" in html
        assert "75/100" in html


class TestCS2ItemCard:
    """CS2饰品卡片测试"""
    
    @pytest.fixture
    def sample_item_data(self):
        """创建示例饰品数据"""
        return {
            'item_name': 'AK-47 | Redline (Field-Tested)',
            'current_price': 45.50,
            'change_24h': 5.2,
            'volume_24h': 150,
            'rarity': '保密',
            'weapon_type': '步枪',
            'quality': '普通'
        }
    
    def test_item_card_creation(self, sample_item_data):
        """测试饰品卡片创建"""
        card = CS2ItemCard(sample_item_data, "test_card")
        
        assert card.item_data == sample_item_data
        assert card.key == "test_card"
    
    def test_item_card_data_access(self, sample_item_data):
        """测试饰品卡片数据访问"""
        card = CS2ItemCard(sample_item_data, "test_card")
        
        # 测试数据访问
        assert card.item_data.get('item_name') == 'AK-47 | Redline (Field-Tested)'
        assert card.item_data.get('current_price') == 45.50
        assert card.item_data.get('change_24h') == 5.2


class TestCS2PriceChart:
    """CS2价格图表测试"""
    
    @pytest.fixture
    def sample_price_data(self):
        """创建示例价格数据"""
        return [
            {'date': '2024-01-01', 'price': 45.0, 'volume': 100},
            {'date': '2024-01-02', 'price': 47.5, 'volume': 120},
            {'date': '2024-01-03', 'price': 46.2, 'volume': 90},
            {'date': '2024-01-04', 'price': 48.8, 'volume': 150},
            {'date': '2024-01-05', 'price': 50.1, 'volume': 180}
        ]
    
    def test_price_chart_creation(self, sample_price_data):
        """测试价格图表创建"""
        chart = CS2PriceChart("AK-47 | Redline", sample_price_data, "test_chart")
        
        assert chart.item_name == "AK-47 | Redline"
        assert chart.price_data == sample_price_data
        assert chart.key == "test_chart"
    
    def test_empty_price_data(self):
        """测试空价格数据处理"""
        chart = CS2PriceChart("Test Item", [], "empty_chart")
        
        assert chart.price_data == []
        assert chart.item_name == "Test Item"


class TestCS2ArbitragePanel:
    """CS2套利面板测试"""
    
    @pytest.fixture
    def sample_arbitrage_data(self):
        """创建示例套利数据"""
        return [
            {
                'item_name': 'AK-47 | Redline (Field-Tested)',
                'buy_platform': 'BUFF',
                'sell_platform': 'Steam',
                'buy_price': 95.0,
                'sell_price': 120.0,
                'gross_profit': 25.0,
                'net_profit': 12.5,
                'profit_margin': 13.2,
                'risk_level': 'medium',
                'confidence_score': 78.5
            },
            {
                'item_name': 'AWP | Asiimov (Field-Tested)',
                'buy_platform': 'C5Game',
                'sell_platform': 'Steam',
                'buy_price': 78.0,
                'sell_price': 85.0,
                'gross_profit': 7.0,
                'net_profit': 3.2,
                'profit_margin': 4.1,
                'risk_level': 'low',
                'confidence_score': 65.2
            }
        ]
    
    def test_arbitrage_panel_creation(self, sample_arbitrage_data):
        """测试套利面板创建"""
        panel = CS2ArbitragePanel(sample_arbitrage_data, "test_panel")
        
        assert panel.arbitrage_data == sample_arbitrage_data
        assert panel.key == "test_panel"
    
    def test_empty_arbitrage_data(self):
        """测试空套利数据处理"""
        panel = CS2ArbitragePanel([], "empty_panel")
        
        assert panel.arbitrage_data == []


class TestCS2FilterPanel:
    """CS2筛选面板测试"""
    
    def test_filter_panel_creation(self):
        """测试筛选面板创建"""
        panel = CS2FilterPanel("test_filter", "测试筛选")
        
        assert panel.key == "test_filter"
        assert panel.title == "测试筛选"
        assert panel.panel is not None
    
    def test_filter_panel_default_title(self):
        """测试筛选面板默认标题"""
        panel = CS2FilterPanel("test_filter")
        
        assert panel.title == "CS2饰品筛选"


class TestCS2MetricsOverview:
    """CS2指标概览测试"""
    
    @pytest.fixture
    def sample_metrics_data(self):
        """创建示例指标数据"""
        return {
            'total_value': 125000.50,
            'total_change_percent': 8.5,
            'total_profit': 15000.25,
            'daily_profit': 850.75,
            'core_pool_count': 30,
            'core_pool_new': 2,
            'arbitrage_count': 12,
            'arbitrage_new': 3,
            'main_pool_count': 970,
            'main_pool_new': 15
        }
    
    def test_metrics_overview_creation(self, sample_metrics_data):
        """测试指标概览创建"""
        overview = CS2MetricsOverview(sample_metrics_data, "test_metrics")
        
        assert overview.metrics_data == sample_metrics_data
        assert overview.key == "test_metrics"
    
    def test_metrics_data_access(self, sample_metrics_data):
        """测试指标数据访问"""
        overview = CS2MetricsOverview(sample_metrics_data, "test_metrics")
        
        assert overview.metrics_data.get('total_value') == 125000.50
        assert overview.metrics_data.get('core_pool_count') == 30
        assert overview.metrics_data.get('arbitrage_count') == 12


class TestCS2TableCreation:
    """CS2表格创建测试"""
    
    @pytest.fixture
    def sample_portfolio_data(self):
        """创建示例投资组合数据"""
        return pd.DataFrame([
            {
                'item_name': 'AK-47 | Redline (Field-Tested)',
                'weapon_type': '步枪',
                'rarity': '保密',
                'quality': '普通',
                'quantity': 2,
                'avg_cost': 45.0,
                'current_price': 50.0,
                'total_value': 100.0,
                'profit_loss': 10.0,
                'profit_percentage': 11.11,
                'investment_score': 85.5
            },
            {
                'item_name': 'AWP | Asiimov (Field-Tested)',
                'weapon_type': '狙击枪',
                'rarity': '保密',
                'quality': '普通',
                'quantity': 1,
                'avg_cost': 80.0,
                'current_price': 85.0,
                'total_value': 85.0,
                'profit_loss': 5.0,
                'profit_percentage': 6.25,
                'investment_score': 78.2
            }
        ])
    
    @pytest.fixture
    def sample_watchlist_data(self):
        """创建示例关注列表数据"""
        return pd.DataFrame([
            {
                'item_name': 'M4A4 | Asiimov (Field-Tested)',
                'weapon_type': '步枪',
                'rarity': '保密',
                'current_price': 65.0,
                'change_24h': 3.2,
                'volume_24h': 120,
                'investment_score': 82.1,
                'priority_score': 8.5
            },
            {
                'item_name': 'Glock-18 | Fade (Factory New)',
                'weapon_type': '手枪',
                'rarity': '受限',
                'current_price': 180.0,
                'change_24h': -1.5,
                'volume_24h': 45,
                'investment_score': 75.8,
                'priority_score': 7.2
            }
        ])
    
    def test_create_cs2_portfolio_table(self, sample_portfolio_data):
        """测试CS2投资组合表格创建"""
        table = create_cs2_portfolio_table(sample_portfolio_data, "test_portfolio")
        
        assert table is not None
        assert table.key == "test_portfolio"
        assert len(table.data) == 2
    
    def test_create_cs2_watchlist_table(self, sample_watchlist_data):
        """测试CS2关注列表表格创建"""
        table = create_cs2_watchlist_table(sample_watchlist_data, "test_watchlist")
        
        assert table is not None
        assert table.key == "test_watchlist"
        assert len(table.data) == 2
    
    def test_empty_dataframe_handling(self):
        """测试空数据框处理"""
        empty_df = pd.DataFrame()
        
        portfolio_table = create_cs2_portfolio_table(empty_df, "empty_portfolio")
        watchlist_table = create_cs2_watchlist_table(empty_df, "empty_watchlist")
        
        assert portfolio_table is not None
        assert watchlist_table is not None
        assert len(portfolio_table.data) == 0
        assert len(watchlist_table.data) == 0


if __name__ == "__main__":
    # 运行基本测试
    print("运行CS2用户界面基本测试...")
    
    # 测试主题颜色
    print("测试主题颜色...")
    assert get_rarity_color('保密') == '#e74c3c'
    assert get_weapon_type_color('步枪') == '#34495e'
    assert get_quality_color('StatTrak™') == '#e74c3c'
    print("✓ 主题颜色测试通过")
    
    # 测试HTML创建
    print("测试HTML创建...")
    metric_html = create_cs2_metric_html("测试", "100", "+5%", "positive")
    assert "cs2-metric-card" in metric_html
    
    alert_html = create_cs2_alert_html("测试消息", "info")
    assert "cs2-alert info" in alert_html
    
    progress_html = create_cs2_progress_html(75, 100, "进度")
    assert "width: 75.0%" in progress_html
    print("✓ HTML创建测试通过")
    
    # 测试组件创建
    print("测试组件创建...")
    item_data = {
        'item_name': 'AK-47 | Redline',
        'current_price': 45.50,
        'change_24h': 5.2,
        'volume_24h': 150,
        'rarity': '保密',
        'weapon_type': '步枪',
        'quality': '普通'
    }
    
    card = CS2ItemCard(item_data, "test")
    assert card.item_data == item_data
    
    chart = CS2PriceChart("Test Item", [], "test")
    assert chart.item_name == "Test Item"
    
    panel = CS2ArbitragePanel([], "test")
    assert panel.arbitrage_data == []
    print("✓ 组件创建测试通过")
    
    print("所有CS2用户界面基本测试通过！")
    
    # 运行pytest
    pytest.main(["-xvs", __file__])
