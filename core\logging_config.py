"""
Ares系统结构化日志配置
提供统一的日志格式、级别管理和输出配置
"""

import os
import sys
import logging
import logging.handlers
import json
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        # 基础日志信息
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_data'):
            log_data.update(record.extra_data)
        
        # 添加其他自定义字段
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                log_data[key] = value
        
        return json.dumps(log_data, ensure_ascii=False, default=str)


class StructuredFormatter(logging.Formatter):
    """结构化文本格式化器"""
    
    def __init__(self):
        super().__init__()
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为结构化文本"""
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # 基础格式
        base_format = f"[{timestamp}] {record.levelname:8} {record.name:20} {record.getMessage()}"
        
        # 添加位置信息
        location = f" ({record.module}:{record.funcName}:{record.lineno})"
        
        # 添加额外信息
        extra_info = ""
        if hasattr(record, 'extra_data') and record.extra_data:
            extra_parts = []
            for key, value in record.extra_data.items():
                extra_parts.append(f"{key}={value}")
            if extra_parts:
                extra_info = f" | {' '.join(extra_parts)}"
        
        # 添加异常信息
        exception_info = ""
        if record.exc_info:
            exception_info = f"\n{self.formatException(record.exc_info)}"
        
        return base_format + location + extra_info + exception_info


class LoggingConfig:
    """日志配置管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化日志配置
        
        Args:
            config: 日志配置字典
        """
        self.config = config
        self.loggers: Dict[str, logging.Logger] = {}
        
        # 设置根日志级别
        root_level = config.get('level', 'INFO').upper()
        logging.getLogger().setLevel(getattr(logging, root_level))
        
        # 配置日志格式
        self.format_type = config.get('format', 'structured').lower()
        
        # 配置输出
        self._setup_handlers()
        
        # 配置特定模块的日志级别
        self._setup_module_loggers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        root_logger = logging.getLogger()
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = None
        file_path = self.config.get('file_path')
        if file_path:
            # 确保日志目录存在
            log_dir = Path(file_path).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建轮转文件处理器
            max_size = self._parse_size(self.config.get('max_size', '100MB'))
            backup_count = self.config.get('backup_count', 5)
            
            file_handler = logging.handlers.RotatingFileHandler(
                filename=file_path,
                maxBytes=max_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
        
        # 设置格式化器
        if self.format_type == 'json':
            formatter = JSONFormatter()
        else:
            formatter = StructuredFormatter()
        
        console_handler.setFormatter(formatter)
        if file_handler:
            file_handler.setFormatter(formatter)
        
        # 添加处理器
        root_logger.addHandler(console_handler)
        if file_handler:
            root_logger.addHandler(file_handler)
    
    def _setup_module_loggers(self):
        """设置模块特定的日志级别"""
        module_levels = self.config.get('loggers', {})
        
        for module_name, level in module_levels.items():
            logger = logging.getLogger(module_name)
            logger.setLevel(getattr(logging, level.upper()))
            self.loggers[module_name] = logger
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串"""
        size_str = size_str.upper()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]
    
    def set_level(self, level: str, logger_name: Optional[str] = None):
        """设置日志级别"""
        log_level = getattr(logging, level.upper())
        
        if logger_name:
            logger = self.get_logger(logger_name)
            logger.setLevel(log_level)
        else:
            logging.getLogger().setLevel(log_level)
    
    def add_handler(self, handler: logging.Handler, logger_name: Optional[str] = None):
        """添加日志处理器"""
        if logger_name:
            logger = self.get_logger(logger_name)
            logger.addHandler(handler)
        else:
            logging.getLogger().addHandler(handler)
    
    def remove_handler(self, handler: logging.Handler, logger_name: Optional[str] = None):
        """移除日志处理器"""
        if logger_name:
            logger = self.get_logger(logger_name)
            logger.removeHandler(handler)
        else:
            logging.getLogger().removeHandler(handler)


class ContextLogger:
    """上下文日志器 - 自动添加上下文信息"""
    
    def __init__(self, logger: logging.Logger, context: Dict[str, Any] = None):
        """
        初始化上下文日志器
        
        Args:
            logger: 基础日志器
            context: 上下文信息
        """
        self.logger = logger
        self.context = context or {}
    
    def _log_with_context(self, level: int, message: str, *args, **kwargs):
        """带上下文的日志记录"""
        extra_data = kwargs.pop('extra_data', {})
        extra_data.update(self.context)
        
        # 创建日志记录
        record = self.logger.makeRecord(
            self.logger.name, level, "", 0, message, args, None
        )
        record.extra_data = extra_data
        
        # 处理日志记录
        self.logger.handle(record)
    
    def debug(self, message: str, *args, **kwargs):
        """调试日志"""
        self._log_with_context(logging.DEBUG, message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """信息日志"""
        self._log_with_context(logging.INFO, message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """警告日志"""
        self._log_with_context(logging.WARNING, message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """错误日志"""
        self._log_with_context(logging.ERROR, message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """严重错误日志"""
        self._log_with_context(logging.CRITICAL, message, *args, **kwargs)
    
    def add_context(self, **kwargs):
        """添加上下文信息"""
        self.context.update(kwargs)
    
    def remove_context(self, *keys):
        """移除上下文信息"""
        for key in keys:
            self.context.pop(key, None)
    
    def clear_context(self):
        """清除所有上下文信息"""
        self.context.clear()


# 全局日志配置实例
_logging_config: Optional[LoggingConfig] = None


def setup_logging(config: Dict[str, Any]):
    """设置全局日志配置"""
    global _logging_config
    _logging_config = LoggingConfig(config)


def get_logger(name: str, context: Dict[str, Any] = None) -> ContextLogger:
    """获取上下文日志器"""
    if _logging_config is None:
        # 使用默认配置
        setup_logging({
            'level': 'INFO',
            'format': 'structured',
            'file_path': 'logs/ares.log'
        })
    
    base_logger = _logging_config.get_logger(name)
    return ContextLogger(base_logger, context)


def get_raw_logger(name: str) -> logging.Logger:
    """获取原始日志器"""
    if _logging_config is None:
        setup_logging({
            'level': 'INFO',
            'format': 'structured',
            'file_path': 'logs/ares.log'
        })
    
    return _logging_config.get_logger(name)


# 便捷函数
def log_api_call(endpoint: str, method: str, status_code: int, duration: float):
    """记录API调用日志"""
    logger = get_logger('api')
    logger.info(
        f"API call completed: {method} {endpoint}",
        extra_data={
            'endpoint': endpoint,
            'method': method,
            'status_code': status_code,
            'duration': duration,
            'type': 'api_call'
        }
    )


def log_database_operation(operation: str, table: str, duration: float, rows_affected: int = 0):
    """记录数据库操作日志"""
    logger = get_logger('database')
    logger.info(
        f"Database operation completed: {operation} on {table}",
        extra_data={
            'operation': operation,
            'table': table,
            'duration': duration,
            'rows_affected': rows_affected,
            'type': 'db_operation'
        }
    )


def log_business_event(event_type: str, details: Dict[str, Any]):
    """记录业务事件日志"""
    logger = get_logger('business')
    logger.info(
        f"Business event: {event_type}",
        extra_data={
            'event_type': event_type,
            'details': details,
            'type': 'business_event'
        }
    )
