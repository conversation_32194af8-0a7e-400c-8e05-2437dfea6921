#!/usr/bin/env python3
"""
导入SteamDT基础数据到数据库
从data/basedata.json文件导入饰品基础信息到items表
"""

import sys
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import get_database_manager, Item
from core.config import get_config_manager
from sqlalchemy.exc import IntegrityError
import os

# 设置必要的环境变量以避免配置验证失败
import base64

# 生成有效的Fernet密钥 (32字节，base64编码)
dummy_key_bytes = b'dummy_key_for_import_only_32byte'  # 正好32字节
dummy_key = base64.urlsafe_b64encode(dummy_key_bytes).decode()

os.environ.setdefault('STEAMDT_API_KEY', 'dummy_key_for_import')
os.environ.setdefault('ENCRYPTION_KEY', dummy_key)
os.environ.setdefault('SECRET_KEY', 'dummy_secret_key_for_import')
# 强制使用SQLite数据库
os.environ['DATABASE_URL'] = 'sqlite:///data/ares.db'


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('import_basedata.log')
        ]
    )
    return logging.getLogger(__name__)


def parse_weapon_info(market_hash_name: str) -> tuple[str, str]:
    """
    从market_hash_name解析武器类型和皮肤名称
    
    Args:
        market_hash_name: 市场哈希名称
        
    Returns:
        tuple[str, str]: (武器类型, 皮肤名称)
    """
    try:
        # 解析格式: "AK-47 | Redline (Field-Tested)"
        if '|' in market_hash_name:
            weapon_part = market_hash_name.split('|')[0].strip()
            skin_part = market_hash_name.split('|')[1].strip()
            
            # 移除磨损等级部分
            if '(' in skin_part:
                skin_name = skin_part.split('(')[0].strip()
            else:
                skin_name = skin_part
            
            return weapon_part, skin_name
        else:
            # 如果没有|分隔符，可能是刀具或其他特殊饰品
            return market_hash_name, ""
            
    except Exception as e:
        logging.warning(f"解析武器信息失败 {market_hash_name}: {e}")
        return "", ""


def load_basedata(file_path: Path) -> Dict[str, Any]:
    """
    加载基础数据文件
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        Dict[str, Any]: 解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data.get('success', False):
            raise ValueError(f"数据文件格式错误: success={data.get('success')}")
        
        return data
    
    except Exception as e:
        raise Exception(f"加载数据文件失败: {e}")


def create_item_from_data(item_data: Dict[str, Any]) -> Item:
    """
    从API数据创建Item对象
    
    Args:
        item_data: 单个饰品的数据
        
    Returns:
        Item: 创建的Item对象
    """
    # 提取基础信息
    name = item_data.get('name', '')
    market_hash_name = item_data.get('marketHashName', '')
    
    if not name or not market_hash_name:
        raise ValueError(f"饰品数据不完整: name={name}, marketHashName={market_hash_name}")
    
    # 解析武器信息
    weapon_type, skin_name = parse_weapon_info(market_hash_name)
    
    # 处理平台数据
    platform_list = item_data.get('platformList', [])
    platform_data = {}
    for platform_info in platform_list:
        platform_name = platform_info.get('name', '').lower()
        item_id = platform_info.get('itemId', '')
        
        if platform_name and item_id:
            # 统一平台名称
            if platform_name == 'buff':
                platform_name = 'buff163'
            elif platform_name == 'c5':
                platform_name = 'c5game'
            elif platform_name == 'youpin':
                platform_name = 'youpin898'
            elif platform_name == 'haloskins':
                platform_name = 'haloskins'
            
            platform_data[platform_name] = item_id
    
    # 创建Item对象
    item = Item(
        market_hash_name=market_hash_name,
        name=name,
        weapon_type=weapon_type,
        skin_name=skin_name,
        platform_data=json.dumps(platform_data, ensure_ascii=False),
        data_source='steamdt',
        last_sync_time=datetime.now(),
        is_active=True,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    return item


def import_data_to_database(data: Dict[str, Any], logger: logging.Logger) -> Dict[str, int]:
    """
    导入数据到数据库
    
    Args:
        data: 基础数据
        logger: 日志记录器
        
    Returns:
        Dict[str, int]: 导入统计信息
    """
    db_manager = get_database_manager()
    
    stats = {
        'total': 0,
        'new': 0,
        'updated': 0,
        'skipped': 0,
        'errors': 0
    }
    
    data_list = data.get('data', [])
    total_items = len(data_list)
    
    logger.info(f"开始导入{total_items}个饰品数据")
    
    try:
        with db_manager.get_session() as session:
            for i, item_data in enumerate(data_list):
                try:
                    stats['total'] += 1
                    
                    # 创建Item对象
                    new_item = create_item_from_data(item_data)
                    
                    # 检查是否已存在
                    existing_item = session.get(Item, new_item.market_hash_name)
                    
                    if existing_item:
                        # 更新现有饰品
                        existing_item.name = new_item.name
                        existing_item.weapon_type = new_item.weapon_type
                        existing_item.skin_name = new_item.skin_name
                        existing_item.platform_data = new_item.platform_data
                        existing_item.data_source = new_item.data_source
                        existing_item.last_sync_time = new_item.last_sync_time
                        existing_item.updated_at = datetime.now()
                        existing_item.is_active = True
                        
                        stats['updated'] += 1
                        logger.debug(f"更新饰品: {new_item.market_hash_name}")
                    else:
                        # 添加新饰品
                        session.add(new_item)
                        stats['new'] += 1
                        logger.debug(f"新增饰品: {new_item.market_hash_name}")
                    
                    # 每1000条记录提交一次
                    if (i + 1) % 1000 == 0:
                        session.commit()
                        logger.info(f"已处理 {i + 1}/{total_items} 个饰品")
                
                except Exception as e:
                    stats['errors'] += 1
                    logger.error(f"处理饰品数据失败 (第{i+1}个): {e}")
                    logger.error(f"问题数据: {item_data}")
                    continue
            
            # 最终提交
            session.commit()
            logger.info("数据导入完成，事务已提交")
    
    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        raise
    
    return stats


def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        logger.info("开始导入SteamDT基础数据")
        
        # 检查数据文件
        data_file = project_root / 'data' / 'basedata.json'
        if not data_file.exists():
            raise FileNotFoundError(f"数据文件不存在: {data_file}")
        
        logger.info(f"数据文件: {data_file}")
        logger.info(f"文件大小: {data_file.stat().st_size / 1024 / 1024:.2f} MB")
        
        # 加载数据
        logger.info("加载数据文件...")
        data = load_basedata(data_file)
        logger.info(f"数据加载成功，共{len(data.get('data', []))}个饰品")
        
        # 初始化配置和数据库
        logger.info("初始化数据库连接...")
        config_manager = get_config_manager()
        
        # 导入数据
        logger.info("开始导入数据到数据库...")
        stats = import_data_to_database(data, logger)
        
        # 输出统计信息
        logger.info("=" * 50)
        logger.info("导入完成！统计信息:")
        logger.info(f"总计处理: {stats['total']} 个饰品")
        logger.info(f"新增饰品: {stats['new']} 个")
        logger.info(f"更新饰品: {stats['updated']} 个")
        logger.info(f"跳过饰品: {stats['skipped']} 个")
        logger.info(f"错误数量: {stats['errors']} 个")
        logger.info("=" * 50)
        
        if stats['errors'] > 0:
            logger.warning(f"导入过程中有{stats['errors']}个错误，请检查日志")
        
        return 0
    
    except Exception as e:
        logger.error(f"导入失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
