"""
CS2饰品投资系统性能测试
验证系统在各种负载条件下的性能表现
"""

import pytest
import asyncio
import time
import sys
import threading
import psutil
import gc
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.steamdt_api import SteamDTAPI
from services.price_comparator import PlatformPriceComparator
from services.arbitrage_detector import ArbitrageDetector
from services.cs2_macro_collector import CS2MacroDataCollector
from monitoring.system_monitor import SystemMonitor


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.memory_start = None
        self.memory_peak = None
        self.cpu_usage = []
        
    def start_monitoring(self):
        """开始性能监控"""
        self.start_time = time.time()
        process = psutil.Process()
        self.memory_start = process.memory_info().rss / 1024 / 1024  # MB
        self.memory_peak = self.memory_start
        self.cpu_usage = []
        
    def update_metrics(self):
        """更新性能指标"""
        process = psutil.Process()
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        self.memory_peak = max(self.memory_peak, current_memory)
        self.cpu_usage.append(process.cpu_percent())
        
    def stop_monitoring(self):
        """停止性能监控"""
        self.end_time = time.time()
        
    def get_results(self):
        """获取性能测试结果"""
        return {
            'execution_time': self.end_time - self.start_time if self.end_time else 0,
            'memory_start_mb': self.memory_start,
            'memory_peak_mb': self.memory_peak,
            'memory_increase_mb': self.memory_peak - self.memory_start,
            'avg_cpu_percent': statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
            'max_cpu_percent': max(self.cpu_usage) if self.cpu_usage else 0
        }


class TestAPIPerformance:
    """API性能测试"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置管理器"""
        with patch('core.config.get_config_manager') as mock:
            config_manager = Mock()
            config_manager.get.side_effect = lambda key, default=None: {
                'STEAMDT_API_KEY': 'test_api_key',
                'API_RATE_LIMIT': 60,
                'CACHE_TTL': 300
            }.get(key, default)
            mock.return_value = config_manager
            yield config_manager
    
    @pytest.fixture
    def steamdt_api(self, mock_config):
        """创建SteamDT API实例"""
        return SteamDTAPI()
    
    def test_single_item_performance(self, steamdt_api):
        """测试单个饰品查询性能"""
        mock_response = {
            'code': 0,
            'data': {
                'items': [
                    {
                        'market_hash_name': 'AK-47 | Redline (Field-Tested)',
                        'steam': {'sell_price': 120.0, 'sell_count': 45},
                        'buff': {'sell_price': 95.0, 'sell_count': 80}
                    }
                ]
            }
        }
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        with patch.object(steamdt_api, '_make_request', return_value=mock_response):
            start_time = time.time()
            result = steamdt_api.get_item_prices(['AK-47 | Redline (Field-Tested)'])
            end_time = time.time()
        
        metrics.stop_monitoring()
        
        # 性能断言
        response_time = end_time - start_time
        assert response_time < 1.0  # 单个查询应在1秒内完成
        assert len(result) == 1
        
        results = metrics.get_results()
        assert results['memory_increase_mb'] < 10  # 内存增长应小于10MB
    
    def test_batch_items_performance(self, steamdt_api):
        """测试批量饰品查询性能"""
        # 创建100个测试饰品
        test_items = [f'Test Item {i}' for i in range(100)]
        
        mock_response = {
            'code': 0,
            'data': {
                'items': [
                    {
                        'market_hash_name': name,
                        'steam': {'sell_price': 50.0 + i, 'sell_count': 10}
                    }
                    for i, name in enumerate(test_items)
                ]
            }
        }
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        with patch.object(steamdt_api, '_make_request', return_value=mock_response):
            start_time = time.time()
            result = steamdt_api.get_item_prices(test_items)
            end_time = time.time()
        
        metrics.stop_monitoring()
        
        # 性能断言
        response_time = end_time - start_time
        assert response_time < 5.0  # 100个查询应在5秒内完成
        assert len(result) == 100
        
        results = metrics.get_results()
        assert results['memory_increase_mb'] < 50  # 内存增长应小于50MB
        
        # 计算每个饰品的平均处理时间
        avg_time_per_item = response_time / 100
        assert avg_time_per_item < 0.05  # 每个饰品平均处理时间应小于50ms
    
    def test_concurrent_api_calls(self, steamdt_api):
        """测试并发API调用性能"""
        mock_response = {
            'code': 0,
            'data': {
                'items': [
                    {
                        'market_hash_name': 'Concurrent Test Item',
                        'steam': {'sell_price': 100.0, 'sell_count': 20}
                    }
                ]
            }
        }
        
        def make_api_call(item_name):
            with patch.object(steamdt_api, '_make_request', return_value=mock_response):
                return steamdt_api.get_item_prices([item_name])
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 并发执行10个API调用
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(make_api_call, f'Item {i}')
                for i in range(10)
            ]
            
            results = []
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        end_time = time.time()
        metrics.stop_monitoring()
        
        # 性能断言
        total_time = end_time - start_time
        assert total_time < 3.0  # 并发执行应在3秒内完成
        assert len(results) == 10
        
        perf_results = metrics.get_results()
        assert perf_results['memory_increase_mb'] < 30  # 内存增长应小于30MB
    
    def test_cache_performance(self, steamdt_api):
        """测试缓存性能"""
        mock_response = {
            'code': 0,
            'data': {
                'items': [
                    {
                        'market_hash_name': 'Cached Item',
                        'steam': {'sell_price': 75.0, 'sell_count': 15}
                    }
                ]
            }
        }
        
        with patch.object(steamdt_api, '_make_request', return_value=mock_response) as mock_request:
            # 第一次调用（缓存未命中）
            start_time1 = time.time()
            result1 = steamdt_api.get_item_prices(['Cached Item'])
            end_time1 = time.time()
            
            # 第二次调用（缓存命中）
            start_time2 = time.time()
            result2 = steamdt_api.get_item_prices(['Cached Item'])
            end_time2 = time.time()
        
        # 验证缓存效果
        cache_miss_time = end_time1 - start_time1
        cache_hit_time = end_time2 - start_time2
        
        assert result1 == result2  # 结果应该相同
        assert cache_hit_time < cache_miss_time  # 缓存命中应该更快
        assert mock_request.call_count == 1  # API只应该被调用一次


class TestDataProcessingPerformance:
    """数据处理性能测试"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置管理器"""
        with patch('core.config.get_config_manager') as mock:
            config_manager = Mock()
            config_manager.get.return_value = None
            mock.return_value = config_manager
            yield config_manager
    
    @pytest.fixture
    def price_comparator(self, mock_config):
        """创建价格比较器"""
        return PlatformPriceComparator()
    
    @pytest.fixture
    def arbitrage_detector(self, mock_config):
        """创建套利检测器"""
        return ArbitrageDetector()
    
    def test_price_comparison_performance(self, price_comparator):
        """测试价格比较性能"""
        from services.steamdt_api import ItemPriceData, PlatformPrice
        
        # 创建大量测试数据
        test_data = []
        for i in range(1000):
            platforms = [
                PlatformPrice(
                    platform='steam',
                    platform_item_id=f'steam_{i}',
                    sell_price=100.0 + i,
                    sell_count=20,
                    bidding_price=95.0 + i,
                    bidding_count=10,
                    update_time=int(time.time())
                ),
                PlatformPrice(
                    platform='buff',
                    platform_item_id=f'buff_{i}',
                    sell_price=90.0 + i,
                    sell_count=30,
                    bidding_price=85.0 + i,
                    bidding_count=15,
                    update_time=int(time.time())
                )
            ]
            
            test_data.append(ItemPriceData(
                market_hash_name=f'Test Item {i}',
                platforms=platforms
            ))
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 批量价格比较
        start_time = time.time()
        comparisons = []
        for data in test_data:
            comparison = price_comparator.compare_prices(data)
            if comparison:
                comparisons.append(comparison)
        end_time = time.time()
        
        metrics.stop_monitoring()
        
        # 性能断言
        processing_time = end_time - start_time
        assert processing_time < 10.0  # 1000个比较应在10秒内完成
        assert len(comparisons) > 0
        
        results = metrics.get_results()
        assert results['memory_increase_mb'] < 100  # 内存增长应小于100MB
        
        # 计算每个比较的平均时间
        avg_time_per_comparison = processing_time / len(test_data)
        assert avg_time_per_comparison < 0.01  # 每个比较平均时间应小于10ms
    
    def test_arbitrage_detection_performance(self, arbitrage_detector, price_comparator):
        """测试套利检测性能"""
        from services.steamdt_api import ItemPriceData, PlatformPrice
        from services.price_comparator import Platform
        
        # 创建测试比较数据
        comparisons = []
        for i in range(500):
            # 模拟价格比较结果
            platform_prices = {
                Platform.STEAM: PlatformPrice(
                    platform='steam',
                    platform_item_id=f'steam_{i}',
                    sell_price=120.0 + i,
                    sell_count=20,
                    bidding_price=115.0 + i,
                    bidding_count=10,
                    update_time=int(time.time())
                ),
                Platform.BUFF: PlatformPrice(
                    platform='buff',
                    platform_item_id=f'buff_{i}',
                    sell_price=100.0 + i,
                    sell_count=30,
                    bidding_price=95.0 + i,
                    bidding_count=15,
                    update_time=int(time.time())
                )
            }
            
            from services.price_comparator import PriceComparison
            comparison = PriceComparison(
                item_name=f'Test Item {i}',
                market_hash_name=f'Test Item {i}',
                platform_prices=platform_prices,
                price_spread={
                    'min_price': 100.0 + i,
                    'max_price': 120.0 + i,
                    'avg_price': 110.0 + i,
                    'absolute_spread': 20.0,
                    'relative_spread_percent': 20.0
                },
                best_buy_platform=Platform.BUFF,
                best_sell_platform=Platform.STEAM,
                max_profit_margin=15.0,
                comparison_time=datetime.now()
            )
            comparisons.append(comparison)
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 批量套利检测
        start_time = time.time()
        opportunities = arbitrage_detector.detect_opportunities(comparisons)
        end_time = time.time()
        
        metrics.stop_monitoring()
        
        # 性能断言
        processing_time = end_time - start_time
        assert processing_time < 5.0  # 500个检测应在5秒内完成
        assert len(opportunities) > 0
        
        results = metrics.get_results()
        assert results['memory_increase_mb'] < 50  # 内存增长应小于50MB


class TestMacroDataPerformance:
    """宏观数据性能测试"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置管理器"""
        with patch('core.config.get_config_manager') as mock:
            config_manager = Mock()
            config_manager.get.side_effect = lambda key, default=None: {
                'STEAMDT_API_KEY': 'test_api_key'
            }.get(key, default)
            mock.return_value = config_manager
            yield config_manager
    
    @pytest.fixture
    def cs2_collector(self, mock_config):
        """创建CS2宏观数据采集器"""
        return CS2MacroDataCollector()
    
    @pytest.mark.asyncio
    async def test_macro_data_collection_performance(self, cs2_collector):
        """测试宏观数据收集性能"""
        mock_response = {
            'response': {
                'player_count': 950000
            }
        }
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_resp = AsyncMock()
            mock_resp.status = 200
            mock_resp.json = AsyncMock(return_value=mock_response)
            mock_get.return_value.__aenter__.return_value = mock_resp
            
            # 连续收集多次数据
            start_time = time.time()
            for i in range(10):
                await cs2_collector.collect_current_player_data()
                metrics.update_metrics()
            end_time = time.time()
        
        metrics.stop_monitoring()
        
        # 性能断言
        total_time = end_time - start_time
        assert total_time < 5.0  # 10次收集应在5秒内完成
        assert len(cs2_collector.player_data_history) == 10
        
        results = metrics.get_results()
        assert results['memory_increase_mb'] < 20  # 内存增长应小于20MB
    
    @pytest.mark.asyncio
    async def test_macro_indicators_calculation_performance(self, cs2_collector):
        """测试宏观指标计算性能"""
        # 添加大量历史数据
        from services.cs2_macro_collector import CS2PlayerData
        
        base_time = datetime.now()
        for i in range(1000):  # 1000个数据点
            timestamp = base_time - timedelta(hours=i)
            player_data = CS2PlayerData(
                timestamp=timestamp,
                player_count=800000 + (i * 100),
                data_source="test"
            )
            cs2_collector._add_to_history(player_data)
        
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 计算宏观指标
        start_time = time.time()
        indicators = await cs2_collector.get_macro_indicators()
        end_time = time.time()
        
        metrics.stop_monitoring()
        
        # 性能断言
        calculation_time = end_time - start_time
        assert calculation_time < 2.0  # 计算应在2秒内完成
        assert indicators is not None
        
        results = metrics.get_results()
        assert results['memory_increase_mb'] < 30  # 内存增长应小于30MB


class TestSystemMonitorPerformance:
    """系统监控性能测试"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置管理器"""
        with patch('core.config.get_config_manager') as mock:
            config_manager = Mock()
            config_manager.get.return_value = None
            mock.return_value = config_manager
            yield config_manager
    
    @pytest.fixture
    def system_monitor(self, mock_config):
        """创建系统监控器"""
        return SystemMonitor()
    
    def test_monitoring_overhead(self, system_monitor):
        """测试监控系统开销"""
        metrics = PerformanceMetrics()
        metrics.start_monitoring()
        
        # 启动监控
        system_monitor.start_monitoring(interval=0.1)  # 100ms间隔
        
        # 运行一段时间
        time.sleep(2)
        
        # 停止监控
        system_monitor.stop_monitoring()
        
        metrics.stop_monitoring()
        
        # 检查监控开销
        results = metrics.get_results()
        assert results['avg_cpu_percent'] < 10  # 平均CPU使用率应小于10%
        assert results['memory_increase_mb'] < 50  # 内存增长应小于50MB
    
    def test_metrics_storage_performance(self, system_monitor):
        """测试指标存储性能"""
        # 添加大量指标数据
        start_time = time.time()
        
        for i in range(10000):
            timestamp = datetime.now()
            system_monitor.add_metric(f"test_metric_{i % 100}", i, timestamp)
        
        end_time = time.time()
        
        # 性能断言
        storage_time = end_time - start_time
        assert storage_time < 5.0  # 10000个指标存储应在5秒内完成
        
        # 检查内存使用
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        assert memory_mb < 200  # 内存使用应小于200MB


if __name__ == "__main__":
    # 运行基本性能测试
    print("运行CS2饰品投资系统性能测试...")
    
    # 测试性能指标收集器
    print("测试性能指标收集器...")
    metrics = PerformanceMetrics()
    metrics.start_monitoring()
    time.sleep(0.1)
    metrics.update_metrics()
    metrics.stop_monitoring()
    
    results = metrics.get_results()
    assert results['execution_time'] > 0
    assert results['memory_start_mb'] > 0
    print("✓ 性能指标收集器测试通过")
    
    print("所有基本性能测试通过！")
    
    # 运行pytest
    pytest.main(["-xvs", __file__])
