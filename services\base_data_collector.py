"""
CS2饰品基础数据收集器
基于SteamDT官方API实现饰品基础信息的获取、解析和存储
"""

import asyncio
import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path

from core.config import get_config_manager
from core.database import get_database_manager
from services.steamdt_api import get_steamdt_api_manager, SteamdtAPIManager


@dataclass
class PlatformInfo:
    """平台信息数据类 - 基于SteamDT API platformList结构"""
    name: str  # 平台名称，如 "steam", "buff163"
    item_id: str  # 平台上的饰品ID


@dataclass
class ItemBaseInfo:
    """饰品基础信息数据类 - 基于SteamDT API /open/cs2/v1/base 响应格式"""
    name: str  # 饰品中文名称
    market_hash_name: str  # Steam市场哈希名称
    platform_list: List[PlatformInfo]  # 各平台信息列表
    data_source: str = 'steamdt'
    sync_time: datetime = field(default_factory=datetime.now)

    @property
    def platform_data(self) -> Dict[str, str]:
        """兼容性属性：返回平台名称到itemId的映射"""
        return {platform.name: platform.item_id for platform in self.platform_list}


@dataclass
class SteamDTAPIResponse:
    """SteamDT API标准响应格式"""
    success: bool
    data: Any
    error_code: int = 0
    error_msg: str = ""
    error_data: Dict[str, Any] = field(default_factory=dict)
    error_code_str: str = ""


@dataclass
class BaseDataResult:
    """基础数据收集结果"""
    success: bool
    total_items: int
    new_items: int = 0
    updated_items: int = 0
    error_message: Optional[str] = None
    sync_duration: float = 0.0
    data_quality_score: float = 0.0
    items: List[ItemBaseInfo] = field(default_factory=list)
    api_response: Optional[SteamDTAPIResponse] = None


@dataclass
class UpdateResult:
    """数据库更新结果"""
    new_count: int
    updated_count: int


@dataclass
class PriceData:
    """价格数据类 - 基于SteamDT价格API响应格式"""
    platform: str  # 平台名称
    platform_item_id: str  # 平台饰品ID
    sell_price: Optional[float] = None  # 在售价
    sell_count: Optional[int] = None  # 在售数量
    bidding_price: Optional[float] = None  # 求购价
    bidding_count: Optional[int] = None  # 求购数量
    update_time: Optional[int] = None  # 更新时间戳


@dataclass
class ItemPriceInfo:
    """饰品价格信息 - 用于批量价格查询"""
    market_hash_name: str
    data_list: List[PriceData]


@dataclass
class AvgPriceData:
    """7天均价数据"""
    platform: str
    avg_price: float


@dataclass
class ItemAvgPriceInfo:
    """饰品7天均价信息"""
    market_hash_name: str
    avg_price: float  # 总体均价
    data_list: List[AvgPriceData]  # 各平台均价


class BaseDataCollector:
    """CS2饰品基础数据收集器"""
    
    def __init__(self):
        """初始化基础数据收集器"""
        self.config_manager = get_config_manager()
        self.db_manager = get_database_manager()
        self.logger = logging.getLogger(__name__)
        
        # API管理器 - 延迟初始化
        self.steamdt_api: Optional[SteamdtAPIManager] = None
        
        # 状态管理
        self.last_update: Optional[datetime] = None
        self.state_file = Path('data/base_data_state.json')

        # API调用统计
        self.api_call_count = 0
        self.api_success_count = 0
        self.api_failure_count = 0
        self.total_items_collected = 0

        # 确保数据目录存在
        self.state_file.parent.mkdir(parents=True, exist_ok=True)

        # 加载状态
        self._load_state()
    
    async def collect_base_data(self, force_refresh: bool = False) -> BaseDataResult:
        """
        收集基础数据的核心方法

        Args:
            force_refresh: 是否强制刷新，忽略每日限制检查

        Returns:
            BaseDataResult: 收集结果
        """
        start_time = time.time()

        try:
            self.logger.info("开始收集CS2饰品基础数据")

            # 检查API调用限制
            if not force_refresh and not self._should_call_api():
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='API调用限制：今日已调用基础数据API，请明日再试'
                )

            # 获取API管理器
            api_manager = await self._get_steamdt_api()

            # 调用基础数据API（带重试机制）
            self.api_call_count += 1
            raw_data = await self._call_api_with_retry(api_manager)

            if not raw_data:
                self.api_failure_count += 1
                self._save_state()
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='API调用失败：无法获取基础数据'
                )

            self.api_success_count += 1

            # 解析API响应
            api_response = self.parse_steamdt_response(raw_data)
            if not api_response.success:
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message=f'API返回错误: {api_response.error_msg} (错误码: {api_response.error_code})',
                    api_response=api_response
                )

            # 解析饰品数据
            items = self._parse_api_response(raw_data)
            if not items:
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='数据解析失败：API返回数据为空或格式错误',
                    api_response=api_response
                )

            # 数据质量验证
            quality_score = self._validate_data_quality(items)

            # 更新统计信息
            self.last_update = datetime.now()
            self.total_items_collected += len(items)
            self._save_state()

            # 计算执行时间
            duration = time.time() - start_time

            # 返回结果
            result = BaseDataResult(
                success=True,
                total_items=len(items),
                sync_duration=duration,
                data_quality_score=quality_score,
                items=items,
                api_response=api_response
            )

            self.logger.info(
                f"基础数据收集完成: 总计{len(items)}个饰品, "
                f"质量评分{quality_score:.2f}, 耗时{duration:.2f}秒"
            )

            return result

        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"基础数据收集失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return BaseDataResult(
                success=False,
                total_items=0,
                error_message=error_msg,
                sync_duration=duration
            )
    
    async def _get_steamdt_api(self) -> SteamdtAPIManager:
        """获取SteamDT API管理器实例"""
        if self.steamdt_api is None:
            self.steamdt_api = await get_steamdt_api_manager()
        return self.steamdt_api

    def _should_call_api(self) -> bool:
        """
        检查是否应该调用API（每日限制检查）

        Returns:
            bool: 是否可以调用API
        """
        if self.last_update is None:
            return True

        # 检查是否已经过了一天
        now = datetime.now()
        time_diff = now - self.last_update

        # 如果超过23小时，允许调用
        if time_diff.total_seconds() > 23 * 3600:
            self.logger.info("距离上次API调用已超过23小时，允许调用")
            return True

        remaining_hours = 24 - (time_diff.total_seconds() / 3600)
        self.logger.warning(f"API每日限制：距离下次可调用还有 {remaining_hours:.1f} 小时")
        return False

    async def _call_api_with_retry(self, api_manager: SteamdtAPIManager, max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        带重试机制的API调用

        Args:
            api_manager: API管理器实例
            max_retries: 最大重试次数

        Returns:
            Optional[Dict[str, Any]]: API响应数据
        """
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # 重试前等待
                    wait_time = min(2 ** attempt, 30)  # 指数退避，最大30秒
                    self.logger.info(f"第{attempt}次重试，等待{wait_time}秒...")
                    await asyncio.sleep(wait_time)

                self.logger.info(f"调用SteamDT基础数据API (尝试 {attempt + 1}/{max_retries + 1})")

                # 调用API
                raw_data = await api_manager.get_base_item_info()

                if raw_data is not None:
                    self.logger.info("API调用成功")
                    return raw_data
                else:
                    last_error = "API返回None"
                    self.logger.warning(f"API调用返回None (尝试 {attempt + 1}/{max_retries + 1})")

            except Exception as e:
                last_error = str(e)
                self.logger.error(f"API调用异常 (尝试 {attempt + 1}/{max_retries + 1}): {e}")

                # 如果是最后一次尝试，不等待
                if attempt == max_retries:
                    break

        self.logger.error(f"API调用失败，已重试{max_retries}次，最后错误: {last_error}")
        return None

    def get_api_statistics(self) -> Dict[str, Any]:
        """
        获取API调用统计信息

        Returns:
            Dict[str, Any]: API调用统计
        """
        success_rate = 0.0
        if self.api_call_count > 0:
            success_rate = (self.api_success_count / self.api_call_count) * 100

        return {
            'total_calls': self.api_call_count,
            'successful_calls': self.api_success_count,
            'failed_calls': self.api_failure_count,
            'success_rate': round(success_rate, 2),
            'total_items_collected': self.total_items_collected,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'can_call_api': self._should_call_api()
        }

    def reset_statistics(self):
        """重置API调用统计"""
        self.api_call_count = 0
        self.api_success_count = 0
        self.api_failure_count = 0
        self.total_items_collected = 0
        self._save_state()
        self.logger.info("API调用统计已重置")
    
    def _parse_api_response(self, data: Dict[str, Any]) -> List[ItemBaseInfo]:
        """
        解析SteamDT API响应数据

        Args:
            data: API响应数据

        Returns:
            List[ItemBaseInfo]: 解析后的饰品基础信息列表
        """
        try:
            items = []

            # 检查API响应格式
            if not isinstance(data, dict) or not data.get('success', False):
                self.logger.error(f"API响应格式错误: {data}")
                return items

            # 获取数据列表
            data_list = data.get('data', [])
            if not isinstance(data_list, list):
                self.logger.error(f"API数据格式错误，期望列表: {type(data_list)}")
                return items

            # 解析每个饰品数据
            for item_data in data_list:
                try:
                    # 提取基础信息
                    name = item_data.get('name', '')
                    market_hash_name = item_data.get('marketHashName', '')

                    if not name or not market_hash_name:
                        self.logger.warning(f"饰品数据不完整，跳过: {item_data}")
                        continue

                    # 解析平台数据列表
                    platform_list = []
                    platform_list_data = item_data.get('platformList', [])

                    for platform_info in platform_list_data:
                        platform_name = platform_info.get('name', '')
                        item_id = platform_info.get('itemId', '')

                        if platform_name and item_id:
                            platform_list.append(PlatformInfo(
                                name=platform_name,
                                item_id=item_id
                            ))

                    # 创建饰品基础信息对象
                    item_info = ItemBaseInfo(
                        name=name,
                        market_hash_name=market_hash_name,
                        platform_list=platform_list,
                        data_source='steamdt',
                        sync_time=datetime.now()
                    )

                    items.append(item_info)

                except Exception as e:
                    self.logger.warning(f"解析单个饰品数据失败: {e}, 数据: {item_data}")
                    continue

            self.logger.info(f"成功解析{len(items)}个饰品的基础信息")
            return items

        except Exception as e:
            self.logger.error(f"解析API响应数据失败: {e}")
            return []
    
    def _validate_data_quality(self, items: List[ItemBaseInfo]) -> float:
        """
        验证数据质量并计算评分
        
        Args:
            items: 饰品基础信息列表
            
        Returns:
            float: 数据质量评分 (0-100)
        """
        if not items:
            return 0.0
        
        total_score = 0.0
        valid_items = 0
        
        for item in items:
            score = 0.0
            
            # 检查必要字段 (40分)
            if item.name and item.market_hash_name:
                score += 40
            
            # 检查平台数据 (30分)
            if item.platform_list and len(item.platform_list) > 0:
                score += 30
            
            # 检查数据格式 (20分)
            if self._validate_market_hash_name(item.market_hash_name):
                score += 20
            
            # 检查平台ID格式 (10分)
            if self._validate_platform_ids(item.platform_list):
                score += 10
            
            total_score += score
            if score >= 70:  # 70分以上认为是有效数据
                valid_items += 1
        
        quality_score = total_score / len(items)
        
        self.logger.info(
            f"数据质量评分: {quality_score:.2f}, "
            f"有效数据: {valid_items}/{len(items)} ({valid_items/len(items)*100:.1f}%)"
        )
        
        return quality_score
    
    def _validate_market_hash_name(self, name: str) -> bool:
        """验证market_hash_name格式"""
        return bool(name and len(name) > 0 and '|' in name)
    
    def _validate_platform_ids(self, platform_list: List[PlatformInfo]) -> bool:
        """验证平台ID格式"""
        if not platform_list:
            return False

        required_platforms = ['steam', 'buff163']
        platform_names = [platform.name for platform in platform_list]
        return any(platform in platform_names for platform in required_platforms)
    
    def _load_state(self):
        """加载状态信息"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                # 加载基础状态
                last_update_str = state_data.get('last_update')
                if last_update_str:
                    self.last_update = datetime.fromisoformat(last_update_str)

                # 加载API调用统计
                self.api_call_count = state_data.get('api_call_count', 0)
                self.api_success_count = state_data.get('api_success_count', 0)
                self.api_failure_count = state_data.get('api_failure_count', 0)
                self.total_items_collected = state_data.get('total_items_collected', 0)

                self.logger.info(
                    f"加载状态成功，上次更新: {self.last_update}, "
                    f"API调用统计: {self.api_success_count}/{self.api_call_count}"
                )
        except Exception as e:
            self.logger.warning(f"加载状态失败: {e}")

    def _save_state(self):
        """保存状态信息"""
        try:
            state_data = {
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'updated_at': datetime.now().isoformat(),
                'api_call_count': self.api_call_count,
                'api_success_count': self.api_success_count,
                'api_failure_count': self.api_failure_count,
                'total_items_collected': self.total_items_collected
            }

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)

            self.logger.debug("状态保存成功")
        except Exception as e:
            self.logger.warning(f"保存状态失败: {e}")

    @staticmethod
    def parse_steamdt_response(response_data: Dict[str, Any]) -> SteamDTAPIResponse:
        """
        解析SteamDT API标准响应格式

        Args:
            response_data: API原始响应数据

        Returns:
            SteamDTAPIResponse: 标准化的响应对象
        """
        return SteamDTAPIResponse(
            success=response_data.get('success', False),
            data=response_data.get('data'),
            error_code=response_data.get('errorCode', 0),
            error_msg=response_data.get('errorMsg', ''),
            error_data=response_data.get('errorData', {}),
            error_code_str=response_data.get('errorCodeStr', '')
        )

    @staticmethod
    def parse_price_data(price_response: Dict[str, Any]) -> List[PriceData]:
        """
        解析价格数据响应

        Args:
            price_response: 价格API响应数据

        Returns:
            List[PriceData]: 价格数据列表
        """
        price_list = []

        if not isinstance(price_response, dict):
            return price_list

        data_list = price_response.get('data', [])
        if not isinstance(data_list, list):
            return price_list

        for item in data_list:
            price_data = PriceData(
                platform=item.get('platform', ''),
                platform_item_id=item.get('platformItemId', ''),
                sell_price=item.get('sellPrice'),
                sell_count=item.get('sellCount'),
                bidding_price=item.get('biddingPrice'),
                bidding_count=item.get('biddingCount'),
                update_time=item.get('updateTime')
            )
            price_list.append(price_data)

        return price_list

    @staticmethod
    def parse_batch_price_data(batch_response: Dict[str, Any]) -> List[ItemPriceInfo]:
        """
        解析批量价格数据响应

        Args:
            batch_response: 批量价格API响应数据

        Returns:
            List[ItemPriceInfo]: 饰品价格信息列表
        """
        item_price_list = []

        if not isinstance(batch_response, dict):
            return item_price_list

        data_list = batch_response.get('data', [])
        if not isinstance(data_list, list):
            return item_price_list

        for item in data_list:
            market_hash_name = item.get('marketHashName', '')
            data_list_raw = item.get('dataList', [])

            price_data_list = []
            for price_item in data_list_raw:
                price_data = PriceData(
                    platform=price_item.get('platform', ''),
                    platform_item_id=price_item.get('platformItemId', ''),
                    sell_price=price_item.get('sellPrice'),
                    sell_count=price_item.get('sellCount'),
                    bidding_price=price_item.get('biddingPrice'),
                    bidding_count=price_item.get('biddingCount'),
                    update_time=price_item.get('updateTime')
                )
                price_data_list.append(price_data)

            item_price_info = ItemPriceInfo(
                market_hash_name=market_hash_name,
                data_list=price_data_list
            )
            item_price_list.append(item_price_info)

        return item_price_list


# 全局基础数据收集器实例
_base_data_collector: Optional[BaseDataCollector] = None


def get_base_data_collector() -> BaseDataCollector:
    """获取全局基础数据收集器实例"""
    global _base_data_collector
    
    if _base_data_collector is None:
        _base_data_collector = BaseDataCollector()
    
    return _base_data_collector
