"""
CS2饰品基础数据收集器
基于SteamDT官方API实现饰品基础信息的获取、解析和存储
"""

import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path

from core.config import get_config_manager
from core.database import get_database_manager
from services.steamdt_api import get_steamdt_api_manager, SteamdtAPIManager


@dataclass
class ItemBaseInfo:
    """饰品基础信息数据类"""
    name: str
    market_hash_name: str
    platform_data: Dict[str, str]  # platform -> itemId映射
    data_source: str = 'steamdt'
    sync_time: datetime = field(default_factory=datetime.now)


@dataclass
class BaseDataResult:
    """基础数据收集结果"""
    success: bool
    total_items: int
    new_items: int = 0
    updated_items: int = 0
    error_message: Optional[str] = None
    sync_duration: float = 0.0
    data_quality_score: float = 0.0
    items: List[ItemBaseInfo] = field(default_factory=list)


@dataclass
class UpdateResult:
    """数据库更新结果"""
    new_count: int
    updated_count: int


class BaseDataCollector:
    """CS2饰品基础数据收集器"""
    
    def __init__(self):
        """初始化基础数据收集器"""
        self.config_manager = get_config_manager()
        self.db_manager = get_database_manager()
        self.logger = logging.getLogger(__name__)
        
        # API管理器 - 延迟初始化
        self.steamdt_api: Optional[SteamdtAPIManager] = None
        
        # 状态管理
        self.last_update: Optional[datetime] = None
        self.state_file = Path('data/base_data_state.json')
        
        # 确保数据目录存在
        self.state_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载状态
        self._load_state()
    
    async def collect_base_data(self) -> BaseDataResult:
        """
        收集基础数据的核心方法
        
        Returns:
            BaseDataResult: 收集结果
        """
        start_time = time.time()
        
        try:
            self.logger.info("开始收集CS2饰品基础数据")
            
            # 获取API管理器
            api_manager = await self._get_steamdt_api()
            
            # 调用基础数据API
            raw_data = await api_manager.get_base_item_info()
            if not raw_data:
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='API调用失败：无法获取基础数据'
                )
            
            # 解析API响应
            items = self._parse_api_response(raw_data)
            if not items:
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='数据解析失败：API返回数据为空或格式错误'
                )
            
            # 数据质量验证
            quality_score = self._validate_data_quality(items)
            
            # 计算执行时间
            duration = time.time() - start_time
            
            # 返回结果
            result = BaseDataResult(
                success=True,
                total_items=len(items),
                sync_duration=duration,
                data_quality_score=quality_score,
                items=items
            )
            
            self.logger.info(
                f"基础数据收集完成: 总计{len(items)}个饰品, "
                f"质量评分{quality_score:.2f}, 耗时{duration:.2f}秒"
            )
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"基础数据收集失败: {str(e)}"
            self.logger.error(error_msg)
            
            return BaseDataResult(
                success=False,
                total_items=0,
                error_message=error_msg,
                sync_duration=duration
            )
    
    async def _get_steamdt_api(self) -> SteamdtAPIManager:
        """获取SteamDT API管理器实例"""
        if self.steamdt_api is None:
            self.steamdt_api = await get_steamdt_api_manager()
        return self.steamdt_api
    
    def _parse_api_response(self, data: Dict[str, Any]) -> List[ItemBaseInfo]:
        """
        解析SteamDT API响应数据
        
        Args:
            data: API响应数据
            
        Returns:
            List[ItemBaseInfo]: 解析后的饰品基础信息列表
        """
        try:
            items = []
            
            # 检查API响应格式
            if not isinstance(data, dict) or not data.get('success', False):
                self.logger.error(f"API响应格式错误: {data}")
                return items
            
            # 获取数据列表
            data_list = data.get('data', [])
            if not isinstance(data_list, list):
                self.logger.error(f"API数据格式错误，期望列表: {type(data_list)}")
                return items
            
            # 解析每个饰品数据
            for item_data in data_list:
                try:
                    # 提取基础信息
                    name = item_data.get('name', '')
                    market_hash_name = item_data.get('marketHashName', '')
                    
                    if not name or not market_hash_name:
                        self.logger.warning(f"饰品数据不完整，跳过: {item_data}")
                        continue
                    
                    # 解析平台数据
                    platform_data = {}
                    platform_list = item_data.get('platformList', [])
                    
                    for platform_info in platform_list:
                        platform_name = platform_info.get('name', '')
                        item_id = platform_info.get('itemId', '')
                        
                        if platform_name and item_id:
                            platform_data[platform_name] = item_id
                    
                    # 创建饰品基础信息对象
                    item_info = ItemBaseInfo(
                        name=name,
                        market_hash_name=market_hash_name,
                        platform_data=platform_data,
                        data_source='steamdt',
                        sync_time=datetime.now()
                    )
                    
                    items.append(item_info)
                    
                except Exception as e:
                    self.logger.warning(f"解析单个饰品数据失败: {e}, 数据: {item_data}")
                    continue
            
            self.logger.info(f"成功解析{len(items)}个饰品的基础信息")
            return items
            
        except Exception as e:
            self.logger.error(f"解析API响应数据失败: {e}")
            return []
    
    def _validate_data_quality(self, items: List[ItemBaseInfo]) -> float:
        """
        验证数据质量并计算评分
        
        Args:
            items: 饰品基础信息列表
            
        Returns:
            float: 数据质量评分 (0-100)
        """
        if not items:
            return 0.0
        
        total_score = 0.0
        valid_items = 0
        
        for item in items:
            score = 0.0
            
            # 检查必要字段 (40分)
            if item.name and item.market_hash_name:
                score += 40
            
            # 检查平台数据 (30分)
            if item.platform_data and len(item.platform_data) > 0:
                score += 30
            
            # 检查数据格式 (20分)
            if self._validate_market_hash_name(item.market_hash_name):
                score += 20
            
            # 检查平台ID格式 (10分)
            if self._validate_platform_ids(item.platform_data):
                score += 10
            
            total_score += score
            if score >= 70:  # 70分以上认为是有效数据
                valid_items += 1
        
        quality_score = total_score / len(items)
        
        self.logger.info(
            f"数据质量评分: {quality_score:.2f}, "
            f"有效数据: {valid_items}/{len(items)} ({valid_items/len(items)*100:.1f}%)"
        )
        
        return quality_score
    
    def _validate_market_hash_name(self, name: str) -> bool:
        """验证market_hash_name格式"""
        return bool(name and len(name) > 0 and '|' in name)
    
    def _validate_platform_ids(self, platform_data: Dict[str, str]) -> bool:
        """验证平台ID格式"""
        required_platforms = ['steam', 'buff163']
        return any(platform in platform_data for platform in required_platforms)
    
    def _load_state(self):
        """加载状态信息"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                    
                last_update_str = state_data.get('last_update')
                if last_update_str:
                    self.last_update = datetime.fromisoformat(last_update_str)
                    
                self.logger.info(f"加载状态成功，上次更新: {self.last_update}")
        except Exception as e:
            self.logger.warning(f"加载状态失败: {e}")
    
    def _save_state(self):
        """保存状态信息"""
        try:
            state_data = {
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'updated_at': datetime.now().isoformat()
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
                
            self.logger.debug("状态保存成功")
        except Exception as e:
            self.logger.warning(f"保存状态失败: {e}")


# 全局基础数据收集器实例
_base_data_collector: Optional[BaseDataCollector] = None


def get_base_data_collector() -> BaseDataCollector:
    """获取全局基础数据收集器实例"""
    global _base_data_collector
    
    if _base_data_collector is None:
        _base_data_collector = BaseDataCollector()
    
    return _base_data_collector
