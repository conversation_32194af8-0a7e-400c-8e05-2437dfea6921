"""
CS2饰品基础数据收集器
基于SteamDT官方API实现饰品基础信息的获取、解析和存储
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from sqlalchemy.sql import func

from core.config import get_config_manager
from core.database import get_database_manager, Item
from services.steamdt_api import get_steamdt_api_manager, SteamdtAPIManager


@dataclass
class PlatformInfo:
    """平台信息数据类 - 基于SteamDT API platformList结构"""
    name: str  # 平台名称，如 "steam", "buff163"
    item_id: str  # 平台上的饰品ID


@dataclass
class ItemBaseInfo:
    """饰品基础信息数据类 - 基于SteamDT API /open/cs2/v1/base 响应格式"""
    name: str  # 饰品中文名称
    market_hash_name: str  # Steam市场哈希名称
    platform_list: List[PlatformInfo]  # 各平台信息列表
    data_source: str = 'steamdt'
    sync_time: datetime = field(default_factory=datetime.now)

    @property
    def platform_data(self) -> Dict[str, str]:
        """兼容性属性：返回平台名称到itemId的映射"""
        return {platform.name: platform.item_id for platform in self.platform_list}


@dataclass
class SteamDTAPIResponse:
    """SteamDT API标准响应格式"""
    success: bool
    data: Any
    error_code: int = 0
    error_msg: str = ""
    error_data: Dict[str, Any] = field(default_factory=dict)
    error_code_str: str = ""


@dataclass
class BaseDataResult:
    """基础数据收集结果"""
    success: bool
    total_items: int
    new_items: int = 0
    updated_items: int = 0
    error_message: Optional[str] = None
    sync_duration: float = 0.0
    data_quality_score: float = 0.0
    items: List[ItemBaseInfo] = field(default_factory=list)
    api_response: Optional[SteamDTAPIResponse] = None


@dataclass
class UpdateResult:
    """数据库更新结果"""
    new_count: int
    updated_count: int


@dataclass
class PriceData:
    """价格数据类 - 基于SteamDT价格API响应格式"""
    platform: str  # 平台名称
    platform_item_id: str  # 平台饰品ID
    sell_price: Optional[float] = None  # 在售价
    sell_count: Optional[int] = None  # 在售数量
    bidding_price: Optional[float] = None  # 求购价
    bidding_count: Optional[int] = None  # 求购数量
    update_time: Optional[int] = None  # 更新时间戳


@dataclass
class ItemPriceInfo:
    """饰品价格信息 - 用于批量价格查询"""
    market_hash_name: str
    data_list: List[PriceData]


@dataclass
class AvgPriceData:
    """7天均价数据"""
    platform: str
    avg_price: float


@dataclass
class ItemAvgPriceInfo:
    """饰品7天均价信息"""
    market_hash_name: str
    avg_price: float  # 总体均价
    data_list: List[AvgPriceData]  # 各平台均价


class BaseDataCollector:
    """CS2饰品基础数据收集器"""
    
    def __init__(self):
        """初始化基础数据收集器"""
        self.config_manager = get_config_manager()
        self.db_manager = get_database_manager()
        self.logger = logging.getLogger(__name__)
        
        # API管理器 - 延迟初始化
        self.steamdt_api: Optional[SteamdtAPIManager] = None
        
        # 状态管理
        self.last_update: Optional[datetime] = None
        self.state_file = Path('data/base_data_state.json')

        # API调用统计
        self.api_call_count = 0
        self.api_success_count = 0
        self.api_failure_count = 0
        self.total_items_collected = 0

        # 调度器
        self.scheduler = AsyncIOScheduler()
        self.running = False

        # 调度配置
        self.sync_time = self.config_manager.get('base_data_collector.sync_time', '02:00')

        # 确保数据目录存在
        self.state_file.parent.mkdir(parents=True, exist_ok=True)

        # 加载状态
        self._load_state()
    
    async def collect_base_data(self, force_refresh: bool = False) -> BaseDataResult:
        """
        收集基础数据的核心方法

        Args:
            force_refresh: 是否强制刷新，忽略每日限制检查

        Returns:
            BaseDataResult: 收集结果
        """
        start_time = time.time()

        try:
            self.logger.info("开始收集CS2饰品基础数据")

            # 检查API调用限制
            if not force_refresh and not self._should_call_api():
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='API调用限制：今日已调用基础数据API，请明日再试'
                )

            # 获取API管理器
            api_manager = await self._get_steamdt_api()

            # 调用基础数据API（带重试机制）
            self.api_call_count += 1
            raw_data = await self._call_api_with_retry(api_manager)

            if not raw_data:
                self.api_failure_count += 1
                self._save_state()
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='API调用失败：无法获取基础数据'
                )

            self.api_success_count += 1

            # 解析API响应
            api_response = self.parse_steamdt_response(raw_data)
            if not api_response.success:
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message=f'API返回错误: {api_response.error_msg} (错误码: {api_response.error_code})',
                    api_response=api_response
                )

            # 解析饰品数据
            items = self._parse_api_response(raw_data)
            if not items:
                return BaseDataResult(
                    success=False,
                    total_items=0,
                    error_message='数据解析失败：API返回数据为空或格式错误',
                    api_response=api_response
                )

            # 数据质量验证
            quality_score = self._validate_data_quality(items)

            # 存储到数据库
            update_result = self._update_database(items)

            # 更新统计信息
            self.last_update = datetime.now()
            self.total_items_collected += len(items)
            self._save_state()

            # 计算执行时间
            duration = time.time() - start_time

            # 返回结果
            result = BaseDataResult(
                success=True,
                total_items=len(items),
                new_items=update_result.new_count,
                updated_items=update_result.updated_count,
                sync_duration=duration,
                data_quality_score=quality_score,
                items=items,
                api_response=api_response
            )

            self.logger.info(
                f"基础数据收集完成: 总计{len(items)}个饰品, "
                f"质量评分{quality_score:.2f}, 耗时{duration:.2f}秒"
            )

            return result

        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"基础数据收集失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return BaseDataResult(
                success=False,
                total_items=0,
                error_message=error_msg,
                sync_duration=duration
            )
    
    async def _get_steamdt_api(self) -> SteamdtAPIManager:
        """获取SteamDT API管理器实例"""
        if self.steamdt_api is None:
            self.steamdt_api = await get_steamdt_api_manager()
        return self.steamdt_api

    def _should_call_api(self) -> bool:
        """
        检查是否应该调用API（每日限制检查）

        Returns:
            bool: 是否可以调用API
        """
        if self.last_update is None:
            return True

        # 检查是否已经过了一天
        now = datetime.now()
        time_diff = now - self.last_update

        # 如果超过23小时，允许调用
        if time_diff.total_seconds() > 23 * 3600:
            self.logger.info("距离上次API调用已超过23小时，允许调用")
            return True

        remaining_hours = 24 - (time_diff.total_seconds() / 3600)
        self.logger.warning(f"API每日限制：距离下次可调用还有 {remaining_hours:.1f} 小时")
        return False

    async def _call_api_with_retry(self, api_manager: SteamdtAPIManager, max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        带重试机制的API调用

        Args:
            api_manager: API管理器实例
            max_retries: 最大重试次数

        Returns:
            Optional[Dict[str, Any]]: API响应数据
        """
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # 重试前等待
                    wait_time = min(2 ** attempt, 30)  # 指数退避，最大30秒
                    self.logger.info(f"第{attempt}次重试，等待{wait_time}秒...")
                    await asyncio.sleep(wait_time)

                self.logger.info(f"调用SteamDT基础数据API (尝试 {attempt + 1}/{max_retries + 1})")

                # 调用API
                raw_data = await api_manager.get_base_item_info()

                if raw_data is not None:
                    self.logger.info("API调用成功")
                    return raw_data
                else:
                    last_error = "API返回None"
                    self.logger.warning(f"API调用返回None (尝试 {attempt + 1}/{max_retries + 1})")

            except Exception as e:
                last_error = str(e)
                self.logger.error(f"API调用异常 (尝试 {attempt + 1}/{max_retries + 1}): {e}")

                # 如果是最后一次尝试，不等待
                if attempt == max_retries:
                    break

        self.logger.error(f"API调用失败，已重试{max_retries}次，最后错误: {last_error}")
        return None

    def get_api_statistics(self) -> Dict[str, Any]:
        """
        获取API调用统计信息

        Returns:
            Dict[str, Any]: API调用统计
        """
        success_rate = 0.0
        if self.api_call_count > 0:
            success_rate = (self.api_success_count / self.api_call_count) * 100

        return {
            'total_calls': self.api_call_count,
            'successful_calls': self.api_success_count,
            'failed_calls': self.api_failure_count,
            'success_rate': round(success_rate, 2),
            'total_items_collected': self.total_items_collected,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'can_call_api': self._should_call_api()
        }

    def reset_statistics(self):
        """重置API调用统计"""
        self.api_call_count = 0
        self.api_success_count = 0
        self.api_failure_count = 0
        self.total_items_collected = 0
        self._save_state()
        self.logger.info("API调用统计已重置")

    def _update_database(self, items: List[ItemBaseInfo]) -> UpdateResult:
        """
        将饰品基础信息存储到数据库

        Args:
            items: 饰品基础信息列表

        Returns:
            UpdateResult: 更新结果
        """
        if not items:
            self.logger.warning("没有数据需要存储到数据库")
            return UpdateResult(new_count=0, updated_count=0)

        new_count = 0
        updated_count = 0

        try:
            # 获取数据库会话
            with self.db_manager.get_session() as session:
                self.logger.info(f"开始处理{len(items)}个饰品的数据库存储")

                # 批量处理饰品数据
                for item_info in items:
                    try:
                        # 查找现有饰品
                        existing_item = session.get(Item, item_info.market_hash_name)

                        if existing_item:
                            # 更新现有饰品
                            self._update_existing_item(existing_item, item_info)
                            updated_count += 1
                            self.logger.debug(f"更新饰品: {item_info.market_hash_name}")
                        else:
                            # 创建新饰品
                            new_item = self._create_new_item(item_info)
                            session.add(new_item)
                            new_count += 1
                            self.logger.debug(f"新增饰品: {item_info.market_hash_name}")

                    except Exception as e:
                        self.logger.error(f"处理饰品数据失败 {item_info.market_hash_name}: {e}")
                        continue

                # 事务会在上下文管理器退出时自动提交
                self.logger.info(
                    f"数据库存储完成: 新增{new_count}个饰品, 更新{updated_count}个饰品"
                )

                return UpdateResult(new_count=new_count, updated_count=updated_count)

        except Exception as e:
            self.logger.error(f"数据库存储失败: {e}", exc_info=True)
            return UpdateResult(new_count=0, updated_count=0)

    def _update_existing_item(self, existing_item: Item, item_info: ItemBaseInfo):
        """
        更新现有饰品信息

        Args:
            existing_item: 现有饰品对象
            item_info: 新的饰品信息
        """
        # 更新基础信息
        existing_item.name = item_info.name
        existing_item.platform_data = json.dumps(item_info.platform_data, ensure_ascii=False)
        existing_item.data_source = item_info.data_source
        existing_item.last_sync_time = item_info.sync_time
        existing_item.updated_at = datetime.now()

        # 如果是活跃状态，确保is_active为True
        if not existing_item.is_active:
            existing_item.is_active = True
            self.logger.info(f"重新激活饰品: {existing_item.market_hash_name}")

    def _create_new_item(self, item_info: ItemBaseInfo) -> Item:
        """
        创建新饰品对象

        Args:
            item_info: 饰品基础信息

        Returns:
            Item: 新创建的饰品对象
        """
        # 从market_hash_name解析基础信息
        weapon_type, skin_name = self._parse_weapon_info(item_info.market_hash_name)

        new_item = Item(
            market_hash_name=item_info.market_hash_name,
            name=item_info.name,
            platform_data=json.dumps(item_info.platform_data, ensure_ascii=False),
            data_source=item_info.data_source,
            last_sync_time=item_info.sync_time,
            weapon_type=weapon_type,
            skin_name=skin_name,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        return new_item

    def _parse_weapon_info(self, market_hash_name: str) -> tuple[str, str]:
        """
        从market_hash_name解析武器类型和皮肤名称

        Args:
            market_hash_name: 市场哈希名称

        Returns:
            tuple[str, str]: (武器类型, 皮肤名称)
        """
        try:
            # 解析格式: "AK-47 | Redline (Field-Tested)"
            if '|' in market_hash_name:
                weapon_part = market_hash_name.split('|')[0].strip()
                skin_part = market_hash_name.split('|')[1].strip()

                # 移除磨损等级部分
                if '(' in skin_part:
                    skin_name = skin_part.split('(')[0].strip()
                else:
                    skin_name = skin_part

                return weapon_part, skin_name
            else:
                # 如果没有|分隔符，可能是刀具或其他特殊饰品
                return market_hash_name, ""

        except Exception as e:
            self.logger.warning(f"解析武器信息失败 {market_hash_name}: {e}")
            return "", ""

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        获取数据库中饰品的统计信息

        Returns:
            Dict[str, Any]: 数据库统计信息
        """
        try:
            with self.db_manager.get_session() as session:
                # 总饰品数量
                total_items = session.query(Item).count()

                # 按数据源分组统计
                steamdt_items = session.query(Item).filter(Item.data_source == 'steamdt').count()

                # 活跃饰品数量
                active_items = session.query(Item).filter(Item.is_active == True).count()

                # 最近同步的饰品数量（24小时内）
                recent_sync_time = datetime.now() - timedelta(hours=24)
                recent_synced = session.query(Item).filter(
                    Item.last_sync_time >= recent_sync_time
                ).count()

                # 按武器类型统计
                weapon_stats = {}
                weapon_results = session.query(Item.weapon_type, func.count(Item.market_hash_name)).group_by(Item.weapon_type).all()
                for weapon_type, count in weapon_results:
                    if weapon_type:
                        weapon_stats[weapon_type] = count

                return {
                    'total_items': total_items,
                    'steamdt_items': steamdt_items,
                    'active_items': active_items,
                    'recent_synced_items': recent_synced,
                    'weapon_type_distribution': weapon_stats,
                    'last_check_time': datetime.now().isoformat()
                }

        except Exception as e:
            self.logger.error(f"获取数据库统计失败: {e}")
            return {
                'error': str(e),
                'last_check_time': datetime.now().isoformat()
            }

    def cleanup_old_items(self, days_threshold: int = 30) -> int:
        """
        清理长时间未同步的饰品数据

        Args:
            days_threshold: 天数阈值，超过此天数未同步的饰品将被标记为非活跃

        Returns:
            int: 被清理的饰品数量
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days_threshold)

            with self.db_manager.get_session() as session:
                # 查找长时间未同步的活跃饰品
                old_items = session.query(Item).filter(
                    Item.is_active == True,
                    Item.last_sync_time < cutoff_time
                ).all()

                cleanup_count = 0
                for item in old_items:
                    item.is_active = False
                    item.updated_at = datetime.now()
                    cleanup_count += 1

                self.logger.info(f"清理了{cleanup_count}个长时间未同步的饰品")
                return cleanup_count

        except Exception as e:
            self.logger.error(f"清理旧饰品数据失败: {e}")
            return 0

    async def start(self):
        """启动基础数据收集器服务"""
        if self.running:
            self.logger.warning("BaseDataCollector已经在运行中")
            return

        try:
            self.logger.info("启动BaseDataCollector服务")

            # 设置定时任务
            self._setup_scheduled_tasks()

            # 启动调度器
            self.scheduler.start()
            self.running = True

            self.logger.info("BaseDataCollector服务启动成功")

        except Exception as e:
            self.logger.error(f"启动BaseDataCollector服务失败: {e}", exc_info=True)
            self.running = False
            raise

    async def stop(self):
        """停止基础数据收集器服务"""
        self.logger.info("停止BaseDataCollector服务")

        self.running = False

        try:
            # 停止调度器
            if self.scheduler.running:
                self.scheduler.shutdown(wait=True)

            # 保存最终状态
            self._save_state()

            self.logger.info("BaseDataCollector服务已优雅停止")

        except Exception as e:
            self.logger.error(f"停止BaseDataCollector服务时出错: {e}")

    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        try:
            # 解析同步时间
            hour, minute = map(int, self.sync_time.split(':'))

            # 添加定时任务
            self.scheduler.add_job(
                self.collect_and_store_data,
                CronTrigger(hour=hour, minute=minute),
                id='base_data_sync',
                name='基础数据同步',
                max_instances=1,
                coalesce=True,
                misfire_grace_time=3600  # 1小时的容错时间
            )

            self.logger.info(f"已设置定时任务: 每日{self.sync_time}执行基础数据同步")

        except Exception as e:
            self.logger.error(f"设置定时任务失败: {e}")
            raise

    async def collect_and_store_data(self):
        """
        执行完整的数据收集和存储流程
        这是调度器调用的主要方法
        """
        try:
            self.logger.info("开始执行定时基础数据同步任务")

            # 执行数据收集
            result = await self.collect_base_data()

            if result.success:
                self.logger.info(
                    f"定时任务执行成功: 总计{result.total_items}个饰品, "
                    f"新增{result.new_items}个, 更新{result.updated_items}个, "
                    f"质量评分{result.data_quality_score:.2f}"
                )

                # 记录成功执行
                self._record_scheduled_execution(True, result)

            else:
                self.logger.error(f"定时任务执行失败: {result.error_message}")
                self._record_scheduled_execution(False, result)

        except Exception as e:
            self.logger.error(f"定时任务执行异常: {e}", exc_info=True)
            self._record_scheduled_execution(False, None, str(e))

    def _record_scheduled_execution(self, success: bool, result: Optional[BaseDataResult], error: Optional[str] = None):
        """记录定时任务执行结果"""
        try:
            execution_record = {
                'timestamp': datetime.now().isoformat(),
                'success': success,
                'error': error
            }

            if result:
                execution_record.update({
                    'total_items': result.total_items,
                    'new_items': result.new_items,
                    'updated_items': result.updated_items,
                    'data_quality_score': result.data_quality_score,
                    'sync_duration': result.sync_duration
                })

            # 保存到执行历史文件
            history_file = self.state_file.parent / 'scheduled_execution_history.json'

            # 读取现有历史
            history = []
            if history_file.exists():
                try:
                    with open(history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except Exception:
                    history = []

            # 添加新记录
            history.append(execution_record)

            # 保留最近100条记录
            if len(history) > 100:
                history = history[-100:]

            # 保存历史
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.warning(f"记录定时任务执行历史失败: {e}")

    async def trigger_manual_sync(self, force: bool = False) -> BaseDataResult:
        """
        手动触发数据同步

        Args:
            force: 是否强制执行，忽略API限制

        Returns:
            BaseDataResult: 同步结果
        """
        self.logger.info(f"手动触发数据同步 (force={force})")

        try:
            result = await self.collect_base_data(force_refresh=force)

            if result.success:
                self.logger.info(
                    f"手动同步成功: 总计{result.total_items}个饰品, "
                    f"新增{result.new_items}个, 更新{result.updated_items}个"
                )
            else:
                self.logger.warning(f"手动同步失败: {result.error_message}")

            return result

        except Exception as e:
            error_msg = f"手动同步异常: {e}"
            self.logger.error(error_msg, exc_info=True)
            return BaseDataResult(
                success=False,
                total_items=0,
                error_message=error_msg
            )

    def get_scheduler_status(self) -> Dict[str, Any]:
        """
        获取调度器状态信息

        Returns:
            Dict[str, Any]: 调度器状态
        """
        try:
            status = {
                'running': self.running,
                'scheduler_running': self.scheduler.running if hasattr(self.scheduler, 'running') else False,
                'sync_time': self.sync_time,
                'next_run_time': None,
                'jobs_count': 0
            }

            if self.scheduler.running:
                # 获取任务信息
                jobs = self.scheduler.get_jobs()
                status['jobs_count'] = len(jobs)

                # 获取基础数据同步任务的下次执行时间
                base_data_job = self.scheduler.get_job('base_data_sync')
                if base_data_job and base_data_job.next_run_time:
                    status['next_run_time'] = base_data_job.next_run_time.isoformat()

            return status

        except Exception as e:
            self.logger.error(f"获取调度器状态失败: {e}")
            return {
                'error': str(e),
                'running': self.running
            }

    def get_execution_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取定时任务执行历史

        Args:
            limit: 返回记录数量限制

        Returns:
            List[Dict[str, Any]]: 执行历史记录
        """
        try:
            history_file = self.state_file.parent / 'scheduled_execution_history.json'

            if not history_file.exists():
                return []

            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)

            # 返回最近的记录
            return history[-limit:] if limit > 0 else history

        except Exception as e:
            self.logger.error(f"获取执行历史失败: {e}")
            return []

    def update_sync_time(self, new_sync_time: str):
        """
        更新同步时间

        Args:
            new_sync_time: 新的同步时间，格式为 "HH:MM"
        """
        try:
            # 验证时间格式
            hour, minute = map(int, new_sync_time.split(':'))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                raise ValueError("时间格式无效")

            old_sync_time = self.sync_time
            self.sync_time = new_sync_time

            # 如果调度器正在运行，需要重新设置任务
            if self.running and self.scheduler.running:
                # 移除旧任务
                try:
                    self.scheduler.remove_job('base_data_sync')
                except:
                    pass

                # 添加新任务
                self._setup_scheduled_tasks()

            self.logger.info(f"同步时间已更新: {old_sync_time} -> {new_sync_time}")

        except Exception as e:
            self.logger.error(f"更新同步时间失败: {e}")
            raise
    
    def _parse_api_response(self, data: Dict[str, Any]) -> List[ItemBaseInfo]:
        """
        解析SteamDT API响应数据

        Args:
            data: API响应数据

        Returns:
            List[ItemBaseInfo]: 解析后的饰品基础信息列表
        """
        try:
            items = []

            # 检查API响应格式
            if not isinstance(data, dict) or not data.get('success', False):
                self.logger.error(f"API响应格式错误: {data}")
                return items

            # 获取数据列表
            data_list = data.get('data', [])
            if not isinstance(data_list, list):
                self.logger.error(f"API数据格式错误，期望列表: {type(data_list)}")
                return items

            # 解析每个饰品数据
            for item_data in data_list:
                try:
                    # 提取基础信息
                    name = item_data.get('name', '')
                    market_hash_name = item_data.get('marketHashName', '')

                    if not name or not market_hash_name:
                        self.logger.warning(f"饰品数据不完整，跳过: {item_data}")
                        continue

                    # 解析平台数据列表
                    platform_list = []
                    platform_list_data = item_data.get('platformList', [])

                    for platform_info in platform_list_data:
                        platform_name = platform_info.get('name', '')
                        item_id = platform_info.get('itemId', '')

                        if platform_name and item_id:
                            platform_list.append(PlatformInfo(
                                name=platform_name,
                                item_id=item_id
                            ))

                    # 创建饰品基础信息对象
                    item_info = ItemBaseInfo(
                        name=name,
                        market_hash_name=market_hash_name,
                        platform_list=platform_list,
                        data_source='steamdt',
                        sync_time=datetime.now()
                    )

                    items.append(item_info)

                except Exception as e:
                    self.logger.warning(f"解析单个饰品数据失败: {e}, 数据: {item_data}")
                    continue

            self.logger.info(f"成功解析{len(items)}个饰品的基础信息")
            return items

        except Exception as e:
            self.logger.error(f"解析API响应数据失败: {e}")
            return []
    
    def _validate_data_quality(self, items: List[ItemBaseInfo]) -> float:
        """
        验证数据质量并计算评分

        Args:
            items: 饰品基础信息列表

        Returns:
            float: 数据质量评分 (0-100)
        """
        if not items:
            self.logger.warning("数据质量验证: 没有数据需要验证")
            return 0.0

        validation_results = []
        total_score = 0.0
        valid_items = 0
        quality_issues = []

        self.logger.info(f"开始数据质量验证，共{len(items)}个饰品")

        for i, item in enumerate(items):
            item_validation = self._validate_single_item(item, i)
            validation_results.append(item_validation)

            total_score += item_validation['score']
            if item_validation['is_valid']:
                valid_items += 1
            else:
                quality_issues.extend(item_validation['issues'])

        # 计算整体质量评分
        quality_score = total_score / len(items)

        # 记录详细的验证结果
        self._log_validation_results(quality_score, valid_items, len(items), quality_issues)

        # 保存验证报告
        self._save_validation_report(validation_results, quality_score)

        return quality_score

    def _validate_single_item(self, item: ItemBaseInfo, index: int) -> Dict[str, Any]:
        """
        验证单个饰品的数据质量

        Args:
            item: 饰品基础信息
            index: 饰品在列表中的索引

        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            'index': index,
            'market_hash_name': item.market_hash_name,
            'score': 0.0,
            'is_valid': False,
            'issues': [],
            'checks': {}
        }

        score = 0.0

        # 1. 必要字段检查 (30分)
        name_check = self._check_required_fields(item)
        validation_result['checks']['required_fields'] = name_check
        if name_check['passed']:
            score += 30
        else:
            validation_result['issues'].extend(name_check['issues'])

        # 2. 平台数据检查 (25分)
        platform_check = self._check_platform_data(item)
        validation_result['checks']['platform_data'] = platform_check
        if platform_check['passed']:
            score += 25
        else:
            validation_result['issues'].extend(platform_check['issues'])

        # 3. 数据格式检查 (20分)
        format_check = self._check_data_format(item)
        validation_result['checks']['data_format'] = format_check
        if format_check['passed']:
            score += 20
        else:
            validation_result['issues'].extend(format_check['issues'])

        # 4. 平台ID格式检查 (15分)
        platform_id_check = self._check_platform_ids(item)
        validation_result['checks']['platform_ids'] = platform_id_check
        if platform_id_check['passed']:
            score += 15
        else:
            validation_result['issues'].extend(platform_id_check['issues'])

        # 5. 数据一致性检查 (10分)
        consistency_check = self._check_data_consistency(item)
        validation_result['checks']['data_consistency'] = consistency_check
        if consistency_check['passed']:
            score += 10
        else:
            validation_result['issues'].extend(consistency_check['issues'])

        validation_result['score'] = score
        validation_result['is_valid'] = score >= 70  # 70分以上认为是有效数据

        return validation_result

    def _check_required_fields(self, item: ItemBaseInfo) -> Dict[str, Any]:
        """检查必要字段"""
        issues = []

        if not item.name or not item.name.strip():
            issues.append("饰品名称为空")

        if not item.market_hash_name or not item.market_hash_name.strip():
            issues.append("market_hash_name为空")

        if not item.data_source:
            issues.append("数据源为空")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'details': f"必要字段检查: name={bool(item.name)}, market_hash_name={bool(item.market_hash_name)}"
        }

    def _check_platform_data(self, item: ItemBaseInfo) -> Dict[str, Any]:
        """检查平台数据"""
        issues = []

        if not item.platform_list:
            issues.append("平台数据列表为空")
        elif len(item.platform_list) == 0:
            issues.append("平台数据列表长度为0")
        else:
            # 检查每个平台数据
            for i, platform in enumerate(item.platform_list):
                if not platform.name:
                    issues.append(f"平台{i}名称为空")
                if not platform.item_id:
                    issues.append(f"平台{i}的itemId为空")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'details': f"平台数据检查: 平台数量={len(item.platform_list) if item.platform_list else 0}"
        }

    def _check_data_format(self, item: ItemBaseInfo) -> Dict[str, Any]:
        """检查数据格式"""
        issues = []

        # 检查market_hash_name格式
        if not self._validate_market_hash_name(item.market_hash_name):
            issues.append("market_hash_name格式不正确（应包含'|'分隔符）")

        # 检查名称长度
        if item.name and len(item.name) > 200:
            issues.append("饰品名称过长（超过200字符）")

        if item.market_hash_name and len(item.market_hash_name) > 300:
            issues.append("market_hash_name过长（超过300字符）")

        # 检查时间戳
        if not item.sync_time:
            issues.append("同步时间为空")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'details': f"格式检查: name_len={len(item.name) if item.name else 0}, hash_name_len={len(item.market_hash_name) if item.market_hash_name else 0}"
        }

    def _check_platform_ids(self, item: ItemBaseInfo) -> Dict[str, Any]:
        """检查平台ID格式"""
        issues = []

        if not item.platform_list:
            issues.append("无平台数据可检查")
            return {
                'passed': False,
                'issues': issues,
                'details': "平台ID检查: 无数据"
            }

        # 检查是否包含必要平台
        required_platforms = ['steam', 'buff163']
        platform_names = [platform.name.lower() for platform in item.platform_list if platform.name]

        found_platforms = []
        for required_platform in required_platforms:
            if required_platform in platform_names:
                found_platforms.append(required_platform)

        if not found_platforms:
            issues.append(f"缺少必要平台数据（需要: {', '.join(required_platforms)}）")

        # 检查平台ID格式
        for platform in item.platform_list:
            if platform.name and platform.item_id:
                if not self._validate_platform_item_id(platform.name, platform.item_id):
                    issues.append(f"平台{platform.name}的itemId格式不正确: {platform.item_id}")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'details': f"平台ID检查: 找到平台={found_platforms}"
        }

    def _check_data_consistency(self, item: ItemBaseInfo) -> Dict[str, Any]:
        """检查数据一致性"""
        issues = []

        # 检查名称一致性
        if item.name and item.market_hash_name:
            # 简单的一致性检查：中文名和英文名应该有某种关联
            if '|' in item.market_hash_name:
                weapon_part = item.market_hash_name.split('|')[0].strip()
                # 检查武器名称是否在中文名称中有对应
                if weapon_part and weapon_part not in item.name:
                    # 这是一个警告而不是错误，因为中英文名称可能差异较大
                    pass

        # 检查平台数据一致性
        if item.platform_list:
            platform_names = [p.name for p in item.platform_list]
            if len(platform_names) != len(set(platform_names)):
                issues.append("存在重复的平台数据")

        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'details': "数据一致性检查完成"
        }

    def _validate_platform_item_id(self, platform_name: str, item_id: str) -> bool:
        """
        验证特定平台的itemId格式

        Args:
            platform_name: 平台名称
            item_id: 平台itemId

        Returns:
            bool: 是否格式正确
        """
        if not item_id or not item_id.strip():
            return False

        platform_name_lower = platform_name.lower()

        # Steam平台ID通常是数字
        if platform_name_lower == 'steam':
            return item_id.isdigit() and len(item_id) > 0

        # BUFF163平台ID通常是数字
        elif platform_name_lower == 'buff163':
            return item_id.isdigit() and len(item_id) > 0

        # 其他平台的基本检查
        else:
            return len(item_id.strip()) > 0

    def _log_validation_results(self, quality_score: float, valid_items: int, total_items: int, quality_issues: List[str]):
        """记录验证结果日志"""
        success_rate = (valid_items / total_items) * 100 if total_items > 0 else 0

        self.logger.info(
            f"数据质量验证完成: "
            f"评分={quality_score:.2f}, "
            f"有效数据={valid_items}/{total_items} ({success_rate:.1f}%), "
            f"问题数量={len(quality_issues)}"
        )

        # 记录质量等级
        if quality_score >= 90:
            quality_level = "优秀"
        elif quality_score >= 80:
            quality_level = "良好"
        elif quality_score >= 70:
            quality_level = "一般"
        elif quality_score >= 60:
            quality_level = "较差"
        else:
            quality_level = "很差"

        self.logger.info(f"数据质量等级: {quality_level}")

        # 如果有质量问题，记录前10个问题
        if quality_issues:
            self.logger.warning(f"发现{len(quality_issues)}个数据质量问题")
            for i, issue in enumerate(quality_issues[:10]):
                self.logger.warning(f"质量问题 {i+1}: {issue}")

            if len(quality_issues) > 10:
                self.logger.warning(f"还有{len(quality_issues) - 10}个问题未显示...")

    def _save_validation_report(self, validation_results: List[Dict[str, Any]], quality_score: float):
        """保存验证报告到文件"""
        try:
            report_file = self.state_file.parent / 'data_quality_report.json'

            report = {
                'timestamp': datetime.now().isoformat(),
                'quality_score': quality_score,
                'total_items': len(validation_results),
                'valid_items': sum(1 for r in validation_results if r['is_valid']),
                'validation_results': validation_results,
                'summary': self._generate_validation_summary(validation_results)
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            self.logger.debug(f"数据质量报告已保存到: {report_file}")

        except Exception as e:
            self.logger.warning(f"保存数据质量报告失败: {e}")

    def _generate_validation_summary(self, validation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成验证摘要"""
        if not validation_results:
            return {}

        # 统计各类检查的通过率
        check_types = ['required_fields', 'platform_data', 'data_format', 'platform_ids', 'data_consistency']
        check_summary = {}

        for check_type in check_types:
            passed_count = sum(1 for r in validation_results if r['checks'].get(check_type, {}).get('passed', False))
            total_count = len(validation_results)
            check_summary[check_type] = {
                'passed': passed_count,
                'total': total_count,
                'rate': (passed_count / total_count) * 100 if total_count > 0 else 0
            }

        # 统计问题类型
        all_issues = []
        for result in validation_results:
            all_issues.extend(result['issues'])

        issue_types = {}
        for issue in all_issues:
            issue_types[issue] = issue_types.get(issue, 0) + 1

        return {
            'check_summary': check_summary,
            'common_issues': dict(sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:10])
        }

    def _validate_market_hash_name(self, name: str) -> bool:
        """验证market_hash_name格式"""
        if not name or not name.strip():
            return False

        # CS2饰品的market_hash_name通常包含'|'分隔符
        # 格式如: "AK-47 | Redline (Field-Tested)"
        return '|' in name and len(name.strip()) > 0

    def _validate_platform_ids(self, platform_list: List[PlatformInfo]) -> bool:
        """验证平台ID格式（保持向后兼容）"""
        if not platform_list:
            return False

        required_platforms = ['steam', 'buff163']
        platform_names = [platform.name.lower() for platform in platform_list if platform.name]
        return any(platform in platform_names for platform in required_platforms)

    def get_latest_quality_report(self) -> Optional[Dict[str, Any]]:
        """
        获取最新的数据质量报告

        Returns:
            Optional[Dict[str, Any]]: 质量报告，如果不存在则返回None
        """
        try:
            report_file = self.state_file.parent / 'data_quality_report.json'

            if not report_file.exists():
                return None

            with open(report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)

            return report

        except Exception as e:
            self.logger.error(f"读取数据质量报告失败: {e}")
            return None

    def validate_items_before_storage(self, items: List[ItemBaseInfo], min_quality_score: float = 70.0) -> List[ItemBaseInfo]:
        """
        在存储前验证并过滤数据

        Args:
            items: 原始饰品数据列表
            min_quality_score: 最低质量评分要求

        Returns:
            List[ItemBaseInfo]: 过滤后的有效数据列表
        """
        if not items:
            return []

        self.logger.info(f"开始验证{len(items)}个饰品，最低质量要求: {min_quality_score}")

        valid_items = []
        filtered_count = 0

        for item in items:
            item_validation = self._validate_single_item(item, len(valid_items))

            if item_validation['score'] >= min_quality_score:
                valid_items.append(item)
            else:
                filtered_count += 1
                self.logger.debug(
                    f"过滤低质量数据: {item.market_hash_name}, "
                    f"评分: {item_validation['score']:.1f}, "
                    f"问题: {', '.join(item_validation['issues'][:3])}"
                )

        self.logger.info(
            f"数据验证完成: 保留{len(valid_items)}个, 过滤{filtered_count}个低质量数据"
        )

        return valid_items

    def get_quality_statistics(self) -> Dict[str, Any]:
        """
        获取数据质量统计信息

        Returns:
            Dict[str, Any]: 质量统计信息
        """
        try:
            # 获取最新质量报告
            latest_report = self.get_latest_quality_report()

            if not latest_report:
                return {
                    'status': 'no_report',
                    'message': '暂无数据质量报告'
                }

            # 从数据库获取统计信息
            db_stats = self.get_database_statistics()

            return {
                'status': 'available',
                'latest_report_time': latest_report.get('timestamp'),
                'quality_score': latest_report.get('quality_score', 0),
                'total_items_validated': latest_report.get('total_items', 0),
                'valid_items': latest_report.get('valid_items', 0),
                'database_total_items': db_stats.get('total_items', 0),
                'database_active_items': db_stats.get('active_items', 0),
                'summary': latest_report.get('summary', {}),
                'quality_level': self._get_quality_level(latest_report.get('quality_score', 0))
            }

        except Exception as e:
            self.logger.error(f"获取质量统计失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    def _get_quality_level(self, score: float) -> str:
        """根据评分获取质量等级"""
        if score >= 90:
            return "优秀"
        elif score >= 80:
            return "良好"
        elif score >= 70:
            return "一般"
        elif score >= 60:
            return "较差"
        else:
            return "很差"
    
    def _load_state(self):
        """加载状态信息"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                # 加载基础状态
                last_update_str = state_data.get('last_update')
                if last_update_str:
                    self.last_update = datetime.fromisoformat(last_update_str)

                # 加载API调用统计
                self.api_call_count = state_data.get('api_call_count', 0)
                self.api_success_count = state_data.get('api_success_count', 0)
                self.api_failure_count = state_data.get('api_failure_count', 0)
                self.total_items_collected = state_data.get('total_items_collected', 0)

                self.logger.info(
                    f"加载状态成功，上次更新: {self.last_update}, "
                    f"API调用统计: {self.api_success_count}/{self.api_call_count}"
                )
        except Exception as e:
            self.logger.warning(f"加载状态失败: {e}")

    def _save_state(self):
        """保存状态信息"""
        try:
            state_data = {
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'updated_at': datetime.now().isoformat(),
                'api_call_count': self.api_call_count,
                'api_success_count': self.api_success_count,
                'api_failure_count': self.api_failure_count,
                'total_items_collected': self.total_items_collected
            }

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)

            self.logger.debug("状态保存成功")
        except Exception as e:
            self.logger.warning(f"保存状态失败: {e}")

    @staticmethod
    def parse_steamdt_response(response_data: Dict[str, Any]) -> SteamDTAPIResponse:
        """
        解析SteamDT API标准响应格式

        Args:
            response_data: API原始响应数据

        Returns:
            SteamDTAPIResponse: 标准化的响应对象
        """
        return SteamDTAPIResponse(
            success=response_data.get('success', False),
            data=response_data.get('data'),
            error_code=response_data.get('errorCode', 0),
            error_msg=response_data.get('errorMsg', ''),
            error_data=response_data.get('errorData', {}),
            error_code_str=response_data.get('errorCodeStr', '')
        )

    @staticmethod
    def parse_price_data(price_response: Dict[str, Any]) -> List[PriceData]:
        """
        解析价格数据响应

        Args:
            price_response: 价格API响应数据

        Returns:
            List[PriceData]: 价格数据列表
        """
        price_list = []

        if not isinstance(price_response, dict):
            return price_list

        data_list = price_response.get('data', [])
        if not isinstance(data_list, list):
            return price_list

        for item in data_list:
            price_data = PriceData(
                platform=item.get('platform', ''),
                platform_item_id=item.get('platformItemId', ''),
                sell_price=item.get('sellPrice'),
                sell_count=item.get('sellCount'),
                bidding_price=item.get('biddingPrice'),
                bidding_count=item.get('biddingCount'),
                update_time=item.get('updateTime')
            )
            price_list.append(price_data)

        return price_list

    @staticmethod
    def parse_batch_price_data(batch_response: Dict[str, Any]) -> List[ItemPriceInfo]:
        """
        解析批量价格数据响应

        Args:
            batch_response: 批量价格API响应数据

        Returns:
            List[ItemPriceInfo]: 饰品价格信息列表
        """
        item_price_list = []

        if not isinstance(batch_response, dict):
            return item_price_list

        data_list = batch_response.get('data', [])
        if not isinstance(data_list, list):
            return item_price_list

        for item in data_list:
            market_hash_name = item.get('marketHashName', '')
            data_list_raw = item.get('dataList', [])

            price_data_list = []
            for price_item in data_list_raw:
                price_data = PriceData(
                    platform=price_item.get('platform', ''),
                    platform_item_id=price_item.get('platformItemId', ''),
                    sell_price=price_item.get('sellPrice'),
                    sell_count=price_item.get('sellCount'),
                    bidding_price=price_item.get('biddingPrice'),
                    bidding_count=price_item.get('biddingCount'),
                    update_time=price_item.get('updateTime')
                )
                price_data_list.append(price_data)

            item_price_info = ItemPriceInfo(
                market_hash_name=market_hash_name,
                data_list=price_data_list
            )
            item_price_list.append(item_price_info)

        return item_price_list


# 全局基础数据收集器实例
_base_data_collector: Optional[BaseDataCollector] = None


def get_base_data_collector() -> BaseDataCollector:
    """获取全局基础数据收集器实例"""
    global _base_data_collector

    if _base_data_collector is None:
        _base_data_collector = BaseDataCollector()

    return _base_data_collector


async def start_base_data_collector():
    """启动基础数据收集器服务"""
    collector = get_base_data_collector()
    await collector.start()
    return collector


async def stop_base_data_collector():
    """停止基础数据收集器服务"""
    global _base_data_collector

    if _base_data_collector and _base_data_collector.running:
        await _base_data_collector.stop()


def is_base_data_collector_running() -> bool:
    """检查基础数据收集器是否正在运行"""
    global _base_data_collector

    return _base_data_collector is not None and _base_data_collector.running
