"""
套利机会检测演示脚本
展示跨平台价格比较和套利机会识别功能
"""

import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Mock配置管理器
with patch('services.price_comparator.get_config_manager') as mock_config1, \
     patch('services.arbitrage_detector.get_config_manager') as mock_config2:
    mock_config1.return_value.get.return_value = None
    mock_config2.return_value.get.return_value = 5.0

    from services.price_comparator import PlatformPriceComparator, Platform, PriceComparison
    from services.arbitrage_detector import ArbitrageDetector, ArbitrageOpportunity, ArbitrageRiskLevel
    from services.steamdt_api import ItemPriceData, PlatformPrice


def create_demo_price_data():
    """创建演示用的价格数据"""
    demo_items = [
        {
            'name': 'AK-47 | Redline (Field-Tested)',
            'platforms': [
                PlatformPrice(
                    platform='steam',
                    platform_item_id='123456',
                    sell_price=120.0,
                    sell_count=45,
                    bidding_price=115.0,
                    bidding_count=20,
                    update_time=1625097600
                ),
                PlatformPrice(
                    platform='buff',
                    platform_item_id='789012',
                    sell_price=95.0,
                    sell_count=80,
                    bidding_price=90.0,
                    bidding_count=35,
                    update_time=1625097600
                ),
                PlatformPrice(
                    platform='c5game',
                    platform_item_id='345678',
                    sell_price=105.0,
                    sell_count=25,
                    bidding_price=100.0,
                    bidding_count=15,
                    update_time=1625097600
                )
            ]
        },
        {
            'name': 'AWP | Asiimov (Field-Tested)',
            'platforms': [
                PlatformPrice(
                    platform='steam',
                    platform_item_id='234567',
                    sell_price=85.0,
                    sell_count=30,
                    bidding_price=80.0,
                    bidding_count=12,
                    update_time=1625097600
                ),
                PlatformPrice(
                    platform='buff',
                    platform_item_id='890123',
                    sell_price=78.0,
                    sell_count=60,
                    bidding_price=75.0,
                    bidding_count=25,
                    update_time=1625097600
                ),
                PlatformPrice(
                    platform='igxe',
                    platform_item_id='456789',
                    sell_price=82.0,
                    sell_count=20,
                    bidding_price=79.0,
                    bidding_count=8,
                    update_time=1625097600
                )
            ]
        },
        {
            'name': 'Karambit | Fade (Factory New)',
            'platforms': [
                PlatformPrice(
                    platform='steam',
                    platform_item_id='345678',
                    sell_price=1800.0,
                    sell_count=5,
                    bidding_price=1750.0,
                    bidding_count=2,
                    update_time=1625097600
                ),
                PlatformPrice(
                    platform='buff',
                    platform_item_id='901234',
                    sell_price=1650.0,
                    sell_count=8,
                    bidding_price=1600.0,
                    bidding_count=3,
                    update_time=1625097600
                )
            ]
        }
    ]
    
    return demo_items


def demonstrate_price_comparison():
    """演示价格比较功能"""
    print("💰 跨平台价格比较演示")
    print("=" * 50)
    
    # 创建价格比较器
    comparator = PlatformPriceComparator()
    
    # 获取演示数据
    demo_items = create_demo_price_data()
    
    comparisons = []
    
    for item_data in demo_items:
        item_name = item_data['name']
        platform_prices = item_data['platforms']
        
        print(f"\n🎯 分析饰品: {item_name}")
        print("-" * 40)
        
        # 解析平台价格
        parsed_prices = comparator._parse_platform_prices(platform_prices)
        
        if len(parsed_prices) < 2:
            print("❌ 平台数据不足，无法比较")
            continue
        
        # 显示各平台价格
        print("📊 各平台价格:")
        for platform, price_data in parsed_prices.items():
            print(f"   {platform.value:8}: 售价 ¥{price_data.sell_price:7.2f} | 求购 ¥{price_data.bidding_price:7.2f} | 交易量 {price_data.sell_count + price_data.bidding_count:3d}")
        
        # 计算价差
        price_spread = comparator._calculate_price_spread(parsed_prices)
        print(f"\n📈 价差分析:")
        print(f"   最低价格: ¥{price_spread['min_price']:.2f}")
        print(f"   最高价格: ¥{price_spread['max_price']:.2f}")
        print(f"   平均价格: ¥{price_spread['avg_price']:.2f}")
        print(f"   绝对价差: ¥{price_spread['absolute_spread']:.2f}")
        print(f"   相对价差: {price_spread['relative_spread_percent']:.1f}%")
        
        # 寻找最佳套利机会
        best_buy, best_sell, max_profit = comparator._find_best_arbitrage_opportunity(parsed_prices)
        
        if best_buy and best_sell and max_profit > 0:
            buy_price = parsed_prices[best_buy].sell_price
            sell_price = parsed_prices[best_sell].sell_price
            
            print(f"\n🎯 最佳套利机会:")
            print(f"   买入平台: {best_buy.value} (¥{buy_price:.2f})")
            print(f"   卖出平台: {best_sell.value} (¥{sell_price:.2f})")
            print(f"   毛利润: ¥{sell_price - buy_price:.2f}")
            print(f"   净利润率: {max_profit:.1f}%")
            
            # 创建价格比较结果
            comparison = PriceComparison(
                item_name=item_name.split(' | ')[0],
                market_hash_name=item_name,
                platform_prices=parsed_prices,
                price_spread=price_spread,
                best_buy_platform=best_buy,
                best_sell_platform=best_sell,
                max_profit_margin=max_profit,
                comparison_time=datetime.now()
            )
            comparisons.append(comparison)
        else:
            print("\n❌ 未发现有利可图的套利机会")
    
    return comparisons


def demonstrate_arbitrage_detection(comparisons):
    """演示套利机会检测"""
    print("\n\n🔍 套利机会检测演示")
    print("=" * 50)
    
    # 创建套利检测器
    detector = ArbitrageDetector()
    
    opportunities = []
    
    for comparison in comparisons:
        print(f"\n🎯 检测饰品: {comparison.item_name}")
        print("-" * 40)
        
        if comparison.max_profit_margin < detector.arbitrage_config.min_profit_margin:
            print(f"❌ 利润率 {comparison.max_profit_margin:.1f}% 低于最小要求 {detector.arbitrage_config.min_profit_margin:.1f}%")
            continue
        
        # 模拟套利机会分析
        buy_platform = comparison.best_buy_platform
        sell_platform = comparison.best_sell_platform
        
        buy_price_data = comparison.platform_prices[buy_platform]
        sell_price_data = comparison.platform_prices[sell_platform]
        
        buy_price = buy_price_data.sell_price
        sell_price = sell_price_data.sell_price
        
        # 计算利润
        gross_profit = sell_price - buy_price
        net_profit = detector._calculate_net_profit(buy_platform, sell_platform, buy_price, sell_price)
        profit_margin = (net_profit / buy_price) * 100 if buy_price > 0 else 0
        
        # 评估风险和流动性
        risk_level = detector._assess_risk_level(comparison, buy_price_data, sell_price_data)
        liquidity_score = detector._calculate_liquidity_score(buy_price_data, sell_price_data)
        time_to_profit = detector._estimate_time_to_profit(buy_platform, sell_platform)
        confidence_score = detector._calculate_confidence_score(comparison, liquidity_score, risk_level)
        
        print(f"💰 财务分析:")
        print(f"   买入价格: ¥{buy_price:.2f} ({buy_platform.value})")
        print(f"   卖出价格: ¥{sell_price:.2f} ({sell_platform.value})")
        print(f"   毛利润: ¥{gross_profit:.2f}")
        print(f"   净利润: ¥{net_profit:.2f}")
        print(f"   利润率: {profit_margin:.1f}%")
        
        print(f"\n📊 风险评估:")
        print(f"   风险等级: {risk_level.value}")
        print(f"   流动性评分: {liquidity_score:.1f}")
        print(f"   预计获利时间: {time_to_profit} 天")
        print(f"   置信度评分: {confidence_score:.1f}")
        
        # 生成投资建议
        if confidence_score >= 80:
            recommendation = "🌟 强烈推荐"
        elif confidence_score >= 70:
            recommendation = "👍 推荐"
        elif confidence_score >= 60:
            recommendation = "🤔 谨慎考虑"
        else:
            recommendation = "❌ 不推荐"
        
        print(f"\n🎯 投资建议: {recommendation}")
        
        # 创建套利机会
        opportunity = ArbitrageOpportunity(
            item_name=comparison.item_name,
            market_hash_name=comparison.market_hash_name,
            buy_platform=buy_platform,
            sell_platform=sell_platform,
            buy_price=buy_price,
            sell_price=sell_price,
            gross_profit=gross_profit,
            net_profit=net_profit,
            profit_margin_percent=profit_margin,
            risk_level=risk_level,
            liquidity_score=liquidity_score,
            time_to_profit_days=time_to_profit,
            confidence_score=confidence_score,
            reasons=[f"利润率{profit_margin:.1f}%", f"流动性评分{liquidity_score:.1f}", f"风险等级{risk_level.value}"],
            detected_at=datetime.now()
        )
        
        # 验证套利机会
        if detector._is_valid_opportunity(opportunity):
            opportunities.append(opportunity)
            print("✅ 套利机会有效")
        else:
            print("❌ 套利机会无效")
    
    return opportunities


def demonstrate_platform_fees():
    """演示平台手续费计算"""
    print("\n\n💳 平台手续费演示")
    print("=" * 50)
    
    comparator = PlatformPriceComparator()
    
    test_price = 100.0
    
    print(f"基准价格: ¥{test_price:.2f}")
    print("\n各平台手续费对比:")
    
    for platform, fees in comparator.platform_fees.items():
        buy_cost = fees.calculate_buy_cost(test_price)
        sell_revenue = fees.calculate_sell_cost(test_price)
        
        print(f"\n{platform.value.upper()}:")
        print(f"   买入成本: ¥{buy_cost:.2f} (手续费率: {fees.buy_fee_rate*100:.1f}%)")
        print(f"   卖出收益: ¥{sell_revenue:.2f} (手续费率: {(fees.sell_fee_rate + fees.withdraw_fee_rate)*100:.1f}%)")
        print(f"   提现时间: {fees.withdraw_time_days} 天")
        print(f"   最小提现: ¥{fees.min_withdraw_amount:.2f}")


def main():
    """主函数"""
    print("🚀 启动CS2饰品套利机会检测演示")
    print()
    
    try:
        # 演示价格比较
        comparisons = demonstrate_price_comparison()
        
        # 演示套利检测
        opportunities = demonstrate_arbitrage_detection(comparisons)
        
        # 演示平台手续费
        demonstrate_platform_fees()
        
        # 总结
        print("\n\n📋 演示总结")
        print("=" * 30)
        print(f"分析饰品数: 3")
        print(f"发现价差: {len(comparisons)}")
        print(f"有效套利机会: {len(opportunities)}")
        
        if opportunities:
            print("\n🏆 最佳套利机会:")
            best_opportunity = max(opportunities, key=lambda x: x.confidence_score)
            print(f"   饰品: {best_opportunity.item_name}")
            print(f"   利润率: {best_opportunity.profit_margin_percent:.1f}%")
            print(f"   置信度: {best_opportunity.confidence_score:.1f}")
            print(f"   风险等级: {best_opportunity.risk_level.value}")
        
        print("\n✅ 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
