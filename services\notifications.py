"""
Ares通知服务扩展
为预警系统提供专门的通知功能，支持消息模板和频率控制
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from core.notifications import get_notification_service, NotificationType
from core.config import get_config_manager

logger = logging.getLogger(__name__)


class MessageTemplate(Enum):
    """消息模板类型"""
    PRICE_OPPORTUNITY = "price_opportunity"
    PRICE_RISK = "price_risk"
    VOLUME_SPIKE = "volume_spike"
    PORTFOLIO_REBALANCE = "portfolio_rebalance"
    HOLDING_RISK = "holding_risk"
    MARKET_ANOMALY = "market_anomaly"


@dataclass
class NotificationTemplate:
    """通知模板"""
    template_id: str
    title_template: str
    content_template: str
    emoji: str = "🔔"
    
    def format(self, data: Dict[str, Any]) -> Dict[str, str]:
        """格式化模板"""
        try:
            title = self.title_template.format(**data)
            content = self.content_template.format(**data)
            return {
                'title': f"{self.emoji} {title}",
                'content': content
            }
        except KeyError as e:
            logger.error(f"Template formatting error: missing key {e}")
            return {
                'title': f"{self.emoji} 预警通知",
                'content': f"预警数据: {data}"
            }


class AlertNotificationService:
    """预警通知服务"""
    
    def __init__(self):
        """初始化预警通知服务"""
        self.notification_service = get_notification_service()
        try:
            self.config = get_config_manager()
        except Exception:
            self.config = None
        
        # 通知频率控制
        self.rate_limits = {
            'low': 3600,      # 低级预警：1小时最多1次
            'medium': 1800,   # 中级预警：30分钟最多1次
            'high': 600,      # 高级预警：10分钟最多1次
            'critical': 300   # 严重预警：5分钟最多1次
        }
        
        # 最后发送时间记录
        self.last_sent = {}
        
        # 初始化消息模板
        self.templates = self._init_templates()
    
    def _init_templates(self) -> Dict[str, NotificationTemplate]:
        """初始化消息模板"""
        templates = {}
        
        # 价格机会模板
        templates[MessageTemplate.PRICE_OPPORTUNITY.value] = NotificationTemplate(
            template_id="price_opportunity",
            title_template="价格机会 - {item_name}",
            content_template="""
💰 **投资机会发现**

**饰品**: {item_name}
**当前价格**: ${current_price:.2f}
**价格变化**: {price_change_percent:+.1f}%
**24小时交易量**: {volume_24h}
**建议**: 考虑买入

*时间*: {timestamp}
""",
            emoji="🟢"
        )
        
        # 价格风险模板
        templates[MessageTemplate.PRICE_RISK.value] = NotificationTemplate(
            template_id="price_risk",
            title_template="价格风险 - {item_name}",
            content_template="""
⚠️ **价格风险预警**

**饰品**: {item_name}
**当前价格**: ${current_price:.2f}
**价格变化**: {price_change_percent:+.1f}%
**持仓数量**: {holding_quantity}
**持仓价值**: ${holding_value:.2f}
**建议**: 考虑止损

*时间*: {timestamp}
""",
            emoji="🔴"
        )
        
        # 交易量激增模板
        templates[MessageTemplate.VOLUME_SPIKE.value] = NotificationTemplate(
            template_id="volume_spike",
            title_template="交易量激增 - {item_name}",
            content_template="""
📈 **交易量异常**

**饰品**: {item_name}
**当前价格**: ${current_price:.2f}
**24小时交易量**: {volume_24h}
**交易量变化**: {volume_change_percent:+.1f}%
**建议**: 关注市场动向

*时间*: {timestamp}
""",
            emoji="📊"
        )
        
        # 投资组合再平衡模板
        templates[MessageTemplate.PORTFOLIO_REBALANCE.value] = NotificationTemplate(
            template_id="portfolio_rebalance",
            title_template="投资组合再平衡提醒",
            content_template="""
⚖️ **投资组合提醒**

**当前偏离度**: {portfolio_deviation:.1f}%
**总价值**: ${total_value:.2f}
**建议操作**: {rebalance_suggestion}

*时间*: {timestamp}
""",
            emoji="⚖️"
        )
        
        # 持仓风险模板
        templates[MessageTemplate.HOLDING_RISK.value] = NotificationTemplate(
            template_id="holding_risk",
            title_template="持仓风险 - {item_name}",
            content_template="""
⚠️ **持仓风险提醒**

**饰品**: {item_name}
**风险等级**: {risk_level}
**持仓数量**: {holding_quantity}
**当前价值**: ${current_value:.2f}
**建议**: {risk_suggestion}

*时间*: {timestamp}
""",
            emoji="⚠️"
        )
        
        # 市场异常模板
        templates[MessageTemplate.MARKET_ANOMALY.value] = NotificationTemplate(
            template_id="market_anomaly",
            title_template="市场异常 - {item_name}",
            content_template="""
🚨 **市场异常检测**

**饰品**: {item_name}
**异常类型**: {anomaly_type}
**异常程度**: {anomaly_score:.2f}
**建议**: 密切关注

*时间*: {timestamp}
""",
            emoji="🚨"
        )
        
        return templates
    
    async def send_alert_notification(
        self,
        template_type: str,
        data: Dict[str, Any],
        severity: str = "medium",
        channels: List[str] = None
    ) -> bool:
        """发送预警通知"""
        try:
            # 检查频率限制
            if not self._check_rate_limit(template_type, severity):
                logger.info(f"Rate limit exceeded for {template_type} with severity {severity}")
                return False
            
            # 获取模板
            template = self.templates.get(template_type)
            if not template:
                logger.error(f"Template not found: {template_type}")
                return False
            
            # 添加时间戳
            data['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 格式化消息
            formatted = template.format(data)
            
            # 确定通知类型
            notification_type = self._get_notification_type(severity)
            
            # 发送通知
            result = await self.notification_service.send_notification(
                title=formatted['title'],
                content=formatted['content'],
                type=notification_type,
                metadata={
                    'template_type': template_type,
                    'severity': severity,
                    'alert_data': data
                },
                channels=channels
            )
            
            # 更新发送时间
            self._update_last_sent(template_type, severity)
            
            return any(result.values()) if result else False
        
        except Exception as e:
            logger.error(f"Failed to send alert notification: {str(e)}")
            return False
    
    def _check_rate_limit(self, template_type: str, severity: str) -> bool:
        """检查频率限制"""
        rate_limit_key = f"{template_type}_{severity}"
        rate_limit_seconds = self.rate_limits.get(severity, 1800)
        
        last_sent_time = self.last_sent.get(rate_limit_key)
        if last_sent_time:
            time_since_last = (datetime.now() - last_sent_time).total_seconds()
            return time_since_last >= rate_limit_seconds
        
        return True
    
    def _update_last_sent(self, template_type: str, severity: str):
        """更新最后发送时间"""
        rate_limit_key = f"{template_type}_{severity}"
        self.last_sent[rate_limit_key] = datetime.now()
    
    def _get_notification_type(self, severity: str) -> NotificationType:
        """根据严重性获取通知类型"""
        severity_map = {
            'low': NotificationType.INFO,
            'medium': NotificationType.INFO,
            'high': NotificationType.WARNING,
            'critical': NotificationType.ERROR
        }
        return severity_map.get(severity, NotificationType.INFO)
    
    async def send_price_opportunity_alert(self, item_data: Dict[str, Any]) -> bool:
        """发送价格机会预警"""
        return await self.send_alert_notification(
            MessageTemplate.PRICE_OPPORTUNITY.value,
            item_data,
            severity="medium"
        )
    
    async def send_price_risk_alert(self, item_data: Dict[str, Any]) -> bool:
        """发送价格风险预警"""
        return await self.send_alert_notification(
            MessageTemplate.PRICE_RISK.value,
            item_data,
            severity="high"
        )
    
    async def send_volume_spike_alert(self, item_data: Dict[str, Any]) -> bool:
        """发送交易量激增预警"""
        return await self.send_alert_notification(
            MessageTemplate.VOLUME_SPIKE.value,
            item_data,
            severity="medium"
        )
    
    async def send_portfolio_rebalance_alert(self, portfolio_data: Dict[str, Any]) -> bool:
        """发送投资组合再平衡预警"""
        return await self.send_alert_notification(
            MessageTemplate.PORTFOLIO_REBALANCE.value,
            portfolio_data,
            severity="low"
        )
    
    async def send_holding_risk_alert(self, holding_data: Dict[str, Any]) -> bool:
        """发送持仓风险预警"""
        return await self.send_alert_notification(
            MessageTemplate.HOLDING_RISK.value,
            holding_data,
            severity="high"
        )
    
    async def send_market_anomaly_alert(self, anomaly_data: Dict[str, Any]) -> bool:
        """发送市场异常预警"""
        return await self.send_alert_notification(
            MessageTemplate.MARKET_ANOMALY.value,
            anomaly_data,
            severity="critical"
        )
    
    async def send_test_notification(self, template_type: str, test_data: Dict[str, Any] = None) -> bool:
        """发送测试通知"""
        if test_data is None:
            test_data = self._get_test_data(template_type)
        
        return await self.send_alert_notification(
            template_type,
            test_data,
            severity="low"
        )
    
    def _get_test_data(self, template_type: str) -> Dict[str, Any]:
        """获取测试数据"""
        test_data_map = {
            MessageTemplate.PRICE_OPPORTUNITY.value: {
                'item_name': 'AK-47 | Redline (Field-Tested)',
                'current_price': 45.67,
                'price_change_percent': -18.5,
                'volume_24h': 156,
                'item_id': 'test_item_001'
            },
            MessageTemplate.PRICE_RISK.value: {
                'item_name': 'AWP | Dragon Lore (Factory New)',
                'current_price': 2850.00,
                'price_change_percent': -22.3,
                'holding_quantity': 2,
                'holding_value': 5700.00,
                'item_id': 'test_item_002'
            },
            MessageTemplate.VOLUME_SPIKE.value: {
                'item_name': 'Karambit | Fade (Factory New)',
                'current_price': 1250.00,
                'volume_24h': 89,
                'volume_change_percent': 450.0,
                'item_id': 'test_item_003'
            },
            MessageTemplate.PORTFOLIO_REBALANCE.value: {
                'portfolio_deviation': 12.5,
                'total_value': 15000.00,
                'rebalance_suggestion': '建议减少高风险饰品配置'
            },
            MessageTemplate.HOLDING_RISK.value: {
                'item_name': 'M4A4 | Howl (Factory New)',
                'risk_level': '高风险',
                'holding_quantity': 1,
                'current_value': 3200.00,
                'risk_suggestion': '考虑部分止盈',
                'item_id': 'test_item_004'
            },
            MessageTemplate.MARKET_ANOMALY.value: {
                'item_name': 'Butterfly Knife | Crimson Web (Minimal Wear)',
                'anomaly_type': '价格异常波动',
                'anomaly_score': 8.7,
                'item_id': 'test_item_005'
            }
        }
        
        return test_data_map.get(template_type, {
            'item_name': 'Test Item',
            'current_price': 100.00,
            'item_id': 'test_item_default'
        })
    
    def get_template_list(self) -> List[Dict[str, str]]:
        """获取模板列表"""
        return [
            {
                'id': template.template_id,
                'name': template.template_id.replace('_', ' ').title(),
                'emoji': template.emoji
            }
            for template in self.templates.values()
        ]
    
    def get_rate_limit_status(self) -> Dict[str, Any]:
        """获取频率限制状态"""
        status = {}
        current_time = datetime.now()
        
        for key, last_sent_time in self.last_sent.items():
            template_type, severity = key.split('_', 1)
            rate_limit_seconds = self.rate_limits.get(severity, 1800)
            
            time_since_last = (current_time - last_sent_time).total_seconds()
            remaining_time = max(0, rate_limit_seconds - time_since_last)
            
            status[key] = {
                'template_type': template_type,
                'severity': severity,
                'last_sent': last_sent_time.isoformat(),
                'remaining_cooldown': remaining_time,
                'can_send': remaining_time == 0
            }
        
        return status


# 全局预警通知服务实例
_alert_notification_service: Optional[AlertNotificationService] = None


def get_alert_notification_service() -> AlertNotificationService:
    """获取全局预警通知服务实例"""
    global _alert_notification_service
    if _alert_notification_service is None:
        _alert_notification_service = AlertNotificationService()
    return _alert_notification_service
