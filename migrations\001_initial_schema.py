"""
初始数据库架构迁移
创建所有基础表和索引
"""

from datetime import datetime
from sqlalchemy import text


def upgrade(connection):
    """升级数据库架构"""
    
    # 创建饰品表
    connection.execute(text("""
        CREATE TABLE IF NOT EXISTS items (
            item_id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            category VARCHAR(50),
            rarity VARCHAR(20),
            float_value FLOAT,
            market_hash_name VARCHAR(300),
            icon_url VARCHAR(500),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    """))
    
    # 创建价格表
    connection.execute(text("""
        CREATE TABLE IF NOT EXISTS prices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id VARCHAR(50) NOT NULL,
            platform VARCHAR(20) NOT NULL,
            ask_price FLOAT,
            bid_price FLOAT,
            last_price FLOAT,
            volume_24h INTEGER DEFAULT 0,
            volume_7d INTEGER DEFAULT 0,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_quality VARCHAR(10) DEFAULT 'good',
            FOREIGN KEY (item_id) REFERENCES items (item_id),
            CONSTRAINT ck_ask_price_positive CHECK (ask_price >= 0),
            CONSTRAINT ck_bid_price_positive CHECK (bid_price >= 0)
        )
    """))
    
    # 创建监控列表表
    connection.execute(text("""
        CREATE TABLE IF NOT EXISTS watchlist_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id VARCHAR(50) NOT NULL,
            pool_type VARCHAR(20) NOT NULL,
            priority_score FLOAT DEFAULT 0.0,
            last_updated DATETIME,
            update_frequency INTEGER,
            next_update_time DATETIME,
            user_added BOOLEAN DEFAULT 0,
            user_priority INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            failure_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES items (item_id),
            CONSTRAINT ck_pool_type_valid CHECK (pool_type IN ('core', 'main'))
        )
    """))
    
    # 创建持仓表
    connection.execute(text("""
        CREATE TABLE IF NOT EXISTS holdings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id VARCHAR(50) NOT NULL,
            quantity INTEGER NOT NULL,
            cost_basis FLOAT NOT NULL,
            purchase_date DATETIME NOT NULL,
            purchase_platform VARCHAR(20),
            target_price FLOAT,
            stop_loss_price FLOAT,
            notes TEXT,
            purchase_reason VARCHAR(100),
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES items (item_id),
            CONSTRAINT ck_quantity_positive CHECK (quantity > 0),
            CONSTRAINT ck_cost_basis_positive CHECK (cost_basis >= 0)
        )
    """))
    
    # 创建预警规则表
    connection.execute(text("""
        CREATE TABLE IF NOT EXISTS alert_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            rule_name VARCHAR(100) NOT NULL,
            rule_type VARCHAR(20) NOT NULL,
            conditions TEXT NOT NULL,
            actions TEXT NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            priority INTEGER DEFAULT 0,
            trigger_count INTEGER DEFAULT 0,
            last_triggered DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT ck_rule_type_valid CHECK (rule_type IN ('price_change', 'volume_spike', 'spread_opportunity', 'portfolio_risk'))
        )
    """))
    
    # 创建宏观数据表
    connection.execute(text("""
        CREATE TABLE IF NOT EXISTS macro_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            metric_name VARCHAR(50) NOT NULL,
            metric_value FLOAT NOT NULL,
            metric_unit VARCHAR(20),
            data_source VARCHAR(50),
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """))
    
    print("基础表创建完成")


def create_indexes(connection):
    """创建索引"""
    
    # 饰品表索引
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_item_category_rarity ON items (category, rarity)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_item_updated_at ON items (updated_at)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_item_name ON items (name)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_item_market_hash ON items (market_hash_name)"))
    
    # 价格表索引 - 关键性能优化
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_price_item_timestamp ON prices (item_id, timestamp)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_price_platform_timestamp ON prices (platform, timestamp)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_price_timestamp ON prices (timestamp)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_price_item_platform ON prices (item_id, platform)"))
    
    # 监控列表索引 - 调度器性能优化
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_watchlist_pool_priority ON watchlist_items (pool_type, priority_score)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_watchlist_next_update ON watchlist_items (next_update_time)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_watchlist_last_updated ON watchlist_items (last_updated)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_watchlist_user_added ON watchlist_items (user_added)"))
    
    # 持仓表索引
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_holding_item_active ON holdings (item_id, is_active)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_holding_purchase_date ON holdings (purchase_date)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_holding_cost_basis ON holdings (cost_basis)"))
    
    # 预警规则索引
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_alert_rule_type_active ON alert_rules (rule_type, is_active)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_alert_priority ON alert_rules (priority)"))
    
    # 宏观数据索引
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_macro_metric_timestamp ON macro_stats (metric_name, timestamp)"))
    connection.execute(text("CREATE INDEX IF NOT EXISTS idx_macro_timestamp ON macro_stats (timestamp)"))
    
    print("索引创建完成")


def create_unique_constraints(connection):
    """创建唯一约束"""
    
    # 价格表唯一约束
    connection.execute(text("""
        CREATE UNIQUE INDEX IF NOT EXISTS uq_price_item_platform_time 
        ON prices (item_id, platform, timestamp)
    """))
    
    # 监控列表唯一约束
    connection.execute(text("""
        CREATE UNIQUE INDEX IF NOT EXISTS uq_watchlist_item_pool 
        ON watchlist_items (item_id, pool_type)
    """))
    
    # 宏观数据唯一约束
    connection.execute(text("""
        CREATE UNIQUE INDEX IF NOT EXISTS uq_macro_metric_time 
        ON macro_stats (metric_name, timestamp)
    """))
    
    print("唯一约束创建完成")


def downgrade(connection):
    """降级数据库架构"""
    
    # 删除表（注意顺序，先删除有外键的表）
    tables = [
        'macro_stats',
        'alert_rules', 
        'holdings',
        'watchlist_items',
        'prices',
        'items'
    ]
    
    for table in tables:
        connection.execute(text(f"DROP TABLE IF EXISTS {table}"))
    
    print("数据库架构已回滚")


def run_migration(connection, direction='upgrade'):
    """运行迁移"""
    if direction == 'upgrade':
        print("开始升级数据库架构...")
        upgrade(connection)
        create_indexes(connection)
        create_unique_constraints(connection)
        print("数据库架构升级完成")
    elif direction == 'downgrade':
        print("开始降级数据库架构...")
        downgrade(connection)
        print("数据库架构降级完成")
    else:
        raise ValueError(f"不支持的迁移方向: {direction}")


if __name__ == '__main__':
    import sys
    import os
    from pathlib import Path
    
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    from core.database import DatabaseManager
    
    # 默认数据库URL
    database_url = os.getenv('DATABASE_URL', 'sqlite:///data/ares.db')
    
    # 创建数据库管理器
    db_manager = DatabaseManager(database_url)
    
    # 获取迁移方向
    direction = sys.argv[1] if len(sys.argv) > 1 else 'upgrade'
    
    # 运行迁移
    with db_manager.engine.connect() as connection:
        run_migration(connection, direction)
        connection.commit()
    
    print(f"迁移完成: {direction}")
