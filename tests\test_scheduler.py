"""
智能调度器测试
验证调度算法、优先级计算和池管理功能
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.scheduler import IntelligentScheduler, ScheduleTask, SchedulerState
from core.priority_calculator import (
    PriorityCalculator, PriorityFactors, PriorityWeights, 
    get_priority_calculator, calculate_priority_score
)
from core.database import DatabaseManager, WatchlistItem
from core.api_manager import APIManager, APIResponse, APICallResult


class MockDatabaseManager:
    """模拟数据库管理器"""
    
    def __init__(self):
        self.watchlist_items = []
        self.prices = []
    
    async def get_session(self):
        """模拟会话上下文管理器"""
        class MockSession:
            def __enter__(self):
                return self
            def __exit__(self, *args):
                pass
            async def __aenter__(self):
                return self
            async def __aexit__(self, *args):
                pass
        return MockSession()
    
    async def get_active_watchlist_items(self, session):
        return [item for item in self.watchlist_items if item.is_active]
    
    async def get_watchlist_items_by_priority(self, session):
        return sorted(
            [item for item in self.watchlist_items if item.is_active],
            key=lambda x: x.priority_score or 0,
            reverse=True
        )
    
    async def get_watchlist_item(self, session, item_id):
        for item in self.watchlist_items:
            if item.item_id == item_id:
                return item
        return None
    
    async def update_watchlist_priority(self, session, item_id, priority_score):
        for item in self.watchlist_items:
            if item.item_id == item_id:
                item.priority_score = priority_score
                break
    
    async def get_latest_price(self, session, item_id):
        return None  # 简化实现
    
    async def get_price_history(self, session, item_id, days=7):
        return []  # 简化实现


class MockAPIManager:
    """模拟API管理器"""
    
    def __init__(self):
        self.call_count = 0
        self.success_rate = 0.9
    
    async def get_item_price(self, item_id):
        self.call_count += 1
        
        # 模拟90%成功率
        import random
        if random.random() < self.success_rate:
            return APIResponse(
                status=APICallResult.SUCCESS,
                data={"item_id": item_id, "price": 100.0},
                response_time=0.5
            )
        else:
            return APIResponse(
                status=APICallResult.ERROR,
                error_message="API call failed",
                response_time=1.0
            )


class TestPriorityCalculator:
    """优先级计算器测试"""
    
    def test_priority_calculator_initialization(self):
        """测试优先级计算器初始化"""
        calculator = PriorityCalculator()
        
        assert calculator.weights is not None
        assert calculator.weights.spread_weight == 0.4
        assert calculator.weights.volume_weight == 0.3
        assert calculator.weights.volatility_weight == 0.2
        assert calculator.weights.user_interest_weight == 0.1
    
    def test_calculate_priority_score(self):
        """测试优先级评分计算"""
        calculator = PriorityCalculator()
        
        # 测试高优先级因子
        high_priority_factors = PriorityFactors(
            price_spread=25.0,  # 高价差
            volume_24h=500,     # 高交易量
            volatility=15.0,    # 高波动率
            user_added=True,    # 用户添加
            user_priority=8     # 高用户优先级
        )
        
        high_score = calculator.calculate_priority_score(high_priority_factors)
        assert high_score > 1.5  # 应该是高分
        
        # 测试低优先级因子
        low_priority_factors = PriorityFactors(
            price_spread=1.0,   # 低价差
            volume_24h=10,      # 低交易量
            volatility=2.0,     # 低波动率
            user_added=False,   # 非用户添加
            user_priority=0     # 无用户优先级
        )
        
        low_score = calculator.calculate_priority_score(low_priority_factors)
        assert low_score < high_score  # 低分应该小于高分
    
    def test_spread_score_calculation(self):
        """测试价差评分计算"""
        calculator = PriorityCalculator()
        
        # 测试不同价差的评分
        assert calculator._calculate_spread_score(0) == 0.0
        assert calculator._calculate_spread_score(25) > calculator._calculate_spread_score(10)
        assert calculator._calculate_spread_score(50) <= 1.0  # 不应超过1.0
    
    def test_volume_score_calculation(self):
        """测试交易量评分计算"""
        calculator = PriorityCalculator()
        
        # 测试不同交易量的评分
        assert calculator._calculate_volume_score(0) == 0.0
        assert calculator._calculate_volume_score(500) > calculator._calculate_volume_score(100)
        assert calculator._calculate_volume_score(1000) <= 1.0  # 不应超过1.0
    
    def test_user_interest_score(self):
        """测试用户兴趣评分"""
        calculator = PriorityCalculator()
        
        assert calculator._calculate_user_interest_score(True) == 1.0
        assert calculator._calculate_user_interest_score(False) == 0.0
    
    def test_pool_priority_thresholds(self):
        """测试池优先级阈值"""
        calculator = PriorityCalculator()
        
        assert calculator.get_pool_priority_threshold('core') > calculator.get_pool_priority_threshold('main')
        assert calculator.get_pool_priority_threshold('unknown') == 1.0
    
    def test_pool_promotion_demotion(self):
        """测试池升降级判断"""
        calculator = PriorityCalculator()
        
        # 测试提升到核心池
        assert calculator.should_promote_to_core(8.0, 'main') == True
        assert calculator.should_promote_to_core(5.0, 'main') == False
        assert calculator.should_promote_to_core(8.0, 'core') == False
        
        # 测试从核心池降级
        assert calculator.should_demote_from_core(5.0, 'core') == True
        assert calculator.should_demote_from_core(8.0, 'core') == False
        assert calculator.should_demote_from_core(5.0, 'main') == False
    
    def test_batch_priority_calculation(self):
        """测试批量优先级计算"""
        calculator = PriorityCalculator()
        
        items_data = [
            {
                'item_id': 'item1',
                'price_spread': 20.0,
                'volume_24h': 300,
                'volatility': 10.0,
                'user_added': True
            },
            {
                'item_id': 'item2',
                'price_spread': 5.0,
                'volume_24h': 50,
                'volatility': 3.0,
                'user_added': False
            }
        ]
        
        priorities = calculator.calculate_batch_priorities(items_data)
        
        assert 'item1' in priorities
        assert 'item2' in priorities
        assert priorities['item1'] > priorities['item2']
    
    def test_weights_update(self):
        """测试权重更新"""
        calculator = PriorityCalculator()
        
        new_weights = PriorityWeights(
            spread_weight=0.5,
            volume_weight=0.3,
            volatility_weight=0.1,
            user_interest_weight=0.1
        )
        
        calculator.update_weights(new_weights)
        
        assert calculator.weights.spread_weight == 0.5
        assert calculator.weights.volume_weight == 0.3


class TestScheduleTask:
    """调度任务测试"""
    
    def test_schedule_task_creation(self):
        """测试调度任务创建"""
        task = ScheduleTask(
            item_id="test_item",
            pool_type="core",
            priority_score=8.5,
            next_update_time=datetime.utcnow(),
            update_frequency=30
        )
        
        assert task.item_id == "test_item"
        assert task.pool_type == "core"
        assert task.priority_score == 8.5
        assert task.update_frequency == 30
        assert task.retry_count == 0
    
    def test_schedule_task_comparison(self):
        """测试调度任务比较（用于优先队列）"""
        now = datetime.utcnow()
        
        task1 = ScheduleTask("item1", "core", 8.0, now, 30)
        task2 = ScheduleTask("item2", "main", 6.0, now + timedelta(minutes=10), 240)
        task3 = ScheduleTask("item3", "core", 9.0, now, 30)
        
        # 时间相同时，优先级高的优先
        assert task3 < task1
        
        # 时间不同时，时间早的优先
        assert task1 < task2


class TestSchedulerState:
    """调度器状态测试"""
    
    def test_scheduler_state_initialization(self):
        """测试调度器状态初始化"""
        state = SchedulerState()
        
        assert state.last_core_update is None
        assert state.last_main_update is None
        assert isinstance(state.core_pool_items, set)
        assert isinstance(state.main_pool_items, set)
        assert state.total_api_calls_today == 0
        assert state.last_reset_date is None


class TestIntelligentScheduler:
    """智能调度器测试"""
    
    @pytest.fixture
    def mock_scheduler(self):
        """创建模拟调度器"""
        db_manager = MockDatabaseManager()
        api_manager = MockAPIManager()
        
        # 添加一些测试数据
        db_manager.watchlist_items = [
            type('WatchlistItem', (), {
                'item_id': 'item1',
                'pool_type': 'core',
                'priority_score': 8.0,
                'last_updated': datetime.utcnow() - timedelta(hours=1),
                'update_frequency': 30,
                'is_active': True,
                'user_added': True,
                'user_priority': 8,
                'failure_count': 0
            })(),
            type('WatchlistItem', (), {
                'item_id': 'item2',
                'pool_type': 'main',
                'priority_score': 5.0,
                'last_updated': datetime.utcnow() - timedelta(hours=2),
                'update_frequency': 240,
                'is_active': True,
                'user_added': False,
                'user_priority': 0,
                'failure_count': 0
            })()
        ]
        
        scheduler = IntelligentScheduler(db_manager, api_manager)
        return scheduler
    
    def test_scheduler_initialization(self, mock_scheduler):
        """测试调度器初始化"""
        assert mock_scheduler.db_manager is not None
        assert mock_scheduler.api_manager is not None
        assert mock_scheduler.priority_calculator is not None
        assert mock_scheduler.core_pool_size == 30
        assert mock_scheduler.main_pool_size == 970
        assert not mock_scheduler.running
    
    @pytest.mark.asyncio
    async def test_add_remove_item_from_schedule(self, mock_scheduler):
        """测试添加和移除调度项"""
        # 添加项目
        await mock_scheduler.add_item_to_schedule("test_item", "core", 7.5)
        
        # 检查是否添加成功
        assert len(mock_scheduler.task_queue) > 0
        
        # 查找添加的任务
        added_task = None
        for task in mock_scheduler.task_queue:
            if task.item_id == "test_item":
                added_task = task
                break
        
        assert added_task is not None
        assert added_task.pool_type == "core"
        assert added_task.priority_score == 7.5
        
        # 移除项目
        await mock_scheduler.remove_item_from_schedule("test_item")
        
        # 检查是否移除成功
        remaining_tasks = [task for task in mock_scheduler.task_queue if task.item_id == "test_item"]
        assert len(remaining_tasks) == 0
    
    def test_api_limit_check(self, mock_scheduler):
        """测试API限制检查"""
        # 重置状态
        mock_scheduler.state.total_api_calls_today = 0
        mock_scheduler.state.last_reset_date = datetime.utcnow().date().isoformat()
        
        # 应该允许API调用
        result = asyncio.run(mock_scheduler._check_api_limit())
        assert result == True
        
        # 设置接近限制
        mock_scheduler.state.total_api_calls_today = 14000  # 接近每日限制
        result = asyncio.run(mock_scheduler._check_api_limit())
        assert result == True
        
        # 超过限制
        mock_scheduler.state.total_api_calls_today = 15000  # 超过每日限制
        result = asyncio.run(mock_scheduler._check_api_limit())
        assert result == False
    
    def test_state_persistence(self, mock_scheduler):
        """测试状态持久化"""
        # 设置一些状态
        mock_scheduler.state.total_api_calls_today = 100
        mock_scheduler.state.last_reset_date = "2025-01-17"
        mock_scheduler.state.core_pool_items.add("item1")
        mock_scheduler.state.main_pool_items.add("item2")
        
        # 保存状态
        mock_scheduler._save_state()
        
        # 检查文件是否存在
        assert mock_scheduler.state_file.exists()
        
        # 创建新的调度器并加载状态
        new_scheduler = IntelligentScheduler(mock_scheduler.db_manager, mock_scheduler.api_manager)
        
        # 验证状态是否正确加载
        assert new_scheduler.state.total_api_calls_today == 100
        assert new_scheduler.state.last_reset_date == "2025-01-17"
        assert "item1" in new_scheduler.state.core_pool_items
        assert "item2" in new_scheduler.state.main_pool_items
    
    def test_get_schedule_status(self, mock_scheduler):
        """测试获取调度状态"""
        status = mock_scheduler.get_schedule_status()
        
        assert 'running' in status
        assert 'queue_size' in status
        assert 'api_calls_today' in status
        assert 'core_pool_size' in status
        assert 'main_pool_size' in status
        
        assert status['running'] == False
        assert isinstance(status['queue_size'], int)
        assert isinstance(status['api_calls_today'], int)


def test_global_priority_calculator():
    """测试全局优先级计算器"""
    calculator1 = get_priority_calculator()
    calculator2 = get_priority_calculator()
    
    # 应该返回同一个实例
    assert calculator1 is calculator2


def test_calculate_priority_score_function():
    """测试便捷优先级计算函数"""
    item_data = {
        'price_spread': 15.0,
        'volume_24h': 200,
        'volatility': 8.0,
        'user_added': True,
        'user_priority': 5
    }
    
    score = calculate_priority_score(item_data)
    assert isinstance(score, float)
    assert 0.0 <= score <= 10.0


if __name__ == "__main__":
    # 运行基本测试
    print("运行智能调度器基本测试...")
    
    # 测试优先级计算器
    print("测试优先级计算器...")
    calculator = PriorityCalculator()
    
    factors = PriorityFactors(
        price_spread=20.0,
        volume_24h=300,
        volatility=10.0,
        user_added=True,
        user_priority=7
    )
    
    score = calculator.calculate_priority_score(factors)
    assert 0.0 <= score <= 10.0
    print(f"✓ 优先级计算测试通过，评分: {score:.2f}")
    
    # 测试调度任务
    print("测试调度任务...")
    task = ScheduleTask(
        item_id="test_item",
        pool_type="core",
        priority_score=8.5,
        next_update_time=datetime.utcnow(),
        update_frequency=30
    )
    
    assert task.item_id == "test_item"
    assert task.pool_type == "core"
    print("✓ 调度任务测试通过")
    
    # 测试调度器状态
    print("测试调度器状态...")
    state = SchedulerState()
    assert isinstance(state.core_pool_items, set)
    assert isinstance(state.main_pool_items, set)
    print("✓ 调度器状态测试通过")
    
    print("所有智能调度器测试通过！")
