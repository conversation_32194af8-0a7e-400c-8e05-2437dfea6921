"""
分层监控核心逻辑
实现30个核心关注池和970个主监控池的差异化监控策略
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Set, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

from core.database import DatabaseManager, Item, Price, get_database_manager
from services.steamdt_api import get_steamdt_api_manager, ItemPriceData
from services.web_scraper import get_web_scraper, RankingItem
from services.api_budget import get_api_budget_manager, APIEndpoint
from core.config import get_config_manager
from core.exceptions import SchedulerError, ErrorContext


class PoolType(Enum):
    """监控池类型"""
    CORE = "core"      # 核心关注池
    MAIN = "main"      # 主监控池


@dataclass
class MonitoringItem:
    """监控项目"""
    item_id: str
    market_hash_name: str
    pool_type: PoolType
    priority_score: float
    last_update: Optional[datetime] = None
    update_count: int = 0
    error_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_id': self.item_id,
            'market_hash_name': self.market_hash_name,
            'pool_type': self.pool_type.value,
            'priority_score': self.priority_score,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'update_count': self.update_count,
            'error_count': self.error_count
        }


@dataclass
class MonitoringStats:
    """监控统计"""
    core_pool_size: int = 0
    main_pool_size: int = 0
    total_updates_today: int = 0
    successful_updates: int = 0
    failed_updates: int = 0
    last_core_update: Optional[datetime] = None
    last_main_update: Optional[datetime] = None
    api_calls_used: int = 0
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        total = self.successful_updates + self.failed_updates
        return (self.successful_updates / total * 100) if total > 0 else 0.0


class TieredMonitoringScheduler:
    """分层监控调度器"""
    
    def __init__(self):
        """初始化分层监控调度器"""
        self.config = get_config_manager()
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.core_pool_size = 30
        self.main_pool_size = 970
        self.core_update_interval = 30  # 30分钟
        self.main_update_interval = 240  # 4小时
        
        # 监控池
        self.core_pool: Dict[str, MonitoringItem] = {}
        self.main_pool: Dict[str, MonitoringItem] = {}
        
        # 统计信息
        self.stats = MonitoringStats()
        
        # 状态管理
        self.running = False
        self.state_file = Path("data/tiered_monitoring_state.json")
        
        # 加载持久化状态
        self._load_state()
        
        self.logger.info("Tiered Monitoring Scheduler initialized")
    
    def _load_state(self):
        """加载持久化状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 恢复核心池
                if 'core_pool' in data:
                    for item_data in data['core_pool']:
                        item = MonitoringItem(
                            item_id=item_data['item_id'],
                            market_hash_name=item_data['market_hash_name'],
                            pool_type=PoolType(item_data['pool_type']),
                            priority_score=item_data['priority_score'],
                            last_update=datetime.fromisoformat(item_data['last_update']) if item_data['last_update'] else None,
                            update_count=item_data.get('update_count', 0),
                            error_count=item_data.get('error_count', 0)
                        )
                        self.core_pool[item.item_id] = item
                
                # 恢复主池
                if 'main_pool' in data:
                    for item_data in data['main_pool']:
                        item = MonitoringItem(
                            item_id=item_data['item_id'],
                            market_hash_name=item_data['market_hash_name'],
                            pool_type=PoolType(item_data['pool_type']),
                            priority_score=item_data['priority_score'],
                            last_update=datetime.fromisoformat(item_data['last_update']) if item_data['last_update'] else None,
                            update_count=item_data.get('update_count', 0),
                            error_count=item_data.get('error_count', 0)
                        )
                        self.main_pool[item.item_id] = item
                
                # 恢复统计信息
                if 'stats' in data:
                    stats_data = data['stats']
                    self.stats = MonitoringStats(
                        core_pool_size=stats_data.get('core_pool_size', 0),
                        main_pool_size=stats_data.get('main_pool_size', 0),
                        total_updates_today=stats_data.get('total_updates_today', 0),
                        successful_updates=stats_data.get('successful_updates', 0),
                        failed_updates=stats_data.get('failed_updates', 0),
                        last_core_update=datetime.fromisoformat(stats_data['last_core_update']) if stats_data.get('last_core_update') else None,
                        last_main_update=datetime.fromisoformat(stats_data['last_main_update']) if stats_data.get('last_main_update') else None,
                        api_calls_used=stats_data.get('api_calls_used', 0)
                    )
                
                self.logger.info("Tiered monitoring state loaded successfully")
                
        except Exception as e:
            self.logger.error(f"Error loading tiered monitoring state: {e}")
    
    def _save_state(self):
        """保存持久化状态"""
        try:
            # 确保目录存在
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'core_pool': [item.to_dict() for item in self.core_pool.values()],
                'main_pool': [item.to_dict() for item in self.main_pool.values()],
                'stats': asdict(self.stats),
                'last_save_time': datetime.now().isoformat()
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("Tiered monitoring state saved")
            
        except Exception as e:
            self.logger.error(f"Error saving tiered monitoring state: {e}")
    
    async def start(self):
        """启动分层监控"""
        if self.running:
            self.logger.warning("Tiered monitoring is already running")
            return
        
        self.running = True
        self.logger.info("Starting tiered monitoring scheduler")
        
        try:
            # 初始化监控池
            await self._initialize_pools()
            
            # 启动监控循环
            await self._monitoring_loop()
            
        except Exception as e:
            self.logger.error(f"Error in tiered monitoring: {e}")
            self.running = False
            raise SchedulerError(f"Tiered monitoring failed to start: {e}")
    
    async def stop(self):
        """停止分层监控"""
        self.logger.info("Stopping tiered monitoring scheduler")
        self.running = False
        self._save_state()
    
    async def _initialize_pools(self):
        """初始化监控池"""
        self.logger.info("Initializing monitoring pools...")
        
        # 如果池为空，需要从排行榜数据初始化
        if not self.core_pool and not self.main_pool:
            await self._initialize_from_rankings()
        
        # 更新统计信息
        self.stats.core_pool_size = len(self.core_pool)
        self.stats.main_pool_size = len(self.main_pool)
        
        self.logger.info(f"Monitoring pools initialized: Core={self.stats.core_pool_size}, Main={self.stats.main_pool_size}")
    
    async def _initialize_from_rankings(self):
        """从排行榜数据初始化监控池"""
        self.logger.info("Initializing pools from ranking data...")
        
        try:
            # 获取web爬取器
            scraper = await get_web_scraper()
            
            # 爬取各种排行榜数据
            all_items = []
            ranking_types = ['hot', 'trending', 'rising', 'falling']
            
            for ranking_type in ranking_types:
                try:
                    items = await scraper.get_ranking_data(ranking_type, limit=100)
                    all_items.extend(items)
                    self.logger.info(f"Scraped {len(items)} items from {ranking_type} ranking")
                except Exception as e:
                    self.logger.error(f"Error scraping {ranking_type} ranking: {e}")
            
            # 去重并按优先级排序
            unique_items = self._deduplicate_and_score(all_items)
            
            # 分配到核心池和主池
            self._allocate_to_pools(unique_items)
            
        except Exception as e:
            self.logger.error(f"Error initializing from rankings: {e}")
            # 如果爬取失败，创建空池
            self.core_pool = {}
            self.main_pool = {}
    
    def _deduplicate_and_score(self, ranking_items: List[RankingItem]) -> List[Tuple[RankingItem, float]]:
        """去重并计算优先级评分"""
        # 使用market_hash_name去重
        unique_items = {}
        
        for item in ranking_items:
            key = item.market_hash_name
            if key not in unique_items:
                unique_items[key] = item
            else:
                # 如果已存在，选择排名更高的
                if item.rank < unique_items[key].rank:
                    unique_items[key] = item
        
        # 计算优先级评分
        scored_items = []
        for item in unique_items.values():
            score = self._calculate_priority_score(item)
            scored_items.append((item, score))
        
        # 按评分排序
        scored_items.sort(key=lambda x: x[1], reverse=True)
        
        return scored_items
    
    def _calculate_priority_score(self, item: RankingItem) -> float:
        """计算优先级评分"""
        score = 0.0
        
        # 排名因子（排名越高分数越高）
        rank_score = max(0, 100 - item.rank)
        score += rank_score * 0.3
        
        # 价格因子（价格适中的更有投资价值）
        if 10 <= item.current_price <= 1000:
            price_score = 50
        elif 1 <= item.current_price < 10:
            price_score = 30
        elif 1000 < item.current_price <= 5000:
            price_score = 40
        else:
            price_score = 10
        score += price_score * 0.2
        
        # 价格变化因子（有变化的更值得关注）
        change_score = min(abs(item.price_change_percent), 50)
        score += change_score * 0.2
        
        # 交易量因子（交易量高的流动性好）
        if item.volume_24h >= 100:
            volume_score = 50
        elif item.volume_24h >= 50:
            volume_score = 30
        elif item.volume_24h >= 10:
            volume_score = 20
        else:
            volume_score = 10
        score += volume_score * 0.3
        
        return score
    
    def _allocate_to_pools(self, scored_items: List[Tuple[RankingItem, float]]):
        """分配到监控池"""
        # 前30个分配到核心池
        for i, (item, score) in enumerate(scored_items[:self.core_pool_size]):
            monitoring_item = MonitoringItem(
                item_id=f"core_{i+1}_{item.market_hash_name}",
                market_hash_name=item.market_hash_name,
                pool_type=PoolType.CORE,
                priority_score=score
            )
            self.core_pool[monitoring_item.item_id] = monitoring_item
        
        # 接下来的970个分配到主池
        start_idx = self.core_pool_size
        end_idx = start_idx + self.main_pool_size
        
        for i, (item, score) in enumerate(scored_items[start_idx:end_idx]):
            monitoring_item = MonitoringItem(
                item_id=f"main_{i+1}_{item.market_hash_name}",
                market_hash_name=item.market_hash_name,
                pool_type=PoolType.MAIN,
                priority_score=score
            )
            self.main_pool[monitoring_item.item_id] = monitoring_item
        
        self.logger.info(f"Allocated {len(self.core_pool)} items to core pool, {len(self.main_pool)} items to main pool")
    
    async def _monitoring_loop(self):
        """主监控循环"""
        while self.running:
            try:
                current_time = datetime.now()
                
                # 检查核心池更新
                if self._should_update_core_pool(current_time):
                    await self._update_core_pool()
                
                # 检查主池更新
                if self._should_update_main_pool(current_time):
                    await self._update_main_pool()
                
                # 定期保存状态
                self._save_state()
                
                # 休眠
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(300)  # 出错时等待5分钟
    
    def _should_update_core_pool(self, current_time: datetime) -> bool:
        """检查是否应该更新核心池"""
        if not self.stats.last_core_update:
            return True
        
        time_diff = current_time - self.stats.last_core_update
        return time_diff.total_seconds() >= (self.core_update_interval * 60)
    
    def _should_update_main_pool(self, current_time: datetime) -> bool:
        """检查是否应该更新主池"""
        if not self.stats.last_main_update:
            return True
        
        time_diff = current_time - self.stats.last_main_update
        return time_diff.total_seconds() >= (self.main_update_interval * 60)
    
    async def _update_core_pool(self):
        """更新核心池"""
        self.logger.info("Updating core pool...")
        
        budget_manager = get_api_budget_manager()
        api_manager = await get_steamdt_api_manager()
        
        updated_count = 0
        failed_count = 0
        
        for item in self.core_pool.values():
            try:
                # 检查API预算
                if not budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "core"):
                    self.logger.warning("Core pool update stopped: API budget exhausted")
                    break
                
                # 获取价格数据
                start_time = datetime.now()
                price_data = await api_manager.get_item_price(item.market_hash_name)
                response_time = (datetime.now() - start_time).total_seconds()
                
                if price_data:
                    # 记录成功的API调用
                    budget_manager.record_call(
                        APIEndpoint.PRICE_SINGLE, "core", True, response_time
                    )
                    
                    # 更新数据库
                    await self._save_price_data(item, price_data)
                    
                    # 更新监控项状态
                    item.last_update = datetime.now()
                    item.update_count += 1
                    
                    updated_count += 1
                    self.stats.successful_updates += 1
                    
                else:
                    # 记录失败的API调用
                    budget_manager.record_call(
                        APIEndpoint.PRICE_SINGLE, "core", False, response_time, "No price data returned"
                    )
                    
                    item.error_count += 1
                    failed_count += 1
                    self.stats.failed_updates += 1
                
                # 避免请求过快
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error updating core pool item {item.item_id}: {e}")
                item.error_count += 1
                failed_count += 1
                self.stats.failed_updates += 1
        
        self.stats.last_core_update = datetime.now()
        self.stats.total_updates_today += updated_count
        
        self.logger.info(f"Core pool update completed: {updated_count} updated, {failed_count} failed")
    
    async def _update_main_pool(self):
        """更新主池（分批轮换）"""
        self.logger.info("Updating main pool...")
        
        budget_manager = get_api_budget_manager()
        api_manager = await get_steamdt_api_manager()
        
        # 计算本次更新的批次大小
        remaining_budget = budget_manager.get_remaining_budget("main")
        batch_size = min(50, remaining_budget, len(self.main_pool))  # 每次最多50个
        
        if batch_size <= 0:
            self.logger.warning("Main pool update skipped: No API budget remaining")
            return
        
        # 选择最需要更新的项目
        items_to_update = self._select_main_pool_items_for_update(batch_size)
        
        updated_count = 0
        failed_count = 0
        
        for item in items_to_update:
            try:
                # 检查API预算
                if not budget_manager.can_make_call(APIEndpoint.PRICE_SINGLE, "main"):
                    break
                
                # 获取价格数据
                start_time = datetime.now()
                price_data = await api_manager.get_item_price(item.market_hash_name)
                response_time = (datetime.now() - start_time).total_seconds()
                
                if price_data:
                    budget_manager.record_call(
                        APIEndpoint.PRICE_SINGLE, "main", True, response_time
                    )
                    
                    await self._save_price_data(item, price_data)
                    
                    item.last_update = datetime.now()
                    item.update_count += 1
                    
                    updated_count += 1
                    self.stats.successful_updates += 1
                    
                else:
                    budget_manager.record_call(
                        APIEndpoint.PRICE_SINGLE, "main", False, response_time, "No price data returned"
                    )
                    
                    item.error_count += 1
                    failed_count += 1
                    self.stats.failed_updates += 1
                
                # 避免请求过快
                await asyncio.sleep(2)
                
            except Exception as e:
                self.logger.error(f"Error updating main pool item {item.item_id}: {e}")
                item.error_count += 1
                failed_count += 1
                self.stats.failed_updates += 1
        
        self.stats.last_main_update = datetime.now()
        self.stats.total_updates_today += updated_count
        
        self.logger.info(f"Main pool update completed: {updated_count} updated, {failed_count} failed")
    
    def _select_main_pool_items_for_update(self, batch_size: int) -> List[MonitoringItem]:
        """选择主池中需要更新的项目"""
        # 按最后更新时间和优先级排序
        items = list(self.main_pool.values())
        
        # 优先选择从未更新过的项目
        never_updated = [item for item in items if item.last_update is None]
        
        # 然后选择更新时间最早的项目
        updated_items = [item for item in items if item.last_update is not None]
        updated_items.sort(key=lambda x: (x.last_update, -x.priority_score))
        
        # 合并列表
        selected_items = never_updated + updated_items
        
        return selected_items[:batch_size]
    
    async def _save_price_data(self, item: MonitoringItem, price_data: ItemPriceData):
        """保存价格数据到数据库"""
        try:
            db_manager = get_database_manager()
            
            async with db_manager.get_session() as session:
                # 这里需要实现价格数据的保存逻辑
                # 由于我们现在不考虑表迁移，直接使用现有的Price表结构
                pass
                
        except Exception as e:
            self.logger.error(f"Error saving price data for {item.item_id}: {e}")
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        return {
            'core_pool': {
                'size': len(self.core_pool),
                'last_update': self.stats.last_core_update.isoformat() if self.stats.last_core_update else None,
                'update_interval_minutes': self.core_update_interval
            },
            'main_pool': {
                'size': len(self.main_pool),
                'last_update': self.stats.last_main_update.isoformat() if self.stats.last_main_update else None,
                'update_interval_minutes': self.main_update_interval
            },
            'stats': {
                'total_updates_today': self.stats.total_updates_today,
                'successful_updates': self.stats.successful_updates,
                'failed_updates': self.stats.failed_updates,
                'success_rate': self.stats.get_success_rate(),
                'api_calls_used': self.stats.api_calls_used
            },
            'running': self.running
        }


# 全局分层监控调度器实例
_tiered_monitoring_scheduler: Optional[TieredMonitoringScheduler] = None


def get_tiered_monitoring_scheduler() -> TieredMonitoringScheduler:
    """获取全局分层监控调度器实例"""
    global _tiered_monitoring_scheduler
    
    if _tiered_monitoring_scheduler is None:
        _tiered_monitoring_scheduler = TieredMonitoringScheduler()
    
    return _tiered_monitoring_scheduler
