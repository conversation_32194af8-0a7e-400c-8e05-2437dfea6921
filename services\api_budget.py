"""
API调用预算管理器
精确跟踪和管理每日API调用限制，智能分配调用配额
"""

import asyncio
import json
import logging
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta, date
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

from core.config import get_config_manager
from core.exceptions import APIResponseError, ErrorContext


class APIEndpoint(Enum):
    """API端点枚举"""
    PRICE_SINGLE = "price_single"
    PRICE_BATCH = "price_batch"
    PRICE_AVG = "price_avg"
    ITEM_BASE = "item_base"
    WEAR_INSPECT = "wear_inspect"
    WEB_SCRAPING = "web_scraping"


@dataclass
class APICallRecord:
    """API调用记录"""
    endpoint: str
    timestamp: datetime
    success: bool
    response_time: float
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'endpoint': self.endpoint,
            'timestamp': self.timestamp.isoformat(),
            'success': self.success,
            'response_time': self.response_time,
            'error_message': self.error_message
        }


@dataclass
class DailyBudget:
    """每日预算配置"""
    total_limit: int = 14400  # 每日总限制
    core_pool_allocation: float = 0.6  # 核心池分配60%
    main_pool_allocation: float = 0.3   # 主池分配30%
    discovery_allocation: float = 0.1   # 发现功能分配10%
    
    def get_core_pool_budget(self) -> int:
        """获取核心池预算"""
        return int(self.total_limit * self.core_pool_allocation)
    
    def get_main_pool_budget(self) -> int:
        """获取主池预算"""
        return int(self.total_limit * self.main_pool_allocation)
    
    def get_discovery_budget(self) -> int:
        """获取发现功能预算"""
        return int(self.total_limit * self.discovery_allocation)


@dataclass
class BudgetStatus:
    """预算状态"""
    date: str
    total_calls: int = 0
    core_pool_calls: int = 0
    main_pool_calls: int = 0
    discovery_calls: int = 0
    endpoint_calls: Dict[str, int] = None
    last_reset_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.endpoint_calls is None:
            self.endpoint_calls = {}


class APIBudgetManager:
    """API预算管理器"""
    
    def __init__(self):
        """初始化预算管理器"""
        self.config = get_config_manager()
        self.logger = logging.getLogger(__name__)
        
        # 预算配置
        self.daily_budget = DailyBudget(
            total_limit=self.config.get('api.daily_limit', 14400),
            core_pool_allocation=self.config.get('api.core_pool_allocation', 0.6),
            main_pool_allocation=self.config.get('api.main_pool_allocation', 0.3),
            discovery_allocation=self.config.get('api.discovery_allocation', 0.1)
        )
        
        # 端点限制配置
        self.endpoint_limits = {
            APIEndpoint.PRICE_SINGLE: 60,    # 每分钟60次
            APIEndpoint.PRICE_BATCH: 1,      # 每分钟1次
            APIEndpoint.PRICE_AVG: 60,       # 每分钟60次
            APIEndpoint.ITEM_BASE: 1,        # 每日1次
            APIEndpoint.WEAR_INSPECT: 600,   # 每分钟600次
            APIEndpoint.WEB_SCRAPING: 10     # 每分钟10次（自定义限制）
        }
        
        # 状态管理
        self.budget_status = BudgetStatus(date=str(date.today()))
        self.call_history: List[APICallRecord] = []
        self.state_file = Path("data/api_budget_state.json")
        
        # 分钟级调用计数器
        self.minute_counters: Dict[str, Dict[str, int]] = {}
        
        # 加载持久化状态
        self._load_state()
        
        # 检查是否需要重置
        self._check_daily_reset()
        
        self.logger.info("API Budget Manager initialized")
    
    def _load_state(self):
        """加载持久化状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 恢复预算状态
                if 'budget_status' in data:
                    status_data = data['budget_status']
                    self.budget_status = BudgetStatus(**status_data)
                
                # 恢复调用历史（只保留最近24小时）
                if 'call_history' in data:
                    cutoff_time = datetime.now() - timedelta(hours=24)
                    for record_data in data['call_history']:
                        record_time = datetime.fromisoformat(record_data['timestamp'])
                        if record_time > cutoff_time:
                            record = APICallRecord(
                                endpoint=record_data['endpoint'],
                                timestamp=record_time,
                                success=record_data['success'],
                                response_time=record_data['response_time'],
                                error_message=record_data.get('error_message')
                            )
                            self.call_history.append(record)
                
                self.logger.info("API budget state loaded successfully")
                
        except Exception as e:
            self.logger.error(f"Error loading API budget state: {e}")
    
    def _save_state(self):
        """保存持久化状态"""
        try:
            # 确保目录存在
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 只保存最近24小时的调用历史
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_history = [
                record.to_dict() for record in self.call_history
                if record.timestamp > cutoff_time
            ]
            
            data = {
                'budget_status': asdict(self.budget_status),
                'call_history': recent_history,
                'last_save_time': datetime.now().isoformat()
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("API budget state saved")
            
        except Exception as e:
            self.logger.error(f"Error saving API budget state: {e}")
    
    def _check_daily_reset(self):
        """检查是否需要每日重置"""
        today = str(date.today())
        
        if self.budget_status.date != today:
            self.logger.info(f"Daily reset: {self.budget_status.date} -> {today}")
            
            # 重置每日计数器
            self.budget_status = BudgetStatus(
                date=today,
                last_reset_time=datetime.now()
            )
            
            # 清理分钟计数器
            self.minute_counters.clear()
            
            # 保存状态
            self._save_state()
    
    def can_make_call(self, endpoint: APIEndpoint, pool_type: str = "main") -> bool:
        """
        检查是否可以进行API调用
        
        Args:
            endpoint: API端点
            pool_type: 池类型 ('core', 'main', 'discovery')
            
        Returns:
            bool: 是否可以调用
        """
        # 检查每日总限制
        if self.budget_status.total_calls >= self.daily_budget.total_limit:
            return False
        
        # 检查池类型预算
        if pool_type == "core":
            if self.budget_status.core_pool_calls >= self.daily_budget.get_core_pool_budget():
                return False
        elif pool_type == "main":
            if self.budget_status.main_pool_calls >= self.daily_budget.get_main_pool_budget():
                return False
        elif pool_type == "discovery":
            if self.budget_status.discovery_calls >= self.daily_budget.get_discovery_budget():
                return False
        
        # 检查端点分钟级限制
        if not self._check_minute_limit(endpoint):
            return False
        
        # 检查端点每日限制（如果有）
        endpoint_name = endpoint.value
        if endpoint == APIEndpoint.ITEM_BASE:
            # 基础信息API每日只能调用1次
            daily_calls = self.budget_status.endpoint_calls.get(endpoint_name, 0)
            if daily_calls >= 1:
                return False
        
        return True
    
    def _check_minute_limit(self, endpoint: APIEndpoint) -> bool:
        """检查分钟级限制"""
        current_minute = datetime.now().strftime("%Y-%m-%d %H:%M")
        endpoint_name = endpoint.value
        
        # 获取当前分钟的调用次数
        if current_minute not in self.minute_counters:
            self.minute_counters[current_minute] = {}
        
        minute_calls = self.minute_counters[current_minute].get(endpoint_name, 0)
        limit = self.endpoint_limits.get(endpoint, 60)
        
        return minute_calls < limit
    
    def record_call(self, endpoint: APIEndpoint, pool_type: str, success: bool, 
                   response_time: float, error_message: Optional[str] = None):
        """
        记录API调用
        
        Args:
            endpoint: API端点
            pool_type: 池类型
            success: 是否成功
            response_time: 响应时间
            error_message: 错误信息
        """
        now = datetime.now()
        current_minute = now.strftime("%Y-%m-%d %H:%M")
        endpoint_name = endpoint.value
        
        # 记录调用历史
        record = APICallRecord(
            endpoint=endpoint_name,
            timestamp=now,
            success=success,
            response_time=response_time,
            error_message=error_message
        )
        self.call_history.append(record)
        
        # 更新预算状态
        self.budget_status.total_calls += 1
        
        if pool_type == "core":
            self.budget_status.core_pool_calls += 1
        elif pool_type == "main":
            self.budget_status.main_pool_calls += 1
        elif pool_type == "discovery":
            self.budget_status.discovery_calls += 1
        
        # 更新端点调用计数
        if endpoint_name not in self.budget_status.endpoint_calls:
            self.budget_status.endpoint_calls[endpoint_name] = 0
        self.budget_status.endpoint_calls[endpoint_name] += 1
        
        # 更新分钟级计数器
        if current_minute not in self.minute_counters:
            self.minute_counters[current_minute] = {}
        if endpoint_name not in self.minute_counters[current_minute]:
            self.minute_counters[current_minute][endpoint_name] = 0
        self.minute_counters[current_minute][endpoint_name] += 1
        
        # 清理旧的分钟计数器（保留最近5分钟）
        self._cleanup_minute_counters()
        
        # 定期保存状态
        if self.budget_status.total_calls % 10 == 0:
            self._save_state()
        
        self.logger.debug(f"Recorded API call: {endpoint_name} ({pool_type}) - Success: {success}")
    
    def _cleanup_minute_counters(self):
        """清理旧的分钟计数器"""
        cutoff_time = datetime.now() - timedelta(minutes=5)
        cutoff_minute = cutoff_time.strftime("%Y-%m-%d %H:%M")
        
        keys_to_remove = [
            key for key in self.minute_counters.keys()
            if key < cutoff_minute
        ]
        
        for key in keys_to_remove:
            del self.minute_counters[key]
    
    def get_budget_summary(self) -> Dict[str, Any]:
        """获取预算使用摘要"""
        return {
            'date': self.budget_status.date,
            'total_calls': self.budget_status.total_calls,
            'total_limit': self.daily_budget.total_limit,
            'usage_percentage': (self.budget_status.total_calls / self.daily_budget.total_limit) * 100,
            'core_pool': {
                'calls': self.budget_status.core_pool_calls,
                'budget': self.daily_budget.get_core_pool_budget(),
                'remaining': self.daily_budget.get_core_pool_budget() - self.budget_status.core_pool_calls
            },
            'main_pool': {
                'calls': self.budget_status.main_pool_calls,
                'budget': self.daily_budget.get_main_pool_budget(),
                'remaining': self.daily_budget.get_main_pool_budget() - self.budget_status.main_pool_calls
            },
            'discovery': {
                'calls': self.budget_status.discovery_calls,
                'budget': self.daily_budget.get_discovery_budget(),
                'remaining': self.daily_budget.get_discovery_budget() - self.budget_status.discovery_calls
            },
            'endpoint_calls': self.budget_status.endpoint_calls.copy(),
            'last_reset_time': self.budget_status.last_reset_time.isoformat() if self.budget_status.last_reset_time else None
        }
    
    def get_remaining_budget(self, pool_type: str) -> int:
        """获取剩余预算"""
        if pool_type == "core":
            return max(0, self.daily_budget.get_core_pool_budget() - self.budget_status.core_pool_calls)
        elif pool_type == "main":
            return max(0, self.daily_budget.get_main_pool_budget() - self.budget_status.main_pool_calls)
        elif pool_type == "discovery":
            return max(0, self.daily_budget.get_discovery_budget() - self.budget_status.discovery_calls)
        else:
            return max(0, self.daily_budget.total_limit - self.budget_status.total_calls)


# 全局预算管理器实例
_api_budget_manager: Optional[APIBudgetManager] = None


def get_api_budget_manager() -> APIBudgetManager:
    """获取全局API预算管理器实例"""
    global _api_budget_manager
    
    if _api_budget_manager is None:
        _api_budget_manager = APIBudgetManager()
    
    return _api_budget_manager
