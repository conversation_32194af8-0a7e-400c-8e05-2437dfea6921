"""
Ares机会发现服务
基于排行榜API的潜在投资机会发现，每日2次自动更新主监控池
"""

import asyncio
import logging
import json
import hashlib
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from core.api_manager import get_api_manager, get_all_rankings, APICallResult
from core.database import get_database_manager
from core.scheduler import get_scheduler
from core.priority_calculator import get_priority_calculator, calculate_priority_score
from core.config import get_config_manager
from core.exceptions import AresException, ErrorSeverity, ErrorContext
from monitoring.system_monitor import get_system_monitor

logger = logging.getLogger(__name__)


@dataclass
class DiscoveryOpportunity:
    """发现的投资机会"""
    item_id: str
    item_name: str
    ranking_type: str  # hot, new, rising, falling
    ranking_position: int
    price_info: Dict[str, Any]
    opportunity_score: float
    discovery_time: datetime
    source_data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_id': self.item_id,
            'item_name': self.item_name,
            'ranking_type': self.ranking_type,
            'ranking_position': self.ranking_position,
            'price_info': self.price_info,
            'opportunity_score': self.opportunity_score,
            'discovery_time': self.discovery_time.isoformat(),
            'source_data': self.source_data
        }


@dataclass
class DiscoveryStats:
    """发现统计信息"""
    total_discoveries: int = 0
    successful_additions: int = 0
    duplicate_items: int = 0
    api_calls_made: int = 0
    last_discovery_time: Optional[datetime] = None
    discovery_sessions: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'total_discoveries': self.total_discoveries,
            'successful_additions': self.successful_additions,
            'duplicate_items': self.duplicate_items,
            'api_calls_made': self.api_calls_made,
            'last_discovery_time': self.last_discovery_time.isoformat() if self.last_discovery_time else None,
            'discovery_sessions': self.discovery_sessions,
            'success_rate': (self.successful_additions / max(self.total_discoveries, 1)) * 100
        }


class DiscovererService:
    """机会发现服务"""
    
    def __init__(self):
        """初始化发现服务"""
        self.config = get_config_manager()
        self.db_manager = get_database_manager()
        self.api_manager = None
        self.scheduler_service = None
        self.priority_calculator = get_priority_calculator()
        self.system_monitor = get_system_monitor()
        
        # 调度器
        self.scheduler = AsyncIOScheduler()
        self.running = False
        
        # 统计信息
        self.stats = DiscoveryStats()
        
        # 配置参数
        self.discovery_limit = self.config.get('discoverer.limit_per_ranking', 50)
        self.min_opportunity_score = self.config.get('discoverer.min_opportunity_score', 3.0)
        self.discovery_times = self.config.get('discoverer.discovery_times', ['09:00', '21:00'])
        
        # 历史记录
        self.history_file = Path("data/discovery_history.json")
        self.discovered_items: Set[str] = set()
        
        # 加载历史记录
        self._load_history()
        
        logger.info("Discoverer service initialized")
    
    async def initialize(self):
        """初始化服务组件"""
        try:
            # 初始化API管理器
            self.api_manager = await get_api_manager()
            
            # 初始化调度器服务
            self.scheduler_service = await get_scheduler()
            
            logger.info("Discoverer service components initialized")
            
        except Exception as e:
            logger.error("Failed to initialize discoverer service: %s", str(e))
            raise AresException(
                message=f"Discoverer initialization failed: {str(e)}",
                context=ErrorContext(operation="discoverer_init"),
                severity=ErrorSeverity.HIGH
            )
    
    async def start(self):
        """启动发现服务"""
        if self.running:
            logger.warning("Discoverer service is already running")
            return
        
        logger.info("Starting discoverer service")
        
        try:
            # 初始化组件
            await self.initialize()
            
            # 设置定时任务
            self._setup_scheduled_tasks()
            
            # 启动调度器
            self.scheduler.start()
            self.running = True
            
            logger.info("Discoverer service started successfully")
            
        except Exception as e:
            logger.error("Error starting discoverer service: %s", str(e))
            self.running = False
            raise
    
    async def stop(self):
        """停止发现服务"""
        logger.info("Stopping discoverer service")
        
        self.running = False
        
        try:
            # 停止调度器
            if self.scheduler.running:
                self.scheduler.shutdown()
            
            # 保存历史记录
            self._save_history()
            
            logger.info("Discoverer service stopped gracefully")
            
        except Exception as e:
            logger.error("Error stopping discoverer service: %s", str(e))
    
    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        for discovery_time in self.discovery_times:
            hour, minute = map(int, discovery_time.split(':'))
            
            self.scheduler.add_job(
                self.discover_opportunities,
                CronTrigger(hour=hour, minute=minute),
                id=f"discovery_{discovery_time}",
                name=f"机会发现 {discovery_time}",
                max_instances=1,
                coalesce=True
            )
            
            logger.info("Scheduled discovery task for %s", discovery_time)
    
    async def discover_opportunities(self) -> List[DiscoveryOpportunity]:
        """发现投资机会"""
        logger.info("Starting opportunity discovery session")
        
        try:
            session_start = datetime.utcnow()
            opportunities = []
            
            # 获取所有排行榜数据
            rankings_data = await self._fetch_all_rankings()
            
            # 分析排行榜数据
            for ranking_type, response in rankings_data.items():
                if response.status == APICallResult.SUCCESS:
                    ranking_opportunities = await self._analyze_ranking(ranking_type, response.data)
                    opportunities.extend(ranking_opportunities)
                else:
                    logger.warning("Failed to get %s rankings: %s", ranking_type, response.error_message)
            
            # 去重和评分
            unique_opportunities = self._deduplicate_opportunities(opportunities)
            scored_opportunities = await self._score_opportunities(unique_opportunities)
            
            # 筛选高质量机会
            quality_opportunities = [
                opp for opp in scored_opportunities 
                if opp.opportunity_score >= self.min_opportunity_score
            ]
            
            # 更新主监控池
            added_count = await self._update_main_pool(quality_opportunities)
            
            # 更新统计信息
            self.stats.total_discoveries += len(opportunities)
            self.stats.successful_additions += added_count
            self.stats.duplicate_items += len(opportunities) - len(unique_opportunities)
            self.stats.last_discovery_time = session_start
            self.stats.discovery_sessions += 1
            
            # 记录发现历史
            self._record_discovery_session(quality_opportunities, session_start)
            
            # 记录系统监控
            self.system_monitor.record_api_request("discoverer_session", True, 
                                                 (datetime.utcnow() - session_start).total_seconds())
            
            logger.info("Discovery session completed: %d opportunities found, %d added to pool", 
                       len(quality_opportunities), added_count)
            
            return quality_opportunities
            
        except Exception as e:
            logger.error("Error in discovery session: %s", str(e))
            self.system_monitor.record_api_request("discoverer_session", False, 0)
            raise
    
    async def _fetch_all_rankings(self) -> Dict[str, Any]:
        """获取所有排行榜数据"""
        try:
            rankings_data = await get_all_rankings(self.api_manager, self.discovery_limit)
            self.stats.api_calls_made += 4  # 4个排行榜类型
            return rankings_data
        except Exception as e:
            logger.error("Error fetching rankings: %s", str(e))
            return {}
    
    async def _analyze_ranking(self, ranking_type: str, ranking_data: Dict[str, Any]) -> List[DiscoveryOpportunity]:
        """分析单个排行榜数据"""
        opportunities = []
        
        try:
            items = ranking_data.get('items', [])
            
            for position, item_data in enumerate(items, 1):
                item_id = item_data.get('id')
                item_name = item_data.get('name', 'Unknown')
                
                if not item_id:
                    continue
                
                # 检查是否已经发现过
                if item_id in self.discovered_items:
                    continue
                
                # 创建机会对象
                opportunity = DiscoveryOpportunity(
                    item_id=item_id,
                    item_name=item_name,
                    ranking_type=ranking_type,
                    ranking_position=position,
                    price_info=item_data.get('price_info', {}),
                    opportunity_score=0.0,  # 稍后计算
                    discovery_time=datetime.utcnow(),
                    source_data=item_data
                )
                
                opportunities.append(opportunity)
                
        except Exception as e:
            logger.error("Error analyzing %s ranking: %s", ranking_type, str(e))
        
        return opportunities
    
    def _deduplicate_opportunities(self, opportunities: List[DiscoveryOpportunity]) -> List[DiscoveryOpportunity]:
        """去重机会列表"""
        seen_items = set()
        unique_opportunities = []
        
        for opportunity in opportunities:
            if opportunity.item_id not in seen_items:
                seen_items.add(opportunity.item_id)
                unique_opportunities.append(opportunity)
        
        return unique_opportunities
    
    async def _score_opportunities(self, opportunities: List[DiscoveryOpportunity]) -> List[DiscoveryOpportunity]:
        """为机会评分"""
        for opportunity in opportunities:
            try:
                # 构建评分数据
                score_data = {
                    'item_id': opportunity.item_id,
                    'price_spread': self._calculate_price_spread(opportunity.price_info),
                    'volume_24h': opportunity.price_info.get('volume_24h', 0),
                    'volatility': opportunity.price_info.get('volatility', 0),
                    'user_added': False,  # 发现的项目不是用户添加
                    'user_priority': 0,
                    'ranking_position': opportunity.ranking_position,
                    'ranking_type': opportunity.ranking_type
                }
                
                # 计算基础优先级评分
                base_score = calculate_priority_score(score_data)
                
                # 应用排行榜位置调整
                position_bonus = self._calculate_position_bonus(
                    opportunity.ranking_position, 
                    opportunity.ranking_type
                )
                
                # 最终评分
                opportunity.opportunity_score = base_score + position_bonus
                
            except Exception as e:
                logger.error("Error scoring opportunity %s: %s", opportunity.item_id, str(e))
                opportunity.opportunity_score = 0.0
        
        return opportunities
    
    def _calculate_price_spread(self, price_info: Dict[str, Any]) -> float:
        """计算价差百分比"""
        try:
            ask_price = price_info.get('ask_price', 0)
            bid_price = price_info.get('bid_price', 0)
            
            if bid_price > 0 and ask_price > bid_price:
                return ((ask_price - bid_price) / bid_price) * 100
            
            return 0.0
        except Exception:
            return 0.0
    
    def _calculate_position_bonus(self, position: int, ranking_type: str) -> float:
        """计算排行榜位置奖励"""
        # 位置越靠前，奖励越高
        position_bonus = max(0, (51 - position) / 50) * 2.0  # 最高2分
        
        # 不同排行榜类型的权重
        type_weights = {
            'rising': 1.5,    # 上涨榜权重最高
            'hot': 1.2,       # 热门榜次之
            'new': 1.0,       # 新品榜标准权重
            'falling': 0.8    # 下跌榜权重较低
        }
        
        weight = type_weights.get(ranking_type, 1.0)
        return position_bonus * weight
    
    async def _update_main_pool(self, opportunities: List[DiscoveryOpportunity]) -> int:
        """更新主监控池"""
        added_count = 0
        
        try:
            async with self.db_manager.get_session() as session:
                for opportunity in opportunities:
                    try:
                        # 检查是否已在监控池中
                        existing_item = await self.db_manager.get_watchlist_item(
                            session, opportunity.item_id
                        )
                        
                        if existing_item:
                            continue
                        
                        # 添加到主监控池
                        await self._add_to_watchlist(session, opportunity)
                        
                        # 添加到调度器
                        if self.scheduler_service:
                            await self.scheduler_service.add_item_to_schedule(
                                opportunity.item_id, 
                                'main', 
                                opportunity.opportunity_score
                            )
                        
                        # 记录已发现
                        self.discovered_items.add(opportunity.item_id)
                        added_count += 1
                        
                        logger.debug("Added opportunity %s to main pool (score: %.2f)", 
                                   opportunity.item_id, opportunity.opportunity_score)
                        
                    except Exception as e:
                        logger.error("Error adding opportunity %s to pool: %s", 
                                   opportunity.item_id, str(e))
                        
        except Exception as e:
            logger.error("Error updating main pool: %s", str(e))
        
        return added_count
    
    async def _add_to_watchlist(self, session, opportunity: DiscoveryOpportunity):
        """添加机会到监控列表"""
        # 这里应该调用数据库管理器的方法添加到监控列表
        # 暂时记录日志
        logger.debug("Adding %s to watchlist (discovered from %s ranking)", 
                    opportunity.item_id, opportunity.ranking_type)
    
    def _record_discovery_session(self, opportunities: List[DiscoveryOpportunity], session_time: datetime):
        """记录发现会话"""
        session_record = {
            'session_time': session_time.isoformat(),
            'opportunities_count': len(opportunities),
            'opportunities': [opp.to_dict() for opp in opportunities],
            'stats_snapshot': self.stats.to_dict()
        }
        
        # 添加到历史记录
        if not hasattr(self, 'discovery_history'):
            self.discovery_history = []
        
        self.discovery_history.append(session_record)
        
        # 保持最近100次记录
        if len(self.discovery_history) > 100:
            self.discovery_history = self.discovery_history[-100:]
        
        # 保存到文件
        self._save_history()
    
    def _load_history(self):
        """加载发现历史"""
        try:
            if self.history_file.exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.discovery_history = data.get('sessions', [])
                
                # 恢复统计信息
                stats_data = data.get('stats', {})
                self.stats.total_discoveries = stats_data.get('total_discoveries', 0)
                self.stats.successful_additions = stats_data.get('successful_additions', 0)
                self.stats.duplicate_items = stats_data.get('duplicate_items', 0)
                self.stats.api_calls_made = stats_data.get('api_calls_made', 0)
                self.stats.discovery_sessions = stats_data.get('discovery_sessions', 0)
                
                if stats_data.get('last_discovery_time'):
                    self.stats.last_discovery_time = datetime.fromisoformat(
                        stats_data['last_discovery_time']
                    )
                
                # 恢复已发现项目集合
                self.discovered_items = set(data.get('discovered_items', []))
                
                logger.info("Discovery history loaded: %d sessions, %d discovered items", 
                           len(self.discovery_history), len(self.discovered_items))
        except Exception as e:
            logger.warning("Failed to load discovery history: %s", str(e))
            self.discovery_history = []
    
    def _save_history(self):
        """保存发现历史"""
        try:
            # 确保目录存在
            self.history_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'sessions': self.discovery_history,
                'stats': self.stats.to_dict(),
                'discovered_items': list(self.discovered_items),
                'last_updated': datetime.utcnow().isoformat()
            }
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.debug("Discovery history saved to %s", self.history_file)
        except Exception as e:
            logger.error("Failed to save discovery history: %s", str(e))
    
    async def manual_discovery(self) -> List[DiscoveryOpportunity]:
        """手动触发发现"""
        logger.info("Manual discovery triggered")
        return await self.discover_opportunities()
    
    def get_status(self) -> Dict[str, Any]:
        """获取发现器状态"""
        next_jobs = []
        if self.scheduler.running:
            for job in self.scheduler.get_jobs():
                next_jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None
                })
        
        return {
            'running': self.running,
            'scheduler_running': self.scheduler.running if hasattr(self.scheduler, 'running') else False,
            'stats': self.stats.to_dict(),
            'discovered_items_count': len(self.discovered_items),
            'next_scheduled_jobs': next_jobs,
            'discovery_times': self.discovery_times,
            'min_opportunity_score': self.min_opportunity_score
        }
    
    def get_discovery_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取发现历史"""
        if not hasattr(self, 'discovery_history'):
            return []
        
        return self.discovery_history[-limit:]


# 全局发现器实例
_discoverer_service: Optional[DiscovererService] = None


def get_discoverer_service() -> DiscovererService:
    """获取全局发现器服务实例"""
    global _discoverer_service
    if _discoverer_service is None:
        _discoverer_service = DiscovererService()
    return _discoverer_service
