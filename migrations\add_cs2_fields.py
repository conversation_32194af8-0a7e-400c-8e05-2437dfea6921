"""
数据库迁移脚本：添加CS2饰品特有字段
安全地为Item表添加CS2特有的字段，不影响现有数据
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from sqlalchemy import text, MetaData, Table, Column, String, Float, inspect
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import DatabaseManager, get_database_manager


class CS2FieldsMigration:
    """CS2字段迁移类"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化迁移器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # 定义要添加的字段
        self.new_fields = [
            {
                'name': 'weapon_type',
                'type': 'VARCHAR(50)',
                'comment': '武器类型：步枪、狙击枪、手枪、刀具等'
            },
            {
                'name': 'skin_name',
                'type': 'VARCHAR(200)',
                'comment': '皮肤名称'
            },
            {
                'name': 'wear_rating',
                'type': 'REAL',
                'comment': '磨损度评级 (0.0-1.0)'
            },
            {
                'name': 'float_min',
                'type': 'REAL',
                'comment': '最小磨损值'
            },
            {
                'name': 'float_max',
                'type': 'REAL',
                'comment': '最大磨损值'
            },
            {
                'name': 'quality',
                'type': 'VARCHAR(20)',
                'comment': '品质：普通、StatTrak、纪念品等'
            },
            {
                'name': 'collection',
                'type': 'VARCHAR(100)',
                'comment': '收藏品系列'
            },
            {
                'name': 'case_name',
                'type': 'VARCHAR(100)',
                'comment': '来源箱子名称'
            },
            {
                'name': 'investment_score',
                'type': 'REAL',
                'comment': '投资评分 (0-100)'
            },
            {
                'name': 'popularity_score',
                'type': 'REAL',
                'comment': '流行度评分 (0-100)'
            },
            {
                'name': 'liquidity_score',
                'type': 'REAL',
                'comment': '流动性评分 (0-100)'
            }
        ]
        
        # 定义要添加的索引
        self.new_indexes = [
            {
                'name': 'idx_item_weapon_type',
                'columns': ['weapon_type']
            },
            {
                'name': 'idx_item_skin_name',
                'columns': ['skin_name']
            },
            {
                'name': 'idx_item_wear_rating',
                'columns': ['wear_rating']
            },
            {
                'name': 'idx_item_investment_score',
                'columns': ['investment_score']
            },
            {
                'name': 'idx_item_weapon_rarity',
                'columns': ['weapon_type', 'rarity']
            },
            {
                'name': 'idx_item_quality_collection',
                'columns': ['quality', 'collection']
            }
        ]
    
    def check_field_exists(self, table_name: str, field_name: str) -> bool:
        """
        检查字段是否已存在
        
        Args:
            table_name: 表名
            field_name: 字段名
            
        Returns:
            bool: 字段是否存在
        """
        try:
            with self.db_manager.get_session() as session:
                inspector = inspect(session.bind)
                columns = inspector.get_columns(table_name)
                return any(col['name'] == field_name for col in columns)
        except Exception as e:
            self.logger.error(f"Error checking field existence: {e}")
            return False
    
    def check_index_exists(self, table_name: str, index_name: str) -> bool:
        """
        检查索引是否已存在
        
        Args:
            table_name: 表名
            index_name: 索引名
            
        Returns:
            bool: 索引是否存在
        """
        try:
            with self.db_manager.get_session() as session:
                inspector = inspect(session.bind)
                indexes = inspector.get_indexes(table_name)
                return any(idx['name'] == index_name for idx in indexes)
        except Exception as e:
            self.logger.error(f"Error checking index existence: {e}")
            return False
    
    def add_field(self, table_name: str, field_info: dict) -> bool:
        """
        添加单个字段
        
        Args:
            table_name: 表名
            field_info: 字段信息
            
        Returns:
            bool: 是否成功
        """
        field_name = field_info['name']
        field_type = field_info['type']
        comment = field_info.get('comment', '')
        
        try:
            # 检查字段是否已存在
            if self.check_field_exists(table_name, field_name):
                self.logger.info(f"Field {field_name} already exists in {table_name}")
                return True
            
            # 添加字段的SQL
            sql = f"ALTER TABLE {table_name} ADD COLUMN {field_name} {field_type}"
            
            with self.db_manager.get_session() as session:
                session.execute(text(sql))
                session.commit()
                self.logger.info(f"Successfully added field {field_name} to {table_name}")
                return True
                
        except SQLAlchemyError as e:
            self.logger.error(f"Error adding field {field_name} to {table_name}: {e}")
            return False
    
    def add_index(self, table_name: str, index_info: dict) -> bool:
        """
        添加单个索引
        
        Args:
            table_name: 表名
            index_info: 索引信息
            
        Returns:
            bool: 是否成功
        """
        index_name = index_info['name']
        columns = index_info['columns']
        
        try:
            # 检查索引是否已存在
            if self.check_index_exists(table_name, index_name):
                self.logger.info(f"Index {index_name} already exists on {table_name}")
                return True
            
            # 创建索引的SQL
            columns_str = ', '.join(columns)
            sql = f"CREATE INDEX {index_name} ON {table_name} ({columns_str})"
            
            with self.db_manager.get_session() as session:
                session.execute(text(sql))
                session.commit()
                self.logger.info(f"Successfully created index {index_name} on {table_name}")
                return True
                
        except SQLAlchemyError as e:
            self.logger.error(f"Error creating index {index_name} on {table_name}: {e}")
            return False
    
    def migrate_up(self) -> bool:
        """
        执行向上迁移（添加字段和索引）
        
        Returns:
            bool: 是否成功
        """
        self.logger.info("Starting CS2 fields migration...")
        
        success = True
        
        # 添加字段
        for field_info in self.new_fields:
            if not self.add_field('items', field_info):
                success = False
        
        # 添加索引
        for index_info in self.new_indexes:
            if not self.add_index('items', index_info):
                success = False
        
        if success:
            self.logger.info("CS2 fields migration completed successfully")
        else:
            self.logger.error("CS2 fields migration completed with errors")
        
        return success
    
    def migrate_down(self) -> bool:
        """
        执行向下迁移（移除字段和索引）
        
        Returns:
            bool: 是否成功
        """
        self.logger.info("Starting CS2 fields rollback...")
        
        success = True
        
        # 移除索引
        for index_info in reversed(self.new_indexes):
            try:
                index_name = index_info['name']
                if self.check_index_exists('items', index_name):
                    with self.db_manager.get_session() as session:
                        sql = f"DROP INDEX {index_name}"
                        session.execute(text(sql))
                        session.commit()
                        self.logger.info(f"Dropped index {index_name}")
            except Exception as e:
                self.logger.error(f"Error dropping index {index_name}: {e}")
                success = False
        
        # 移除字段（SQLite不支持DROP COLUMN，需要重建表）
        self.logger.warning("SQLite does not support DROP COLUMN. Manual table recreation required for complete rollback.")
        
        return success
    
    def validate_migration(self) -> bool:
        """
        验证迁移结果
        
        Returns:
            bool: 验证是否通过
        """
        self.logger.info("Validating CS2 fields migration...")
        
        # 检查所有字段是否存在
        for field_info in self.new_fields:
            if not self.check_field_exists('items', field_info['name']):
                self.logger.error(f"Field {field_info['name']} not found after migration")
                return False
        
        # 检查所有索引是否存在
        for index_info in self.new_indexes:
            if not self.check_index_exists('items', index_info['name']):
                self.logger.error(f"Index {index_info['name']} not found after migration")
                return False
        
        self.logger.info("Migration validation passed")
        return True


def run_migration():
    """运行迁移脚本"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # 获取数据库管理器
        db_manager = get_database_manager()
        
        # 创建迁移器
        migration = CS2FieldsMigration(db_manager)
        
        # 执行迁移
        if migration.migrate_up():
            # 验证迁移
            if migration.validate_migration():
                logger.info("CS2 fields migration completed and validated successfully")
                return True
            else:
                logger.error("Migration validation failed")
                return False
        else:
            logger.error("Migration failed")
            return False
            
    except Exception as e:
        logger.error(f"Migration script failed: {e}")
        return False


if __name__ == "__main__":
    success = run_migration()
    exit(0 if success else 1)
