# 基础数据导入脚本使用说明

## 概述

这些脚本用于将SteamDT基础数据从JSON文件导入到数据库的items表中。

## 脚本说明

### 1. import_basedata.py (完整版)
- 功能最完整的导入脚本
- 包含详细的日志记录
- 支持错误处理和统计
- 生成导入日志文件

### 2. simple_import.py (简化版)
- 简化版导入脚本
- 代码更简洁，易于调试
- 实时显示进度
- 推荐用于快速导入

## 使用方法

### 方法1: 使用简化版脚本 (推荐)

```bash
# 进入项目根目录
cd e:\csgo\csgoares

# 运行简化版导入脚本
python scripts/simple_import.py
```

### 方法2: 使用完整版脚本

```bash
# 进入项目根目录
cd e:\csgo\csgoares

# 运行完整版导入脚本
python scripts/import_basedata.py
```

## 数据文件要求

- 数据文件路径: `data/basedata.json`
- 文件格式: SteamDT API标准响应格式
- 必须包含: `success: true` 和 `data` 数组

## 导入过程

1. **数据验证**: 检查文件格式和必要字段
2. **数据解析**: 解析饰品名称、武器类型、平台信息等
3. **数据库操作**: 
   - 新饰品: 插入新记录
   - 已存在饰品: 更新现有记录
4. **批量提交**: 每1000条记录提交一次，提高性能

## 数据映射

### 基础字段映射
- `name` → `name` (饰品中文名称)
- `marketHashName` → `market_hash_name` (主键)
- `platformList` → `platform_data` (JSON格式存储)

### 自动解析字段
- `weapon_type`: 从marketHashName解析武器类型
- `skin_name`: 从marketHashName解析皮肤名称
- `data_source`: 固定为 'steamdt'
- `last_sync_time`: 当前时间
- `is_active`: 设为 True

### 平台名称标准化
- `BUFF` → `buff163`
- `C5` → `c5game`
- `YOUPIN` → `youpin898`
- `HALOSKINS` → `haloskins`

## 输出示例

```
开始导入SteamDT基础数据...
读取数据文件: e:\csgo\csgoares\data\basedata.json
共有 149223 个饰品数据
已处理 1000/149223 个饰品 (新增: 1000, 更新: 0)
已处理 2000/149223 个饰品 (新增: 2000, 更新: 0)
...
==================================================
导入完成！
新增饰品: 149223
更新饰品: 0
错误数量: 0
总计处理: 149223
==================================================
```

## 注意事项

1. **数据库连接**: 确保数据库配置正确且可连接
2. **文件路径**: 确保 `data/basedata.json` 文件存在
3. **权限**: 确保有数据库写入权限
4. **备份**: 建议在导入前备份数据库
5. **重复运行**: 脚本支持重复运行，会更新已存在的记录

## 故障排除

### 常见错误

1. **文件不存在**
   ```
   FileNotFoundError: 数据文件不存在
   ```
   解决: 检查 `data/basedata.json` 文件是否存在

2. **数据库连接失败**
   ```
   数据库操作失败: connection error
   ```
   解决: 检查数据库配置和连接

3. **JSON格式错误**
   ```
   数据文件格式错误: success=False
   ```
   解决: 检查JSON文件格式是否正确

### 调试建议

1. 使用简化版脚本进行初步测试
2. 检查日志文件 `import_basedata.log`
3. 确认数据库表结构是否正确
4. 验证JSON文件内容格式

## 性能优化

- 批量提交: 每1000条记录提交一次
- 索引优化: 确保market_hash_name有索引
- 内存管理: 大文件分批处理
- 事务控制: 使用数据库事务确保一致性

## 后续步骤

导入完成后，可以：
1. 验证数据完整性
2. 运行基础数据收集器测试
3. 启动定时同步服务
4. 检查数据质量报告
