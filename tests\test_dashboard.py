"""
投资组合仪表盘测试
验证仪表盘页面和组件的核心功能
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.components.data_table import DataTable, create_portfolio_table, create_watchlist_table, create_discovery_table
from app.components.filter_panel import FilterPanel, FilterType, create_portfolio_filter_panel, create_watchlist_filter_panel


class TestDataTable:
    """数据表格组件测试"""
    
    def test_data_table_initialization(self):
        """测试数据表格初始化"""
        # 创建测试数据
        data = pd.DataFrame({
            'name': ['Item 1', 'Item 2', 'Item 3'],
            'price': [100.0, 200.0, 300.0],
            'quantity': [1, 2, 3]
        })
        
        table = DataTable(
            data=data,
            key="test_table",
            title="测试表格",
            page_size=10,
            sortable=True,
            filterable=True,
            selectable=True
        )
        
        assert table.data.equals(data)
        assert table.key == "test_table"
        assert table.title == "测试表格"
        assert table.page_size == 10
        assert table.sortable == True
        assert table.filterable == True
        assert table.selectable == True
    
    def test_create_portfolio_table(self):
        """测试创建投资组合表格"""
        data = pd.DataFrame({
            'name': ['AK-47 | Redline', 'AWP | Dragon Lore'],
            'current_price': [52.30, 2500.00],
            'quantity': [3, 1],
            'profit_percentage': [15.2, 8.5]
        })
        
        table = create_portfolio_table(data, "test_portfolio")
        
        assert table.key == "test_portfolio"
        assert table.title == "💼 投资组合"
        assert table.page_size == 15
        assert table.sortable == True
        assert table.filterable == True
        assert table.selectable == True
        assert len(table.actions) == 3
    
    def test_create_watchlist_table(self):
        """测试创建监控列表表格"""
        data = pd.DataFrame({
            'item_name': ['Karambit | Fade', 'M4A4 | Howl'],
            'pool_type': ['core', 'main'],
            'priority_score': [8.5, 6.2],
            'change_24h': [5.2, -2.1]
        })
        
        table = create_watchlist_table(data, "test_watchlist")
        
        assert table.key == "test_watchlist"
        assert table.title == "👁️ 监控列表"
        assert table.page_size == 20
        assert len(table.actions) == 2
    
    def test_create_discovery_table(self):
        """测试创建发现机会表格"""
        data = pd.DataFrame({
            'item_name': ['StatTrak™ M4A1-S | Hyper Beast', 'AK-47 | Neon Rider'],
            'ranking_type': ['rising', 'hot'],
            'opportunity_score': [7.8, 6.5],
            'ranking_position': [5, 12]
        })
        
        table = create_discovery_table(data, "test_discovery")
        
        assert table.key == "test_discovery"
        assert table.title == "🔍 发现机会"
        assert table.page_size == 10
        assert len(table.actions) == 2


class TestFilterPanel:
    """筛选面板组件测试"""
    
    def test_filter_panel_initialization(self):
        """测试筛选面板初始化"""
        panel = FilterPanel("test_panel", "测试筛选")
        
        assert panel.key == "test_panel"
        assert panel.title == "测试筛选"
        assert panel.filters == {}
        assert panel.filter_configs == {}
    
    def test_add_filter(self):
        """测试添加筛选器"""
        panel = FilterPanel("test_panel")
        
        # 添加文本筛选器
        panel.add_filter(
            "item_name", FilterType.TEXT, "项目名称",
            help_text="搜索项目名称"
        )
        
        # 添加选择筛选器
        panel.add_filter(
            "category", FilterType.SELECT, "类别",
            options=["武器", "刀具", "手套"],
            default_value="武器"
        )
        
        # 添加数值范围筛选器
        panel.add_filter(
            "price_range", FilterType.NUMBER_RANGE, "价格范围",
            min_value=0.0, max_value=1000.0, default_value=(0.0, 1000.0)
        )
        
        assert len(panel.filter_configs) == 3
        assert "item_name" in panel.filter_configs
        assert "category" in panel.filter_configs
        assert "price_range" in panel.filter_configs
        
        # 检查配置
        text_config = panel.filter_configs["item_name"]
        assert text_config['type'] == FilterType.TEXT
        assert text_config['label'] == "项目名称"
        assert text_config['help_text'] == "搜索项目名称"
        
        select_config = panel.filter_configs["category"]
        assert select_config['type'] == FilterType.SELECT
        assert select_config['options'] == ["武器", "刀具", "手套"]
        assert select_config['default_value'] == "武器"
    
    def test_apply_filters_to_dataframe(self):
        """测试将筛选器应用到DataFrame"""
        # 创建测试数据
        data = pd.DataFrame({
            'item_name': ['AK-47 | Redline', 'AWP | Dragon Lore', 'Karambit | Fade'],
            'category': ['武器', '武器', '刀具'],
            'price': [52.30, 2500.00, 800.00],
            'volume_24h': [150, 50, 200]
        })
        
        panel = FilterPanel("test_filter")
        
        # 添加筛选器
        panel.add_filter("item_name", FilterType.TEXT, "项目名称")
        panel.add_filter("category", FilterType.SELECT, "类别", 
                        options=["全部", "武器", "刀具"], default_value="全部")
        panel.add_filter("price", FilterType.NUMBER_RANGE, "价格范围",
                        min_value=0.0, max_value=3000.0, default_value=(0.0, 3000.0))
        
        # 模拟筛选器状态（通常由Streamlit组件设置）
        import streamlit as st
        if not hasattr(st, 'session_state'):
            st.session_state = {}
        
        # 模拟活跃筛选器
        st.session_state[f"{panel.key}_filters"] = {
            "category": "武器",
            "price": (50.0, 1000.0)
        }
        st.session_state[f"{panel.key}_active_filters"] = {"category", "price"}
        
        # 应用筛选器
        filtered_data = panel.apply_filters_to_dataframe(data)
        
        # 验证筛选结果
        assert len(filtered_data) == 1  # 只有AK-47符合条件
        assert filtered_data.iloc[0]['item_name'] == 'AK-47 | Redline'
    
    def test_create_portfolio_filter_panel(self):
        """测试创建投资组合筛选面板"""
        panel = create_portfolio_filter_panel("test_portfolio_filter")
        
        assert panel.key == "test_portfolio_filter"
        assert panel.title == "投资组合筛选"
        assert len(panel.filter_configs) == 5
        
        # 检查筛选器配置
        assert "item_name" in panel.filter_configs
        assert "category" in panel.filter_configs
        assert "price_range" in panel.filter_configs
        assert "profit_percentage" in panel.filter_configs
        assert "holding_period" in panel.filter_configs
    
    def test_create_watchlist_filter_panel(self):
        """测试创建监控列表筛选面板"""
        panel = create_watchlist_filter_panel("test_watchlist_filter")
        
        assert panel.key == "test_watchlist_filter"
        assert panel.title == "监控列表筛选"
        assert len(panel.filter_configs) == 5
        
        # 检查筛选器配置
        assert "item_name" in panel.filter_configs
        assert "pool_type" in panel.filter_configs
        assert "priority_score" in panel.filter_configs
        assert "change_24h" in panel.filter_configs
        assert "volume_24h" in panel.filter_configs


class TestDashboardDataGeneration:
    """仪表盘数据生成测试"""
    
    def test_generate_core_watchlist_data(self):
        """测试生成核心关注池数据"""
        # 由于函数在dashboard.py中，我们需要导入它
        try:
            from app.pages.dashboard import generate_core_watchlist_data
            
            data = generate_core_watchlist_data()
            
            # 验证数据结构
            assert isinstance(data, pd.DataFrame)
            assert len(data) == 20  # 应该有20个项目
            
            # 验证列
            expected_columns = [
                'item_name', 'pool_type', 'current_price', 'change_24h',
                'volume_24h', 'priority_score', 'last_updated', 'category', 'rarity'
            ]
            for col in expected_columns:
                assert col in data.columns
            
            # 验证数据类型和范围
            assert data['current_price'].min() >= 50
            assert data['current_price'].max() <= 2000
            assert data['priority_score'].min() >= 3.0
            assert data['priority_score'].max() <= 9.5
            assert data['pool_type'].isin(['core', 'main']).all()
            
        except ImportError:
            pytest.skip("Dashboard module not available for testing")
    
    def test_generate_discovery_data(self):
        """测试生成发现机会数据"""
        try:
            from app.pages.dashboard import generate_discovery_data
            
            data = generate_discovery_data()
            
            # 验证数据结构
            assert isinstance(data, pd.DataFrame)
            assert len(data) == 12  # 应该有12个发现项目
            
            # 验证列
            expected_columns = [
                'item_name', 'ranking_type', 'ranking_position', 'opportunity_score',
                'current_price', 'price_change_7d', 'discovery_date', 'source', 'status'
            ]
            for col in expected_columns:
                assert col in data.columns
            
            # 验证数据类型和范围
            assert data['ranking_position'].min() >= 1
            assert data['ranking_position'].max() <= 50
            assert data['opportunity_score'].min() >= 4.0
            assert data['opportunity_score'].max() <= 9.0
            assert data['ranking_type'].isin(['hot', 'rising', 'new', 'falling']).all()
            
        except ImportError:
            pytest.skip("Dashboard module not available for testing")


class TestFilterTypes:
    """筛选器类型测试"""
    
    def test_filter_type_enum(self):
        """测试筛选器类型枚举"""
        assert FilterType.TEXT.value == "text"
        assert FilterType.NUMBER_RANGE.value == "number_range"
        assert FilterType.SELECT.value == "select"
        assert FilterType.MULTI_SELECT.value == "multi_select"
        assert FilterType.DATE_RANGE.value == "date_range"
        assert FilterType.BOOLEAN.value == "boolean"
        assert FilterType.SLIDER.value == "slider"


if __name__ == "__main__":
    # 运行基本测试
    print("运行投资组合仪表盘基本测试...")
    
    # 测试数据表格
    print("测试数据表格...")
    data = pd.DataFrame({
        'name': ['Item 1', 'Item 2'],
        'price': [100.0, 200.0],
        'quantity': [1, 2]
    })
    table = DataTable(data, "test", "测试表格")
    assert table.key == "test"
    assert table.title == "测试表格"
    print("✓ 数据表格测试通过")
    
    # 测试筛选面板
    print("测试筛选面板...")
    panel = FilterPanel("test_panel", "测试筛选")
    panel.add_filter("name", FilterType.TEXT, "名称")
    assert len(panel.filter_configs) == 1
    assert "name" in panel.filter_configs
    print("✓ 筛选面板测试通过")
    
    # 测试投资组合表格创建
    print("测试投资组合表格创建...")
    portfolio_data = pd.DataFrame({
        'name': ['AK-47 | Redline'],
        'current_price': [52.30],
        'quantity': [3]
    })
    portfolio_table = create_portfolio_table(portfolio_data)
    assert portfolio_table.title == "💼 投资组合"
    print("✓ 投资组合表格创建测试通过")
    
    # 测试监控列表表格创建
    print("测试监控列表表格创建...")
    watchlist_data = pd.DataFrame({
        'item_name': ['Karambit | Fade'],
        'pool_type': ['core'],
        'priority_score': [8.5]
    })
    watchlist_table = create_watchlist_table(watchlist_data)
    assert watchlist_table.title == "👁️ 监控列表"
    print("✓ 监控列表表格创建测试通过")
    
    # 测试发现机会表格创建
    print("测试发现机会表格创建...")
    discovery_data = pd.DataFrame({
        'item_name': ['StatTrak™ M4A1-S | Hyper Beast'],
        'ranking_type': ['rising'],
        'opportunity_score': [7.8]
    })
    discovery_table = create_discovery_table(discovery_data)
    assert discovery_table.title == "🔍 发现机会"
    print("✓ 发现机会表格创建测试通过")
    
    print("所有投资组合仪表盘测试通过！")
