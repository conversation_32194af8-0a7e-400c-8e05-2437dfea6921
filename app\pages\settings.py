"""
Ares系统设置页面
管理系统配置、用户偏好和应用设置
"""

import streamlit as st
import json
from datetime import datetime

from app.utils.state_manager import StateManager


def show_settings_page():
    """显示系统设置页面"""
    st.markdown('<h1 class="section-title">⚙️ 系统设置</h1>', unsafe_allow_html=True)
    
    # 获取状态管理器
    state_manager = StateManager()
    
    # 页面选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["🎨 界面设置", "🔔 通知设置", "🔧 系统配置", "📊 数据管理"])
    
    with tab1:
        show_ui_settings(state_manager)
    
    with tab2:
        show_notification_settings(state_manager)
    
    with tab3:
        show_system_config(state_manager)
    
    with tab4:
        show_data_management(state_manager)


def show_ui_settings(state_manager: StateManager):
    """显示界面设置"""
    st.markdown("### 🎨 界面设置")
    
    # 主题设置
    st.markdown("#### 🌙 主题设置")
    
    current_theme = state_manager.get_preference('theme', 'dark')
    theme = st.selectbox(
        "选择主题",
        options=['dark', 'light', 'auto'],
        index=['dark', 'light', 'auto'].index(current_theme),
        format_func=lambda x: {'dark': '🌙 深色主题', 'light': '☀️ 浅色主题', 'auto': '🔄 自动切换'}[x]
    )
    
    if theme != current_theme:
        state_manager.set_preference('theme', theme)
        st.success("主题设置已更新")
    
    # 语言设置
    st.markdown("#### 🌐 语言设置")
    
    current_language = state_manager.get_preference('language', 'zh-CN')
    language = st.selectbox(
        "选择语言",
        options=['zh-CN', 'en-US', 'ja-JP'],
        index=['zh-CN', 'en-US', 'ja-JP'].index(current_language),
        format_func=lambda x: {'zh-CN': '🇨🇳 简体中文', 'en-US': '🇺🇸 English', 'ja-JP': '🇯🇵 日本語'}[x]
    )
    
    if language != current_language:
        state_manager.set_preference('language', language)
        st.success("语言设置已更新")
    
    # 布局设置
    st.markdown("#### 📐 布局设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        sidebar_collapsed = st.checkbox(
            "默认折叠侧边栏",
            value=state_manager.get_preference('sidebar_collapsed', False)
        )
        state_manager.set_preference('sidebar_collapsed', sidebar_collapsed)
        
        compact_mode = st.checkbox(
            "紧凑模式",
            value=state_manager.get_preference('compact_mode', False)
        )
        state_manager.set_preference('compact_mode', compact_mode)
    
    with col2:
        show_tooltips = st.checkbox(
            "显示工具提示",
            value=state_manager.get_preference('show_tooltips', True)
        )
        state_manager.set_preference('show_tooltips', show_tooltips)
        
        animations_enabled = st.checkbox(
            "启用动画效果",
            value=state_manager.get_preference('animations_enabled', True)
        )
        state_manager.set_preference('animations_enabled', animations_enabled)
    
    # 数据显示设置
    st.markdown("#### 📊 数据显示设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        currency_format = st.selectbox(
            "货币格式",
            options=['USD', 'CNY', 'EUR', 'JPY'],
            index=['USD', 'CNY', 'EUR', 'JPY'].index(state_manager.get_preference('currency_format', 'USD')),
            format_func=lambda x: {'USD': '$ 美元', 'CNY': '¥ 人民币', 'EUR': '€ 欧元', 'JPY': '¥ 日元'}[x]
        )
        state_manager.set_preference('currency_format', currency_format)
        
        decimal_places = st.number_input(
            "小数位数",
            min_value=0,
            max_value=6,
            value=state_manager.get_preference('decimal_places', 2)
        )
        state_manager.set_preference('decimal_places', decimal_places)
    
    with col2:
        date_format = st.selectbox(
            "日期格式",
            options=['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY'],
            index=['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY'].index(state_manager.get_preference('date_format', 'YYYY-MM-DD'))
        )
        state_manager.set_preference('date_format', date_format)
        
        time_format = st.selectbox(
            "时间格式",
            options=['24h', '12h'],
            index=['24h', '12h'].index(state_manager.get_preference('time_format', '24h')),
            format_func=lambda x: {'24h': '24小时制', '12h': '12小时制'}[x]
        )
        state_manager.set_preference('time_format', time_format)


def show_notification_settings(state_manager: StateManager):
    """显示通知设置"""
    st.markdown("### 🔔 通知设置")
    
    # 通知开关
    st.markdown("#### 📢 通知开关")
    
    notifications_enabled = st.checkbox(
        "启用通知",
        value=state_manager.get_preference('notifications_enabled', True),
        help="总开关，关闭后将不会收到任何通知"
    )
    state_manager.set_preference('notifications_enabled', notifications_enabled)
    
    if notifications_enabled:
        # 通知类型设置
        st.markdown("#### 📋 通知类型")
        
        col1, col2 = st.columns(2)
        
        with col1:
            price_alerts = st.checkbox(
                "价格预警",
                value=state_manager.get_preference('price_alerts', True)
            )
            state_manager.set_preference('price_alerts', price_alerts)
            
            portfolio_alerts = st.checkbox(
                "投资组合预警",
                value=state_manager.get_preference('portfolio_alerts', True)
            )
            state_manager.set_preference('portfolio_alerts', portfolio_alerts)
            
            system_alerts = st.checkbox(
                "系统通知",
                value=state_manager.get_preference('system_alerts', True)
            )
            state_manager.set_preference('system_alerts', system_alerts)
        
        with col2:
            market_alerts = st.checkbox(
                "市场动态",
                value=state_manager.get_preference('market_alerts', True)
            )
            state_manager.set_preference('market_alerts', market_alerts)
            
            trading_alerts = st.checkbox(
                "交易提醒",
                value=state_manager.get_preference('trading_alerts', True)
            )
            state_manager.set_preference('trading_alerts', trading_alerts)
            
            news_alerts = st.checkbox(
                "新闻资讯",
                value=state_manager.get_preference('news_alerts', False)
            )
            state_manager.set_preference('news_alerts', news_alerts)
        
        # 通知方式设置
        st.markdown("#### 📱 通知方式")
        
        col1, col2 = st.columns(2)
        
        with col1:
            browser_notifications = st.checkbox(
                "浏览器通知",
                value=state_manager.get_preference('browser_notifications', True)
            )
            state_manager.set_preference('browser_notifications', browser_notifications)
            
            email_notifications = st.checkbox(
                "邮件通知",
                value=state_manager.get_preference('email_notifications', False)
            )
            state_manager.set_preference('email_notifications', email_notifications)
        
        with col2:
            discord_notifications = st.checkbox(
                "Discord通知",
                value=state_manager.get_preference('discord_notifications', False)
            )
            state_manager.set_preference('discord_notifications', discord_notifications)
            
            telegram_notifications = st.checkbox(
                "Telegram通知",
                value=state_manager.get_preference('telegram_notifications', False)
            )
            state_manager.set_preference('telegram_notifications', telegram_notifications)
        
        # 通知频率设置
        st.markdown("#### ⏰ 通知频率")
        
        notification_frequency = st.selectbox(
            "通知频率",
            options=['immediate', 'hourly', 'daily'],
            index=['immediate', 'hourly', 'daily'].index(state_manager.get_preference('notification_frequency', 'immediate')),
            format_func=lambda x: {'immediate': '立即通知', 'hourly': '每小时汇总', 'daily': '每日汇总'}[x]
        )
        state_manager.set_preference('notification_frequency', notification_frequency)
        
        # 免打扰时间
        st.markdown("#### 🔕 免打扰时间")
        
        col1, col2 = st.columns(2)
        
        with col1:
            quiet_hours_enabled = st.checkbox(
                "启用免打扰时间",
                value=state_manager.get_preference('quiet_hours_enabled', False)
            )
            state_manager.set_preference('quiet_hours_enabled', quiet_hours_enabled)
        
        if quiet_hours_enabled:
            with col2:
                quiet_start = st.time_input(
                    "开始时间",
                    value=datetime.strptime(state_manager.get_preference('quiet_start', '22:00'), '%H:%M').time()
                )
                state_manager.set_preference('quiet_start', quiet_start.strftime('%H:%M'))
                
                quiet_end = st.time_input(
                    "结束时间",
                    value=datetime.strptime(state_manager.get_preference('quiet_end', '08:00'), '%H:%M').time()
                )
                state_manager.set_preference('quiet_end', quiet_end.strftime('%H:%M'))


def show_system_config(state_manager: StateManager):
    """显示系统配置"""
    st.markdown("### 🔧 系统配置")
    
    # API设置
    st.markdown("#### 🔌 API设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        api_timeout = st.number_input(
            "API超时时间(秒)",
            min_value=5,
            max_value=120,
            value=state_manager.get_app_config('api_timeout', 30)
        )
        state_manager.set_app_config('api_timeout', api_timeout)
        
        max_retries = st.number_input(
            "最大重试次数",
            min_value=0,
            max_value=10,
            value=state_manager.get_app_config('max_retries', 3)
        )
        state_manager.set_app_config('max_retries', max_retries)
    
    with col2:
        rate_limit = st.number_input(
            "API调用频率限制(次/分钟)",
            min_value=1,
            max_value=100,
            value=state_manager.get_app_config('rate_limit', 10)
        )
        state_manager.set_app_config('rate_limit', rate_limit)
        
        batch_size = st.number_input(
            "批处理大小",
            min_value=1,
            max_value=1000,
            value=state_manager.get_app_config('batch_size', 100)
        )
        state_manager.set_app_config('batch_size', batch_size)
    
    # 缓存设置
    st.markdown("#### 💾 缓存设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        cache_enabled = st.checkbox(
            "启用缓存",
            value=state_manager.get_app_config('cache_enabled', True)
        )
        state_manager.set_app_config('cache_enabled', cache_enabled)
        
        cache_ttl = st.number_input(
            "缓存过期时间(秒)",
            min_value=60,
            max_value=86400,
            value=state_manager.get_app_config('cache_ttl', 300)
        )
        state_manager.set_app_config('cache_ttl', cache_ttl)
    
    with col2:
        max_cache_size = st.number_input(
            "最大缓存条目数",
            min_value=10,
            max_value=10000,
            value=state_manager.get_app_config('max_cache_size', 1000)
        )
        state_manager.set_app_config('max_cache_size', max_cache_size)
        
        if st.button("清除缓存"):
            state_manager.clear_cache()
            st.success("缓存已清除")
    
    # 性能设置
    st.markdown("#### ⚡ 性能设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        auto_refresh = st.checkbox(
            "自动刷新数据",
            value=state_manager.get_preference('auto_refresh', True)
        )
        state_manager.set_preference('auto_refresh', auto_refresh)
        
        if auto_refresh:
            refresh_interval = st.number_input(
                "刷新间隔(秒)",
                min_value=10,
                max_value=3600,
                value=state_manager.get_preference('refresh_interval', 30)
            )
            state_manager.set_preference('refresh_interval', refresh_interval)
    
    with col2:
        lazy_loading = st.checkbox(
            "延迟加载",
            value=state_manager.get_app_config('lazy_loading', True)
        )
        state_manager.set_app_config('lazy_loading', lazy_loading)
        
        parallel_requests = st.checkbox(
            "并行请求",
            value=state_manager.get_app_config('parallel_requests', True)
        )
        state_manager.set_app_config('parallel_requests', parallel_requests)
    
    # 调试设置
    st.markdown("#### 🐛 调试设置")
    
    debug_mode = st.checkbox(
        "调试模式",
        value=state_manager.get_app_config('debug_mode', False),
        help="启用后将显示详细的调试信息"
    )
    state_manager.set_app_config('debug_mode', debug_mode)
    
    if debug_mode:
        log_level = st.selectbox(
            "日志级别",
            options=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            index=['DEBUG', 'INFO', 'WARNING', 'ERROR'].index(state_manager.get_app_config('log_level', 'INFO'))
        )
        state_manager.set_app_config('log_level', log_level)


def show_data_management(state_manager: StateManager):
    """显示数据管理"""
    st.markdown("### 📊 数据管理")
    
    # 数据导入导出
    st.markdown("#### 📤 数据导入导出")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**导出数据**")
        
        export_format = st.selectbox(
            "导出格式",
            options=['JSON', 'CSV', 'Excel'],
            key="export_format"
        )
        
        export_data_type = st.multiselect(
            "选择导出数据",
            options=['持仓数据', '交易记录', '预警规则', '用户设置'],
            default=['持仓数据']
        )
        
        if st.button("导出数据"):
            # 模拟导出过程
            export_data = {}
            
            if '持仓数据' in export_data_type:
                portfolio_data = state_manager.get_portfolio_data()
                export_data['portfolio'] = portfolio_data
            
            if '用户设置' in export_data_type:
                export_data['settings'] = state_manager.export_session_data()
            
            # 生成下载链接
            if export_format == 'JSON':
                data_str = json.dumps(export_data, indent=2, ensure_ascii=False)
                st.download_button(
                    label="下载JSON文件",
                    data=data_str,
                    file_name=f"ares_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
            else:
                st.info(f"{export_format}格式导出功能开发中...")
    
    with col2:
        st.markdown("**导入数据**")
        
        uploaded_file = st.file_uploader(
            "选择数据文件",
            type=['json', 'csv', 'xlsx'],
            key="import_file"
        )
        
        if uploaded_file is not None:
            st.success("文件上传成功")
            
            import_options = st.multiselect(
                "选择导入内容",
                options=['持仓数据', '交易记录', '预警规则', '用户设置'],
                default=['用户设置']
            )
            
            if st.button("开始导入"):
                try:
                    if uploaded_file.type == "application/json":
                        import_data = json.load(uploaded_file)
                        
                        if '用户设置' in import_options and 'settings' in import_data:
                            success = state_manager.import_session_data(import_data['settings'])
                            if success:
                                st.success("用户设置导入成功")
                            else:
                                st.error("用户设置导入失败")
                        
                        st.success("数据导入完成")
                    else:
                        st.error("暂不支持此文件格式")
                
                except Exception as e:
                    st.error(f"导入失败: {str(e)}")
    
    # 数据清理
    st.markdown("#### 🧹 数据清理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**缓存管理**")
        
        cache_stats = state_manager.get_cache_stats()
        
        st.metric("缓存条目数", cache_stats['total_items'])
        st.metric("过期条目数", cache_stats['expired_items'])
        st.metric("缓存命中率", f"{cache_stats['cache_hit_rate']:.1%}")
        
        if st.button("清理过期缓存"):
            # 这里应该实现清理过期缓存的逻辑
            st.success("过期缓存已清理")
    
    with col2:
        st.markdown("**数据重置**")
        
        st.warning("⚠️ 以下操作不可撤销，请谨慎操作")
        
        if st.button("重置用户偏好", type="secondary"):
            # 重置用户偏好到默认值
            st.session_state.user_preferences = {
                'theme': 'dark',
                'language': 'zh-CN',
                'timezone': 'Asia/Shanghai',
                'auto_refresh': True,
                'refresh_interval': 30,
                'notifications_enabled': True,
                'sound_enabled': False
            }
            st.success("用户偏好已重置")
        
        if st.button("清除所有数据", type="secondary"):
            confirm = st.checkbox("我确认要清除所有数据")
            if confirm:
                state_manager.clear_cache()
                state_manager.clear_temp_data()
                st.success("所有数据已清除")
    
    # 数据统计
    st.markdown("#### 📈 数据统计")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("会话时长", "2小时15分钟")
    
    with col2:
        st.metric("页面访问次数", "47")
    
    with col3:
        st.metric("API调用次数", "156")
    
    with col4:
        st.metric("数据更新次数", "23")


if __name__ == "__main__":
    show_settings_page()
