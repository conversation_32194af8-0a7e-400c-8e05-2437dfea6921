"""
Ares分层追踪服务
实现核心关注池和主监控池的差异化数据更新，集成智能调度算法
"""

import asyncio
import logging
import json
import signal
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

from core.scheduler import get_scheduler, IntelligentScheduler
from core.api_manager import get_api_manager, APIManager, APICallResult
from core.database import get_database_manager, DatabaseManager
from core.config import get_config_manager
from core.exceptions import AresException, ErrorSeverity, ErrorContext
from monitoring.system_monitor import get_system_monitor

logger = logging.getLogger(__name__)


@dataclass
class TrackingStats:
    """追踪统计信息"""
    total_updates: int = 0
    successful_updates: int = 0
    failed_updates: int = 0
    api_calls_today: int = 0
    core_pool_updates: int = 0
    main_pool_updates: int = 0
    last_update_time: Optional[datetime] = None
    uptime_start: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'total_updates': self.total_updates,
            'successful_updates': self.successful_updates,
            'failed_updates': self.failed_updates,
            'api_calls_today': self.api_calls_today,
            'core_pool_updates': self.core_pool_updates,
            'main_pool_updates': self.main_pool_updates,
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'uptime_start': self.uptime_start.isoformat() if self.uptime_start else None,
            'success_rate': (self.successful_updates / self.total_updates * 100) if self.total_updates > 0 else 0
        }


@dataclass
class TrackingBatch:
    """追踪批次"""
    items: List[str]  # 饰品ID列表
    pool_type: str    # 池类型
    priority_range: tuple  # 优先级范围
    estimated_time: int    # 预估执行时间(秒)


class TrackerService:
    """分层追踪服务"""
    
    def __init__(self):
        """初始化追踪服务"""
        self.config = get_config_manager()
        self.db_manager = get_database_manager()
        self.scheduler: Optional[IntelligentScheduler] = None
        self.api_manager: Optional[APIManager] = None
        self.system_monitor = get_system_monitor()
        
        # 追踪状态
        self.running = False
        self.stats = TrackingStats()
        self.state_file = Path("data/tracker_state.json")
        
        # 配置参数
        self.batch_size = self.config.get('tracker.batch_size', 5)
        self.cycle_interval = self.config.get('tracker.cycle_interval', 30)  # 秒
        self.health_check_interval = self.config.get('tracker.health_check_interval', 300)  # 5分钟
        self.max_retries = self.config.get('tracker.max_retries', 3)
        
        # 信号处理
        self._setup_signal_handlers()
        
        logger.info("Tracker service initialized")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except Exception as e:
            logger.warning("Failed to setup signal handlers: %s", str(e))
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info("Received signal %d, initiating graceful shutdown", signum)
        asyncio.create_task(self.stop())
    
    async def initialize(self):
        """初始化服务组件"""
        try:
            # 初始化调度器
            self.scheduler = await get_scheduler()
            
            # 初始化API管理器
            self.api_manager = await get_api_manager()
            
            # 加载状态
            self._load_state()
            
            # 设置运行时间
            if not self.stats.uptime_start:
                self.stats.uptime_start = datetime.utcnow()
            
            logger.info("Tracker service components initialized")
            
        except Exception as e:
            logger.error("Failed to initialize tracker service: %s", str(e))
            raise AresException(
                message=f"Tracker initialization failed: {str(e)}",
                context=ErrorContext(operation="tracker_init"),
                severity=ErrorSeverity.CRITICAL
            )
    
    async def start(self):
        """启动追踪服务"""
        if self.running:
            logger.warning("Tracker service is already running")
            return
        
        logger.info("Starting tracker service")
        
        try:
            # 初始化组件
            await self.initialize()
            
            # 启动调度器
            if not self.scheduler.running:
                await self.scheduler.start()
            
            # 设置运行状态
            self.running = True
            
            # 启动主循环
            await self._run_main_loop()
            
        except Exception as e:
            logger.error("Error starting tracker service: %s", str(e))
            self.running = False
            raise
    
    async def stop(self):
        """停止追踪服务"""
        logger.info("Stopping tracker service")
        
        self.running = False
        
        try:
            # 停止调度器
            if self.scheduler:
                await self.scheduler.stop()
            
            # 保存状态
            self._save_state()
            
            logger.info("Tracker service stopped gracefully")
            
        except Exception as e:
            logger.error("Error stopping tracker service: %s", str(e))
    
    async def _run_main_loop(self):
        """运行主循环"""
        logger.info("Starting tracker main loop")
        
        last_health_check = datetime.utcnow()
        
        while self.running:
            try:
                cycle_start = datetime.utcnow()
                
                # 执行追踪周期
                await self._run_tracking_cycle()
                
                # 定期健康检查
                if (datetime.utcnow() - last_health_check).total_seconds() >= self.health_check_interval:
                    await self._perform_health_check()
                    last_health_check = datetime.utcnow()
                
                # 计算休眠时间
                cycle_duration = (datetime.utcnow() - cycle_start).total_seconds()
                sleep_time = max(0, self.cycle_interval - cycle_duration)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
            except Exception as e:
                logger.error("Error in tracker main loop: %s", str(e))
                
                # 记录系统监控
                self.system_monitor.record_api_request("tracker_cycle", False, 0, False)
                
                # 短暂休眠后重试
                await asyncio.sleep(min(60, self.cycle_interval))
    
    async def _run_tracking_cycle(self):
        """运行追踪周期"""
        logger.debug("Starting tracking cycle")
        
        try:
            # 获取下一批待更新的饰品
            batch = await self._get_next_batch()
            
            if not batch.items:
                logger.debug("No items to update in this cycle")
                return
            
            logger.info("Processing batch: %d items from %s pool", 
                       len(batch.items), batch.pool_type)
            
            # 处理批次
            await self._process_batch(batch)
            
            # 更新统计
            self.stats.last_update_time = datetime.utcnow()
            
            # 定期保存状态
            if self.stats.total_updates % 10 == 0:
                self._save_state()
            
        except Exception as e:
            logger.error("Error in tracking cycle: %s", str(e))
            raise
    
    async def _get_next_batch(self) -> TrackingBatch:
        """获取下一批待处理的饰品"""
        try:
            # 从调度器获取待处理任务
            if not self.scheduler or not self.scheduler.task_queue:
                return TrackingBatch([], "none", (0, 0), 0)
            
            # 获取当前时间
            now = datetime.utcnow()
            
            # 收集需要更新的任务
            ready_tasks = []
            for task in self.scheduler.task_queue:
                if task.next_update_time <= now:
                    ready_tasks.append(task)
                    if len(ready_tasks) >= self.batch_size:
                        break
            
            if not ready_tasks:
                return TrackingBatch([], "none", (0, 0), 0)
            
            # 分析批次特征
            pool_types = [task.pool_type for task in ready_tasks]
            priorities = [task.priority_score for task in ready_tasks]
            
            # 确定主要池类型
            main_pool_type = max(set(pool_types), key=pool_types.count)
            
            # 创建批次
            batch = TrackingBatch(
                items=[task.item_id for task in ready_tasks],
                pool_type=main_pool_type,
                priority_range=(min(priorities), max(priorities)),
                estimated_time=len(ready_tasks) * 6  # 每个饰品约6秒
            )
            
            return batch
            
        except Exception as e:
            logger.error("Error getting next batch: %s", str(e))
            return TrackingBatch([], "error", (0, 0), 0)
    
    async def _process_batch(self, batch: TrackingBatch):
        """处理饰品批次"""
        successful_count = 0
        failed_count = 0
        
        for item_id in batch.items:
            try:
                # 更新单个饰品
                success = await self._update_item(item_id, batch.pool_type)
                
                if success:
                    successful_count += 1
                    self.stats.successful_updates += 1
                    
                    # 根据池类型更新统计
                    if batch.pool_type == 'core':
                        self.stats.core_pool_updates += 1
                    else:
                        self.stats.main_pool_updates += 1
                else:
                    failed_count += 1
                    self.stats.failed_updates += 1
                
                self.stats.total_updates += 1
                
                # 记录系统监控
                self.system_monitor.record_api_request(
                    f"tracker_{batch.pool_type}", success, 6.0, False
                )
                
            except Exception as e:
                logger.error("Error updating item %s: %s", item_id, str(e))
                failed_count += 1
                self.stats.failed_updates += 1
                self.stats.total_updates += 1
        
        logger.info("Batch processed: %d successful, %d failed", 
                   successful_count, failed_count)
    
    async def _update_item(self, item_id: str, pool_type: str) -> bool:
        """更新单个饰品数据"""
        try:
            # 调用API获取数据
            response = await self.api_manager.get_item_price(item_id)
            
            if response.status == APICallResult.SUCCESS:
                # 更新数据库
                await self._update_database(item_id, response.data)
                
                # 更新API调用统计
                self.stats.api_calls_today += 1
                
                logger.debug("Successfully updated item %s", item_id)
                return True
            else:
                logger.warning("API call failed for item %s: %s", 
                             item_id, response.error_message)
                return False
                
        except Exception as e:
            logger.error("Error updating item %s: %s", item_id, str(e))
            return False
    
    async def _update_database(self, item_id: str, data: Dict[str, Any]):
        """更新数据库"""
        try:
            async with self.db_manager.get_session() as session:
                # 这里应该实现具体的数据库更新逻辑
                # 暂时记录日志
                logger.debug("Database update for item %s: %s", item_id, data)
                
        except Exception as e:
            logger.error("Database update failed for item %s: %s", item_id, str(e))
            raise
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            logger.debug("Performing health check")
            
            # 检查调度器状态
            if not self.scheduler or not self.scheduler.running:
                logger.warning("Scheduler is not running")
                return False
            
            # 检查API管理器状态
            if not self.api_manager:
                logger.warning("API manager is not available")
                return False
            
            # 检查数据库连接
            try:
                async with self.db_manager.get_session() as session:
                    # 简单的数据库连接测试
                    pass
            except Exception as e:
                logger.warning("Database health check failed: %s", str(e))
                return False
            
            logger.debug("Health check passed")
            return True
            
        except Exception as e:
            logger.error("Health check error: %s", str(e))
            return False
    
    def _load_state(self):
        """加载状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                # 恢复统计信息
                self.stats.total_updates = state_data.get('total_updates', 0)
                self.stats.successful_updates = state_data.get('successful_updates', 0)
                self.stats.failed_updates = state_data.get('failed_updates', 0)
                self.stats.api_calls_today = state_data.get('api_calls_today', 0)
                self.stats.core_pool_updates = state_data.get('core_pool_updates', 0)
                self.stats.main_pool_updates = state_data.get('main_pool_updates', 0)
                
                # 恢复时间信息
                if state_data.get('last_update_time'):
                    self.stats.last_update_time = datetime.fromisoformat(state_data['last_update_time'])
                if state_data.get('uptime_start'):
                    self.stats.uptime_start = datetime.fromisoformat(state_data['uptime_start'])
                
                logger.info("Tracker state loaded from %s", self.state_file)
        except Exception as e:
            logger.warning("Failed to load tracker state: %s", str(e))
    
    def _save_state(self):
        """保存状态"""
        try:
            # 确保目录存在
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存状态数据
            state_data = self.stats.to_dict()
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            logger.debug("Tracker state saved to %s", self.state_file)
        except Exception as e:
            logger.error("Failed to save tracker state: %s", str(e))
    
    def get_status(self) -> Dict[str, Any]:
        """获取追踪器状态"""
        uptime = None
        if self.stats.uptime_start:
            uptime_seconds = (datetime.utcnow() - self.stats.uptime_start).total_seconds()
            uptime = f"{int(uptime_seconds // 3600)}h {int((uptime_seconds % 3600) // 60)}m"
        
        return {
            'running': self.running,
            'uptime': uptime,
            'stats': self.stats.to_dict(),
            'scheduler_status': self.scheduler.get_schedule_status() if self.scheduler else None,
            'last_health_check': datetime.utcnow().isoformat()
        }
    
    async def force_update_item(self, item_id: str) -> bool:
        """强制更新指定饰品"""
        try:
            logger.info("Force updating item %s", item_id)
            
            # 通过调度器强制更新
            if self.scheduler:
                await self.scheduler.force_update_item(item_id)
            
            # 直接更新
            return await self._update_item(item_id, "manual")
            
        except Exception as e:
            logger.error("Force update failed for item %s: %s", item_id, str(e))
            return False


# 全局追踪器实例
_tracker_service: Optional[TrackerService] = None


def get_tracker_service() -> TrackerService:
    """获取全局追踪器服务实例"""
    global _tracker_service
    if _tracker_service is None:
        _tracker_service = TrackerService()
    return _tracker_service
