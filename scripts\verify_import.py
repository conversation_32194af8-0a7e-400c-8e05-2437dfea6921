#!/usr/bin/env python3
"""
验证基础数据导入结果
检查数据库中的饰品数据是否正确导入
"""

import sys
import json
from pathlib import Path
from collections import Counter

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import get_database_manager, Item
from sqlalchemy import func


def main():
    """主函数"""
    print("验证基础数据导入结果...")
    
    # 获取数据库管理器
    db_manager = get_database_manager()
    
    with db_manager.get_session() as session:
        # 1. 总数统计
        total_items = session.query(Item).count()
        print(f"数据库中总饰品数量: {total_items}")
        
        # 2. 按数据源统计
        steamdt_items = session.query(Item).filter(Item.data_source == 'steamdt').count()
        print(f"SteamDT来源饰品数量: {steamdt_items}")
        
        # 3. 活跃饰品统计
        active_items = session.query(Item).filter(Item.is_active == True).count()
        print(f"活跃饰品数量: {active_items}")
        
        # 4. 按武器类型统计 (前10)
        weapon_stats = session.query(
            Item.weapon_type, 
            func.count(Item.market_hash_name)
        ).filter(
            Item.weapon_type.isnot(None),
            Item.weapon_type != ''
        ).group_by(Item.weapon_type).order_by(
            func.count(Item.market_hash_name).desc()
        ).limit(10).all()
        
        print("\n武器类型统计 (前10):")
        for weapon_type, count in weapon_stats:
            print(f"  {weapon_type}: {count}")
        
        # 5. 平台数据统计
        items_with_platform_data = session.query(Item).filter(
            Item.platform_data.isnot(None),
            Item.platform_data != ''
        ).count()
        print(f"\n有平台数据的饰品: {items_with_platform_data}")
        
        # 6. 检查一些示例数据
        print("\n示例饰品数据:")
        sample_items = session.query(Item).limit(5).all()
        for item in sample_items:
            print(f"  {item.name} ({item.market_hash_name})")
            if item.platform_data:
                try:
                    platform_data = json.loads(item.platform_data)
                    platforms = list(platform_data.keys())
                    print(f"    平台: {', '.join(platforms)}")
                except:
                    print("    平台数据解析失败")
        
        # 7. 数据质量检查
        print("\n数据质量检查:")
        
        # 检查空名称
        empty_names = session.query(Item).filter(
            (Item.name == '') | (Item.name.is_(None))
        ).count()
        print(f"  空名称饰品: {empty_names}")
        
        # 检查空market_hash_name (理论上不应该有，因为是主键)
        empty_hash_names = session.query(Item).filter(
            (Item.market_hash_name == '') | (Item.market_hash_name.is_(None))
        ).count()
        print(f"  空market_hash_name: {empty_hash_names}")
        
        # 检查没有平台数据的饰品
        no_platform_data = session.query(Item).filter(
            (Item.platform_data == '') | 
            (Item.platform_data.is_(None)) |
            (Item.platform_data == '{}')
        ).count()
        print(f"  无平台数据饰品: {no_platform_data}")
        
        # 8. 最近同步时间检查
        recent_synced = session.query(Item).filter(
            Item.last_sync_time.isnot(None)
        ).count()
        print(f"  有同步时间的饰品: {recent_synced}")
    
    # 9. 与原始文件对比
    data_file = project_root / 'data' / 'basedata.json'
    if data_file.exists():
        print(f"\n与原始文件对比:")
        with open(data_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        original_count = len(original_data.get('data', []))
        print(f"  原始文件饰品数量: {original_count}")
        print(f"  数据库饰品数量: {total_items}")
        print(f"  差异: {abs(original_count - total_items)}")
        
        if original_count == total_items:
            print("  ✅ 数量匹配")
        else:
            print("  ❌ 数量不匹配")
    
    print("\n验证完成!")
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"验证失败: {e}")
        sys.exit(1)
