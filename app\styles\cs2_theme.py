"""
CS2饰品专用主题和样式
为CS2饰品投资界面提供专门的视觉设计
"""

import streamlit as st


def apply_cs2_theme():
    """应用CS2主题样式"""
    st.markdown("""
    <style>
    /* CS2主题色彩 */
    :root {
        --cs2-primary: #667eea;
        --cs2-secondary: #764ba2;
        --cs2-accent: #f093fb;
        --cs2-success: #4ecdc4;
        --cs2-warning: #ffe66d;
        --cs2-danger: #ff6b6b;
        --cs2-info: #4dabf7;
        --cs2-dark: #2c3e50;
        --cs2-light: #ecf0f1;
        --cs2-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --cs2-card-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
    
    /* CS2页面背景 */
    .cs2-page {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
    }
    
    /* CS2卡片样式 */
    .cs2-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }
    
    .cs2-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
    }
    
    /* CS2标题样式 */
    .cs2-title {
        background: var(--cs2-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 2.5rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 30px;
    }
    
    .cs2-subtitle {
        color: var(--cs2-dark);
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 20px;
        border-left: 4px solid var(--cs2-primary);
        padding-left: 15px;
    }
    
    /* CS2按钮样式 */
    .cs2-button {
        background: var(--cs2-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .cs2-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .cs2-button.secondary {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    }
    
    .cs2-button.success {
        background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    }
    
    .cs2-button.warning {
        background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    }
    
    .cs2-button.danger {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    }
    
    /* CS2指标卡片 */
    .cs2-metric-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-top: 4px solid var(--cs2-primary);
        transition: all 0.3s ease;
    }
    
    .cs2-metric-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .cs2-metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--cs2-primary);
        margin-bottom: 5px;
    }
    
    .cs2-metric-label {
        color: var(--cs2-dark);
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .cs2-metric-change {
        font-size: 0.8rem;
        margin-top: 5px;
    }
    
    .cs2-metric-change.positive {
        color: var(--cs2-success);
    }
    
    .cs2-metric-change.negative {
        color: var(--cs2-danger);
    }
    
    /* CS2表格样式 */
    .cs2-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .cs2-table thead {
        background: var(--cs2-gradient);
        color: white;
    }
    
    .cs2-table th {
        padding: 15px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.8rem;
    }
    
    .cs2-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
    }
    
    .cs2-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
    }
    
    /* CS2标签样式 */
    .cs2-tag {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin: 2px;
    }
    
    .cs2-tag.rarity-consumer {
        background: #95a5a6;
        color: white;
    }
    
    .cs2-tag.rarity-industrial {
        background: #3498db;
        color: white;
    }
    
    .cs2-tag.rarity-milspec {
        background: #2ecc71;
        color: white;
    }
    
    .cs2-tag.rarity-restricted {
        background: #9b59b6;
        color: white;
    }
    
    .cs2-tag.rarity-classified {
        background: #e74c3c;
        color: white;
    }
    
    .cs2-tag.rarity-covert {
        background: #f39c12;
        color: white;
    }
    
    .cs2-tag.rarity-contraband {
        background: #e67e22;
        color: white;
    }
    
    .cs2-tag.weapon-rifle {
        background: #34495e;
        color: white;
    }
    
    .cs2-tag.weapon-sniper {
        background: #16a085;
        color: white;
    }
    
    .cs2-tag.weapon-pistol {
        background: #8e44ad;
        color: white;
    }
    
    .cs2-tag.weapon-knife {
        background: #c0392b;
        color: white;
    }
    
    .cs2-tag.weapon-gloves {
        background: #d35400;
        color: white;
    }
    
    .cs2-tag.quality-normal {
        background: #7f8c8d;
        color: white;
    }
    
    .cs2-tag.quality-stattrak {
        background: #e74c3c;
        color: white;
    }
    
    .cs2-tag.quality-souvenir {
        background: #f39c12;
        color: white;
    }
    
    /* CS2进度条 */
    .cs2-progress {
        background: #ecf0f1;
        border-radius: 10px;
        height: 8px;
        overflow: hidden;
        margin: 10px 0;
    }
    
    .cs2-progress-bar {
        height: 100%;
        background: var(--cs2-gradient);
        border-radius: 10px;
        transition: width 0.3s ease;
    }
    
    /* CS2警告框 */
    .cs2-alert {
        padding: 15px 20px;
        border-radius: 8px;
        margin: 15px 0;
        border-left: 4px solid;
    }
    
    .cs2-alert.info {
        background: rgba(74, 171, 247, 0.1);
        border-color: var(--cs2-info);
        color: #1c7ed6;
    }
    
    .cs2-alert.success {
        background: rgba(78, 205, 196, 0.1);
        border-color: var(--cs2-success);
        color: #0ca678;
    }
    
    .cs2-alert.warning {
        background: rgba(255, 230, 109, 0.1);
        border-color: var(--cs2-warning);
        color: #fab005;
    }
    
    .cs2-alert.danger {
        background: rgba(255, 107, 107, 0.1);
        border-color: var(--cs2-danger);
        color: #e03131;
    }
    
    /* CS2图表容器 */
    .cs2-chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    /* CS2响应式设计 */
    @media (max-width: 768px) {
        .cs2-card {
            margin: 10px 0;
            padding: 15px;
        }
        
        .cs2-title {
            font-size: 2rem;
        }
        
        .cs2-subtitle {
            font-size: 1.2rem;
        }
        
        .cs2-metric-card {
            padding: 15px;
        }
        
        .cs2-metric-value {
            font-size: 1.5rem;
        }
    }
    
    /* CS2动画效果 */
    @keyframes cs2-fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .cs2-fade-in {
        animation: cs2-fadeIn 0.6s ease-out;
    }
    
    @keyframes cs2-pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }
    
    .cs2-pulse {
        animation: cs2-pulse 2s infinite;
    }
    
    /* CS2滚动条样式 */
    .cs2-scrollbar::-webkit-scrollbar {
        width: 8px;
    }
    
    .cs2-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    
    .cs2-scrollbar::-webkit-scrollbar-thumb {
        background: var(--cs2-gradient);
        border-radius: 10px;
    }
    
    .cs2-scrollbar::-webkit-scrollbar-thumb:hover {
        background: var(--cs2-secondary);
    }
    </style>
    """, unsafe_allow_html=True)


def get_rarity_color(rarity: str) -> str:
    """获取稀有度对应的颜色"""
    rarity_colors = {
        '消费级': '#95a5a6',
        '工业级': '#3498db',
        '军规级': '#2ecc71',
        '受限': '#9b59b6',
        '保密': '#e74c3c',
        '隐秘': '#f39c12',
        '违禁品': '#e67e22'
    }
    return rarity_colors.get(rarity, '#95a5a6')


def get_weapon_type_color(weapon_type: str) -> str:
    """获取武器类型对应的颜色"""
    weapon_colors = {
        '步枪': '#34495e',
        '狙击枪': '#16a085',
        '手枪': '#8e44ad',
        '冲锋枪': '#27ae60',
        '霰弹枪': '#2980b9',
        '机枪': '#8e44ad',
        '刀具': '#c0392b',
        '手套': '#d35400'
    }
    return weapon_colors.get(weapon_type, '#7f8c8d')


def get_quality_color(quality: str) -> str:
    """获取品质对应的颜色"""
    quality_colors = {
        '普通': '#7f8c8d',
        'StatTrak™': '#e74c3c',
        '纪念品': '#f39c12'
    }
    return quality_colors.get(quality, '#7f8c8d')


def create_cs2_metric_html(label: str, value: str, change: str = None, 
                          change_type: str = "neutral") -> str:
    """创建CS2风格的指标HTML"""
    change_html = ""
    if change:
        change_class = f"cs2-metric-change {change_type}"
        change_html = f'<div class="{change_class}">{change}</div>'
    
    return f"""
    <div class="cs2-metric-card cs2-fade-in">
        <div class="cs2-metric-value">{value}</div>
        <div class="cs2-metric-label">{label}</div>
        {change_html}
    </div>
    """


def create_cs2_alert_html(message: str, alert_type: str = "info") -> str:
    """创建CS2风格的警告框HTML"""
    return f"""
    <div class="cs2-alert {alert_type}">
        {message}
    </div>
    """


def create_cs2_progress_html(value: float, max_value: float = 100, 
                            label: str = "") -> str:
    """创建CS2风格的进度条HTML"""
    percentage = (value / max_value) * 100
    
    return f"""
    <div>
        {f'<div class="cs2-metric-label">{label}</div>' if label else ''}
        <div class="cs2-progress">
            <div class="cs2-progress-bar" style="width: {percentage}%"></div>
        </div>
        <div style="text-align: right; font-size: 0.8rem; color: #7f8c8d;">
            {value}/{max_value}
        </div>
    </div>
    """
