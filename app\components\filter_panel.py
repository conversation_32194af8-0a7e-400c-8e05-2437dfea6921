"""
Ares系统筛选面板组件
提供智能筛选功能，支持多维度筛选条件
"""

import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum


class FilterType(Enum):
    """筛选器类型"""
    TEXT = "text"
    NUMBER_RANGE = "number_range"
    SELECT = "select"
    MULTI_SELECT = "multi_select"
    DATE_RANGE = "date_range"
    BOOLEAN = "boolean"
    SLIDER = "slider"


class FilterPanel:
    """筛选面板组件"""
    
    def __init__(self, key: str, title: str = "智能筛选"):
        """
        初始化筛选面板
        
        Args:
            key: 组件唯一标识
            title: 面板标题
        """
        self.key = key
        self.title = title
        self.filters = {}
        self.filter_configs = {}
        
        # 初始化会话状态
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if f"{self.key}_filters" not in st.session_state:
            st.session_state[f"{self.key}_filters"] = {}
        
        if f"{self.key}_active_filters" not in st.session_state:
            st.session_state[f"{self.key}_active_filters"] = set()
    
    def add_filter(self, 
                   filter_key: str,
                   filter_type: FilterType,
                   label: str,
                   options: Optional[List[Any]] = None,
                   min_value: Optional[float] = None,
                   max_value: Optional[float] = None,
                   default_value: Any = None,
                   help_text: Optional[str] = None):
        """
        添加筛选器
        
        Args:
            filter_key: 筛选器键
            filter_type: 筛选器类型
            label: 显示标签
            options: 选项列表（用于select类型）
            min_value: 最小值（用于数值类型）
            max_value: 最大值（用于数值类型）
            default_value: 默认值
            help_text: 帮助文本
        """
        self.filter_configs[filter_key] = {
            'type': filter_type,
            'label': label,
            'options': options,
            'min_value': min_value,
            'max_value': max_value,
            'default_value': default_value,
            'help_text': help_text
        }
    
    def render(self) -> Dict[str, Any]:
        """渲染筛选面板"""
        with st.expander(f"🎛️ {self.title}", expanded=True):
            # 快速筛选预设
            self._render_quick_filters()
            
            st.markdown("---")
            
            # 自定义筛选器
            self._render_custom_filters()
            
            # 筛选器操作按钮
            self._render_filter_actions()
        
        return self.get_active_filters()
    
    def _render_quick_filters(self):
        """渲染快速筛选预设"""
        st.markdown("**🚀 快速筛选**")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("💎 高价值项目", key=f"{self.key}_quick_high_value", use_container_width=True):
                self._apply_quick_filter("high_value")
        
        with col2:
            if st.button("📈 上涨趋势", key=f"{self.key}_quick_rising", use_container_width=True):
                self._apply_quick_filter("rising")
        
        with col3:
            if st.button("🔥 热门项目", key=f"{self.key}_quick_hot", use_container_width=True):
                self._apply_quick_filter("hot")
        
        with col4:
            if st.button("⚡ 新发现", key=f"{self.key}_quick_new", use_container_width=True):
                self._apply_quick_filter("new")
    
    def _render_custom_filters(self):
        """渲染自定义筛选器"""
        if not self.filter_configs:
            st.info("暂无可用的筛选器")
            return
        
        st.markdown("**🔧 自定义筛选**")
        
        # 按行排列筛选器
        cols_per_row = 3
        filter_items = list(self.filter_configs.items())
        
        for i in range(0, len(filter_items), cols_per_row):
            cols = st.columns(cols_per_row)
            
            for j, (filter_key, config) in enumerate(filter_items[i:i+cols_per_row]):
                with cols[j]:
                    self._render_single_filter(filter_key, config)
    
    def _render_single_filter(self, filter_key: str, config: Dict[str, Any]):
        """渲染单个筛选器"""
        filter_type = config['type']
        label = config['label']
        help_text = config.get('help_text')
        
        widget_key = f"{self.key}_filter_{filter_key}"
        
        if filter_type == FilterType.TEXT:
            value = st.text_input(
                label,
                value=config.get('default_value', ''),
                help=help_text,
                key=widget_key
            )
            
        elif filter_type == FilterType.SELECT:
            options = config.get('options', [])
            default_index = 0
            if config.get('default_value') in options:
                default_index = options.index(config.get('default_value'))
            
            value = st.selectbox(
                label,
                options,
                index=default_index,
                help=help_text,
                key=widget_key
            )
            
        elif filter_type == FilterType.MULTI_SELECT:
            options = config.get('options', [])
            default_value = config.get('default_value', [])
            
            value = st.multiselect(
                label,
                options,
                default=default_value,
                help=help_text,
                key=widget_key
            )
            
        elif filter_type == FilterType.NUMBER_RANGE:
            min_val = config.get('min_value', 0.0)
            max_val = config.get('max_value', 100.0)
            default_val = config.get('default_value', (min_val, max_val))
            
            value = st.slider(
                label,
                min_value=min_val,
                max_value=max_val,
                value=default_val,
                help=help_text,
                key=widget_key
            )
            
        elif filter_type == FilterType.SLIDER:
            min_val = config.get('min_value', 0.0)
            max_val = config.get('max_value', 100.0)
            default_val = config.get('default_value', min_val)
            
            value = st.slider(
                label,
                min_value=min_val,
                max_value=max_val,
                value=default_val,
                help=help_text,
                key=widget_key
            )
            
        elif filter_type == FilterType.BOOLEAN:
            default_val = config.get('default_value', False)
            
            value = st.checkbox(
                label,
                value=default_val,
                help=help_text,
                key=widget_key
            )
            
        elif filter_type == FilterType.DATE_RANGE:
            default_start = config.get('default_value', [datetime.now() - timedelta(days=30), datetime.now()])
            
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input(
                    f"{label} 开始",
                    value=default_start[0],
                    help=help_text,
                    key=f"{widget_key}_start"
                )
            with col2:
                end_date = st.date_input(
                    f"{label} 结束",
                    value=default_start[1],
                    help=help_text,
                    key=f"{widget_key}_end"
                )
            
            value = (start_date, end_date)
        
        else:
            st.error(f"不支持的筛选器类型: {filter_type}")
            return
        
        # 保存筛选器值
        st.session_state[f"{self.key}_filters"][filter_key] = value
        
        # 检查是否为活跃筛选器
        if self._is_filter_active(filter_key, value, config):
            st.session_state[f"{self.key}_active_filters"].add(filter_key)
        else:
            st.session_state[f"{self.key}_active_filters"].discard(filter_key)
    
    def _is_filter_active(self, filter_key: str, value: Any, config: Dict[str, Any]) -> bool:
        """检查筛选器是否处于活跃状态"""
        filter_type = config['type']
        default_value = config.get('default_value')
        
        if filter_type == FilterType.TEXT:
            return bool(value and value.strip())
        
        elif filter_type in [FilterType.SELECT]:
            return value != default_value
        
        elif filter_type == FilterType.MULTI_SELECT:
            return bool(value)
        
        elif filter_type == FilterType.NUMBER_RANGE:
            min_val = config.get('min_value', 0.0)
            max_val = config.get('max_value', 100.0)
            return value != (min_val, max_val)
        
        elif filter_type == FilterType.SLIDER:
            return value != default_value
        
        elif filter_type == FilterType.BOOLEAN:
            return value != default_value
        
        elif filter_type == FilterType.DATE_RANGE:
            return True  # 日期范围总是被认为是活跃的
        
        return False
    
    def _render_filter_actions(self):
        """渲染筛选器操作按钮"""
        st.markdown("---")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 重置所有", key=f"{self.key}_reset_all", use_container_width=True):
                self.reset_all_filters()
                st.rerun()
        
        with col2:
            active_count = len(st.session_state[f"{self.key}_active_filters"])
            st.metric("活跃筛选器", active_count)
        
        with col3:
            if st.button("💾 保存预设", key=f"{self.key}_save_preset", use_container_width=True):
                self._save_filter_preset()
    
    def _apply_quick_filter(self, filter_type: str):
        """应用快速筛选预设"""
        if filter_type == "high_value":
            # 高价值项目筛选
            if "price_range" in self.filter_configs:
                st.session_state[f"{self.key}_filter_price_range"] = (1000.0, 10000.0)
            if "profit_percentage" in self.filter_configs:
                st.session_state[f"{self.key}_filter_profit_percentage"] = 10.0
                
        elif filter_type == "rising":
            # 上涨趋势筛选
            if "change_24h" in self.filter_configs:
                st.session_state[f"{self.key}_filter_change_24h"] = (5.0, 100.0)
                
        elif filter_type == "hot":
            # 热门项目筛选
            if "volume_24h" in self.filter_configs:
                st.session_state[f"{self.key}_filter_volume_24h"] = (100, 10000)
                
        elif filter_type == "new":
            # 新发现筛选
            if "discovery_date" in self.filter_configs:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
                st.session_state[f"{self.key}_filter_discovery_date_start"] = start_date.date()
                st.session_state[f"{self.key}_filter_discovery_date_end"] = end_date.date()
        
        st.rerun()
    
    def _save_filter_preset(self):
        """保存筛选器预设"""
        # 这里可以实现保存用户自定义筛选预设的功能
        st.success("筛选预设已保存！")
    
    def get_active_filters(self) -> Dict[str, Any]:
        """获取活跃的筛选器"""
        return {
            key: st.session_state[f"{self.key}_filters"].get(key)
            for key in st.session_state[f"{self.key}_active_filters"]
        }
    
    def reset_all_filters(self):
        """重置所有筛选器"""
        # 清除筛选器值
        for filter_key in self.filter_configs.keys():
            widget_key = f"{self.key}_filter_{filter_key}"
            if widget_key in st.session_state:
                del st.session_state[widget_key]
        
        # 清除筛选器状态
        st.session_state[f"{self.key}_filters"] = {}
        st.session_state[f"{self.key}_active_filters"] = set()
    
    def apply_filters_to_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """将筛选器应用到DataFrame"""
        filtered_df = df.copy()
        active_filters = self.get_active_filters()
        
        for filter_key, filter_value in active_filters.items():
            if filter_key not in self.filter_configs:
                continue
                
            config = self.filter_configs[filter_key]
            filter_type = config['type']
            
            # 根据筛选器类型应用筛选
            if filter_type == FilterType.TEXT and filter_key in df.columns:
                filtered_df = filtered_df[
                    filtered_df[filter_key].astype(str).str.contains(filter_value, case=False, na=False)
                ]
                
            elif filter_type == FilterType.SELECT and filter_key in df.columns:
                filtered_df = filtered_df[filtered_df[filter_key] == filter_value]
                
            elif filter_type == FilterType.MULTI_SELECT and filter_key in df.columns:
                if filter_value:
                    filtered_df = filtered_df[filtered_df[filter_key].isin(filter_value)]
                    
            elif filter_type == FilterType.NUMBER_RANGE and filter_key in df.columns:
                min_val, max_val = filter_value
                filtered_df = filtered_df[
                    (filtered_df[filter_key] >= min_val) & (filtered_df[filter_key] <= max_val)
                ]
                
            elif filter_type == FilterType.SLIDER and filter_key in df.columns:
                filtered_df = filtered_df[filtered_df[filter_key] >= filter_value]
                
            elif filter_type == FilterType.BOOLEAN and filter_key in df.columns:
                filtered_df = filtered_df[filtered_df[filter_key] == filter_value]
        
        return filtered_df


def create_portfolio_filter_panel(key: str = "portfolio_filter") -> FilterPanel:
    """创建投资组合筛选面板"""
    panel = FilterPanel(key, "投资组合筛选")
    
    # 添加筛选器
    panel.add_filter(
        "item_name", FilterType.TEXT, "项目名称",
        help_text="搜索项目名称"
    )
    
    panel.add_filter(
        "category", FilterType.SELECT, "类别",
        options=["全部", "武器", "刀具", "手套", "贴纸", "其他"],
        default_value="全部"
    )
    
    panel.add_filter(
        "price_range", FilterType.NUMBER_RANGE, "价格范围",
        min_value=0.0, max_value=10000.0, default_value=(0.0, 10000.0)
    )
    
    panel.add_filter(
        "profit_percentage", FilterType.SLIDER, "最低盈利率 (%)",
        min_value=-50.0, max_value=100.0, default_value=0.0
    )
    
    panel.add_filter(
        "holding_period", FilterType.SELECT, "持有时间",
        options=["全部", "1天内", "1周内", "1月内", "3月内", "6月以上"],
        default_value="全部"
    )
    
    return panel


def create_watchlist_filter_panel(key: str = "watchlist_filter") -> FilterPanel:
    """创建监控列表筛选面板"""
    panel = FilterPanel(key, "监控列表筛选")
    
    panel.add_filter(
        "item_name", FilterType.TEXT, "项目名称"
    )
    
    panel.add_filter(
        "pool_type", FilterType.SELECT, "池类型",
        options=["全部", "核心关注池", "主监控池"],
        default_value="全部"
    )
    
    panel.add_filter(
        "priority_score", FilterType.SLIDER, "最低优先级评分",
        min_value=0.0, max_value=10.0, default_value=0.0
    )
    
    panel.add_filter(
        "change_24h", FilterType.NUMBER_RANGE, "24小时涨跌幅 (%)",
        min_value=-50.0, max_value=50.0, default_value=(-50.0, 50.0)
    )
    
    panel.add_filter(
        "volume_24h", FilterType.NUMBER_RANGE, "24小时交易量",
        min_value=0, max_value=10000, default_value=(0, 10000)
    )
    
    return panel
