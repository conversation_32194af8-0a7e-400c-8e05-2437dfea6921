"""
Ares系统统一错误处理器
提供异常处理、错误恢复、通知和日志记录功能
"""

import logging
import traceback
import asyncio
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

from .exceptions import (
    AresException, ErrorSeverity, ErrorCategory, ErrorContext,
    APILimitExceeded, APIConnectionError, DatabaseConnectionError
)


@dataclass
class ErrorStats:
    """错误统计信息"""
    total_errors: int = 0
    errors_by_category: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    errors_by_severity: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    recent_errors: deque = field(default_factory=lambda: deque(maxlen=100))
    last_error_time: Optional[datetime] = None


class RecoveryStrategy(Enum):
    """错误恢复策略"""
    IGNORE = "ignore"           # 忽略错误，继续执行
    RETRY = "retry"             # 重试操作
    FALLBACK = "fallback"       # 使用备用方案
    CIRCUIT_BREAK = "circuit_break"  # 断路器模式
    ESCALATE = "escalate"       # 升级处理
    SHUTDOWN = "shutdown"       # 关闭服务


@dataclass
class RecoveryAction:
    """恢复动作配置"""
    strategy: RecoveryStrategy
    max_retries: int = 3
    retry_delay: float = 1.0
    fallback_function: Optional[Callable] = None
    escalation_threshold: int = 5
    circuit_break_duration: int = 300  # 5分钟


class CircuitBreaker:
    """断路器实现"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 300):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
    
    def call(self, func, *args, **kwargs):
        """通过断路器调用函数"""
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half_open"
            else:
                raise Exception("Circuit breaker is open")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """检查是否应该尝试重置"""
        if self.last_failure_time is None:
            return True
        return (datetime.utcnow() - self.last_failure_time).total_seconds() > self.recovery_timeout
    
    def _on_success(self):
        """成功时的处理"""
        self.failure_count = 0
        self.state = "closed"
    
    def _on_failure(self):
        """失败时的处理"""
        self.failure_count += 1
        self.last_failure_time = datetime.utcnow()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化错误处理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.stats = ErrorStats()
        self.recovery_strategies: Dict[str, RecoveryAction] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.notification_handlers: List[Callable] = []
        
        # 初始化默认恢复策略
        self._init_default_strategies()
        
        # 初始化通知处理器
        self._init_notification_handlers()
    
    def _init_default_strategies(self):
        """初始化默认恢复策略"""
        self.recovery_strategies = {
            # API相关错误
            ErrorCategory.API_ERROR.value: RecoveryAction(
                strategy=RecoveryStrategy.RETRY,
                max_retries=3,
                retry_delay=2.0
            ),
            
            # 数据库连接错误
            ErrorCategory.DATABASE_ERROR.value: RecoveryAction(
                strategy=RecoveryStrategy.CIRCUIT_BREAK,
                max_retries=2,
                circuit_break_duration=300
            ),
            
            # 验证错误
            ErrorCategory.VALIDATION_ERROR.value: RecoveryAction(
                strategy=RecoveryStrategy.IGNORE,
                max_retries=0
            ),
            
            # 业务逻辑错误
            ErrorCategory.BUSINESS_LOGIC_ERROR.value: RecoveryAction(
                strategy=RecoveryStrategy.FALLBACK,
                max_retries=1
            ),
            
            # 系统错误
            ErrorCategory.SYSTEM_ERROR.value: RecoveryAction(
                strategy=RecoveryStrategy.ESCALATE,
                escalation_threshold=3
            ),
            
            # 配置错误
            ErrorCategory.CONFIGURATION_ERROR.value: RecoveryAction(
                strategy=RecoveryStrategy.SHUTDOWN,
                max_retries=0
            )
        }
    
    def _init_notification_handlers(self):
        """初始化通知处理器"""
        # 这里可以添加Discord、Telegram等通知处理器
        pass
    
    def handle_exception(
        self, 
        exception: Exception, 
        context: Optional[ErrorContext] = None,
        operation: Optional[str] = None
    ) -> Any:
        """
        统一异常处理入口
        
        Args:
            exception: 异常对象
            context: 错误上下文
            operation: 操作名称
            
        Returns:
            处理结果或None
        """
        # 包装为Ares异常
        if isinstance(exception, AresException):
            ares_exception = exception
        else:
            ares_exception = AresException(
                message=str(exception),
                category=ErrorCategory.SYSTEM_ERROR,
                severity=ErrorSeverity.HIGH,
                context=context,
                cause=exception
            )
        
        # 更新统计信息
        self._update_stats(ares_exception)
        
        # 记录日志
        self._log_exception(ares_exception)
        
        # 发送通知（高严重性错误）
        if ares_exception.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._send_notification(ares_exception)
        
        # 执行恢复策略
        return self._execute_recovery_strategy(ares_exception, operation)
    
    def _update_stats(self, exception: AresException):
        """更新错误统计"""
        self.stats.total_errors += 1
        self.stats.errors_by_category[exception.category.value] += 1
        self.stats.errors_by_severity[exception.severity.value] += 1
        self.stats.recent_errors.append({
            'timestamp': exception.timestamp,
            'category': exception.category.value,
            'severity': exception.severity.value,
            'message': exception.message
        })
        self.stats.last_error_time = exception.timestamp
    
    def _log_exception(self, exception: AresException):
        """记录异常日志"""
        log_data = {
            'error_timestamp': exception.timestamp.isoformat(),
            'error_category': exception.category.value,
            'error_severity': exception.severity.value,
            'error_message': exception.message,
            'error_context': exception.context.__dict__ if exception.context else {},
            'error_traceback': traceback.format_exc() if exception.cause else None
        }
        
        # 根据严重性选择日志级别
        if exception.severity == ErrorSeverity.CRITICAL:
            self.logger.critical("Critical error occurred", extra=log_data)
        elif exception.severity == ErrorSeverity.HIGH:
            self.logger.error("High severity error occurred", extra=log_data)
        elif exception.severity == ErrorSeverity.MEDIUM:
            self.logger.warning("Medium severity error occurred", extra=log_data)
        else:
            self.logger.info("Low severity error occurred", extra=log_data)
    
    def _send_notification(self, exception: AresException):
        """发送错误通知"""
        notification_data = {
            'title': f"Ares System Error - {exception.severity.value.upper()}",
            'message': exception.message,
            'category': exception.category.value,
            'severity': exception.severity.value,
            'timestamp': exception.timestamp.isoformat(),
            'context': exception.context.__dict__ if exception.context else {}
        }
        
        for handler in self.notification_handlers:
            try:
                handler(notification_data)
            except Exception as e:
                self.logger.error(f"Failed to send notification: {str(e)}")
    
    def _execute_recovery_strategy(self, exception: AresException, operation: Optional[str] = None) -> Any:
        """执行恢复策略"""
        strategy_config = self.recovery_strategies.get(
            exception.category.value,
            RecoveryAction(strategy=RecoveryStrategy.IGNORE)
        )
        
        strategy = strategy_config.strategy
        
        if strategy == RecoveryStrategy.IGNORE:
            return None
        
        elif strategy == RecoveryStrategy.RETRY:
            return self._retry_operation(exception, strategy_config, operation)
        
        elif strategy == RecoveryStrategy.FALLBACK:
            return self._execute_fallback(exception, strategy_config)
        
        elif strategy == RecoveryStrategy.CIRCUIT_BREAK:
            return self._handle_circuit_break(exception, strategy_config, operation)
        
        elif strategy == RecoveryStrategy.ESCALATE:
            return self._escalate_error(exception, strategy_config)
        
        elif strategy == RecoveryStrategy.SHUTDOWN:
            return self._initiate_shutdown(exception)
        
        return None
    
    def _retry_operation(self, exception: AresException, config: RecoveryAction, operation: Optional[str]) -> Any:
        """重试操作"""
        if operation and hasattr(self, f'_retry_{operation}'):
            retry_func = getattr(self, f'_retry_{operation}')
            return retry_func(exception, config)
        
        self.logger.info(f"Retry strategy applied for {exception.category.value}")
        return None
    
    def _execute_fallback(self, exception: AresException, config: RecoveryAction) -> Any:
        """执行备用方案"""
        if config.fallback_function:
            try:
                return config.fallback_function(exception)
            except Exception as e:
                self.logger.error(f"Fallback function failed: {str(e)}")
        
        self.logger.info(f"Fallback strategy applied for {exception.category.value}")
        return None
    
    def _handle_circuit_break(self, exception: AresException, config: RecoveryAction, operation: Optional[str]) -> Any:
        """处理断路器"""
        circuit_key = f"{exception.category.value}_{operation or 'default'}"
        
        if circuit_key not in self.circuit_breakers:
            self.circuit_breakers[circuit_key] = CircuitBreaker(
                failure_threshold=config.escalation_threshold,
                recovery_timeout=config.circuit_break_duration
            )
        
        circuit_breaker = self.circuit_breakers[circuit_key]
        circuit_breaker._on_failure()  # 记录失败
        
        self.logger.warning(f"Circuit breaker activated for {circuit_key}")
        return None
    
    def _escalate_error(self, exception: AresException, config: RecoveryAction) -> Any:
        """升级错误处理"""
        # 检查是否达到升级阈值
        recent_similar_errors = [
            error for error in self.stats.recent_errors
            if error['category'] == exception.category.value
            and (datetime.utcnow() - error['timestamp']).total_seconds() < 3600  # 1小时内
        ]
        
        if len(recent_similar_errors) >= config.escalation_threshold:
            self.logger.critical(f"Error escalation triggered for {exception.category.value}")
            # 这里可以触发更高级别的处理，如发送紧急通知
        
        return None
    
    def _initiate_shutdown(self, exception: AresException) -> Any:
        """启动关闭流程"""
        self.logger.critical(f"System shutdown initiated due to: {exception.message}")
        # 这里可以实现优雅关闭逻辑
        return None
    
    def add_notification_handler(self, handler: Callable):
        """添加通知处理器"""
        self.notification_handlers.append(handler)
    
    def remove_notification_handler(self, handler: Callable):
        """移除通知处理器"""
        if handler in self.notification_handlers:
            self.notification_handlers.remove(handler)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            'total_errors': self.stats.total_errors,
            'errors_by_category': dict(self.stats.errors_by_category),
            'errors_by_severity': dict(self.stats.errors_by_severity),
            'recent_errors_count': len(self.stats.recent_errors),
            'last_error_time': self.stats.last_error_time.isoformat() if self.stats.last_error_time else None,
            'circuit_breakers': {
                key: {
                    'state': cb.state,
                    'failure_count': cb.failure_count,
                    'last_failure_time': cb.last_failure_time.isoformat() if cb.last_failure_time else None
                }
                for key, cb in self.circuit_breakers.items()
            }
        }
    
    def reset_stats(self):
        """重置错误统计"""
        self.stats = ErrorStats()
        self.logger.info("Error statistics reset")
    
    def set_recovery_strategy(self, category: ErrorCategory, action: RecoveryAction):
        """设置恢复策略"""
        self.recovery_strategies[category.value] = action
        self.logger.info(f"Recovery strategy updated for {category.value}")


# 全局错误处理器实例
_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例"""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler({})
    return _error_handler


def handle_error(exception: Exception, context: Optional[ErrorContext] = None, operation: Optional[str] = None) -> Any:
    """快捷方式：处理错误"""
    return get_error_handler().handle_exception(exception, context, operation)
