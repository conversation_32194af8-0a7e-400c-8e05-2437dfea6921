"""
新监控系统测试
验证健康检查和系统监控功能
"""

import asyncio
import time
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from monitoring.health_check import (
    HealthChecker, HealthCheckResult, HealthStatus, get_health_checker
)
from monitoring.system_monitor import (
    SystemMonitor, MetricPoint, AlertRule, Alert, get_system_monitor
)


def test_health_checker():
    """测试健康检查器"""
    print("测试健康检查器...")
    
    health_checker = HealthChecker()
    
    # 测试初始化
    assert len(health_checker.checks) > 0
    assert "database" in health_checker.checks
    assert "redis" in health_checker.checks
    assert "disk_space" in health_checker.checks
    assert "memory_usage" in health_checker.checks
    assert "cpu_usage" in health_checker.checks
    print("✓ 健康检查器初始化测试通过")
    
    # 测试整体状态计算
    healthy_results = {
        "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
        "check2": HealthCheckResult("check2", HealthStatus.HEALTHY, "OK")
    }
    assert health_checker.get_overall_status(healthy_results) == HealthStatus.HEALTHY
    
    warning_results = {
        "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
        "check2": HealthCheckResult("check2", HealthStatus.WARNING, "Warning")
    }
    assert health_checker.get_overall_status(warning_results) == HealthStatus.WARNING
    
    critical_results = {
        "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
        "check2": HealthCheckResult("check2", HealthStatus.CRITICAL, "Critical")
    }
    assert health_checker.get_overall_status(critical_results) == HealthStatus.CRITICAL
    print("✓ 整体状态计算测试通过")
    
    # 测试健康摘要生成
    results = {
        "check1": HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
        "check2": HealthCheckResult("check2", HealthStatus.WARNING, "Warning"),
        "check3": HealthCheckResult("check3", HealthStatus.CRITICAL, "Critical")
    }
    
    summary = health_checker.get_health_summary(results)
    
    assert summary["overall_status"] == HealthStatus.CRITICAL.value
    assert summary["total_checks"] == 3
    assert summary["status_counts"]["healthy"] == 1
    assert summary["status_counts"]["warning"] == 1
    assert summary["status_counts"]["critical"] == 1
    assert "checks" in summary
    assert "timestamp" in summary
    print("✓ 健康摘要生成测试通过")


async def test_health_checks():
    """测试具体的健康检查"""
    print("测试具体健康检查...")
    
    health_checker = HealthChecker()
    
    # 测试磁盘空间检查
    result = await health_checker._check_disk_space()
    assert isinstance(result, HealthCheckResult)
    assert result.name == "disk_space"
    assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
    assert "total_gb" in result.details
    assert "used_percent" in result.details
    print("✓ 磁盘空间检查测试通过")
    
    # 测试内存使用检查
    result = await health_checker._check_memory_usage()
    assert isinstance(result, HealthCheckResult)
    assert result.name == "memory_usage"
    assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
    assert "total_gb" in result.details
    assert "used_percent" in result.details
    print("✓ 内存使用检查测试通过")
    
    # 测试CPU使用率检查
    result = await health_checker._check_cpu_usage()
    assert isinstance(result, HealthCheckResult)
    assert result.name == "cpu_usage"
    assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
    assert "cpu_percent" in result.details
    print("✓ CPU使用率检查测试通过")
    
    # 测试运行所有健康检查
    results = await health_checker.run_health_checks(force=True)
    assert len(results) > 0
    assert all(isinstance(result, HealthCheckResult) for result in results.values())
    
    # 检查所有默认检查都有结果
    expected_checks = ["database", "redis", "disk_space", "memory_usage", "cpu_usage"]
    for check_name in expected_checks:
        assert check_name in results
    print("✓ 运行所有健康检查测试通过")


def test_system_monitor():
    """测试系统监控器"""
    print("测试系统监控器...")
    
    system_monitor = SystemMonitor()
    
    # 测试初始化
    assert len(system_monitor.alert_rules) > 0
    assert not system_monitor.monitoring_active
    assert system_monitor.monitor_thread is None
    print("✓ 系统监控器初始化测试通过")
    
    # 测试添加指标
    timestamp = datetime.utcnow()
    system_monitor.add_metric("test_metric", 42.0, timestamp)
    
    latest = system_monitor.get_latest_metric("test_metric")
    assert latest is not None
    assert latest.value == 42.0
    assert latest.timestamp == timestamp
    print("✓ 添加指标测试通过")
    
    # 测试指标历史
    base_time = datetime.utcnow()
    for i in range(5):
        timestamp = base_time + timedelta(minutes=i)
        system_monitor.add_metric("history_test", float(i), timestamp)
    
    history = system_monitor.get_metric_history("history_test", duration_minutes=10)
    assert len(history) == 5
    
    values = [point.value for point in history]
    assert values == [0.0, 1.0, 2.0, 3.0, 4.0]
    print("✓ 指标历史测试通过")
    
    # 测试API请求记录
    endpoint = "/api/test"

    system_monitor.record_api_request(endpoint, True, 0.1)
    system_monitor.record_api_request(endpoint, True, 0.2)
    system_monitor.record_api_request(endpoint, False, 0.5)

    stats = system_monitor.get_api_stats(endpoint)

    assert stats['total_requests'] == 3
    assert stats['success_requests'] == 2
    assert stats['error_requests'] == 1
    # 平均响应时间计算：第一次成功请求0.1，第二次成功请求0.2
    # 第一次：avg = 0.1
    # 第二次：avg = (0.1 * 1 + 0.2) / 2 = 0.15
    # 失败请求不影响平均响应时间
    print(f"实际平均响应时间: {stats['avg_response_time']}")
    assert abs(stats['avg_response_time'] - 0.15) < 0.01
    print("✓ API请求记录测试通过")
    
    # 测试告警规则管理
    rule = AlertRule("test_rule", "test_metric", ">", 100.0, 60, "high")
    system_monitor.add_alert_rule(rule)
    
    rule_names = [r.name for r in system_monitor.alert_rules]
    assert "test_rule" in rule_names
    
    success = system_monitor.remove_alert_rule("test_rule")
    assert success
    
    rule_names = [r.name for r in system_monitor.alert_rules]
    assert "test_rule" not in rule_names
    print("✓ 告警规则管理测试通过")
    
    # 测试告警评估
    rule = AlertRule("test_alert", "test_metric", ">", 50.0, 60, "medium")
    system_monitor.add_alert_rule(rule)
    
    system_monitor.add_metric("test_metric", 75.0)
    system_monitor._check_alerts()
    
    active_alerts = system_monitor.get_active_alerts()
    assert len(active_alerts) > 0
    
    alert = active_alerts[0]
    assert alert.rule_name == "test_alert"
    assert alert.value == 75.0
    assert alert.threshold == 50.0
    print("✓ 告警评估测试通过")
    
    # 测试系统概览
    system_monitor.add_metric("cpu_usage", 45.0)
    system_monitor.add_metric("memory_usage", 60.0)
    system_monitor.add_metric("disk_usage", 30.0)

    # 使用新的端点避免与之前的测试冲突
    system_monitor.record_api_request("/api/overview", True, 0.1)
    system_monitor.record_api_request("/api/overview", False, 0.2)

    overview = system_monitor.get_system_overview()

    assert "system_metrics" in overview
    assert "api_metrics" in overview
    assert "alerts" in overview
    assert "monitoring_status" in overview
    assert "timestamp" in overview

    assert overview["system_metrics"]["cpu_usage"] == 45.0
    assert overview["system_metrics"]["memory_usage"] == 60.0
    assert overview["system_metrics"]["disk_usage"] == 30.0

    # 检查API指标存在（总数可能包含之前测试的请求）
    assert overview["api_metrics"]["total_requests"] >= 2
    assert overview["api_metrics"]["total_errors"] >= 1
    print("✓ 系统概览测试通过")


def test_metric_point():
    """测试指标数据点"""
    print("测试指标数据点...")
    
    timestamp = datetime.utcnow()
    tags = {"host": "server1", "env": "prod"}
    
    point = MetricPoint(timestamp, 42.5, tags)
    
    assert point.timestamp == timestamp
    assert point.value == 42.5
    assert point.tags == tags
    
    point_dict = point.to_dict()
    assert point_dict["timestamp"] == timestamp.isoformat()
    assert point_dict["value"] == 42.5
    assert point_dict["tags"] == tags
    print("✓ 指标数据点测试通过")


def test_alert_rule():
    """测试告警规则"""
    print("测试告警规则...")
    
    # 大于条件
    rule_gt = AlertRule("test", "metric", ">", 50.0, 60, "medium")
    assert rule_gt.evaluate(75.0) == True
    assert rule_gt.evaluate(25.0) == False
    assert rule_gt.evaluate(50.0) == False
    
    # 小于条件
    rule_lt = AlertRule("test", "metric", "<", 50.0, 60, "medium")
    assert rule_lt.evaluate(25.0) == True
    assert rule_lt.evaluate(75.0) == False
    assert rule_lt.evaluate(50.0) == False
    
    # 等于条件
    rule_eq = AlertRule("test", "metric", "==", 50.0, 60, "medium")
    assert rule_eq.evaluate(50.0) == True
    assert rule_eq.evaluate(51.0) == False
    
    # 大于等于条件
    rule_gte = AlertRule("test", "metric", ">=", 50.0, 60, "medium")
    assert rule_gte.evaluate(50.0) == True
    assert rule_gte.evaluate(51.0) == True
    assert rule_gte.evaluate(49.0) == False
    
    # 小于等于条件
    rule_lte = AlertRule("test", "metric", "<=", 50.0, 60, "medium")
    assert rule_lte.evaluate(50.0) == True
    assert rule_lte.evaluate(49.0) == True
    assert rule_lte.evaluate(51.0) == False
    print("✓ 告警规则测试通过")


def test_alert():
    """测试告警"""
    print("测试告警...")
    
    timestamp = datetime.utcnow()
    
    alert = Alert(
        id="alert_001",
        rule_name="test_rule",
        metric="cpu_usage",
        value=85.5,
        threshold=80.0,
        severity="high",
        message="CPU usage is high",
        timestamp=timestamp
    )
    
    assert alert.id == "alert_001"
    assert alert.rule_name == "test_rule"
    assert alert.metric == "cpu_usage"
    assert alert.value == 85.5
    assert alert.threshold == 80.0
    assert alert.severity == "high"
    assert alert.message == "CPU usage is high"
    assert alert.timestamp == timestamp
    assert not alert.resolved
    assert alert.resolved_at is None
    
    alert_dict = alert.to_dict()
    assert alert_dict["id"] == "alert_001"
    assert alert_dict["rule_name"] == "test_rule"
    assert alert_dict["metric"] == "cpu_usage"
    assert alert_dict["value"] == 85.5
    assert alert_dict["threshold"] == 80.0
    assert alert_dict["severity"] == "high"
    assert alert_dict["message"] == "CPU usage is high"
    assert alert_dict["timestamp"] == timestamp.isoformat()
    assert alert_dict["resolved"] == False
    assert alert_dict["resolved_at"] is None
    print("✓ 告警测试通过")


async def main():
    """主测试函数"""
    print("运行新监控系统测试...")
    
    # 运行所有测试
    test_health_checker()
    await test_health_checks()
    test_system_monitor()
    test_metric_point()
    test_alert_rule()
    test_alert()
    
    print("\n所有新监控系统测试通过！")


if __name__ == "__main__":
    asyncio.run(main())
