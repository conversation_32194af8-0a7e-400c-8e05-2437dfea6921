"""
CS2饰品模型测试
验证CS2饰品数据模型和业务逻辑的正确性
"""

import pytest
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import Item
from services.cs2_item import (
    CS2Item, CS2Rarity, CS2WeaponType, CS2Quality, CS2ItemAnalysis
)
from services.steamdt_api import ItemPriceData, PlatformPrice


class TestCS2Rarity:
    """CS2稀有度枚举测试"""
    
    def test_rarity_values(self):
        """测试稀有度枚举值"""
        assert CS2Rarity.CONSUMER.value == "消费级"
        assert CS2Rarity.INDUSTRIAL.value == "工业级"
        assert CS2Rarity.MIL_SPEC.value == "军规级"
        assert CS2Rarity.RESTRICTED.value == "受限"
        assert CS2Rarity.CLASSIFIED.value == "保密"
        assert CS2Rarity.COVERT.value == "隐秘"
        assert CS2Rarity.CONTRABAND.value == "违禁品"


class TestCS2WeaponType:
    """CS2武器类型枚举测试"""
    
    def test_weapon_type_values(self):
        """测试武器类型枚举值"""
        assert CS2WeaponType.RIFLE.value == "步枪"
        assert CS2WeaponType.SNIPER.value == "狙击枪"
        assert CS2WeaponType.PISTOL.value == "手枪"
        assert CS2WeaponType.KNIFE.value == "刀具"
        assert CS2WeaponType.GLOVES.value == "手套"


class TestCS2Quality:
    """CS2品质枚举测试"""
    
    def test_quality_values(self):
        """测试品质枚举值"""
        assert CS2Quality.NORMAL.value == "普通"
        assert CS2Quality.STATTRAK.value == "StatTrak™"
        assert CS2Quality.SOUVENIR.value == "纪念品"


class TestCS2Item:
    """CS2饰品业务逻辑测试"""
    
    @pytest.fixture
    def mock_item(self):
        """创建模拟Item对象"""
        item = Mock(spec=Item)
        item.item_id = "ak47_redline_ft"
        item.name = "AK-47 | Redline (Field-Tested)"
        item.category = "weapon"
        item.rarity = "军规级"
        item.weapon_type = "步枪"
        item.skin_name = "Redline"
        item.wear_rating = 0.25
        item.float_min = 0.15
        item.float_max = 0.38
        item.quality = "普通"
        item.collection = "Huntsman Collection"
        item.case_name = "Huntsman Weapon Case"
        item.investment_score = None
        item.popularity_score = None
        item.liquidity_score = None
        return item
    
    @pytest.fixture
    def cs2_item(self, mock_item):
        """创建CS2Item实例"""
        return CS2Item(mock_item)
    
    @pytest.fixture
    def mock_price_data(self):
        """创建模拟价格数据"""
        steam_price = PlatformPrice(
            platform='steam',
            platform_item_id='123456',
            sell_price=10.50,
            sell_count=100,
            bidding_price=9.75,
            bidding_count=50,
            update_time=1625097600
        )
        
        buff_price = PlatformPrice(
            platform='buff',
            platform_item_id='789012',
            sell_price=9.80,
            sell_count=200,
            bidding_price=9.50,
            bidding_count=80,
            update_time=1625097600
        )
        
        return ItemPriceData(
            market_hash_name="AK-47 | Redline (Field-Tested)",
            platform_prices=[steam_price, buff_price],
            query_time=datetime.now()
        )
    
    def test_get_rarity_multiplier(self, cs2_item):
        """测试稀有度权重计算"""
        multiplier = cs2_item.get_rarity_multiplier()
        assert multiplier == 1.5  # 军规级的权重
    
    def test_get_weapon_type_multiplier(self, cs2_item):
        """测试武器类型权重计算"""
        multiplier = cs2_item.get_weapon_type_multiplier()
        assert multiplier == 2.0  # 步枪的权重
    
    def test_get_quality_multiplier(self, cs2_item):
        """测试品质权重计算"""
        multiplier = cs2_item.get_quality_multiplier()
        assert multiplier == 1.0  # 普通品质的权重
    
    def test_calculate_wear_score(self, cs2_item):
        """测试磨损度评分计算"""
        score = cs2_item.calculate_wear_score()
        assert score == 6.0  # 0.25磨损值对应久经沙场，评分6.0
    
    def test_calculate_wear_score_factory_new(self, cs2_item):
        """测试崭新出厂磨损度评分"""
        cs2_item.item.wear_rating = 0.05
        score = cs2_item.calculate_wear_score()
        assert score == 10.0  # 崭新出厂最高评分
    
    def test_calculate_wear_score_battle_scarred(self, cs2_item):
        """测试战痕累累磨损度评分"""
        cs2_item.item.wear_rating = 0.8
        score = cs2_item.calculate_wear_score()
        assert score == 2.0  # 战痕累累最低评分
    
    def test_calculate_liquidity_score(self, cs2_item, mock_price_data):
        """测试流动性评分计算"""
        score = cs2_item.calculate_liquidity_score(mock_price_data)

        # 2个平台 = 2分，总交易量430 (>=100, <500) = 3分，总计5分
        assert score == 5.0
    
    def test_calculate_liquidity_score_no_data(self, cs2_item):
        """测试无价格数据时的流动性评分"""
        score = cs2_item.calculate_liquidity_score(None)
        assert score == 5.0  # 默认中等流动性
    
    def test_analyze_price_trend(self, cs2_item, mock_price_data):
        """测试价格趋势分析"""
        score = cs2_item.analyze_price_trend(mock_price_data)
        
        # 价格相对稳定，应该有较高评分
        assert score > 5.0
    
    def test_calculate_investment_score(self, cs2_item, mock_price_data):
        """测试综合投资评分计算"""
        score = cs2_item.calculate_investment_score(mock_price_data)
        
        # 验证评分在合理范围内
        assert 0 <= score <= 100
        assert isinstance(score, float)
    
    def test_get_comprehensive_analysis(self, cs2_item, mock_price_data):
        """测试综合分析功能"""
        analysis = cs2_item.get_comprehensive_analysis(mock_price_data)
        
        assert isinstance(analysis, CS2ItemAnalysis)
        assert 0 <= analysis.investment_score <= 100
        assert analysis.rarity_multiplier > 0
        assert analysis.popularity_score > 0
        assert analysis.liquidity_score > 0
        assert analysis.price_trend_score > 0
        assert analysis.risk_level in ["低风险", "中等风险", "较高风险", "高风险"]
        assert analysis.recommendation in ["强烈推荐", "推荐", "谨慎考虑", "不推荐"]
        assert isinstance(analysis.analysis_time, datetime)
    
    def test_knife_weapon_type_high_score(self, mock_item):
        """测试刀具武器类型的高评分"""
        mock_item.weapon_type = "刀具"
        mock_item.rarity = "隐秘"
        
        cs2_item = CS2Item(mock_item)
        weapon_multiplier = cs2_item.get_weapon_type_multiplier()
        rarity_multiplier = cs2_item.get_rarity_multiplier()
        
        assert weapon_multiplier == 3.0  # 刀具最高权重
        assert rarity_multiplier == 5.0   # 隐秘稀有度高权重
    
    def test_stattrak_quality_bonus(self, mock_item):
        """测试StatTrak品质加成"""
        mock_item.quality = "StatTrak™"
        
        cs2_item = CS2Item(mock_item)
        quality_multiplier = cs2_item.get_quality_multiplier()
        
        assert quality_multiplier == 1.5  # StatTrak有1.5倍加成
    
    def test_invalid_rarity_fallback(self, mock_item):
        """测试无效稀有度的回退处理"""
        mock_item.rarity = "无效稀有度"
        
        cs2_item = CS2Item(mock_item)
        multiplier = cs2_item.get_rarity_multiplier()
        
        assert multiplier == 1.0  # 无效稀有度返回默认值
    
    def test_none_values_handling(self, mock_item):
        """测试None值的处理"""
        mock_item.rarity = None
        mock_item.weapon_type = None
        mock_item.quality = None
        mock_item.wear_rating = None
        
        cs2_item = CS2Item(mock_item)
        
        assert cs2_item.get_rarity_multiplier() == 1.0
        assert cs2_item.get_weapon_type_multiplier() == 1.0
        assert cs2_item.get_quality_multiplier() == 1.0
        assert cs2_item.calculate_wear_score() == 5.0
    
    def test_investment_score_error_handling(self, cs2_item):
        """测试投资评分计算的错误处理"""
        # 模拟计算过程中的异常
        with patch.object(cs2_item, 'get_rarity_multiplier', side_effect=Exception("Test error")):
            score = cs2_item.calculate_investment_score()
            assert score == 0.0  # 异常时返回0分


class TestCS2ItemAnalysis:
    """CS2饰品分析结果测试"""
    
    def test_analysis_dataclass(self):
        """测试分析结果数据类"""
        analysis = CS2ItemAnalysis(
            investment_score=75.5,
            rarity_multiplier=2.0,
            popularity_score=80.0,
            liquidity_score=70.0,
            price_trend_score=65.0,
            risk_level="中等风险",
            recommendation="推荐",
            analysis_time=datetime.now()
        )
        
        assert analysis.investment_score == 75.5
        assert analysis.rarity_multiplier == 2.0
        assert analysis.popularity_score == 80.0
        assert analysis.liquidity_score == 70.0
        assert analysis.price_trend_score == 65.0
        assert analysis.risk_level == "中等风险"
        assert analysis.recommendation == "推荐"
        assert isinstance(analysis.analysis_time, datetime)


if __name__ == "__main__":
    # 运行基本测试
    print("运行CS2饰品模型基本测试...")
    
    # 测试枚举
    print("测试枚举...")
    assert CS2Rarity.COVERT.value == "隐秘"
    assert CS2WeaponType.KNIFE.value == "刀具"
    assert CS2Quality.STATTRAK.value == "StatTrak™"
    print("✓ 枚举测试通过")
    
    # 测试权重映射
    print("测试权重映射...")
    assert CS2Item.RARITY_WEIGHTS[CS2Rarity.COVERT] == 5.0
    assert CS2Item.WEAPON_TYPE_WEIGHTS[CS2WeaponType.KNIFE] == 3.0
    assert CS2Item.QUALITY_WEIGHTS[CS2Quality.STATTRAK] == 1.5
    print("✓ 权重映射测试通过")
    
    print("所有CS2饰品模型基本测试通过！")
    
    # 运行pytest
    pytest.main(["-xvs", __file__])
