"""
机会发现服务测试
验证发现器服务的核心功能
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.discoverer import (
    DiscovererService, DiscoveryOpportunity, DiscoveryStats, get_discoverer_service
)


class MockAPIManager:
    """模拟API管理器"""
    
    def __init__(self):
        self.call_count = 0
        self.rankings_data = {
            'hot': {
                'items': [
                    {
                        'id': 'item_hot_1',
                        'name': 'Hot Item 1',
                        'price_info': {
                            'ask_price': 110.0,
                            'bid_price': 100.0,
                            'volume_24h': 500,
                            'volatility': 5.0
                        }
                    },
                    {
                        'id': 'item_hot_2',
                        'name': 'Hot Item 2',
                        'price_info': {
                            'ask_price': 55.0,
                            'bid_price': 50.0,
                            'volume_24h': 300,
                            'volatility': 3.0
                        }
                    }
                ]
            },
            'rising': {
                'items': [
                    {
                        'id': 'item_rising_1',
                        'name': 'Rising Item 1',
                        'price_info': {
                            'ask_price': 220.0,
                            'bid_price': 200.0,
                            'volume_24h': 800,
                            'volatility': 8.0
                        }
                    }
                ]
            }
        }


class MockScheduler:
    """模拟调度器服务"""
    
    def __init__(self):
        self.items = []
    
    async def add_item_to_schedule(self, item_id, pool_type, priority_score):
        self.items.append({
            'item_id': item_id,
            'pool_type': pool_type,
            'priority_score': priority_score
        })


class MockDatabaseManager:
    """模拟数据库管理器"""
    
    def __init__(self):
        self.watchlist_items = []
    
    async def get_session(self):
        class MockSession:
            async def __aenter__(self):
                return self
            async def __aexit__(self, *args):
                pass
        return MockSession()
    
    async def get_watchlist_item(self, session, item_id):
        for item in self.watchlist_items:
            if item.get('item_id') == item_id:
                return item
        return None


class MockConfigManager:
    """模拟配置管理器"""
    
    def __init__(self):
        self.config = {
            'discoverer.limit_per_ranking': 50,
            'discoverer.min_opportunity_score': 3.0,
            'discoverer.discovery_times': ['09:00', '21:00']
        }
    
    def get(self, key, default=None):
        return self.config.get(key, default)


class TestDiscoveryOpportunity:
    """发现机会测试"""
    
    def test_discovery_opportunity_creation(self):
        """测试发现机会创建"""
        opportunity = DiscoveryOpportunity(
            item_id="test_item",
            item_name="Test Item",
            ranking_type="hot",
            ranking_position=5,
            price_info={"ask_price": 110.0, "bid_price": 100.0},
            opportunity_score=7.5,
            discovery_time=datetime.utcnow(),
            source_data={"test": "data"}
        )
        
        assert opportunity.item_id == "test_item"
        assert opportunity.item_name == "Test Item"
        assert opportunity.ranking_type == "hot"
        assert opportunity.ranking_position == 5
        assert opportunity.opportunity_score == 7.5
    
    def test_discovery_opportunity_to_dict(self):
        """测试发现机会转换为字典"""
        discovery_time = datetime.utcnow()
        opportunity = DiscoveryOpportunity(
            item_id="test_item",
            item_name="Test Item",
            ranking_type="rising",
            ranking_position=3,
            price_info={"ask_price": 55.0, "bid_price": 50.0},
            opportunity_score=6.2,
            discovery_time=discovery_time,
            source_data={"volume": 100}
        )
        
        opp_dict = opportunity.to_dict()
        
        assert opp_dict['item_id'] == "test_item"
        assert opp_dict['item_name'] == "Test Item"
        assert opp_dict['ranking_type'] == "rising"
        assert opp_dict['ranking_position'] == 3
        assert opp_dict['opportunity_score'] == 6.2
        assert opp_dict['discovery_time'] == discovery_time.isoformat()


class TestDiscoveryStats:
    """发现统计测试"""
    
    def test_discovery_stats_initialization(self):
        """测试发现统计初始化"""
        stats = DiscoveryStats()
        
        assert stats.total_discoveries == 0
        assert stats.successful_additions == 0
        assert stats.duplicate_items == 0
        assert stats.api_calls_made == 0
        assert stats.last_discovery_time is None
        assert stats.discovery_sessions == 0
    
    def test_discovery_stats_to_dict(self):
        """测试发现统计转换为字典"""
        stats = DiscoveryStats(
            total_discoveries=100,
            successful_additions=80,
            duplicate_items=15,
            api_calls_made=20,
            discovery_sessions=5
        )
        
        stats_dict = stats.to_dict()
        
        assert stats_dict['total_discoveries'] == 100
        assert stats_dict['successful_additions'] == 80
        assert stats_dict['duplicate_items'] == 15
        assert stats_dict['api_calls_made'] == 20
        assert stats_dict['discovery_sessions'] == 5
        assert stats_dict['success_rate'] == 80.0


class TestDiscovererService:
    """发现器服务测试"""
    
    @pytest.fixture
    def mock_discoverer(self):
        """创建模拟发现器"""
        discoverer = DiscovererService()
        
        # 替换依赖组件为模拟对象
        discoverer.config = MockConfigManager()
        discoverer.db_manager = MockDatabaseManager()
        discoverer.api_manager = MockAPIManager()
        discoverer.scheduler_service = MockScheduler()
        
        # 设置测试历史文件路径
        discoverer.history_file = Path("test_discovery_history.json")
        
        return discoverer
    
    def test_discoverer_initialization(self, mock_discoverer):
        """测试发现器初始化"""
        assert mock_discoverer.config is not None
        assert mock_discoverer.db_manager is not None
        assert not mock_discoverer.running
        assert isinstance(mock_discoverer.stats, DiscoveryStats)
        assert mock_discoverer.discovery_limit == 50
        assert mock_discoverer.min_opportunity_score == 3.0
        assert mock_discoverer.discovery_times == ['09:00', '21:00']
    
    def test_calculate_price_spread(self, mock_discoverer):
        """测试价差计算"""
        # 正常价差
        price_info = {'ask_price': 110.0, 'bid_price': 100.0}
        spread = mock_discoverer._calculate_price_spread(price_info)
        assert spread == 10.0  # (110-100)/100 * 100
        
        # 无价差
        price_info = {'ask_price': 100.0, 'bid_price': 100.0}
        spread = mock_discoverer._calculate_price_spread(price_info)
        assert spread == 0.0
        
        # 缺失数据
        price_info = {}
        spread = mock_discoverer._calculate_price_spread(price_info)
        assert spread == 0.0
    
    def test_calculate_position_bonus(self, mock_discoverer):
        """测试位置奖励计算"""
        # 第1位的rising排行榜
        bonus = mock_discoverer._calculate_position_bonus(1, 'rising')
        assert bonus > 0
        
        # 第1位的hot排行榜
        hot_bonus = mock_discoverer._calculate_position_bonus(1, 'hot')
        rising_bonus = mock_discoverer._calculate_position_bonus(1, 'rising')
        assert rising_bonus > hot_bonus  # rising权重更高
        
        # 第50位的奖励应该很低
        low_bonus = mock_discoverer._calculate_position_bonus(50, 'hot')
        high_bonus = mock_discoverer._calculate_position_bonus(1, 'hot')
        assert high_bonus > low_bonus
    
    def test_deduplicate_opportunities(self, mock_discoverer):
        """测试机会去重"""
        opportunities = [
            DiscoveryOpportunity(
                "item1", "Item 1", "hot", 1, {}, 5.0, datetime.utcnow(), {}
            ),
            DiscoveryOpportunity(
                "item2", "Item 2", "rising", 2, {}, 6.0, datetime.utcnow(), {}
            ),
            DiscoveryOpportunity(
                "item1", "Item 1 Duplicate", "new", 3, {}, 4.0, datetime.utcnow(), {}
            )
        ]
        
        unique_opportunities = mock_discoverer._deduplicate_opportunities(opportunities)
        
        assert len(unique_opportunities) == 2
        assert unique_opportunities[0].item_id == "item1"
        assert unique_opportunities[1].item_id == "item2"
    
    @pytest.mark.asyncio
    async def test_analyze_ranking(self, mock_discoverer):
        """测试排行榜分析"""
        ranking_data = {
            'items': [
                {
                    'id': 'test_item_1',
                    'name': 'Test Item 1',
                    'price_info': {'ask_price': 110.0, 'bid_price': 100.0}
                },
                {
                    'id': 'test_item_2',
                    'name': 'Test Item 2',
                    'price_info': {'ask_price': 55.0, 'bid_price': 50.0}
                }
            ]
        }
        
        opportunities = await mock_discoverer._analyze_ranking('hot', ranking_data)
        
        assert len(opportunities) == 2
        assert opportunities[0].item_id == 'test_item_1'
        assert opportunities[0].ranking_type == 'hot'
        assert opportunities[0].ranking_position == 1
        assert opportunities[1].item_id == 'test_item_2'
        assert opportunities[1].ranking_position == 2
    
    @pytest.mark.asyncio
    async def test_score_opportunities(self, mock_discoverer):
        """测试机会评分"""
        opportunities = [
            DiscoveryOpportunity(
                "item1", "Item 1", "rising", 1, 
                {'ask_price': 220.0, 'bid_price': 200.0, 'volume_24h': 800, 'volatility': 8.0},
                0.0, datetime.utcnow(), {}
            ),
            DiscoveryOpportunity(
                "item2", "Item 2", "hot", 10,
                {'ask_price': 55.0, 'bid_price': 50.0, 'volume_24h': 100, 'volatility': 2.0},
                0.0, datetime.utcnow(), {}
            )
        ]
        
        scored_opportunities = await mock_discoverer._score_opportunities(opportunities)
        
        # 检查评分是否已计算
        assert scored_opportunities[0].opportunity_score > 0
        assert scored_opportunities[1].opportunity_score > 0
        
        # rising排行榜第1位应该比hot排行榜第10位评分更高
        assert scored_opportunities[0].opportunity_score > scored_opportunities[1].opportunity_score
    
    def test_history_persistence(self, mock_discoverer):
        """测试历史记录持久化"""
        # 设置一些统计数据
        mock_discoverer.stats.total_discoveries = 50
        mock_discoverer.stats.successful_additions = 40
        mock_discoverer.stats.discovery_sessions = 3
        mock_discoverer.discovered_items.add("item1")
        mock_discoverer.discovered_items.add("item2")
        
        # 保存历史
        mock_discoverer._save_history()
        
        # 检查文件是否存在
        assert mock_discoverer.history_file.exists()
        
        # 创建新的发现器并加载历史
        new_discoverer = DiscovererService()
        new_discoverer.config = MockConfigManager()
        new_discoverer.history_file = mock_discoverer.history_file
        new_discoverer._load_history()
        
        # 验证历史是否正确加载
        assert new_discoverer.stats.total_discoveries == 50
        assert new_discoverer.stats.successful_additions == 40
        assert new_discoverer.stats.discovery_sessions == 3
        assert "item1" in new_discoverer.discovered_items
        assert "item2" in new_discoverer.discovered_items
        
        # 清理测试文件
        if mock_discoverer.history_file.exists():
            mock_discoverer.history_file.unlink()
    
    def test_get_status(self, mock_discoverer):
        """测试获取状态"""
        mock_discoverer.stats.total_discoveries = 100
        mock_discoverer.stats.successful_additions = 80
        mock_discoverer.discovered_items.add("item1")
        mock_discoverer.discovered_items.add("item2")
        mock_discoverer.running = True
        
        status = mock_discoverer.get_status()
        
        assert 'running' in status
        assert 'stats' in status
        assert 'discovered_items_count' in status
        assert 'discovery_times' in status
        assert 'min_opportunity_score' in status
        
        assert status['running'] == True
        assert status['discovered_items_count'] == 2
        assert status['discovery_times'] == ['09:00', '21:00']
        assert status['min_opportunity_score'] == 3.0
    
    def test_get_discovery_history(self, mock_discoverer):
        """测试获取发现历史"""
        # 添加一些历史记录
        mock_discoverer.discovery_history = [
            {
                'session_time': '2025-01-17T09:00:00',
                'opportunities_count': 5,
                'opportunities': []
            },
            {
                'session_time': '2025-01-17T21:00:00',
                'opportunities_count': 3,
                'opportunities': []
            }
        ]
        
        history = mock_discoverer.get_discovery_history(limit=1)
        
        assert len(history) == 1
        assert history[0]['session_time'] == '2025-01-17T21:00:00'  # 最新的记录


def test_global_discoverer_service():
    """测试全局发现器服务"""
    discoverer1 = get_discoverer_service()
    discoverer2 = get_discoverer_service()
    
    # 应该返回同一个实例
    assert discoverer1 is discoverer2


if __name__ == "__main__":
    # 运行基本测试
    print("运行机会发现服务基本测试...")
    
    # 测试发现机会
    print("测试发现机会...")
    opportunity = DiscoveryOpportunity(
        "test_item", "Test Item", "hot", 1, {}, 7.5, datetime.utcnow(), {}
    )
    assert opportunity.item_id == "test_item"
    assert opportunity.ranking_type == "hot"
    print("✓ 发现机会测试通过")
    
    # 测试发现统计
    print("测试发现统计...")
    stats = DiscoveryStats(total_discoveries=100, successful_additions=80)
    assert stats.to_dict()['success_rate'] == 80.0
    print("✓ 发现统计测试通过")
    
    # 测试发现器服务
    print("测试发现器服务...")

    # 创建模拟发现器，避免配置验证
    class MockDiscoverer:
        def __init__(self):
            self.config = MockConfigManager()
            self.discovery_limit = self.config.get('discoverer.limit_per_ranking', 50)
            self.min_opportunity_score = self.config.get('discoverer.min_opportunity_score', 3.0)

        def _calculate_price_spread(self, price_info):
            try:
                ask_price = price_info.get('ask_price', 0)
                bid_price = price_info.get('bid_price', 0)
                if bid_price > 0 and ask_price > bid_price:
                    return ((ask_price - bid_price) / bid_price) * 100
                return 0.0
            except Exception:
                return 0.0

        def _calculate_position_bonus(self, position, ranking_type):
            position_bonus = max(0, (51 - position) / 50) * 2.0
            type_weights = {
                'rising': 1.5, 'hot': 1.2, 'new': 1.0, 'falling': 0.8
            }
            weight = type_weights.get(ranking_type, 1.0)
            return position_bonus * weight

    discoverer = MockDiscoverer()
    assert discoverer.discovery_limit == 50
    assert discoverer.min_opportunity_score == 3.0
    print("✓ 发现器服务测试通过")
    
    # 测试价差计算
    print("测试价差计算...")
    price_info = {'ask_price': 110.0, 'bid_price': 100.0}
    spread = discoverer._calculate_price_spread(price_info)
    assert spread == 10.0
    print("✓ 价差计算测试通过")
    
    # 测试位置奖励
    print("测试位置奖励...")
    bonus = discoverer._calculate_position_bonus(1, 'rising')
    assert bonus > 0
    print("✓ 位置奖励测试通过")
    
    print("所有机会发现服务测试通过！")
