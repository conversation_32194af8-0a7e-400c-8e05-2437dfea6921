"""
API管理器测试模块
验证令牌桶限流、重试机制和API调用功能
"""

import asyncio
import pytest
import time
from unittest.mock import AsyncMock, patch, MagicMock
from cryptography.fernet import Fernet

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.api_manager import TokenBucket, ExponentialBackoff, APIManager, APICallResult
from core.exceptions import APILimitExceeded, APIConnectionError


class TestTokenBucket:
    """令牌桶测试类"""
    
    @pytest.mark.asyncio
    async def test_token_bucket_initialization(self):
        """测试令牌桶初始化"""
        bucket = TokenBucket(capacity=10, refill_rate=1.0)
        assert bucket.capacity == 10
        assert bucket.tokens == 10.0
        assert bucket.refill_rate == 1.0
    
    @pytest.mark.asyncio
    async def test_token_acquisition(self):
        """测试令牌获取"""
        bucket = TokenBucket(capacity=5, refill_rate=1.0)
        
        # 应该能够获取令牌
        assert await bucket.acquire(1) == True
        assert bucket.tokens == 4.0
        
        # 获取多个令牌
        assert await bucket.acquire(3) == True
        assert bucket.tokens == 1.0
        
        # 令牌不足时应该失败
        assert await bucket.acquire(2) == False
        assert bucket.tokens == 1.0
    
    @pytest.mark.asyncio
    async def test_token_refill(self):
        """测试令牌补充"""
        bucket = TokenBucket(capacity=5, refill_rate=2.0)  # 每秒2个令牌
        
        # 消耗所有令牌
        await bucket.acquire(5)
        assert bucket.tokens == 0.0
        
        # 等待1秒，应该补充2个令牌
        await asyncio.sleep(1.1)
        assert await bucket.acquire(1) == True
        
        # 验证令牌数量
        status = bucket.get_status()
        assert status['capacity'] == 5
        assert status['refill_rate'] == 2.0
    
    @pytest.mark.asyncio
    async def test_wait_for_token(self):
        """测试等待令牌功能"""
        bucket = TokenBucket(capacity=2, refill_rate=1.0)
        
        # 消耗所有令牌
        await bucket.acquire(2)
        
        # 等待令牌应该成功（在超时时间内）
        start_time = time.time()
        result = await bucket.wait_for_token(1, timeout=2.0)
        elapsed = time.time() - start_time
        
        assert result == True
        assert elapsed >= 1.0  # 至少等待了1秒


class TestExponentialBackoff:
    """指数退避测试类"""
    
    def test_delay_calculation(self):
        """测试延迟时间计算"""
        backoff = ExponentialBackoff(base_delay=1.0, max_delay=10.0)
        
        assert backoff.get_delay(0) == 1.0
        assert backoff.get_delay(1) == 2.0
        assert backoff.get_delay(2) == 4.0
        assert backoff.get_delay(3) == 8.0
        assert backoff.get_delay(4) == 10.0  # 受max_delay限制
    
    @pytest.mark.asyncio
    async def test_retry_success(self):
        """测试重试成功场景"""
        backoff = ExponentialBackoff(base_delay=0.1, max_retries=3)
        
        call_count = 0
        async def mock_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = await backoff.execute_with_retry(mock_function)
        assert result == "success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_retry_exhausted(self):
        """测试重试耗尽场景"""
        backoff = ExponentialBackoff(base_delay=0.1, max_retries=2)
        
        async def mock_function():
            raise Exception("Persistent failure")
        
        with pytest.raises(Exception, match="Persistent failure"):
            await backoff.execute_with_retry(mock_function)


class TestAPIManager:
    """API管理器测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.config = {
            'base_url': 'https://api.test.com',
            'api_key': 'test_key',
            'encryption_key': Fernet.generate_key().decode(),
            'rate_limit': {
                'calls_per_minute': 10,
                'retry_attempts': 3,
                'retry_base_delay': 0.1,
                'retry_max_delay': 1.0
            }
        }
    
    @pytest.mark.asyncio
    async def test_api_manager_initialization(self):
        """测试API管理器初始化"""
        manager = APIManager(self.config)
        
        assert manager.base_url == 'https://api.test.com'
        assert manager.api_key == 'test_key'
        assert manager.bucket.capacity == 10
        assert manager.backoff.max_retries == 3
    
    @pytest.mark.asyncio
    async def test_session_management(self):
        """测试会话管理"""
        manager = APIManager(self.config)
        
        # 启动会话
        await manager.start_session()
        assert manager.session is not None
        
        # 关闭会话
        await manager.close_session()
        assert manager.session is None
    
    @pytest.mark.asyncio
    async def test_api_key_encryption(self):
        """测试API密钥加密"""
        manager = APIManager(self.config)
        
        original_key = "test_api_key"
        encrypted_key = manager.encrypt_api_key(original_key)
        decrypted_key = manager.decrypt_api_key(encrypted_key)
        
        assert encrypted_key != original_key
        assert decrypted_key == original_key
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """测试限流功能"""
        # 设置非常低的限流
        config = self.config.copy()
        config['rate_limit']['calls_per_minute'] = 1
        
        manager = APIManager(config)
        
        # 模拟HTTP会话
        mock_session = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {'data': 'test'}
        mock_session.request.return_value.__aenter__.return_value = mock_response
        
        manager.session = mock_session
        
        # 第一次调用应该成功
        response1 = await manager.call_api('/test')
        assert response1.status == APICallResult.SUCCESS
        
        # 立即第二次调用应该被限流
        response2 = await manager.call_api('/test', timeout=0.1)
        assert response2.status == APICallResult.RATE_LIMITED
    
    @pytest.mark.asyncio
    async def test_metrics_collection(self):
        """测试指标收集"""
        manager = APIManager(self.config)
        
        # 记录一些指标
        await manager.metrics.record_call(APICallResult.SUCCESS, 0.5)
        await manager.metrics.record_call(APICallResult.ERROR, 1.0)
        await manager.metrics.record_call(APICallResult.RATE_LIMITED, 0.0)
        
        metrics = manager.get_metrics()
        
        assert metrics['api_metrics']['total_calls'] == 3
        assert metrics['api_metrics']['successful_calls'] == 1
        assert metrics['api_metrics']['failed_calls'] == 1
        assert metrics['api_metrics']['rate_limited_calls'] == 1
        assert metrics['api_metrics']['success_rate'] == 1/3


if __name__ == "__main__":
    # 运行基本测试
    async def run_basic_tests():
        print("运行API管理器基本测试...")
        
        # 测试令牌桶
        print("测试令牌桶...")
        bucket = TokenBucket(capacity=5, refill_rate=1.0)
        assert await bucket.acquire(3) == True
        assert await bucket.acquire(3) == False
        print("✓ 令牌桶测试通过")
        
        # 测试指数退避
        print("测试指数退避...")
        backoff = ExponentialBackoff(base_delay=0.1, max_delay=1.0)
        assert backoff.get_delay(0) == 0.1
        assert backoff.get_delay(3) == 0.8
        print("✓ 指数退避测试通过")
        
        # 测试API管理器
        print("测试API管理器...")
        config = {
            'base_url': 'https://api.test.com',
            'api_key': 'test_key',
            'encryption_key': Fernet.generate_key().decode(),
            'rate_limit': {'calls_per_minute': 10}
        }
        
        manager = APIManager(config)
        await manager.start_session()
        
        # 测试加密
        encrypted = manager.encrypt_api_key("test")
        decrypted = manager.decrypt_api_key(encrypted)
        assert decrypted == "test"
        
        await manager.close_session()
        print("✓ API管理器测试通过")
        
        print("所有测试通过！")
    
    asyncio.run(run_basic_tests())
